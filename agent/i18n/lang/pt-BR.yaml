ErrInvalidParams: 'Erro de parâmetro de solicitação: {{ .detail }}'
ErrTokenParse: 'Erro de geração de token: {{ .detail }}'
ErrInitialPassword: 'A senha original está incorreta'
ErrInternalServer: 'Erro interno do servidor: {{ .detail }}'
ErrRecordExist: 'O registro já existe'
ErrRecordNotFound: 'Registro não encontrado'
ErrStructTransform: 'Falha na conversão de tipo: {{ .err }}'
ErrNotLogin: 'Usuário não logado: {{ .detail }}'
ErrPasswordExpired: 'A senha atual expirou: {{ .detail }}'
ErrNotSupportType: 'O sistema não suporta o tipo atual: {{ .name }}'
ErrProxy: 'Erro de solicitação, verifique o status do nó: {{ .detail }}'
ErrApiConfigStatusInvalid: 'O acesso à interface da API é proibido: {{ .detail }}'
ErrApiConfigKeyInvalid: 'Erro de chave da interface da API: {{ .detail }}'
ErrApiConfigIPInvalid: 'O IP usado para chamar a interface da API não está na lista de permissões: {{ .detail }}'
ErrApiConfigDisable: 'Esta interface proíbe o uso de chamadas de interface de API: {{ .detail }}'
ErrApiConfigKeyTimeInvalid: 'Erro de registro de data e hora da interface da API: {{ .detail }}'

#comum
ErrUsernameIsExist: 'Nome de usuário já existe'
ErrNameIsExist: 'Nome já existe'
ErrDemoEnvironment: 'Servidor de demonstração, esta operação é proibida!'
ErrCmdTimeout: 'Tempo limite de execução do comando expirou!'
ErrCmdIllegal: 'Há caracteres ilegais no comando de execução, modifique-o e tente novamente!'
ErrPortExist: 'A porta {{ .port }} já está ocupada por {{ .type }} [{{ .name }}]'
TYPE_APP: 'Aplicativo'
TYPE_RUNTIME: 'Ambiente de tempo de execução'
TYPE_DOMAIN: 'Nome de domínio'
ErrTypePort: 'O formato da porta {{ .name }} está incorreto'
ErrTypePortRange: 'O intervalo de portas precisa estar entre 1-65535'
Success: 'Sucesso'
Failed: 'Falhou'
SystemRestart: 'Tarefa interrompida devido à reinicialização do sistema'
ErrGroupIsDefault: 'Grupo padrão, não pode ser excluído'
ErrGroupIsInWebsiteUse: 'O grupo está sendo usado por outro site e não pode ser excluído.'

#backup
ErrBackupInUsed: 'A conta de backup foi usada na tarefa agendada e não pode ser excluída.'
ErrBackupCheck: 'Falha na conexão de teste da conta de backup {{ .err }}'
ErrBackupLocalDelete: 'A exclusão da conta de backup do servidor local ainda não é suportada'
ErrBackupLocalCreate: 'A criação de contas de backup do servidor local ainda não é suportada'

#aplicativo
ErrPortInUsed: 'A porta {{ .detail }} já está ocupada!'
ErrAppLimit: 'O número de aplicativos instalados excedeu o limite'
ErrNotInstall: 'Aplicativo não instalado'
ErrPortInOtherApp: 'A porta {{ .port }} já está ocupada pelo aplicativo {{ .apps }}!'
ErrDbUserNotValid: 'Banco de dados existente, nome de usuário e senha não correspondem!'
ErrUpdateBuWebsite: 'O aplicativo foi atualizado com sucesso, mas a modificação do arquivo de configuração do site falhou. Por favor, verifique a configuração! '
Err1PanelNetworkFailed: 'Falha na criação da rede de contêiner padrão! {{ .detalhe }}'
ErrFileParse: 'Falha na análise do arquivo docker-compose do aplicativo!'
ErrInstallDirNotFound: 'O diretório de instalação não existe. Se precisar desinstalar, selecione Forçar desinstalação'
AppStoreIsUpToDate: 'A loja de aplicativos já é a versão mais recente'
LocalAppVersionNull: 'O aplicativo {{ .name }} não está sincronizado com a versão! Não é possível adicionar à lista de aplicativos'
LocalAppVersionErr: '{{ .name }} sincronização da versão {{ .version }} falhou! {{ .err }}'
ErrFileNotFound: 'arquivo {{ .name }} não existe'
ErrFileParseApp: 'Falha na análise do arquivo {{ .name }} {{ .err }}'
ErrAppDirNull: 'A pasta da versão não existe'
LocalAppErr: 'Falha na sincronização do aplicativo {{ .name }}! {{ .err }}'
ErrContainerName: 'O nome do contêiner já existe'
ErrCreateHttpClient: 'Falha ao criar solicitação {{ .err }}'
ErrHttpReqTimeOut: 'Tempo limite da solicitação esgotado {{ .err }}'
ErrHttpReqFailed: 'Falha na solicitação {{ .err }}'
ErrNoSuchHost: 'Não foi possível encontrar o servidor solicitado {{ .err }}'
ErrHttpReqNotFound: 'O recurso solicitado {{ .err }} não pôde ser encontrado'
ErrContainerNotFound: '{{ .name }} container não existe'
ErrContainerMsg: 'O contêiner {{ .name }} está anormal. Por favor, verifique o log na página do contêiner para obter detalhes.'
ErrAppBackup: '{{ .name }} falha no backup do aplicativo {{ .err }}'
ErrVersionTooLow: 'A versão atual do 1Panel é muito antiga para atualizar a App Store. Por favor, atualize a versão antes de operar.'
ErrAppNameExist: 'O nome do aplicativo já existe'
AppStoreIsSyncing: 'A App Store está sincronizando, tente novamente mais tarde'
ErrGetCompose: 'Falha ao obter o arquivo docker-compose.yml! {{ .detail }}'
ErrAppWarn: 'Status anormal, verifique o log'
ErrAppParamKey: 'O campo de parâmetro {{ .name }} está anormal'
ErrAppUpgrade: 'Falha na atualização do aplicativo {{ .name }} {{ .err }}'
AppRecover: 'Reverter aplicativo {{ .name }}'
PullImageStart: 'Comece a extrair a imagem {{ .name }}'
PullImageSuccess: 'Imagem retirada com sucesso'
AppStoreIsLastVersion: 'A App Store já é a versão mais recente'
AppStoreSyncSuccess: 'Sincronização da App Store bem-sucedida'
SyncAppDetail: 'Sincronizar configuração do aplicativo'
AppVersionNotMatch: 'o aplicativo {{ .name }} requer uma versão superior do 1Panel, ignorando a sincronização'
MoveSiteDir: 'A atualização atual requer a migração do diretório do site OpenResty'
MoveSiteToDir: 'Migrar diretório do site para {{ .name }}'
ErrMoveSiteDir: 'Falha ao migrar o diretório do site'
MoveSiteDirSuccess: 'Migração bem-sucedida do diretório do site'
DeleteRuntimePHP: 'Excluir tempo de execução do PHP'
CustomAppStoreFileValid: 'Os pacotes da App Store precisam estar no formato .tar.gz'
PullImageTimeout: 'Tempo limite para puxar imagem, tente aumentar a aceleração da imagem ou altere para outra aceleração de imagem'
ErrAppIsDown: 'O status do aplicativo {{ .name }} é anormal, verifique'
ErrCustomApps: 'Há um aplicativo instalado, desinstale-o primeiro'
ErrCustomRuntimes: 'Há um ambiente de execução instalado, exclua-o primeiro'
ErrAppVersionDeprecated: "O aplicativo {{ .name }} não é compatível com a versão atual do 1Panel, ignorado"
ErrDockerFailed: "O estado do Docker está anormal, verifique o status do serviço"
ErrDockerComposeCmdNotFound: "O comando Docker Compose não existe, por favor, instale este comando na máquina host primeiro"

#arquivo
ErrFileCanNotRead: 'Este arquivo não suporta visualização'
ErrFileToLarge: 'O arquivo é maior que 10M e não pode ser aberto'
ErrPathNotFound: 'O diretório não existe'
ErrMovePathFailed: 'O caminho de destino não pode conter o caminho original!'
ErrLinkPathNotFound: 'O caminho de destino não existe!'
ErrFileIsExist: 'O arquivo ou pasta já existe!'
ErrFileUpload: '{{ .name }} falhou ao carregar o arquivo {{ .detail }}'
ErrFileDownloadDir: 'A pasta de download não é suportada'
ErrCmdNotFound: 'O comando {{ .name}} não existe, instale este comando no host primeiro'
ErrSourcePathNotFound: 'O diretório de origem não existe'
ErrFavoriteExist: 'Este caminho já foi favorito'
ErrInvalidChar: 'Caracteres ilegais não são permitidos'
ErrPathNotDelete: 'O diretório selecionado não pode ser excluído'
ErrLogFileToLarge: "Fail log melebihi 500MB, tidak boleh dibuka"

#site
ErrAliasIsExist: 'Alias já existe'
ErrBackupMatch: 'O arquivo de backup não corresponde a alguns dados atuais do site {{ .detail }}'
ErrBackupExist: 'A parte correspondente dos dados de origem no arquivo de backup não existe {{ .detail }}'
ErrPHPResource: 'O ambiente operacional local não suporta comutação! '
ErrPathPermission: 'Uma pasta com permissões diferentes de 1000:1000 foi detectada no diretório de índice, o que pode causar um erro de acesso negado no site. Clique no botão Salvar acima'
ErrDomainIsUsed: 'O nome de domínio já está sendo usado pelo site [{{ .name }}]'
ErrDomainFormat: 'o formato do nome de domínio {{ .name }} está incorreto'
ErrDefaultAlias: 'padrão é um código reservado, use outro código'
ErrParentWebsite: 'Você precisa excluir o subsite {{ .name }} primeiro'
ErrBuildDirNotFound: 'O diretório de compilação não existe'
ErrImageNotExist: 'A imagem do ambiente operacional {{ .name }} não existe, edite novamente o ambiente operacional'
ErrProxyIsUsed: "Balanceamento de carga foi usado por proxy reverso, não pode ser excluído"
ErrSSLValid: 'O arquivo do certificado está anormal, verifique o status do certificado!'

#ssl
ErrSSLCannotDelete: 'O certificado {{ .name }} está sendo usado por um site e não pode ser excluído'
ErrAccountCannotDelete: 'A conta está associada a um certificado e não pode ser excluída'
ErrSSLApply: 'Renovação do certificado bem-sucedida, falha na recarga do Openresty, verifique a configuração!'
ErrEmailIsExist: 'A caixa de correio já existe'
ErrSSLKeyNotFound: 'O arquivo de chave privada não existe'
ErrSSLCertificateNotFound: 'O arquivo de certificado não existe'
ErrSSLKeyFormat: 'Falha na verificação do arquivo de chave privada'
ErrSSLCertificateFormat: 'O formato do arquivo de certificado está incorreto, use o formato pem'
ErrEabKidOrEabHmacKeyCannotBlank: 'EabKid ou EabHmacKey não podem estar em branco'
ErrOpenrestyNotFound: 'O modo HTTP requer que o Openresty seja instalado primeiro'
ApplySSLStart: 'Comece a solicitar um certificado, nome de domínio [{{ .domain }}] método de solicitação [{{ .type }}]'
dnsAccount: 'DNS Automático'
dnsManual: 'Manual DNS'
http: 'HTTP'
ApplySSLFailed: 'Falha na solicitação de certificado [{{ .domain }}], {{ .detail }} '
ApplySSLSuccess: 'Solicitação de certificado [{{ .domain }}] realizada com sucesso! ! '
DNSAccountName: 'Conta DNS [{{ .name }}] fornecedor [{{ .type }}]'
PushDirLog: 'Certificado enviado para o diretório [{{ .path }}] {{ .status }}'
ErrDeleteCAWithSSL: 'A organização atual tem um certificado que foi emitido e não pode ser excluído.'
ErrDeleteWithPanelSSL: 'A configuração SSL do painel usa este certificado e não pode ser excluída'
ErrDefaultCA: 'A autoridade padrão não pode ser excluída'
ApplyWebSiteSSLLog: 'Iniciando a renovação do certificado do site {{ .name }}'
ErrUpdateWebsiteSSL: 'Falha na atualização do certificado do site {{ .name }}: {{ .err }}'
ApplyWebSiteSSLSuccess: 'Atualizar certificado do site com sucesso'
ErrExecShell: 'Falha ao executar o script {{ .err }}'
ExecShellStart: 'Iniciar execução do script'
ExecShellSuccess: 'Execução do script bem-sucedida'
StartUpdateSystemSSL: 'Iniciar atualização do certificado do sistema'
UpdateSystemSSLSuccess: 'Atualizar certificado do sistema com sucesso'
ErrWildcardDomain: 'Não é possível solicitar certificado de nome de domínio curinga no modo HTTP'
ErrApplySSLCanNotDelete: "O certificado {{.name}} que está sendo solicitado não pode ser excluído, tente novamente mais tarde."

#mysql
ErrUserIsExist: 'O usuário atual já existe, digite novamente'
ErrDatabaseIsExist: 'O banco de dados atual já existe, digite novamente'
ErrExecTimeOut: 'Tempo limite de execução do SQL expirou, verifique o banco de dados'
ErrRemoteExist: 'O banco de dados remoto já existe com este nome, modifique-o e tente novamente'
ErrLocalExist: 'O nome já existe no banco de dados local, modifique-o e tente novamente'

#redis
ErrTypeOfRedis: 'O tipo de arquivo de recuperação não corresponde ao método de persistência atual, modifique-o e tente novamente'

#recipiente
ErrInUsed: '{{ .detail }} está em uso e não pode ser excluído'
ErrObjectInUsed: 'O objeto está em uso e não pode ser excluído'
ErrObjectBeDependent: 'Esta imagem depende de outras imagens e não pode ser excluída'
ErrPortRules: 'O número da porta não corresponde, digite novamente!'
ErrPgImagePull: 'Tempo limite para extração de imagem. Configure a aceleração de imagem ou extraia manualmente a imagem {{ .name }} e tente novamente'
PruneHelper: "Esta limpeza removeu {{ .name }} {{ .count }} itens, liberando {{ .size }} de espaço em disco"
ImageRemoveHelper: "Excluída a imagem {{ .name }}, liberando {{ .size }} de espaço em disco"
BuildCache: "Cache de construção"
Volume: "Volume de armazenamento"
Network: "Rede"

#tempo de execução
ErrFileNotExist: 'O arquivo {{ .detail }} não existe! Verifique a integridade do arquivo de origem!'
ErrImageBuildErr: 'Falha na criação da imagem'
ErrImageExist: "A imagem já existe! Por favor, modifique o nome da imagem."
ErrDelWithWebsite: 'O ambiente operacional já está associado a um site e não pode ser excluído'
ErrRuntimeStart: 'Falha na inicialização'
ErrPackageJsonNotFound: 'o arquivo package.json não existe'
ErrScriptsNotFound: 'O item de configuração de scripts não foi encontrado em package.json'
ErrContainerNameNotFound: 'Não foi possível obter o nome do contêiner, verifique o arquivo .env'
ErrNodeModulesNotFound: 'A pasta node_modules não existe! Edite o ambiente de execução ou aguarde até que o ambiente de execução inicie com sucesso'
ErrContainerNameIsNull: 'O nome do contêiner não existe'
ErrPHPPortIsDefault: "Port 9000 adalah port lalai, sila ubah dan cuba lagi"
ErrPHPRuntimePortFailed: "Port {{ .name }} telah digunakan oleh persekitaran runtime semasa, sila ubah dan cuba lagi"

#ferramenta
ErrConfigNotFound: 'O arquivo de configuração não existe'
ErrConfigParse: 'O formato do arquivo de configuração está incorreto'
ErrConfigIsNull: 'O arquivo de configuração não pode estar vazio'
ErrConfigDirNotFound: 'O diretório de execução não existe'
ErrConfigAlreadyExist: 'Um arquivo de configuração com o mesmo nome já existe'
ErrUserFindErr: 'Falha na pesquisa do usuário {{ .name }} {{ .err }}'

#cronjob
CutWebsiteLogSuccess: '{{ .name }} registro do site cortado com sucesso, caminho de backup {{ .path }}'
HandleShell: 'Executar script {{ .name }}'
HandleNtpSync: 'Sincronização de hora do sistema'
HandleSystemClean: 'Limpeza de cache do sistema'
SystemLog: 'Log do Sistema'
CutWebsiteLog: 'Rotacionar Log do Website'
FileOrDir: 'Diretório / Arquivo'
UploadFile: 'Enviando arquivo de backup {{ .file }} para {{ .backup }}'
IgnoreBackupErr: 'Backup falhou, erro: {{ .detail }}, ignorando este erro...'
IgnoreUploadErr: 'Upload falhou, erro: {{ .detail }}, ignorando este erro...'

#caixa de ferramentas
ErrNotExistUser: 'O usuário atual não existe, modifique e tente novamente!'
ErrBanAction: 'Falha na configuração. O serviço atual {{ .name }} não está disponível. Verifique e tente novamente!'
ErrClamdscanNotFound: 'O comando clamdscan não foi detectado, consulte a documentação para instalá-lo!'

#waf
ErrScope: 'A modificação desta configuração não é suportada'
ErrStateChange: 'Falha na alteração de estado'
ErrRuleExist: 'A regra já existe'
ErrRuleNotExist: 'A regra não existe'
ErrParseIP: 'Formato de IP errado'
ErrDefaultIP: 'padrão é um nome reservado, altere-o para outro nome'
ErrGroupInUse: 'O grupo de IP é usado pela lista negra/lista branca e não pode ser excluído'
ErrIPGroupAclUse: "O grupo de IP está sendo usado por regras personalizadas do site {{ .name }}, não pode ser excluído"
ErrGroupExist: 'O nome do grupo IP já existe'
ErrIPRange: 'Intervalo de IP errado'
ErrIPExist: 'IP já existe'
urlDefense: 'regras de URL'
urlHelper: 'URL proibido'
dirFilter: 'Filtro de diretório'
xss: 'XSS'
phpExec: 'Execução de script PHP'
oneWordTrojan: 'Cavalo de Tróia de Uma Palavra'
appFilter: 'Aplicar filtragem de diretório perigosa'
webshell: 'Webshell'
args: 'Regras de parâmetros'
protocolFilter: 'Filtragem de protocolo'
javaFileter: 'Filtro de arquivo perigoso Java'
scannerFilter: 'Filtro do scanner'
escapeFilter: 'filtro de escape'
customRule: 'Regra personalizada'
httpMethod: 'Filtragem de método HTTP'
fileExt: 'Restrições de upload de arquivo'
defaultIpBlack: 'Grupo de IP malicioso'
cookie: 'Regras de cookies'
urlBlack: 'lista negra de URLs'
uaBlack: 'Lista negra de agentes do usuário'
attackCount: 'Limite de frequência de ataque'
fileExtCheck: 'Restrições de upload de arquivo'
geoRestrict: 'Restrições de acesso regionais'
unknownWebsite: 'Acesso não autorizado ao nome de domínio'
notFoundCount: 'Limite de taxa 404'
headerDefense: 'Regras de cabeçalho'
defaultUaBlack: 'Regras do agente do usuário'
methodWhite: 'regras HTTP'
captcha: 'verificação homem-máquina'
fiveSeconds: 'verificação de 5 segundos'
vulnCheck: 'Regras suplementares'
acl: 'Regras personalizadas'
sql: 'injeção de SQL'
cc: 'Limite de frequência de acesso'
defaultUrlBlack: 'regras de URL'
sqlInject: 'injeção de SQL'
ErrDBNotExist: 'O banco de dados não existe'
allow: 'permitir'
deny: 'negar'
OpenrestyNotFound: 'Openresty não está instalado'
remoteIpIsNull: "A lista de IP está vazia"
OpenrestyVersionErr: "A versão do Openresty é muito baixa, por favor atualize o Openresty para ********-2-2-focal"

#tarefa
TaskStart: '{{ .name }} A tarefa inicia [START]'
TaskEnd: '{{ .name }} Tarefa concluída [CONCLUÍDA]'
TaskFailed: '{{ .name }} tarefa falhou'
TaskTimeout: '{{ .name }} expirou'
TaskSuccess: '{{ .name }} Tarefa bem-sucedida'
TaskRetry: 'Iniciar {{ .name }}ª nova tentativa'
SubTaskSuccess: '{{ .name }} bem-sucedido'
SubTaskFailed: '{{ .name }} falhou: {{ .err }}'
TaskInstall: 'Instalar'
TaskUninstall: 'Desinstalar'
TaskCreate: 'Criar'
TaskDelete: 'Excluir'
TaskUpgrade: 'Atualizar'
TaskUpdate: 'Atualizar'
TaskRestart: 'Reiniciar'
Backup de Tarefa: 'Backup'
TaskRecover: 'Recuperar'
TaskRollback: 'Reverter'
TaskPull: 'Puxar'
TaskCommit: 'Commit'
TaskBuild: 'Construir'
TaskPush: 'Empurrar'
TaskClean: "Limpeza"
TaskHandle: 'Executar'
Website: 'Site'
App: 'Aplicativo'
Runtime: 'Ambiente de tempo de execução'
Database: 'Banco de dados'
ConfigFTP: 'Criar usuário FTP {{ .name }}'
ConfigOpenresty: 'Criar arquivo de configuração Openresty'
InstallAppSuccess: 'Aplicativo {{ .name }} instalado com sucesso'
ConfigRuntime: 'Configurar o ambiente de tempo de execução'
ConfigApp: 'Aplicativo de configuração'
SuccessStatus: '{{ .name }} bem-sucedido'
FailedStatus: '{{ .name }} falhou {{ .err }}'
HandleLink: 'Manipular associação de aplicativos'
HandleDatabaseApp: 'Manipulando parâmetros do aplicativo'
ExecShell: 'Executar script {{ .name }}'
PullImage: 'Puxar imagem'
Start: 'Iniciar'
Run: 'Iniciar'
Stop: 'Pare'
Image: 'Espelho'
Compose: 'Orquestração'
Container: 'Recipiente'
AppLink: 'Aplicativo vinculado'
EnableSSL: 'Habilitar HTTPS'
AppStore: 'Loja de aplicativos'
TaskSync: 'Sincronizar'
LocalApp: 'Aplicativo local'
SubTask: 'Subtarefa'
RuntimeExtension: 'Extensão do ambiente de tempo de execução'
TaskIsExecuting: 'A tarefa está em execução'
CustomAppstore: 'Armazém de aplicativos personalizados'

# tarefa - ai
OllamaModelPull: 'Puxar modelo Ollama {{ .name }}'
OllamaModelSize: 'Obtenha o tamanho do modelo Ollama {{ .name }}'

# tarefa-instantânea
Snapshot: 'Instantâneo'
SnapDBInfo: 'Escrever informações do banco de dados 1Panel'
SnapCopy: 'Copiar arquivos e diretórios {{ .name }}'
SnapNewDB: 'Inicializar conexão com o banco de dados {{ .name }}'
SnapDeleteOperationLog: 'Excluir log de operação'
SnapDeleteLoginLog: 'Excluir log de acesso'
SnapDeleteMonitor: 'Excluir dados de monitoramento'
SnapRemoveSystemIP: 'Remover IP do sistema'
SnapBaseInfo: 'Escreva informações básicas do 1Panel'
SnapInstallAppImageEmpty: 'Nenhuma imagem de aplicativo selecionada, pulando...'
SnapInstallApp: 'Backup dos aplicativos instalados do 1Panel'
SnapDockerSave: 'Compactar aplicativos instalados'
SnapLocalBackup: 'Backup do diretório de backup local do 1Panel'
SnapCompressBackup: 'Compactar diretório de backup local'
SnapPanelData: 'Backup do diretório de dados do 1Panel'
SnapCompressPanel: 'Diretório de dados compactados'
SnapWebsite: 'Backup do diretório do site 1Panel'
SnapCloseDBConn: 'Fechar a conexão com o banco de dados'
SnapCompress: 'Criar arquivos de instantâneos'
SnapCompressFile: 'Compactar arquivo de instantâneo'
SnapCheckCompress: 'Verificar arquivo de compactação de instantâneo'
SnapCompressSize: 'Tamanho do arquivo de instantâneo {{ .name }}'
SnapUpload: 'Carregar arquivo de instantâneo'
SnapLoadBackup: 'Obter informações da conta de backup'
SnapUploadTo: 'Carregar arquivo de instantâneo para {{ .name }}'
SnapUploadRes: 'Carregar arquivo de instantâneo para {{ .name }}'

SnapshotRecover: 'Restauração de instantâneo'
RecoverDownload: 'Baixar arquivo de instantâneo'
Download: 'Baixar'
RecoverDownloadAccount: 'Obter conta de backup de download de instantâneo {{ .name }}'
RecoverDecompress: 'Descompacte arquivos compactados de instantâneos'
Decompress: 'Descompressão'
BackupBeforeRecover: 'Fazer backup de dados relacionados ao sistema antes do snapshot'
Readjson: 'Ler o arquivo Json no snapshot'
ReadjsonPath: 'Obtenha o caminho do arquivo Json no snapshot'
ReadjsonContent: 'Ler arquivo Json'
ReadjsonMarshal: 'Processamento de escape JSON'
RecoverApp: 'Restaurar aplicativos instalados'
RecoverWebsite: 'Recuperar diretório de sites'
RecoverAppImage: 'Restaurar backup de imagem instantânea'
RecoverCompose: 'Restaurar outro conteúdo do compositor'
RecoverComposeList: 'Obter todos os compositores para serem restaurados'
RecoverComposeItem: 'Recuperar composição {{ .name }}'
RecoverAppEmpty: 'Nenhum backup de imagem de aplicativo foi encontrado no arquivo de instantâneo'
RecoverBaseData: 'Recuperar dados e arquivos básicos'
RecoverDaemonJsonEmpty: 'O arquivo de instantâneo e a máquina atual não têm o arquivo daemon.json de configuração do contêiner'
RecoverDaemonJson: 'Restaurar arquivo daemon.json de configuração do contêiner'
RecoverDBData: 'Recuperar dados do banco de dados'
RecoverBackups: 'Restaurar diretório de backup local'
RecoverPanelData: 'Diretório de dados de recuperação'

# tarefa - contêiner
ContainerNewCliet: 'Inicializar cliente Docker'
ContainerImagePull: 'Puxar imagem do contêiner {{ .name }}'
ContainerRemoveOld: 'Remover o contêiner original {{ .name }}'
ContainerImageCheck: 'Verifique se a imagem é extraída normalmente'
ContainerLoadInfo: 'Obter informações básicas do contêiner'
ContainerRecreate: 'A atualização do contêiner falhou, agora iniciando a restauração do contêiner original'
ContainerCreate: 'Criar um novo contêiner {{ .name }}'
ContainerCreateFailed: 'Falha na criação do contêiner, exclua o contêiner com falha'
ContainerStartCheck: 'Verifique se o contêiner foi iniciado'

# tarefa - imagem
ImageBuild: 'Construção de imagem'
ImageBuildStdoutCheck: 'Analisar conteúdo de saída da imagem'
ImageBuildRes: 'Saída da criação da imagem: {{ .name }}'
ImagePull: 'Puxar imagem'
ImageRepoAuthFromDB: 'Obter informações de autenticação do repositório do banco de dados'
ImaegPullRes: 'Saída de extração de imagem: {{ .name }}'
ImagePush: 'Enviar imagem'
ImageRenameTag: 'Modificar tag de imagem'
ImageNewTag: 'Nova tag de imagem {{ .name }}'
ImaegPushRes: 'Saída de envio de imagem: {{ .name }}'
ComposeCreate: 'Criar uma composição'
ComposeCreateRes: 'Compose cria saída: {{ .name }}'

# tarefa - site
BackupNginxConfig: 'Fazer backup do arquivo de configuração do OpenResty do site'
CompressFileSuccess: 'Diretório compactado com sucesso, compactado para {{ .name }}'
CompressDir: 'Diretório de compressão'
DeCompressFile: 'Descompacte o arquivo {{ .name }}'
ErrCheckValid: 'Falha na verificação do arquivo de backup, {{ .name }}'
Rollback: 'Reversão'
websiteDir: 'Diretório de sites'
RecoverFailedStartRollBack: 'Falha na recuperação, iniciar reversão'
AppBackupFileIncomplete: 'O arquivo de backup está incompleto e não contém os arquivos app.json ou app.tar.gz'
AppAttributesNotMatch: 'O tipo ou nome do aplicativo não corresponde'

#alerta
ErrAlert: 'O formato da mensagem de aviso está incorreto, verifique e tente novamente!'
ErrAlertPush: 'Erro ao enviar informações de alerta, verifique e tente novamente!'
ErrAlertSave: 'Erro ao salvar as informações do alarme. Verifique e tente novamente!'
ErrAlertSync: 'Erro de sincronização de informações de alarme, verifique e tente novamente!'
ErrAlertRemote: 'Erro remoto na mensagem de alarme, verifique e tente novamente!'

#task - runtime
ErrInstallExtension: "Já existe uma tarefa de instalação em andamento, aguarde a conclusão da tarefa"

# alert mail template
PanelAlertTitle: "Notificação de Alerta do Painel"
TestAlertTitle: "E-mail de Teste - Verificar Conectividade de E-mail"
TestAlert: "Este é um e-mail de teste para verificar se sua configuração de envio de e-mails está correta."
LicenseExpirationAlert: "A licença do seu 1Panel expirará em {{ .day }} dias. Acesse o painel para mais detalhes."
CronJobFailedAlert: "A tarefa agendada '{{ .name }}' do seu 1Panel falhou. Acesse o painel para mais detalhes."
ClamAlert: "A verificação de vírus do 1Panel detectou {{ .num }} arquivos infectados. Acesse o painel para mais detalhes."
WebSiteAlert: "{{ .num }} sites do seu 1Panel expirarão em {{ .day }} dias. Acesse o painel para mais detalhes."
SSLAlert: "{{ .num }} certificados SSL do seu 1Panel expirarão em {{ .day }} dias. Acesse o painel para mais detalhes."
DiskUsedAlert: "O disco '{{ .name }}' do seu 1Panel está com uso de {{ .used }}. Acesse o painel para mais detalhes."
ResourceAlert: "O uso médio de {{ .name }} em {{ .time }} minutos no seu 1Panel é de {{ .used }}. Acesse o painel para mais detalhes."
PanelVersionAlert: "Uma nova versão do 1Panel está disponível. Acesse o painel para atualizá-lo."
PanelPwdExpirationAlert: "A senha do 1Panel expirará em {{ .day }} dias. Acesse o painel para mais detalhes."