ErrInvalidParams: '요청 매개변수 오류: {{ .detail }}'
ErrTokenParse: '토큰 생성 오류: {{ .detail }}'
ErrInitialPassword: '원래 비밀번호가 올바르지 않습니다'
ErrInternalServer: '내부 서버 오류: {{ .detail }}'
ErrRecordExist: '레코드가 이미 존재합니다'
ErrRecordNotFound: '레코드를 찾을 수 없습니다'
ErrStructTransform: '유형 변환에 실패했습니다: {{ .err }}'
ErrNotLogin: '사용자가 로그인하지 않았습니다: {{ .detail }}'
ErrPasswordExpired: '현재 비밀번호가 만료되었습니다: {{ .detail }}'
ErrNotSupportType: '시스템은 현재 유형 {{ .name }}을 지원하지 않습니다.'
ErrProxy: '요청 오류, 노드 상태를 확인하세요: {{ .detail }}'
ErrApiConfigStatusInvalid: 'API 인터페이스에 대한 액세스가 금지되었습니다: {{ .detail }}'
ErrApiConfigKeyInvalid: 'API 인터페이스 키 오류: {{ .detail }}'
ErrApiConfigIPInvalid: 'API 인터페이스를 호출하는 데 사용된 IP가 허용 목록에 없습니다: {{ .detail }}'
ErrApiConfigDisable: '이 인터페이스는 API 인터페이스 호출 사용을 금지합니다: {{ .detail }}'
ErrApiConfigKeyTimeInvalid: 'API 인터페이스 타임스탬프 오류: {{ .detail }}'

#흔한
ErrUsernameIsExist: '사용자 이름이 이미 존재합니다'
ErrNameIsExist: '이름이 이미 존재합니다'
ErrDemoEnvironment: '데모 서버, 이 작업은 금지되어 있습니다!'
ErrCmdTimeout: '명령 실행 시간이 초과되었습니다!'
ErrCmdIllegal: '실행 명령에 잘못된 문자가 있습니다. 수정하여 다시 시도하세요!'
ErrPortExist: '{{ .port }} 포트는 이미 {{ .type }} [{{ .name }}]에 의해 사용되고 있습니다.'
TYPE_APP: '응용 프로그램'
TYPE_RUNTIME: '런타임 환경'
TYPE_DOMAIN: '도메인 이름'
ErrTypePort: '포트 {{ .name }} 형식이 올바르지 않습니다'
ErrTypePortRange: '포트 범위는 1-65535 사이여야 합니다.'
Success: '성공'
Failed: '실패했습니다'
SystemRestart: '시스템 재시작으로 인해 작업이 중단되었습니다'
ErrGroupIsDefault: '기본 그룹, 삭제할 수 없습니다'
ErrGroupIsInWebsiteUse: '그룹이 다른 웹사이트에서 사용 중이므로 삭제할 수 없습니다.'

#지원
ErrBackupInUsed: '백업 계정이 예약된 작업에 사용되었으므로 삭제할 수 없습니다.'
ErrBackupCheck: '백업 계정 테스트 연결에 실패했습니다 {{ .err }}'
ErrBackupLocalDelete: '로컬 서버 백업 계정 삭제는 아직 지원되지 않습니다.'
ErrBackupLocalCreate: '로컬 서버 백업 계정 생성은 아직 지원되지 않습니다.'

#앱
ErrPortInUsed: '{{ .detail }} 포트가 이미 사용 중입니다!'
ErrAppLimit: '설치된 애플리케이션 수가 한도를 초과했습니다'
ErrNotInstall: '응용 프로그램이 설치되지 않았습니다'
ErrPortInOtherApp: '{{ .port }} 포트는 이미 {{ .apps }} 애플리케이션에 의해 사용되고 있습니다!'
ErrDbUserNotValid: '기존 데이터베이스, 사용자 이름 및 비밀번호가 일치하지 않습니다!'
ErrUpdateBuWebsite: '응용 프로그램이 성공적으로 업데이트되었지만, 웹사이트 구성 파일 수정에 실패했습니다. 구성을 확인하세요! '
Err1PanelNetworkFailed: '기본 컨테이너 네트워크 생성에 실패했습니다! {{ .세부 사항 }}'
ErrFileParse: '응용 프로그램 docker-compose 파일 구문 분석에 실패했습니다!'
ErrInstallDirNotFound: '설치 디렉토리가 존재하지 않습니다. 제거를 원하실 경우 강제 제거 를 선택해주세요.'
AppStoreIsUpToDate: '앱 스토어가 이미 최신 버전입니다'
LocalAppVersionNull: '{{ .name }} 애플리케이션이 버전과 동기화되지 않았습니다! 앱 목록에 추가할 수 없습니다.'
LocalAppVersionErr: '{{ .name }} 동기화 버전 {{ .version }}이 실패했습니다! {{ .err }}'
ErrFileNotFound: '{{ .name }} 파일이 존재하지 않습니다'
ErrFileParseApp: '{{ .name }} 파일 구문 분석에 실패했습니다 {{ .err }}'
ErrAppDirNull: '버전 폴더가 존재하지 않습니다'
LocalAppErr: '애플리케이션 {{ .name }} 동기화에 실패했습니다! {{ .err }}'
ErrContainerName: '컨테이너 이름이 이미 존재합니다'
ErrCreateHttpClient: '요청 {{ .err }}을(를) 생성하지 못했습니다.'
ErrHttpReqTimeOut: '요청 시간이 초과되었습니다 {{ .err }}'
ErrHttpReqFailed: '요청이 실패했습니다 {{ .err }}'
ErrNoSuchHost: '요청한 서버 {{ .err }}을 찾을 수 없습니다'
ErrHttpReqNotFound: '요청한 리소스 {{ .err }}을 찾을 수 없습니다.'
ErrContainerNotFound: '{{ .name }} 컨테이너가 존재하지 않습니다'
ErrContainerMsg: '{{ .name }} 컨테이너가 비정상입니다. 자세한 내용은 컨테이너 페이지의 로그를 확인하세요.'
ErrAppBackup: '{{ .name }} 애플리케이션 백업에 실패했습니다 {{ .err }}'
ErrVersionTooLow: '현재 1Panel 버전이 너무 낮아 App Store를 업데이트할 수 없습니다. 작동하시기 전에 버전을 업그레이드하세요.'
ErrAppNameExist: '응용 프로그램 이름이 이미 존재합니다'
AppStoreIsSyncing: 'App Store가 동기화 중입니다. 나중에 다시 시도하세요.'
ErrGetCompose: 'docker-compose.yml 파일을 가져오지 못했습니다! {{ .detail }}'
ErrAppWarn: '비정상적인 상태입니다. 로그를 확인해 주세요.'
ErrAppParamKey: '매개변수 {{ .name }} 필드가 비정상입니다.'
ErrAppUpgrade: '애플리케이션 {{ .name }} 업그레이드에 실패했습니다 {{ .err }}'
AppRecover: '롤백 애플리케이션 {{ .name }}'
PullImageStart: '이미지 {{ .name }} 가져오기 시작'
PullImageSuccess: '이미지 가져오기 성공'
AppStoreIsLastVersion: '앱스토어가 이미 최신 버전입니다'
AppStoreSyncSuccess: '앱스토어 동기화 성공'
SyncAppDetail: '애플리케이션 구성 동기화'
AppVersionNotMatch: '{{ .name }} 애플리케이션에 더 높은 1Panel 버전이 필요하므로 동기화를 건너뜁니다.'
MoveSiteDir: '현재 업그레이드에는 OpenResty 웹사이트 디렉토리 마이그레이션이 필요합니다.'
MoveSiteToDir: '사이트 디렉토리를 {{ .name }}로 마이그레이션'
ErrMoveSiteDir: '사이트 디렉토리를 마이그레이션하지 못했습니다'
MoveSiteDirSuccess: '웹사이트 디렉토리 마이그레이션 성공'
DeleteRuntimePHP: 'PHP 런타임 삭제'
CustomAppStoreFileValid: '앱 스토어 패키지는 .tar.gz 형식이어야 합니다.'
PullImageTimeout: '이미지 가져오기 시간 초과, 이미지 가속을 높이거나 다른 이미지 가속으로 변경해 보세요.'
ErrAppIsDown: '{{ .name }} 애플리케이션 상태가 비정상적입니다. 확인해 주세요'
ErrCustomApps: '설치된 애플리케이션이 있습니다. 먼저 제거해 주세요'
ErrCustomRuntimes: '설치된 런타임 환경이 있습니다. 먼저 삭제해 주세요'
ErrAppVersionDeprecated: "{{ .name }} 응용 프로그램은 현재 1Panel 버전과 호환되지 않아 건너뛰었습니다"
ErrDockerFailed: "Docker의 상태가 비정상입니다. 서비스 상태를 확인하세요"
ErrDockerComposeCmdNotFound: "Docker Compose 명령이 없습니다. 호스트 머신에 먼저 이 명령을 설치하세요"

#파일
ErrFileCanNotRead: '이 파일은 미리보기를 지원하지 않습니다'
ErrFileToLarge: '파일이 10M보다 커서 열 수 없습니다'
ErrPathNotFound: '디렉토리가 존재하지 않습니다'
ErrMovePathFailed: '대상 경로에는 원래 경로가 포함될 수 없습니다!'
ErrLinkPathNotFound: '대상 경로가 존재하지 않습니다!'
ErrFileIsExist: '파일이나 폴더가 이미 존재합니다!'
ErrFileUpload: '{{ .name }}이 파일 {{ .detail }}을 업로드하지 못했습니다.'
ErrFileDownloadDir: '다운로드 폴더가 지원되지 않습니다'
ErrCmdNotFound: '{{ .name}} 명령이 존재하지 않습니다. 먼저 호스트에 이 명령을 설치하세요'
ErrSourcePathNotFound: '소스 디렉토리가 존재하지 않습니다'
ErrFavoriteExist: '이 경로는 이미 즐겨찾기되었습니다'
ErrInvalidChar: '불법 문자는 허용되지 않습니다'
ErrPathNotDelete: '선택한 디렉토리를 삭제할 수 없습니다'
ErrLogFileToLarge: "로그 파일이 500MB를 초과하여 열 수 없습니다"

#웹사이트
ErrAliasIsExist: '별칭이 이미 존재합니다'
ErrBackupMatch: '백업 파일이 현재 웹사이트 데이터 중 일부 {{ .detail }}와 일치하지 않습니다.'
ErrBackupExist: '백업 파일에 있는 소스 데이터의 해당 부분이 존재하지 않습니다 {{ .detail }}'
ErrPHPResource: '로컬 운영 환경이 전환을 지원하지 않습니다! '
ErrPathPermission: '인덱스 디렉토리에서 1000:1000이 아닌 권한을 가진 폴더가 감지되었습니다. 이로 인해 웹사이트에서 액세스 거부 오류가 발생할 수 있습니다. 위의 저장 버튼을 클릭해주세요.'
ErrDomainIsUsed: '[{{ .name }}] 웹사이트에서 이미 도메인 이름을 사용하고 있습니다.'
ErrDomainFormat: '{{ .name }} 도메인 이름 형식이 올바르지 않습니다'
ErrDefaultAlias: '기본값은 예약된 코드입니다. 다른 코드를 사용하세요'
ErrParentWebsite: '먼저 하위 사이트 {{ .name }}을 삭제해야 합니다.'
ErrBuildDirNotFound: '빌드 디렉토리가 존재하지 않습니다'
ErrImageNotExist: '운영 환경 {{ .name }} 이미지가 존재하지 않습니다. 운영 환경을 다시 편집하세요.'
ErrProxyIsUsed: "로드 밸런싱이 역방향 프록시에 의해 사용되었으므로 삭제할 수 없습니다"
ErrSSLValid: '인증서 파일에 문제가 있습니다. 인증서 상태를 확인하세요!'

#SSL인증
ErrSSLCannotDelete: '{{ .name }} 인증서는 웹사이트에서 사용 중이므로 삭제할 수 없습니다.'
ErrAccountCannotDelete: '계정이 인증서와 연결되어 있어 삭제할 수 없습니다.'
ErrSSLApply: '인증서 갱신이 성공했으나, openresty 재로드에 실패했습니다. 구성을 확인해 주세요!'
ErrEmailIsExist: '사서함이 이미 존재합니다'
ErrSSLKeyNotFound: '개인 키 파일이 존재하지 않습니다'
ErrSSLCertificateNotFound: '인증서 파일이 존재하지 않습니다'
ErrSSLKeyFormat: '개인 키 파일 검증에 실패했습니다'
ErrSSLCertificateFormat: '인증서 파일 형식이 올바르지 않습니다. pem 형식을 사용하세요.'
ErrEabKidOrEabHmacKeyCannotBlank: 'EabKid 또는 EabHmacKey는 비워둘 수 없습니다'
ErrOpenrestyNotFound: 'Http 모드를 사용하려면 먼저 Openresty를 설치해야 합니다.'
ApplySSLStart: '인증서 신청 시작, 도메인 이름 [{{ .domain }}] 신청 방법 [{{ .type }}] '
dnsAccount: 'DNS 자동'
dnsManual: 'DNS 매뉴얼'
http: 'HTTP'
ApplySSLFailed: '[{{ .domain }}] 인증서 신청이 실패했습니다. {{ .detail }} '
ApplySSLSuccess: '[{{ .domain }}] 인증서 신청이 성공적으로 완료되었습니다! ! '
DNSAccountName: 'DNS 계정 [{{ .name }}] 공급업체 [{{ .type }}]'
PushDirLog: '인증서가 [{{ .path }}] {{ .status }} 디렉토리로 푸시되었습니다.'
ErrDeleteCAWithSSL: '현재 조직에는 발급된 인증서가 있어 삭제할 수 없습니다.'
ErrDeleteWithPanelSSL: '패널 SSL 구성은 이 인증서를 사용하므로 삭제할 수 없습니다.'
ErrDefaultCA: '기본 권한을 삭제할 수 없습니다'
ApplyWebSiteSSLLog: '{{ .name }} 웹사이트 인증서 갱신 시작'
ErrUpdateWebsiteSSL: '{{ .name }} 웹사이트 인증서 업데이트에 실패했습니다: {{ .err }}'
ApplyWebSiteSSLSuccess: '웹사이트 인증서 업데이트 성공'
ErrExecShell: '스크립트 {{ .err }}을 실행하지 못했습니다.'
ExecShellStart: '스크립트 실행 시작'
ExecShellSuccess: '스크립트 실행 성공'
StartUpdateSystemSSL: '시스템 인증서 업데이트 시작'
UpdateSystemSSLSuccess: '시스템 인증서 업데이트가 성공적으로 완료되었습니다.'
ErrWildcardDomain: 'HTTP 모드에서 와일드카드 도메인 이름 인증서를 신청할 수 없습니다'
ErrApplySSLCanNotDelete: "신청 중인 인증서 {{.name}}는 삭제할 수 없습니다. 나중에 다시 시도해 주세요."

#마이SQL
ErrUserIsExist: '현재 사용자가 이미 존재합니다. 다시 입력하세요'
ErrDatabaseIsExist: '현재 데이터베이스가 이미 존재합니다. 다시 입력하세요'
ErrExecTimeOut: 'SQL 실행 시간이 초과되었습니다. 데이터베이스를 확인하십시오.'
ErrRemoteExist: '이 이름을 가진 원격 데이터베이스가 이미 존재합니다. 수정하고 다시 시도하세요'
ErrLocalExist: '이름이 로컬 데이터베이스에 이미 존재합니다. 이름을 수정하고 다시 시도하세요'

#레디스
ErrTypeOfRedis: '복구 파일 유형이 현재 지속성 방법과 일치하지 않습니다. 수정하고 다시 시도하세요'

#컨테이너
ErrInUsed: '{{ .detail }}이 사용 중이므로 삭제할 수 없습니다'
ErrObjectInUsed: '개체가 사용 중이므로 삭제할 수 없습니다'
ErrObjectBeDependent: '이 이미지는 다른 이미지에 의존하므로 삭제할 수 없습니다'
ErrPortRules: '포트 번호가 일치하지 않습니다. 다시 입력하세요!'
ErrPgImagePull: '이미지 풀링 시간이 초과되었습니다. 이미지 가속을 구성하거나 {{ .name }} 이미지를 수동으로 풀링한 다음 다시 시도하세요.'
PruneHelper: "이번 정리에서 {{ .name }} {{ .count }}개를 제거하여 {{ .size }} 디스크 공간을 확보했습니다"
ImageRemoveHelper: "이미지 {{ .name }} 삭제, {{ .size }} 디스크 공간 확보"
BuildCache: "빌드 캐시"
Volume: "스토리지 볼륨"
Network: "네트워크"

#실행 시간
ErrFileNotExist: '{{ .detail }} 파일이 존재하지 않습니다! 소스 파일의 무결성을 확인하세요!'
ErrImageBuildErr: '이미지 빌드 실패'
ErrImageExist: "이미지가 이미 존재합니다! 이미지 이름을 수정하세요."
ErrDelWithWebsite: '운영 환경이 이미 웹사이트와 연결되어 있어 삭제할 수 없습니다'
ErrRuntimeStart: '시작 실패'
ErrPackageJsonNotFound: 'package.json 파일이 존재하지 않습니다'
ErrScriptsNotFound: 'package.json에서 스크립트 구성 항목을 찾을 수 없습니다.'
ErrContainerNameNotFound: '컨테이너 이름을 가져올 수 없습니다. .env 파일을 확인하세요.'
ErrNodeModulesNotFound: 'node_modules 폴더가 없습니다! 런타임 환경을 편집하거나 런타임 환경이 성공적으로 시작될 때까지 기다리십시오'
ErrContainerNameIsNull: '컨테이너 이름이 존재하지 않습니다'
ErrPHPPortIsDefault: "9000 포트는 기본 포트입니다. 수정 후 다시 시도하세요"
ErrPHPRuntimePortFailed: "포트 {{ .name }} 는 현재 런타임 환경에서 이미 사용 중입니다. 수정 후 다시 시도하세요"

#도구
ErrConfigNotFound: '구성 파일이 존재하지 않습니다'
ErrConfigParse: '구성 파일 형식이 올바르지 않습니다'
ErrConfigIsNull: '구성 파일은 비어 있을 수 없습니다'
ErrConfigDirNotFound: '실행 디렉토리가 존재하지 않습니다'
ErrConfigAlreadyExist: '같은 이름의 구성 파일이 이미 존재합니다'
ErrUserFindErr: '사용자 {{ .name }} 검색에 실패했습니다 {{ .err }}'

#크론잡
CutWebsiteLogSuccess: '{{ .name }} 웹사이트 로그가 성공적으로 잘렸습니다. 백업 경로 {{ .path }}'
HandleShell: '스크립트 {{ .name }} 실행'
HandleNtpSync: '시스템 시간 동기화'
HandleSystemClean: '시스템 캐시 정리'
SystemLog: '시스템 로그'
CutWebsiteLog: '웹사이트 로그 회전'
FileOrDir: '디렉터리 / 파일'
UploadFile: '백업 파일 {{ .file }} 을(를) {{ .backup }}(으)로 업로드 중'
IgnoreBackupErr: 'Sandaran gagal, ralat: {{ .detail }}, abaikan ralat ini...'
IgnoreUploadErr: 'Muat naik gagal, ralat: {{ .detail }}, abaikan ralat ini...'

#도구상자
ErrNotExistUser: '현재 사용자가 존재하지 않습니다. 수정한 후 다시 시도하세요!'
ErrBanAction: '설정에 실패했습니다. 현재 {{ .name }} 서비스를 사용할 수 없습니다. 확인하고 다시 시도하세요!'
ErrClamdscanNotFound: 'clamdscan 명령이 감지되지 않았습니다. 설명서를 참조하여 설치하세요!'

#와프
ErrScope: '이 구성을 수정하는 것은 지원되지 않습니다'
ErrStateChange: '상태 변경에 실패했습니다'
ErrRuleExist: '규칙이 이미 존재합니다'
ErrRuleNotExist: '규칙이 존재하지 않습니다'
ErrParseIP: '잘못된 IP 형식'
ErrDefaultIP: '기본값은 예약된 이름입니다. 다른 이름으로 변경해 주세요'
ErrGroupInUse: 'IP 그룹이 블랙리스트/화이트리스트에 사용 중이므로 삭제할 수 없습니다.'
ErrIPGroupAclUse: "IP 그룹은 웹사이트 {{ .name }} 의 사용자 정의 규칙에 사용되므로 삭제할 수 없습니다"
ErrGroupExist: 'IP 그룹 이름이 이미 존재합니다'
ErrIPRange: '잘못된 IP 범위'
ErrIPExist: 'IP가 이미 존재합니다'
urlDefense: 'URL 규칙'
urlHelper: '금지된 URL'
dirFilter: '디렉토리 필터'
xss: 'XSS'
phpExec: 'PHP 스크립트 실행'
oneWordTrojan: '한 단어 트로이'
appFilter: '위험한 디렉토리 필터링 적용'
webshell: '웹쉘'
args: '매개변수 규칙'
protocolFilter: '프로토콜 필터링'
javaFileter: 'Java 위험 파일 필터'
scannerFilter: '스캐너 필터'
escapeFilter: '탈출 필터'
customRule: '사용자 정의 규칙'
httpMethod: 'HTTP 메서드 필터링'
fileExt: '파일 업로드 제한'
defaultIpBlack: '악성 IP 그룹'
cookie: '쿠키 규칙'
urlBlack: 'URL 블랙리스트'
uaBlack: '사용자 에이전트 블랙리스트'
attackCount: '공격 빈도 제한'
fileExtCheck: '파일 업로드 제한'
geoRestrict: '지역 접근 제한'
unknownWebsite: '허가받지 않은 도메인 이름 접근'
notFoundCount: '404 속도 제한'
headerDefense: '헤더 규칙'
defaultUaBlack: '사용자 에이전트 규칙'
methodWhite: 'HTTP 규칙'
captcha: '인간-기계 검증'
fiveSeconds: '5초 검증'
vulnCheck: '보충 규칙'
acl: '사용자 정의 규칙'
sql: 'SQL 주입'
cc: '접근 주파수 제한'
defaultUrlBlack: 'URL 규칙'
sqlInject: 'SQL 주입'
ErrDBNotExist: '데이터베이스가 존재하지 않습니다'
allow: '허용하다'
deny: '거부하다'
OpenrestyNotFound: 'Openresty가 설치되지 않았습니다'
remoteIpIsNull: "IP 목록이 비어 있습니다"
OpenrestyVersionErr: "Openresty 버전이 너무 낮습니다. Openresty를 ********-2-2-focal로 업그레이드하세요"

#일
TaskStart: '[START] {{ .name }} 작업이 시작됩니다.'
TaskEnd: '{{ .name }} 작업이 완료되었습니다 [완료]'
TaskFailed: '{{ .name }} 작업이 실패했습니다'
TaskTimeout: '{{ .name }}의 시간이 초과되었습니다'
TaskSuccess: '{{ .name }} 작업이 성공했습니다'
TaskRetry: '{{ .name }}번째 재시도 시작'
SubTaskSuccess: '{{ .name }}이 성공했습니다'
SubTaskFailed: '{{ .name }}이 실패했습니다: {{ .err }}'
TaskInstall: '설치'
TaskUninstall: '제거'
TaskCreate: '생성'
TaskDelete: '삭제'
TaskUpgrade: '업그레이드'
TaskUpdate: '업데이트'
TaskRestart: '다시 시작'
TaskBackup: '백업'
TaskRecover: '복구'
TaskRollback: '롤백'
TaskPull: '당기기'
TaskCommit: '커밋'
TaskBuild: '빌드'
TaskPush: '푸시'
TaskClean: "정리"
TaskHandle: '실행'
Website: '웹사이트'
App: '애플리케이션'
Runtime: '런타임 환경'
Database: '데이터베이스'
ConfigFTP: 'FTP 사용자 {{ .name }} 생성'
ConfigOpenresty: 'Openresty 구성 파일 생성'
InstallAppSuccess: '애플리케이션 {{ .name }}이 성공적으로 설치되었습니다.'
ConfigRuntime: '런타임 환경 구성'
ConfigApp: '구성 애플리케이션'
SuccessStatus: '{{ .name }}이 성공했습니다'
FailedStatus: '{{ .name }}이(가) {{ .err }}에 실패했습니다'
HandleLink: '핸들 애플리케이션 연결'
HandleDatabaseApp: '애플리케이션 매개변수 처리'
ExecShell: '{{ .name }} 스크립트 실행'
PullImage: '이미지 가져오기'
Start: '시작'
Run: '시작'
Stop: '정지'
Image: '거울'
Compose : '오케스트레이션'
Container: '컨테이너'
AppLink: '연결된 애플리케이션'
EnableSSL: 'HTTPS 활성화'
AppStore: '앱스토어'
TaskSync: '동기화'
LocalApp: '로컬 애플리케이션'
SubTask: '하위 작업'
RuntimeExtension: '런타임 환경 확장'
TaskIsExecuting: '작업이 실행 중입니다'
CustomAppstore: '사용자 정의 애플리케이션 웨어하우스'

# 작업 - ai
OllamaModelPull: 'Ollama 모델 {{ .name }}을(를) 끌어오세요'
OllamaModelSize: 'Ollama 모델 {{ .name }}의 크기를 가져옵니다'

# 작업-스냅샷
Snapshot: '스냅샷'
SnapDBInfo: '1Panel 데이터베이스 정보 쓰기'
SnapCopy: '파일 및 디렉토리 {{ .name }} 복사'
SnapNewDB: '데이터베이스 {{ .name }} 연결 초기화'
SnapDeleteOperationLog: '작업 로그 삭제'
SnapDeleteLoginLog: '액세스 로그 삭제'
SnapDeleteMonitor: '모니터링 데이터 삭제'
SnapRemoveSystemIP: '시스템 IP 제거'
SnapBaseInfo: '1패널 기본 정보 쓰기'
SnapInstallAppImageEmpty: '애플리케이션 이미지가 선택되지 않아 건너뜁니다...'
SnapInstallApp: '1Panel 설치된 애플리케이션 백업'
SnapDockerSave: '설치된 애플리케이션 압축'
SnapLocalBackup: '1Panel 로컬 백업 디렉토리 백업'
SnapCompressBackup: '로컬 백업 디렉토리 압축'
SnapPanelData: '1Panel 데이터 디렉토리 백업'
SnapCompressPanel: '압축 데이터 디렉토리'
SnapWebsite: '1Panel 웹사이트 디렉토리 백업'
SnapCloseDBConn: '데이터베이스 연결 닫기'
SnapCompress: '스냅샷 파일 만들기'
SnapCompressFile: '스냅샷 파일 압축'
SnapCheckCompress: '스냅샷 압축 파일 확인'
SnapCompressSize: '스냅샷 파일 크기 {{ .name }}'
SnapUpload: '스냅샷 파일 업로드'
SnapLoadBackup: '백업 계정 정보 가져오기'
SnapUploadTo: '스냅샷 파일을 {{ .name }}에 업로드'
SnapUploadRes: '스냅샷 파일을 {{ .name }}에 업로드'

SnapshotRecover: '스냅샷 복원'
RecoverDownload: '스냅샷 파일 다운로드'
Download: '다운로드'
RecoverDownloadAccount: '스냅샷 다운로드 백업 계정 {{ .name }} 가져오기'
RecoverDecompress: '스냅샷 압축 파일 압축 해제'
Decompress: '감압'
BackupBeforeRecover: '스냅샷 전에 시스템 관련 데이터 백업'
Readjson: '스냅샷에서 Json 파일을 읽습니다'
ReadjsonPath: '스냅샷의 Json 파일 경로 가져오기'
ReadjsonContent: 'Json 파일 읽기'
ReadjsonMarshal: 'Json 이스케이프 처리'
RecoverApp: '설치된 앱 복원'
RecoverWebsite: '웹사이트 디렉토리 복구'
RecoverAppImage: '스냅샷 이미지 백업 복원'
RecoverCompose: '다른 Composer 콘텐츠 복원'
RecoverComposeList: '모든 작곡가를 복원합니다'
RecoverComposeItem: '작성 {{ .name }} 복구'
RecoverAppEmpty: '스냅샷 파일에서 애플리케이션 이미지 백업을 찾을 수 없습니다'
RecoverBaseData: '기본 데이터 및 파일 복구'
RecoverDaemonJsonEmpty: '스냅샷 파일과 현재 머신 모두 컨테이너 구성 daemon.json 파일이 없습니다.'
RecoverDaemonJson: '컨테이너 구성 daemon.json 파일 복원'
RecoverDBData: '데이터베이스 데이터 복구'
RecoverBackups: '로컬 백업 디렉토리 복원'
RecoverPanelData: '복구 데이터 디렉토리'

# 작업 - 컨테이너
ContainerNewCliet: 'Docker 클라이언트 초기화'
ContainerImagePull: '컨테이너 이미지 {{ .name }} 가져오기'
ContainerRemoveOld: '원래 컨테이너 {{ .name }} 제거'
ContainerImageCheck: '이미지가 정상적으로 당겨졌는지 확인'
ContainerLoadInfo: '기본 컨테이너 정보 가져오기'
ContainerRecreate: '컨테이너 업데이트에 실패했습니다. 이제 원래 컨테이너를 복원하기 시작합니다.'
ContainerCreate: '새로운 컨테이너 {{ .name }}를 만듭니다'
ContainerCreateFailed: '컨테이너 생성에 실패했습니다. 실패한 컨테이너를 삭제하세요'
ContainerStartCheck: '컨테이너가 시작되었는지 확인'

# 작업 - 이미지
ImageBuild: '이미지 빌드'
ImageBuildStdoutCheck: '이미지 출력 콘텐츠 구문 분석'
ImageBuildRes: '이미지 빌드 출력: {{ .name }}'
ImagePull: '이미지 가져오기'
ImageRepoAuthFromDB: '데이터베이스에서 저장소 인증 정보 가져오기'
ImaegPullRes: '이미지 풀 출력: {{ .name }}'
ImagePush: '이미지 푸시'
ImageRenameTag: '이미지 태그 수정'
ImageNewTag: '새로운 이미지 태그 {{ .name }}'
ImaegPushRes: '이미지 푸시 출력: {{ .name }}'
ComposeCreate: '작곡 만들기'
ComposeCreateRes: 'Compose 생성 출력: {{ .name }}'

# 작업 - 웹사이트
BackupNginxConfig: '웹사이트 OpenResty 구성 파일 백업'
CompressFileSuccess: '디렉토리 압축이 성공했습니다. {{ .name }}으로 압축되었습니다.'
CompressDir: '압축 디렉토리'
DeCompressFile: '파일 {{ .name }} 압축 해제'
ErrCheckValid: '백업 파일 검증에 실패했습니다, {{ .name }}'
Rollback: '롤백'
websiteDir: '웹사이트 디렉토리'
RecoverFailedStartRollBack: '복구 실패, 롤백 시작'
AppBackupFileIncomplete: '백업 파일이 불완전하며 app.json 또는 app.tar.gz 파일이 없습니다.'
AppAttributesNotMatch: '애플리케이션 유형 또는 이름이 일치하지 않습니다'

#알리다
ErrAlert: '경고 메시지의 형식이 올바르지 않습니다. 확인하고 다시 시도하세요!'
ErrAlertPush: '알림 정보를 푸시하는 중 오류가 발생했습니다. 확인하고 다시 시도하세요!'
ErrAlertSave: '알람 정보를 저장하는 중 오류가 발생했습니다. 확인하고 다시 시도하세요!'
ErrAlertSync: '알람 정보 동기화 오류입니다. 확인하고 다시 시도하세요!'
ErrAlertRemote: '알람 메시지 원격 오류, 확인하고 다시 시도하세요!'

#task - runtime
ErrInstallExtension: "이미 설치 작업이 진행 중입니다. 작업이 완료될 때까지 기다려 주세요."

# alert mail template
PanelAlertTitle: "패널 알림 통지"
TestAlertTitle: "테스트 이메일 - 이메일 연결 확인"
TestAlert: "이것은 테스트 이메일이며 이메일 발송 설정이 올바른지 확인합니다."
LicenseExpirationAlert: "귀하의 1Panel 라이선스는 {{ .day }}일 후에 만료됩니다. 자세한 내용은 패널에 로그인하세요."
CronJobFailedAlert: "1Panel 예약 작업 '{{ .name }}' 실행에 실패했습니다. 자세한 내용은 패널에서 확인하세요."
ClamAlert: "1Panel 바이러스 검사에서 {{ .num }}개의 감염된 파일이 발견되었습니다. 자세한 내용은 패널에서 확인하세요."
WebSiteAlert: "1Panel 에 있는 {{ .num }}개의 웹사이트가 {{ .day }}일 후에 만료됩니다. 자세한 내용은 패널에서 확인하세요."
SSLAlert: "1Panel 에 있는 {{ .num }}개의 SSL 인증서가 {{ .day }}일 후에 만료됩니다. 자세한 내용은 패널에서 확인하세요."
DiskUsedAlert: "1Panel 디스크 '{{ .name }}'의 사용량은 {{ .used }}입니다. 자세한 내용은 패널에서 확인하세요."
ResourceAlert: "1Panel 의 평균 {{ .time }}분 동안 {{ .name }} 사용률은 {{ .used }}입니다. 자세한 내용은 패널에서 확인하세요."
PanelVersionAlert: "1Panel 의 새로운 버전이 이용 가능합니다. 패널에 로그인하여 업그레이드하세요."
PanelPwdExpirationAlert: "1Panel 비밀번호가 {{ .day }}일 후에 만료됩니다. 자세한 내용은 패널에서 확인하세요."