ErrInvalidParams: 'リクエストパラメータエラー: {{ .detail }}'
ErrTokenParse: 'トークン生成エラー: {{ .detail }}'
ErrInitialPassword: '元のパスワードが正しくありません'
ErrInternalServer: '内部サーバーエラー: {{ .detail }}'
ErrRecordExist: 'レコードがすでに存在します'
ErrRecordNotFound: 'レコードが見つかりません'
ErrStructTransform: '型変換に失敗しました: {{ .err }}'
ErrNotLogin: 'ユーザーがログインしていません: {{ .detail }}'
ErrPasswordExpired: '現在のパスワードの有効期限が切れています: {{ .detail }}'
ErrNotSupportType: 'システムは現在のタイプ: {{ .name }} をサポートしていません'
ErrProxy: 'リクエスト エラーです。ノードのステータスを確認してください: {{ .detail }}'
ErrApiConfigStatusInvalid: 'API インターフェースへのアクセスは禁止されています: {{ .detail }}'
ErrApiConfigKeyInvalid: 'API インターフェース キー エラー: {{ .detail }}'
ErrApiConfigIPInvalid: 'API インターフェースの呼び出しに使用された IP がホワイトリストにありません: {{ .detail }}'
ErrApiConfigDisable: 'このインターフェースは、API インターフェース呼び出しの使用を禁止しています: {{ .detail }}'
ErrApiConfigKeyTimeInvalid: 'API インターフェースのタイムスタンプ エラー: {{ .detail }}'

#common
ErrUsernameIsExist: 'ユーザー名は既に存在します'
ErrNameIsExist: '名前は既に存在します'
ErrDemoEnvironment: 'デモ サーバー、この操作は禁止されています!'
ErrCmdTimeout: 'コマンドの実行がタイムアウトしました!'
ErrCmdIllegal: '実行コマンドに不正な文字が含まれています。修正してもう一度お試しください。'
ErrPortExist: '{{ .port }} ポートは、{{ .type }} [{{ .name }}] によって既に使用されています'
TYPE_APP: 'アプリケーション'
TYPE_RUNTIME: 'ランタイム環境'
TYPE_DOMAIN: 'ドメイン名'
ErrTypePort: 'ポート {{ .name }} の形式が正しくありません'
ErrTypePortRange: 'ポート範囲は 1 ～ 65535 である必要があります'
Success: '成功'
Failed: '失敗'
SystemRestart: 'システムの再起動によりタスクが中断されました'
ErrGroupIsDefault: 'デフォルト グループ、削除できません'
ErrGroupIsInWebsiteUse: 'グループは別の Web サイトで使用されているため、削除できません。'

#backup
ErrBackupInUsed: 'バックアップ アカウントはスケジュールされたタスクで使用されているため、削除できません。'
ErrBackupCheck: 'バックアップ アカウントのテスト接続に失敗しました {{ .err }}'
ErrBackupLocalDelete: 'ローカル サーバーのバックアップ アカウントの削除はまだサポートされていません'
ErrBackupLocalCreate: 'ローカル サーバーのバックアップ アカウントの作成はまだサポートされていません'

#app
ErrPortInUsed: '{{ .detail }} ポートはすでに使用されています!'
ErrAppLimit: 'インストールされているアプリケーションの数が制限を超えました'
ErrNotInstall: 'アプリケーションがインストールされていません'
ErrPortInOtherApp: '{{ .port }} ポートは既にアプリケーション {{ .apps }} によって使用されています!'
ErrDbUserNotValid: '既存のデータベース、ユーザー名、およびパスワードが一致しません!'
ErrUpdateBuWebsite: 'アプリケーションは正常に更新されましたが、Web サイト構成ファイルの変更に失敗しました。設定を確認してください! '
Err1PanelNetworkFailed: 'デフォルトのコンテナ ネットワークの作成に失敗しました。 {{ .detail }}'
ErrFileParse: 'アプリケーションの docker-compose ファイルの解析に失敗しました!'
ErrInstallDirNotFound: 'インストール ディレクトリが存在しません。アンインストールする必要がある場合は、強制アンインストールを選択してください。'
AppStoreIsUpToDate: 'アプリストアはすでに最新バージョンです'
LocalAppVersionNull: '{{ .name }} アプリケーションはバージョンに同期されていません。アプリリストに追加できません'
LocalAppVersionErr: '{{ .name }} バージョン {{ .version }} の同期に失敗しました! {{ .err }}'
ErrFileNotFound: '{{ .name }} ファイルが存在しません'
ErrFileParseApp: '{{ .name }} ファイルの解析に失敗しました {{ .err }}'
ErrAppDirNull: 'バージョン フォルダーが存在しません'
LocalAppErr: 'アプリケーション {{ .name }} の同期に失敗しました! {{ .err }}'
ErrContainerName: 'コンテナ名が既に存在します'
ErrCreateHttpClient: 'リクエスト {{ .err }} の作成に失敗しました'
ErrHttpReqTimeOut: 'リクエストがタイムアウトしました {{ .err }}'
ErrHttpReqFailed: 'リクエストが失敗しました {{ .err }}'
ErrNoSuchHost: '要求されたサーバー {{ .err }} が見つかりません'
ErrHttpReqNotFound: '要求されたリソース {{ .err }} が見つかりませんでした'
ErrContainerNotFound: '{{ .name }} コンテナが存在しません'
ErrContainerMsg: '{{ .name }} コンテナが異常です。詳細についてはコンテナページのログを確認してください。'
ErrAppBackup: '{{ .name }} アプリケーションのバックアップに失敗しました。エラー {{ .err }}'
ErrVersionTooLow: '現在の 1Panel のバージョンが低すぎるため、App Store を更新できません。操作する前にバージョンをアップグレードしてください。'
ErrAppNameExist: 'アプリケーション名がすでに存在します'
AppStoreIsSyncing: 'App Store が同期中です。しばらくしてからもう一度お試しください'
ErrGetCompose: 'docker-compose.yml ファイルの取得に失敗しました! {{ .detail }}'
ErrAppWarn: '異常な状態です。ログを確認してください'
ErrAppParamKey: 'パラメータ {{ .name }} フィールドが異常です'
ErrAppUpgrade: 'アプリケーション {{ .name }} のアップグレードに失敗しました {{ .err }}'
AppRecover: 'アプリケーション {{ .name }} をロールバックします'
PullImageStart: "イメージ {{ .name }} のプルを開始します"
PullImageSuccess: 'イメージのプルが成功しました'
AppStoreIsLastVersion: 'App Store はすでに最新バージョンです'
AppStoreSyncSuccess: 'App Store の同期が成功しました'
SyncAppDetail: 'アプリケーション構成を同期する'
AppVersionNotMatch: '{{ .name }} アプリケーションには、より高い 1Panel バージョンが必要なため、同期をスキップします'
MoveSiteDir: '現在のアップグレードでは、OpenResty Web サイト ディレクトリの移行が必要です'
MoveSiteToDir: 'サイト ディレクトリを {{ .name }} に移行します'
ErrMoveSiteDir: 'サイト ディレクトリの移行に失敗しました'
MoveSiteDirSuccess: 'Web サイト ディレクトリの移行に成功しました'
DeleteRuntimePHP: 'PHP ランタイムを削除する'
CustomAppStoreFileValid: 'App Store パッケージは .tar.gz 形式である必要があります'
PullImageTimeout: 'プル イメージのタイムアウトです。イメージのアクセラレーションを増やすか、別のイメージのアクセラレーションに変更してください'
ErrAppIsDown: '{{ .name }} アプリケーションの状態が異常です。確認してください'
ErrCustomApps: 'インストールされているアプリケーションがあります。まずアンインストールしてください'
ErrCustomRuntimes: 'ランタイム環境がインストールされています。まずそれを削除してください'
ErrAppVersionDeprecated: "{{ .name }} アプリケーションは現在の 1Panel バージョンと互換性がありません、スキップしました"
ErrDockerFailed: "Docker の状態が異常です。サービス状態を確認してください"
ErrDockerComposeCmdNotFound: "Docker Compose コマンドは存在しません。ホストマシンにこのコマンドを先にインストールしてください"

#file
ErrFileCanNotRead: 'このファイルはプレビューをサポートしていません'
ErrFileToLarge: 'ファイルは 10M より大きいため開けません'
ErrPathNotFound: 'ディレクトリが存在しません'
ErrMovePathFailed: 'ターゲット パスに元のパスを含めることはできません!'
ErrLinkPathNotFound: 'ターゲット パスが存在しません!'
ErrFileIsExist: 'ファイルまたはフォルダーは既に存在します!'
ErrFileUpload: '{{ .name }} はファイル {{ .detail }} のアップロードに失敗しました'
ErrFileDownloadDir: 'ダウンロード フォルダーはサポートされていません'
ErrCmdNotFound: '{{ .name}} コマンドが存在しません。まずこのコマンドをホストにインストールしてください'
ErrSourcePathNotFound: 'ソース ディレクトリが存在しません'
ErrFavoriteExist: 'このパスはすでにお気に入りに登録されています'
ErrInvalidChar: '不正な文字は許可されません'
ErrPathNotDelete: '選択されたディレクトリは削除できません'
ErrLogFileToLarge: "ログファイルが500MBを超えており、開くことができません"

#website
ErrAliasIsExist: 'エイリアスがすでに存在します'
ErrBackupMatch: 'バックアップ ファイルは、現在の Web サイト データ {{ .detail }} の一部と一致しません'
ErrBackupExist: 'バックアップ ファイル内のソース データの対応する部分が存在しません {{ .detail }}'
ErrPHPResource: 'ローカルオペレーティング環境は切り替えをサポートしていません! '
ErrPathPermission: 'インデックス ディレクトリに 1000:1000 以外の権限を持つフォルダーが検出されました。これにより、Web サイトでアクセス拒否エラーが発生する可能性があります。上記の保存ボタンをクリックしてください。'
ErrDomainIsUsed: 'ドメイン名はウェブサイト [{{ .name }}] で既に使用されています'
ErrDomainFormat: '{{ .name }} ドメイン名の形式が正しくありません'
ErrDefaultAlias: 'デフォルトは予約済みのコードです。別のコードを使用してください'
ErrParentWebsite: 'まずサブサイト {{ .name }} を削除する必要があります'
ErrBuildDirNotFound: 'ビルド ディレクトリが存在しません'
ErrImageNotExist: 'オペレーティング環境 {{ .name }} イメージが存在しません。オペレーティング環境を再編集してください'
ErrProxyIsUsed: "ロードバランシングはリバースプロキシによって使用されているため、削除できません"
ErrSSLValid: '証明書ファイルが異常です、証明書の状態を確認してください！'

#ssl
ErrSSLCannotDelete: '{{ .name }} 証明書は Web サイトで使用されているため、削除できません'
ErrAccountCannotDelete: 'アカウントは証明書に関連付けられているため、削除できません'
ErrSSLApply: '証明書の更新は成功しましたが、openresty の再ロードに失敗しました。設定を確認してください。'
ErrEmailIsExist: 'メールボックスは既に存在します'
ErrSSLKeyNotFound: '秘密鍵ファイルが存在しません'
ErrSSLCertificateNotFound: '証明書ファイルが存在しません'
ErrSSLKeyFormat: '秘密鍵ファイルの検証に失敗しました'
ErrSSLCertificateFormat: '証明書ファイルの形式が正しくありません。pem 形式を使用してください'
ErrEabKidOrEabHmacKeyCannotBlank: 'EabKid または EabHmacKey は空白にできません'
ErrOpenrestyNotFound: 'Http モードでは、まず Openresty をインストールする必要があります'
ApplySSLStart: '証明書の申請を開始します。ドメイン名 [{{ .domain }}] 申請方法 [{{ .type }}] '
dnsAccount: 'DNS 自動'
dnsManual: 'DNS マニュアル'
http: "HTTP"
ApplySSLFailed: '[{{ .domain }}] 証明書の申請に失敗しました、{{ .detail }}'
ApplySSLSuccess: '[{{ .domain }}] 証明書の申請に成功しました! '
DNSAccountName: 'DNS アカウント [{{ .name }}] ベンダー [{{ .type }}]'
PushDirLog: '証明書がディレクトリ [{{ .path }}] {{ .status }} にプッシュされました'
ErrDeleteCAWithSSL: '現在の組織には発行済みの証明書があり、削除できません。'
ErrDeleteWithPanelSSL: 'パネル SSL 構成はこの証明書を使用しているため、削除できません'
ErrDefaultCA: 'デフォルトの権限を削除できません'
ApplyWebSiteSSLLog: '{{ .name }} ウェブサイト証明書の更新を開始しています'
ErrUpdateWebsiteSSL: '{{ .name }} Web サイト証明書の更新に失敗しました: {{ .err }}'
ApplyWebSiteSSLSuccess: 'Web サイトの証明書を正常に更新しました'
ErrExecShell: 'スクリプト {{ .err }} の実行に失敗しました'
ExecShellStart: 'スクリプトの実行を開始します'
ExecShellSuccess: 'スクリプトの実行が成功しました'
StartUpdateSystemSSL: 'システム証明書の更新を開始します'
UpdateSystemSSLSuccess: 'システム証明書を正常に更新しました'
ErrWildcardDomain: 'HTTP モードでワイルドカード ドメイン名証明書を申請できません'
ErrApplySSLCanNotDelete: "申請中の証明書 {{.name}} は削除できません。しばらくしてからもう一度お試しください。"

#mysql
ErrUserIsExist: '現在のユーザーは既に存在します。再入力してください'
ErrDatabaseIsExist: '現在のデータベースは既に存在します。再入力してください'
ErrExecTimeOut: 'SQL 実行がタイムアウトしました。データベースを確認してください'
ErrRemoteExist: 'この名前のリモート データベースは既に存在します。変更してもう一度お試しください'
ErrLocalExist: '名前はローカル データベースに既に存在します。変更してもう一度お試しください'

#redis
ErrTypeOfRedis: 'リカバリ ファイルの種類が現在の永続化方法と一致しません。変更して再試行してください'

#container
ErrInUsed: '{{ .detail }} は使用中のため削除できません'
ErrObjectInUsed: 'オブジェクトは使用中のため削除できません'
ErrObjectBeDependent: 'このイメージは他のイメージに依存しているため、削除できません'
ErrPortRules: 'ポート番号が一致しません。再入力してください。'
ErrPgImagePull: 'イメージのプルがタイムアウトしました。イメージのアクセラレーションを設定するか、{{ .name }} イメージを手動でプルして再試行してください'
PruneHelper: "今回のクリーンアップで{{ .name }} {{ .count }}個を削除し、{{ .size }}のディスク領域を解放しました"
ImageRemoveHelper: "イメージ{{ .name }}を削除し、{{ .size }}のディスク領域を解放しました"
BuildCache: "ビルドキャッシュ"
Volume: "ストレージボリューム"
Network: "ネットワーク"

#runtime
ErrFileNotExist: '{{ .detail }} ファイルが存在しません。ソース ファイルの整合性を確認してください。'
ErrImageBuildErr: 'イメージのビルドに失敗しました'
ErrImageExist: "イメージはすでに存在します！イメージ名を変更してください。"
ErrDelWithWebsite: 'オペレーティング環境は既に Web サイトに関連付けられているため、削除できません'
ErrRuntimeStart: '起動に失敗しました'
ErrPackageJsonNotFound: 'package.json ファイルが存在しません'
ErrScriptsNotFound: 'スクリプト構成項目が package.json に見つかりませんでした'
ErrContainerNameNotFound: 'コンテナ名を取得できません。.env ファイルを確認してください'
ErrNodeModulesNotFound: 'node_modules フォルダが存在しません。ランタイム環境を編集するか、ランタイム環境が正常に起動するまでお待ちください。'
ErrContainerNameIsNull: 'コンテナ名が存在しません'
ErrPHPPortIsDefault: "ポート9000はデフォルトポートです。修正してから再試行してください"
ErrPHPRuntimePortFailed: "ポート {{ .name }} は現在のランタイム環境で使用されています。修正してから再試行してください"

#tool
ErrConfigNotFound: '構成ファイルが存在しません'
ErrConfigParse: '構成ファイルの形式が正しくありません'
ErrConfigIsNull: '構成ファイルは空にできません'
ErrConfigDirNotFound: '実行ディレクトリが存在しません'
ErrConfigAlreadyExist: '同じ名前の設定ファイルがすでに存在します'
ErrUserFindErr: 'ユーザー {{ .name }} の検索に失敗しました {{ .err }}'

#cronjob
CutWebsiteLogSuccess: '{{ .name }} ウェブサイトのログが正常にカットされました。バックアップ パス {{ .path }}'
HandleShell: 'スクリプト {{ .name }} を実行します'
HandleNtpSync: 'システム時刻の同期'
HandleSystemClean: 'システム キャッシュのクリーンアップ'
SystemLog: 'システムログ'
CutWebsiteLog: 'ウェブサイトログのローテーション'
FileOrDir: 'ディレクトリ / ファイル'
UploadFile: 'バックアップファイル {{ .file }} を {{ .backup }} にアップロード中'
IgnoreBackupErr: 'バックアップ失敗、エラー：{{ .detail }}、このエラーを無視します...'
IgnoreUploadErr: 'アップロード失敗、エラー：{{ .detail }}、このエラーを無視します...'

#toolbox
ErrNotExistUser: '現在のユーザーは存在しません。変更してもう一度お試しください。'
ErrBanAction: '設定に失敗しました。現在の {{ .name }} サービスは利用できません。確認してもう一度お試しください。'
ErrClamdscanNotFound: 'clamdscan コマンドが検出されませんでした。インストールするにはドキュメントを参照してください。'

#waf
ErrScope: 'この構成の変更はサポートされていません'
ErrStateChange: '状態の変更に失敗しました'
ErrRuleExist: 'ルールは既に存在します'
ErrRuleNotExist: 'ルールが存在しません'
ErrParseIP: 'IP 形式が間違っています'
ErrDefaultIP: 'デフォルトは予約名です。別の名前に変更してください'
ErrGroupInUse: 'IP グループはブラックリスト/ホワイトリストで使用されており、削除できません'
ErrIPGroupAclUse: "IPグループはウェブサイト {{ .name }} のカスタムルールで使用されているため、削除できません"
ErrGroupExist: 'IP グループ名がすでに存在します'
ErrIPRange: 'IP 範囲が間違っています'
ErrIPExist: 'IP がすでに存在します'
urlDefense: 'URL ルール'
urlHelper: '禁止された URL'
dirFilter: 'ディレクトリ フィルター'
xss: 'XSS'
phpExec: 'PHP スクリプトの実行'
oneWordTrojan: 'ワンワードトロイの木馬'
appFilter: '危険なディレクトリフィルタリングを適用する'
webshell: 'ウェブシェル'
args: 'パラメータルール'
protocolFilter: 'プロトコルフィルタリング'
javaFileter: 'Java 危険ファイル フィルター'
scannerFilter: 'スキャナ フィルタ'
escapeFilter: 'エスケープフィルター'
customRule: 'カスタムルール'
httpMethod: 'HTTP メソッド フィルタリング'
fileExt: 'ファイルアップロード制限'
defaultIpBlack: '悪意のある IP グループ'
cookie: 'クッキールール'
urlBlack: 'URL ブラックリスト'
uaBlack: 'ユーザーエージェント ブラックリスト'
attackCount: '攻撃頻度の制限'
fileExtCheck: 'ファイルアップロード制限'
geoRestrict: '地域アクセス制限'
unknownWebsite: '不正なドメイン名アクセス'
notFoundCount: '404 レート制限'
headerDefense: 'ヘッダールール'
defaultUaBlack: 'ユーザーエージェントルール'
methodWhite: 'HTTP ルール'
captcha: '人間と機械による認証'
fiveSeconds: '5秒検証'
vulnCheck: '補足ルール'
acl: 'カスタムルール'
sql: 'SQL インジェクション'
cc: 'アクセス頻度制限'
defaultUrlBlack: 'URL ルール'
sqlInject: 'SQL インジェクション'
ErrDBNotExist: 'データベースが存在しません'
allow: '許可'
deny: '拒否'
OpenrestyNotFound: 'Openresty がインストールされていません'
remoteIpIsNull: "IPリストが空です"
OpenrestyVersionErr: "Openrestyのバージョンが低すぎます。Openrestyを********-2-2-focalにアップグレードしてください"

#task
TaskStart: '{{ .name }} タスクが開始されました [START]'
TaskEnd: '{{ .name }} タスクが完了しました [COMPLETED]'
TaskFailed: '{{ .name }} タスクが失敗しました'
TaskTimeout: '{{ .name }} がタイムアウトしました'
TaskSuccess: '{{ .name }} タスクが成功しました'
TaskRetry: '{{ .name }} 回目の再試行を開始します'
SubTaskSuccess: '{{ .name }} が成功しました'
SubTaskFailed: '{{ .name }} が失敗しました: {{ .err }}'
TaskInstall: 'インストール'
TaskUninstall: 'アンインストール'
TaskCreate: '作成'
TaskDelete: '削除'
TaskUpgrade: 'アップグレード'
TaskUpdate: '更新'
TaskRestart: '再起動'
TaskBackup: 'バックアップ'
TaskRecover: '回復'
TaskRollback: 'ロールバック'
TaskPull: 'プル'
TaskCommit: 'コミット'
TaskBuild: 'ビルド'
TaskPush: 'プッシュ'
TaskClean: "クリーンアップ"
TaskHandle: '実行'
Website: 'ウェブサイト'
App: 'アプリケーション'
Runtime: 'ランタイム環境'
Database: 'データベース'
ConfigFTP: 'FTP ユーザー {{ .name }} を作成します'
ConfigOpenresty: 'Openresty 構成ファイルを作成する'
InstallAppSuccess: 'アプリケーション {{ .name }} が正常にインストールされました'
ConfigRuntime: 'ランタイム環境を構成する'
ConfigApp: '構成アプリケーション'
SuccessStatus: '{{ .name }} は成功しました'
FailedStatus: '{{ .name }} が {{ .err }} に失敗しました'
HandleLink: 'ハンドルアプリケーションの関連付け'
HandleDatabaseApp: 'アプリケーション パラメータの処理'
ExecShell: '{{ .name }} スクリプトを実行する'
PullImage: 'イメージをプル'
Start: '開始'
Run: '開始'
Stop: '停止'
Image: '鏡'
Compose: 'オーケストレーション'
Container: 'コンテナ'
AppLink: 'リンクされたアプリケーション'
EnableSSL: 'HTTPS を有効にする'
AppStore: 'Appストア'
TaskSync: '同期'
LocalApp: 'ローカルアプリケーション'
SubTask: 'サブタスク'
RuntimeExtension: 'ランタイム環境拡張'
TaskIsExecuting: 'タスクは実行中です'
CustomAppstore: 'カスタム アプリケーション ウェアハウス'

#task - ai
OllamaModelPull: 'Ollama モデル {{ .name }} をプルします'
OllamaModelSize: "Ollama モデル {{ .name }} のサイズを取得します"

#task - snapshot
Snapshot: 'スナップショット'
SnapDBInfo: '1Panel データベース情報を書き込む'
SnapCopy: 'ファイルとディレクトリ {{ .name }} をコピーします'
SnapNewDB: 'データベース {{ .name }} 接続を初期化します'
SnapDeleteOperationLog: '操作ログの削除'
SnapDeleteLoginLog: 'アクセスログを削除する'
SnapDeleteMonitor: '監視データを削除する'
SnapRemoveSystemIP: 'システム IP を削除します'
SnapBaseInfo: '1Panel の基本情報を書き込む'
SnapInstallAppImageEmpty: 'アプリケーションイメージが選択されていないため、スキップします...'
SnapInstallApp: '1Panelインストール済みアプリケーションのバックアップ'
SnapDockerSave: 'インストールされたアプリケーションを圧縮する'
SnapLocalBackup: '1Panel ローカル バックアップ ディレクトリをバックアップします'
SnapCompressBackup: 'ローカル バックアップ ディレクトリを圧縮する'
SnapPanelData: '1Panel データ ディレクトリをバックアップ'
SnapCompressPanel: '圧縮データ ディレクトリ'
SnapWebsite: '1Panel ウェブサイト ディレクトリのバックアップ'
SnapCloseDBConn: 'データベース接続を閉じる'
SnapCompress: 'スナップショット ファイルを作成する'
SnapCompressFile: 'スナップショット ファイルを圧縮する'
SnapCheckCompress: 'スナップショット圧縮ファイルをチェックする'
SnapCompressSize: 'スナップショット ファイル サイズ {{ .name }}'
SnapUpload: 'スナップショット ファイルをアップロード'
SnapLoadBackup: 'バックアップ アカウント情報を取得する'
SnapUploadTo: 'スナップショット ファイルを {{ .name }} にアップロードします'
SnapUploadRes: 'スナップショット ファイルを {{ .name }} にアップロードします'

SnapshotRecover: 'スナップショットの復元'
RecoverDownload: 'スナップショット ファイルをダウンロード'
Download: 'ダウンロード'
RecoverDownloadAccount: 'スナップショット ダウンロード バックアップ アカウント {{ .name }} を取得します'
RecoverDecompress: 'スナップショット圧縮ファイルを解凍する'
Decompress: '減圧'
BackupBeforeRecover: 'スナップショットの前にシステム関連データをバックアップする'
Readjson: 'スナップショット内の Json ファイルを読み取ります'
ReadjsonPath: 'スナップショット内の Json ファイル パスを取得します'
ReadjsonContent: 'Json ファイルを読み取ります'
ReadjsonMarshal: 'Json エスケープ処理'
RecoverApp: 'インストールされたアプリを復元する'
RecoverWebsite: 'ウェブサイトディレクトリを回復する'
RecoverAppImage: 'スナップショットイメージのバックアップを復元する'
RecoverCompose: '他のコンポーザーのコンテンツを復元する'
RecoverComposeList: 'すべてのコンポーザーを復元する'
RecoverComposeItem: '{{ .name }} の作成を回復する'
RecoverAppEmpty: 'スナップショット ファイルにアプリケーション イメージのバックアップが見つかりませんでした'
RecoverBaseData: '基本データとファイルを回復する'
RecoverDaemonJsonEmpty: 'スナップショット ファイルと現在のマシンの両方に、コンテナー構成 daemon.json ファイルがありません'
RecoverDaemonJson: 'コンテナ構成 daemon.json ファイルを復元する'
RecoverDBData: 'データベース データを回復する'
RecoverBackups: 'ローカル バックアップ ディレクトリを復元する'
RecoverPanelData: 'リカバリデータディレクトリ'

#task - container
ContainerNewCliet: 'Docker クライアントを初期化する'
ContainerImagePull: 'コンテナ イメージ {{ .name }} をプルします'
ContainerRemoveOld: '元のコンテナ {{ .name }} を削除します'
ContainerImageCheck: 'イメージが正常にプルされているかどうかを確認します'
ContainerLoadInfo: 'コンテナの基本情報を取得する'
ContainerRecreate: 'コンテナの更新に失敗しました。元のコンテナの復元を開始しています'
ContainerCreate: '新しいコンテナ {{ .name }} を作成します'
ContainerCreateFailed: 'コンテナの作成に失敗しました。失敗したコンテナを削除してください'
ContainerStartCheck: 'コンテナが起動されているかどうかを確認します'

#task - image
ImageBuild: 'イメージビルド'
ImageBuildStdoutCheck: '画像出力コンテンツを解析する'
ImageBuildRes: 'イメージビルド出力: {{ .name }}'
ImagePull: '画像をプル'
ImageRepoAuthFromDB: 'データベースからリポジトリ認証情報を取得する'
ImaegPullRes: "画像プル出力: {{ .name }}"
ImagePush: '画像をプッシュ'
ImageRenameTag: '画像タグを変更する'
ImageNewTag: '新しい画像タグ {{ .name }}'
ImaegPushRes: '画像プッシュ出力: {{ .name }}'
ComposeCreate: 'コンポジションを作成する'
ComposeCreateRes: "Compose 作成出力: {{ .name }}"

#task - website
BackupNginxConfig: 'ウェブサイトの OpenResty 構成ファイルをバックアップする'
CompressFileSuccess: 'ディレクトリを正常に圧縮しました。{{ .name }} に圧縮されました'
CompressDir: '圧縮ディレクトリ'
DeCompressFile: 'ファイル {{ .name }} を解凍します'
ErrCheckValid: 'バックアップ ファイルの検証に失敗しました、{{ .name }}'
Rollback: 'ロールバック'
websiteDir: 'ウェブサイトディレクトリ'
RecoverFailedStartRollBack: 'リカバリに失敗しました。ロールバックを開始します'
AppBackupFileIncomplete: 'バックアップ ファイルが不完全で、app.json または app.tar.gz ファイルが不足しています'
AppAttributesNotMatch: 'アプリケーションの種類または名前が一致しません'

#alert
ErrAlert: '警告メッセージの形式が正しくありません。確認してもう一度お試しください。'
ErrAlertPush: 'アラート情報のプッシュ中にエラーが発生しました。確認してもう一度お試しください。'
ErrAlertSave: 'アラーム情報の保存中にエラーが発生しました。確認してもう一度お試しください。'
ErrAlertSync: 'アラーム情報の同期エラーです。確認してもう一度お試しください。'
ErrAlertRemote: 'アラーム メッセージのリモート エラーです。確認してもう一度お試しください。'

#task - runtime
ErrInstallExtension: "インストールタスクが進行中です、タスクが終了するのを待ってください"

# alert mail template
PanelAlertTitle: "パネルアラート通知"
TestAlertTitle: "テストメール - メール接続の確認"
TestAlert: "これはテストメールです。メール送信設定が正しく構成されているかを確認します。"
LicenseExpirationAlert: "お使いの 1Panel ライセンスは {{ .day }} 日後に有効期限が切れます。詳細はパネルにログインしてご確認ください。"
CronJobFailedAlert: "1Panel のスケジュールタスク '{{ .name }}' の実行に失敗しました。詳細はパネルでご確認ください。"
ClamAlert: "1Panel のウイルススキャンで {{ .num }} 件の感染ファイルが検出されました。詳細はパネルでご確認ください。"
WebSiteAlert: "1Panel にある {{ .num }} 件のウェブサイトが {{ .day }} 日後に有効期限を迎えます。詳細はパネルでご確認ください。"
SSLAlert: "1Panel にある {{ .num }} 枚のSSL証明書が {{ .day }} 日後に有効期限を迎えます。詳細はパネルでご確認ください。"
DiskUsedAlert: "1Panel のディスク '{{ .name }}' は {{ .used }} 使用されています。詳細はパネルでご確認ください。"
ResourceAlert: "1Panel の {{ .time }} 分間の平均 {{ .name }} 使用率は {{ .used }} です。詳細はパネルでご確認ください。"
PanelVersionAlert: "1Panel に新しいバージョンが利用可能です。アップグレードはパネルから行ってください。"
PanelPwdExpirationAlert: "1Panel のパスワードは {{ .day }} 日後に期限切れとなります。詳細はパネルでご確認ください。"