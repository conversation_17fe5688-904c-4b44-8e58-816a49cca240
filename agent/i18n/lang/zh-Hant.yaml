ErrInvalidParams: '請求參數錯誤: {{ .detail }}'
ErrTokenParse: 'Token 產生錯誤: {{ .detail }}'
ErrInitialPassword: '原密碼錯誤'
ErrInternalServer: '服務內部錯誤: {{ .detail }}'
ErrRecordExist: '記錄已存在'
ErrRecordNotFound: '記錄未能找到'
ErrStructTransform: '型別轉換失敗: {{ .err }}'
ErrNotLogin: '使用者未登入: {{ .detail }}'
ErrPasswordExpired: '目前密碼已過期: {{ .detail }}'
ErrNotSupportType: '系統暫不支援目前類型: {{ .name }}'
ErrProxy: '請求錯誤，請檢查該節點狀態: {{ .detail }}'
ErrApiConfigStatusInvalid: 'API 介面禁止存取: {{ .detail }}'
ErrApiConfigKeyInvalid: 'API 介面金鑰錯誤: {{ .detail }}'
ErrApiConfigIPInvalid: '呼叫 API 介面 IP 不在白名單: {{ .detail }}'
ErrApiConfigDisable: '此介面禁止使用 API 介面呼叫: {{ .detail }}'
ErrApiConfigKeyTimeInvalid: 'API 介面時間戳記錯誤: {{ .detail }}'

#common
ErrUsernameIsExist: '使用者名稱已存在'
ErrNameIsExist: '名稱已存在'
ErrDemoEnvironment: '示範伺服器，禁止此操作!'
ErrCmdTimeout: '指令執行逾時！'
ErrCmdIllegal: '執行指令中存在不合法字元，請修改後重試！'
ErrPortExist: '{{ .port }} 埠已被{{ .type }} [{{ .name }}] 佔用'
TYPE_APP: '應用'
TYPE_RUNTIME: '運作環境'
TYPE_DOMAIN: '網域名稱'
ErrTypePort: '埠{{ .name }} 格式錯誤'
ErrTypePortRange: '連接埠範圍需要在 1-65535 之間'
Success: '成功'
Failed: '失敗'
SystemRestart: '系統重新啟動導致任務中斷'
ErrGroupIsDefault: '預設分組，無法刪除'
ErrGroupIsInWebsiteUse: '分組正在被其他網站使用，無法刪除'

#backup
ErrBackupInUsed: '此備份帳號已在排程任務中使用，無法刪除'
ErrBackupCheck: '備份帳號測試連線失敗{{ .err }}'
ErrBackupLocalDelete: '暫時不支援刪除本機伺服器備份帳號'
ErrBackupLocalCreate: '暫時不支援建立本機伺服器備份帳號'

#app
ErrPortInUsed: '{{ .detail }} 連接埠已被佔用！'
ErrAppLimit: '應用程式超出安裝數量限制'
ErrNotInstall: '應用程式未安裝'
ErrPortInOtherApp: '{{ .port }} 連接埠已被應用程式{{ .apps }} 佔用！'
ErrDbUserNotValid: '存量資料庫，使用者名稱密碼不符！'
ErrUpdateBuWebsite: '應用程式更新成功，但網站設定檔修改失敗，請檢查設定！ '
Err1PanelNetworkFailed: '預設容器網路建立失敗！ {{ .detail }}'
ErrFileParse: '應用docker-compose 檔案解析失敗!'
ErrInstallDirNotFound: '安裝目錄不存在，如需移除，請選擇強制移除'
AppStoreIsUpToDate: '應用程式商店已經是最新版本'
LocalAppVersionNull: '{{ .name }} 應用程式未同步到版本！無法加入到應用程式列表'
LocalAppVersionErr: '{{ .name }} 同步版本{{ .version }} 失敗！ {{ .err }}'
ErrFileNotFound: '{{ .name }} 檔案不存在'
ErrFileParseApp: '{{ .name }} 檔案解析失敗{{ .err }}'
ErrAppDirNull: '版本資料夾不存在'
LocalAppErr: '應用程式{{ .name }} 同步失敗！{{ .err }}'
ErrContainerName: '容器名稱已存在'
ErrCreateHttpClient: '建立請求失敗{{ .err }}'
ErrHttpReqTimeOut: '請求逾時{{ .err }}'
ErrHttpReqFailed: '請求失敗{{ .err }}'
ErrNoSuchHost: '無法找到請求的伺服器{{ .err }}'
ErrHttpReqNotFound: '無法找到要求的資源{{ .err }}'
ErrContainerNotFound: '{{ .name }} 容器不存在'
ErrContainerMsg: '{{ .name }} 容器異常，請在容器頁面上查看日誌'
ErrAppBackup: '{{ .name }} 應用備份失敗 {{ .err }}'
ErrVersionTooLow: '目前1Panel 版本過低，無法更新應用程式商店，請升級版本之後操作'
ErrAppNameExist: '應用程式名稱已存在'
AppStoreIsSyncing: '應用程式商店正在同步中，請稍後再試'
ErrGetCompose: 'docker-compose.yml 檔案取得失敗！{{ .detail }}'
ErrAppWarn: '狀態異常，請檢視日誌'
ErrAppParamKey: '參數{{ .name }} 欄位異常'
ErrAppUpgrade: '應用程式{{ .name }} 升級失敗{{ .err }}'
AppRecover: '應用程式{{ .name }} 回滾'
PullImageStart: '開始拉取鏡像{{ .name }}'
PullImageSuccess: '鏡像拉取成功'
AppStoreSyncSuccess: '應用程式商店同步成功'
SyncAppDetail: '同步應用程式設定'
AppVersionNotMatch: '{{ .name }} 應用程式需要更高的 1Panel 版本，跳過同步'
MoveSiteDir: '目前升級需要遷移 OpenResty 網站目錄'
MoveSiteToDir: '遷移網站目錄到{{ .name }}'
ErrMoveSiteDir: '遷移網站目錄失敗'
MoveSiteDirSuccess: '遷移網站目錄成功'
DeleteRuntimePHP: '刪除PHP 執行環境'
CustomAppStoreFileValid: '應用程式商店包需要 .tar.gz 格式'
PullImageTimeout: '拉取鏡像逾時,請嘗試增加鏡像加速或更換其他鏡像加速'
ErrAppIsDown: '{{ .name }} 應用程式狀態異常，請檢查'
ErrCustomApps: '存在已安裝的應用程式，請先解除安裝'
ErrCustomRuntimes: '存在已安裝的運作環境，請先刪除'
ErrAppVersionDeprecated: "{{ .name }} 應用不適配目前 1Panel 版本，跳過"
ErrDockerFailed: "Docker 狀態異常，請檢查服務狀態"
ErrDockerComposeCmdNotFound: "Docker Compose 命令不存在，請先在宿主機安裝此命令"

#file
ErrFileCanNotRead: '此檔案不支援預覽'
ErrFileToLarge: '檔案超過10M，無法開啟'
ErrPathNotFound: '目錄不存在'
ErrMovePathFailed: '目標路徑不能包含原路徑！'
ErrLinkPathNotFound: '目標路徑不存在!'
ErrFileIsExist: '檔案或資料夾已存在！'
ErrFileUpload: '{{ .name }} 上傳檔案失敗{{ .detail }}'
ErrFileDownloadDir: '不支援下載資料夾'
ErrCmdNotFound: '{{ .name}} 指令不存在，請先在宿主機安裝此指令'
ErrSourcePathNotFound: '來源目錄不存在'
ErrFavoriteExist: '已收藏此路徑'
ErrInvalidChar: '禁止使用非法字元'
ErrPathNotDelete: '所選目錄不可刪除'
ErrLogFileToLarge: "日志文件超过 500M，無法打開"

#website
ErrAliasIsExist: '代號已存在'
ErrBackupMatch: '該備份檔案與目前網站部分資料不符{{ .detail }}'
ErrBackupExist: '該備份檔案對應部分來源資料不存在{{ .detail }}'
ErrPHPResource: '本機執行環境不支援切換！ '
ErrPathPermission: 'index 目錄下偵測到非 1000:1000 權限資料夾，可能導致網站存取 Access denied 錯誤，請點擊上方儲存按鈕'
ErrDomainIsUsed: '網域名稱已被網站【{{ .name }}】使用'
ErrDomainFormat: '{{ .name }} 網域格式不正確'
ErrDefaultAlias: 'default 為保留代號，請使用其他代號'
ErrParentWebsite: '需要先移除子網站{{ .name }}'
ErrBuildDirNotFound: '建置目錄不存在'
ErrImageNotExist: '執行環境{{ .name }} 映像不存在，請重新編輯執行環境'
ErrProxyIsUsed: "負載均衡已被反向代理使用，無法刪除"
ErrSSLValid: '證書文件異常，請檢查證書狀態！'

#ssl
ErrSSLCannotDelete: '{{ .name }} 憑證正在被網站使用，無法刪除'
ErrAccountCannotDelete: '帳號關聯證書，無法刪除'
ErrSSLApply: '憑證續簽成功，openresty reload 失敗，請檢查設定！'
ErrEmailIsExist: '信箱已存在'
ErrSSLKeyNotFound: '私鑰檔案不存在'
ErrSSLCertificateNotFound: '憑證檔案不存在'
ErrSSLKeyFormat: '私鑰檔案校驗失敗'
ErrSSLCertificateFormat: '憑證檔案格式錯誤，請使用 pem 格式'
ErrEabKidOrEabHmacKeyCannotBlank: 'EabKid 或 EabHmacKey 不能為空'
ErrOpenrestyNotFound: 'Http 模式需要先安裝Openresty'
ApplySSLStart: '開始申請證書，網域名稱[{{ .domain }}] 申請方式[{{ .type }}] '
dnsAccount: 'DNS 自動'
dnsManual: 'DNS 手排'
http: 'HTTP'
ApplySSLFailed: '申請[{{ .domain }}] 憑證失敗， {{ .detail }} '
ApplySSLSuccess: '申請[{{ .domain }}] 憑證成功！ ！ '
DNSAccountName: 'DNS 帳號[{{ .name }}] 廠商[{{ .type }}]'
PushDirLog: '憑證推送到目錄[{{ .path }}] {{ .status }}'
ErrDeleteCAWithSSL: '目前機構下存在已簽發證書，無法刪除'
ErrDeleteWithPanelSSL: '面板SSL 配置使用此證書，無法刪除'
ErrDefaultCA: '預設機構不能刪除'
ApplyWebSiteSSLLog: '開始更新{{ .name }} 網站憑證'
ErrUpdateWebsiteSSL: '{{ .name }} 網站更新憑證失敗: {{ .err }}'
ApplyWebSiteSSLSuccess: '更新網站憑證成功'
ErrExecShell: '執行腳本失敗{{ .err }}'
ExecShellStart: '開始執行腳本'
ExecShellSuccess: '腳本執行成功'
StartUpdateSystemSSL: '開始更新系統憑證'
UpdateSystemSSLSuccess: '更新系統憑證成功'
ErrWildcardDomain: 'HTTP 模式無法申請泛網域憑證'
ErrApplySSLCanNotDelete: "正在申請的證書 {{.name}} 無法刪除，請稍後再試"

#mysql
ErrUserIsExist: '目前使用者已存在，請重新輸入'
ErrDatabaseIsExist: '目前資料庫已存在，請重新輸入'
ErrExecTimeOut: 'SQL 執行逾時，請檢查資料庫'
ErrRemoteExist: '遠端資料庫已存在該名稱，請修改後重試'
ErrLocalExist: '本機資料庫已存在該名稱，請修改後重試'

#redis
ErrTypeOfRedis: '復原檔案類型與目前持久化方式不符，請修改後重試'

#container
ErrInUsed: '{{ .detail }} 正被使用，無法刪除'
ErrObjectInUsed: '該物件正被使用，無法刪除'
ErrObjectBeDependent: '此鏡像依賴其他鏡像，無法刪除'
ErrPortRules: '連接埠數目不匹配，請重新輸入！'
ErrPgImagePull: '鏡像拉取逾時，請配置鏡像加速或手動拉取{{ .name }} 鏡像後重試'
PruneHelper: "本次清理 {{ .name }} {{ .count }} 個，釋放磁盤空間 {{ .size }}"
ImageRemoveHelper: "刪除鏡像 {{ .name }} ，釋放磁盤空間 {{ .size }}"
BuildCache: "構建緩存"
Volume: "存儲卷"
Network: "網絡"

#runtime
ErrFileNotExist: '{{ .detail }} 檔案不存在！請檢查來源檔案完整性！'
ErrImageBuildErr: '鏡像build 失敗'
ErrImageExist: "鏡像已存在！請修改鏡像名稱。"
ErrDelWithWebsite: '執行環境已經關聯網站，無法刪除'
ErrRuntimeStart: '啟動失敗'
ErrPackageJsonNotFound: 'package.json 檔案不存在'
ErrScriptsNotFound: '沒有在 package.json 中找到 scripts 設定項'
ErrContainerNameNotFound: '無法取得容器名稱，請檢查 .env 檔案'
ErrNodeModulesNotFound: 'node_modules 資料夾不存在！請編輯執行環境或等待執行環境啟動成功'
ErrContainerNameIsNull: '容器名稱不存在'
ErrPHPPortIsDefault: "9000 埠為預設埠，請修改後重試"
ErrPHPRuntimePortFailed: "{{ .name }} 埠已被目前執行環境使用，請修改後重試"

#tool
ErrConfigNotFound: '設定檔不存在'
ErrConfigParse: '設定檔格式有誤'
ErrConfigIsNull: '設定檔不允許為空'
ErrConfigDirNotFound: '執行目錄不存在'
ErrConfigAlreadyExist: '已存在同名設定檔'
ErrUserFindErr: '使用者{{ .name }} 尋找失敗{{ .err }}'

#cronjob
CutWebsiteLogSuccess: '{{ .name }} 網站日誌切割成功，備份路徑{{ .path }}'
HandleShell: '執行腳本{{ .name }}'
HandleNtpSync: '系統時間同步'
HandleSystemClean: '系統快取清理'
SystemLog: '系統日誌'
CutWebsiteLog: '切割網站日誌'
FileOrDir: '目錄 / 檔案'
UploadFile: '上傳備份文件 {{ .file }} 到 {{ .backup }}'
IgnoreBackupErr: '備份失敗，錯誤：{{ .detail }}，忽略本次錯誤...'
IgnoreUploadErr: '上傳失敗，錯誤：{{ .detail }}，忽略本次錯誤...'

#toolbox
ErrNotExistUser: '目前使用者不存在，請修改後重試！'
ErrBanAction: '設定失敗，目前{{ .name }} 服務不可用，請檢查後再試一次！'
ErrClamdscanNotFound: '未偵測到clamdscan 指令，請參考文件安裝！'

#waf
ErrScope: '不支援修改此配置'
ErrStateChange: '狀態修改失敗'
ErrRuleExist: '規則已存在'
ErrRuleNotExist: '規則不存在'
ErrParseIP: 'IP 格式錯誤'
ErrDefaultIP: 'default 為保留名稱，請更換其他名稱'
ErrGroupInUse: 'IP 群組被駭/白名單使用，無法刪除'
ErrIPGroupAclUse: "IP 群組被網站 {{ .name }} 自訂規則使用，無法刪除"
ErrGroupExist: 'IP 群組名稱已存在'
ErrIPRange: 'IP 範圍錯誤'
ErrIPExist: 'IP 已存在'
urlDefense: 'URL 規則'
urlHelper: '禁止訪問的URL'
dirFilter: '目錄過濾'
xss: 'XSS'
phpExec: 'PHP 腳本執行'
oneWordTrojan: '一句話木馬'
appFilter: '套用危險目錄過濾'
webshell: 'Webshell'
args: '參數規則'
protocolFilter: '協定過濾'
javaFileter: 'Java 危險檔案過濾'
scannerFilter: '掃描器過濾'
escapeFilter: '轉義過濾'
customRule: '自訂規則'
httpMethod: 'HTTP 方法過濾'
fileExt: '檔案上傳限制'
defaultIpBlack: '惡意IP 群組'
cookie: 'Cookie 規則'
urlBlack: 'URL 黑名單'
uaBlack: 'User-Agent 黑名單'
attackCount: '攻擊頻率限制'
fileExtCheck: '檔案上傳限制'
geoRestrict: '地區訪問限制'
unknownWebsite: '未授權網域存取'
notFoundCount: '404 頻率限制'
headerDefense: 'Header 規則'
defaultUaBlack: 'User-Agent 規則'
methodWhite: 'HTTP 規則'
captcha: '人機驗證'
fiveSeconds: '5 秒驗證'
vulnCheck: '補充規則'
acl: '自訂規則'
sql: 'SQL 注入'
cc: '訪問頻率限制'
defaultUrlBlack: 'URL 規則'
sqlInject: 'SQL 注入'
ErrDBNotExist: '資料庫不存在'
allow: '允許'
deny: '禁止'
OpenrestyNotFound: 'Openresty 未安裝'
remoteIpIsNull: "IP 列表為空"
OpenrestyVersionErr: "Openresty 版本過低，請升級 Openresty 至 ********-2-2-focal"

#task
TaskStart: '{{ .name }} 任務開始[START]'
TaskEnd: '{{ .name }} 任務結束[COMPLETED]'
TaskFailed: '{{ .name }} 任務失敗'
TaskTimeout: '{{ .name }} 逾時'
TaskSuccess: '{{ .name }} 任務成功'
TaskRetry: '開始第{{ .name }} 次重試'
SubTaskSuccess: '{{ .name }} 成功'
SubTaskFailed: '{{ .name }} 失敗: {{ .err }}'
TaskInstall: '安裝'
TaskUninstall: '移除'
TaskCreate: '建立'
TaskDelete: '刪除'
TaskUpgrade: '升級'
TaskUpdate: '更新'
TaskRestart: '重啟'
TaskBackup: '備份'
TaskRecover: '復原'
TaskRollback: '回滾'
TaskPull: '拉取'
TaskCommit: '制作'
TaskBuild: '建置'
TaskPush: '推送'
TaskClean: "清理"
TaskHandle: '執行'
Website: '網站'
App: '應用程式'
Runtime: '運作環境'
Database: '資料庫'
ConfigFTP: '建立FTP 使用者{{ .name }}'
ConfigOpenresty: '建立Openresty 設定檔'
InstallAppSuccess: '應用程式{{ .name }} 安裝成功'
ConfigRuntime: '設定執行環境'
ConfigApp: '設定應用程式'
SuccessStatus: '{{ .name }} 成功'
FailedStatus: '{{ .name }} 失敗{{ .err }}'
HandleLink: '處理應用程式關聯'
HandleDatabaseApp: '處理應用程式參數'
ExecShell: '執行{{ .name }} 腳本'
PullImage: '拉取鏡像'
Start: '開始'
Run: '啟動'
Stop: '停止'
Image: '鏡像'
Compose: '編排'
Container: '容器'
AppLink: '關聯應用程式'
EnableSSL: '開啟HTTPS'
AppStore: '應用程式商店'
TaskSync: '同步'
LocalApp: '本機應用'
SubTask: '子任務'
RuntimeExtension: '執行環境擴充'
TaskIsExecuting: '任務正在運作'
CustomAppstore: '自訂應用程式倉庫'

# task - ai
OllamaModelPull: '拉取 Ollama 模型{{ .name }} '
OllamaModelSize: '取得 Ollama 模型{{ .name }} 大小'

# task - snapshot
Snapshot: '快照'
SnapDBInfo: '寫入1Panel 資料庫資訊'
SnapCopy: '複製檔案&目錄{{ .name }} '
SnapNewDB: '初始化資料庫{{ .name }} 連線'
SnapDeleteOperationLog: '刪除操作日誌'
SnapDeleteLoginLog: '刪除存取日誌'
SnapDeleteMonitor: '刪除監控資料'
SnapRemoveSystemIP: '移除系統IP'
SnapBaseInfo: '寫入1Panel 基本資料'
SnapInstallAppImageEmpty: '當前未勾選應用鏡像，跳過...'
SnapInstallApp: '備份 1Panel 已安裝應用'
SnapDockerSave: '壓縮已安裝應用'
SnapLocalBackup: '備份1Panel 本機備份目錄'
SnapCompressBackup: '壓縮本機備份目錄'
SnapPanelData: '備份1Panel 資料目錄'
SnapCompressPanel: '壓縮資料目錄'
SnapWebsite: '備份1Panel 網站目錄'
SnapCloseDBConn: '關閉資料庫連線'
SnapCompress: '製作快照檔案'
SnapCompressFile: '壓縮快照檔案'
SnapCheckCompress: '檢查快照壓縮檔'
SnapCompressSize: '快照檔案大小{{ .name }}'
SnapUpload: '上傳快照檔案'
SnapLoadBackup: '取得備份帳號資訊'
SnapUploadTo: '上傳快照檔案到{{ .name }}'
SnapUploadRes: '上傳快照檔案到{{ .name }}'

SnapshotRecover: '快照復原'
RecoverDownload: '下載快照檔案'
Download: '下載'
RecoverDownloadAccount: '取得快照下載備份帳號{{ .name }}'
RecoverDecompress: '解壓縮快照壓縮檔'
Decompress: '解壓縮'
BackupBeforeRecover: '快照前備份系統相關資料'
Readjson: '讀取快照內Json 檔案'
ReadjsonPath: '取得快照內Json 檔案路徑'
ReadjsonContent: '讀取Json 檔案'
ReadjsonMarshal: 'Json 轉義處理'
RecoverApp: '復原已安裝應用程式'
RecoverWebsite: '復原網站目錄'
RecoverAppImage: '復原快照鏡像備份'
RecoverCompose: '復原其他編排內容'
RecoverComposeList: '取得所有待復原編排'
RecoverComposeItem: '復原編排{{ .name }}'
RecoverAppEmpty: '快照檔案中未發現應用程式鏡像備份'
RecoverBaseData: '復原基礎資料及檔案'
RecoverDaemonJsonEmpty: '快照檔案及目前機器都不存在容器配置daemon.json 檔案'
RecoverDaemonJson: '復原容器配置daemon.json 檔案'
RecoverDBData: '復原資料庫資料'
RecoverBackups: '還原本機備份目錄'
RecoverPanelData: '復原資料目錄'

# task - container
ContainerNewCliet: '初始化Docker Client'
ContainerImagePull: '拉取容器鏡像{{ .name }}'
ContainerRemoveOld: '刪除原容器{{ .name }}'
ContainerImageCheck: '檢查鏡像是否正常拉取'
ContainerLoadInfo: '取得容器基本資訊'
ContainerRecreate: '容器更新失敗，現在開始復原原容器'
ContainerCreate: '建立新容器{{ .name }}'
ContainerCreateFailed: '容器建立失敗，刪除失敗容器'
ContainerStartCheck: '檢查容器是否已啟動'

# task - image
ImageBuild: '鏡像建置'
ImageBuildStdoutCheck: '解析鏡像輸出內容'
ImageBuildRes: '鏡像建置輸出：{{ .name }}'
ImagePull: '拉取鏡像'
ImageRepoAuthFromDB: '從資料庫取得倉庫認證資訊'
ImaegPullRes: '鏡像拉取輸出：{{ .name }}'
ImagePush: '推播鏡像'
ImageRenameTag: '修改鏡像Tag'
ImageNewTag: '新鏡像Tag {{ .name }}'
ImaegPushRes: '鏡像推播輸出：{{ .name }}'
ComposeCreate: '建立編排'
ComposeCreateRes: '編排建立輸出：{{ .name }}'

# task - website
BackupNginxConfig: '備份網站OpenResty 設定檔'
CompressFileSuccess: '壓縮目錄成功，壓縮為{{ .name }}'
CompressDir: '壓縮目錄'
DeCompressFile: '解壓縮檔案{{ .name }}'
ErrCheckValid: '校驗備份檔失敗，{{ .name }}'
Rollback: '回滾'
websiteDir: '網站目錄'
RecoverFailedStartRollBack: '復原失敗，開始回溯'
AppBackupFileIncomplete: '備份檔案不完整缺少 app.json 或 app.tar.gz 檔案'
AppAttributesNotMatch: '應用程式類型或名稱不一致'

#alert
ErrAlert: '警告訊息格式錯誤，請檢查後重試！'
ErrAlertPush: '警告訊息推送錯誤，請檢查後重試！'
ErrAlertSave: '警告訊息儲存錯誤，請檢查後重試！'
ErrAlertSync: '警告訊息同步錯誤，請檢查後重試！'
ErrAlertRemote: '警告訊息遠端錯誤，請檢查後重試！'

#task - runtime
ErrInstallExtension: "已有安裝任務正在進行，請等待任務結束"

# alert mail template
PanelAlertTitle: "面板警示通知"
TestAlertTitle: "測試郵件 - 驗證郵箱連通性"
TestAlert: "這是一封測試郵件，用於確認您的郵件發送設定是否正確。"
LicenseExpirationAlert: "您的 1Panel 授權將於 {{ .day }} 天後到期，詳情請登入面板查看。"
CronJobFailedAlert: "您的 1Panel 面板中，計畫任務 '{{ .name }}' 執行失敗，詳情請登入面板查看。"
ClamAlert: "您的 1Panel 面板病毒掃描任務發現 {{ .num }} 個受感染的檔案，詳情請登入面板查看。"
WebSiteAlert: "您的 1Panel 面板中有 {{ .num }} 個網站將於 {{ .day }} 天後到期，詳情請登入面板查看。"
SSLAlert: "您的 1Panel 面板中有 {{ .num }} 張 SSL 憑證將於 {{ .day }} 天後到期，詳情請登入面板查看。"
DiskUsedAlert: "您的 1Panel 面板磁碟 '{{ .name }}' 已使用 {{ .used }}，詳情請登入面板查看。"
ResourceAlert: "您的 1Panel 面板於 {{ .time }} 分鐘內的平均 {{ .name }} 使用率為 {{ .used }}，詳情請登入面板查看。"
PanelVersionAlert: "您的 1Panel 面板有可升級的新版本，詳情請登入面板查看。"
PanelPwdExpirationAlert: "您的 1Panel 面板密碼將於 {{ .day }} 天後到期，詳情請登入面板查看。"
