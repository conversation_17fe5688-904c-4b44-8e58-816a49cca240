ErrInvalidParams: 'Request parameter error: {{ .detail }}'
ErrTokenParse: 'Token generation error: {{ .detail }}'
ErrInitialPassword: 'Original password is incorrect'
ErrInternalServer: 'Internal server error: {{ .detail }}'
ErrRecordExist: 'Record already exists'
ErrRecordNotFound: 'Record not found'
ErrStructTransform: 'Type conversion failed: {{ .err }}'
ErrNotLogin: 'User not logged in: {{ .detail }}'
ErrPasswordExpired: 'The current password has expired: {{ .detail }}'
ErrNotSupportType: 'The system does not support the current type: {{ .name }}'
ErrProxy: 'Request error, please check the node status: {{ .detail }}'
ErrApiConfigStatusInvalid: 'Access to the API interface is prohibited: {{ .detail }}'
ErrApiConfigKeyInvalid: 'API interface key error: {{ .detail }}'
ErrApiConfigIPInvalid: 'The IP used to call the API interface is not in the whitelist: {{ .detail }}'
ErrApiConfigDisable: 'This interface prohibits the use of API interface calls: {{ .detail }}'
ErrApiConfigKeyTimeInvalid: 'API interface timestamp error: {{ .detail }}'

#common
ErrUsernameIsExist: 'Username already exists'
ErrNameIsExist: 'Name already exists'
ErrDemoEnvironment: 'Demo server, this operation is forbidden!'
ErrCmdTimeout: 'Command execution timed out!'
ErrCmdIllegal: 'There are illegal characters in the execution command, please modify it and try again!'
ErrPortExist: '{{ .port }} port is already occupied by {{ .type }} [{{ .name }}]'
TYPE_APP: 'Application'
TYPE_RUNTIME: 'Runtime environment'
TYPE_DOMAIN: 'Domain Name'
ErrTypePort: 'Port {{ .name }} format is incorrect'
ErrTypePortRange: 'The port range needs to be between 1-65535'
Success: 'Success'
Failed: 'Failed'
SystemRestart: 'Task interrupted due to system restart'
ErrGroupIsDefault: 'Default group, cannot be deleted'
ErrGroupIsInWebsiteUse: 'The group is being used by another website and cannot be deleted.'

#backup
ErrBackupInUsed: 'The backup account has been used in the scheduled task and cannot be deleted.'
ErrBackupCheck: 'Backup account test connection failed {{ .err }}'
ErrBackupLocalDelete: 'Deleting the local server backup account is not supported yet'
ErrBackupLocalCreate: 'Creating local server backup accounts is not supported yet'

#app
ErrPortInUsed: '{{ .detail }} port is already occupied!'
ErrAppLimit: 'The number of applications installed has exceeded the limit'
ErrNotInstall: 'Application not installed'
ErrPortInOtherApp: '{{ .port }} port is already occupied by application {{ .apps }}!'
ErrDbUserNotValid: 'Existing database, username and password do not match!'
ErrUpdateBuWebsite: 'The application was updated successfully, but the website configuration file modification failed. Please check the configuration! '
Err1PanelNetworkFailed: 'Default container network creation failed! {{ .detail }}'
ErrFileParse: 'Application docker-compose file parsing failed!'
ErrInstallDirNotFound: 'The installation directory does not exist. If you need to uninstall, please select Force Uninstall'
AppStoreIsUpToDate: 'The app store is already the latest version'
LocalAppVersionNull: '{{ .name }} application is not synchronized to the version! Cannot be added to the application list'
LocalAppVersionErr: '{{ .name }} sync version {{ .version }} failed! {{ .err }}'
ErrFileNotFound: '{{ .name }} file does not exist'
ErrFileParseApp: '{{ .name }} file parsing failed {{ .err }}'
ErrAppDirNull: 'The version folder does not exist'
LocalAppErr: 'Application {{ .name }} sync failed! {{ .err }}'
ErrContainerName: 'Container name already exists'
ErrCreateHttpClient: 'Failed to create request {{ .err }}'
ErrHttpReqTimeOut: 'Request timed out {{ .err }}'
ErrHttpReqFailed: 'Request failed {{ .err }}'
ErrNoSuchHost: 'Unable to find the requested server {{ .err }}'
ErrHttpReqNotFound: 'The requested resource {{ .err }} could not be found'
ErrContainerNotFound: '{{ .name }} container does not exist'
ErrContainerMsg: '{{ .name }} container is abnormal. Please check the log on the container page for details'
ErrAppBackup: '{{ .name }} application backup failed {{ .err }}'
ErrVersionTooLow: 'The current 1Panel version is too low to update the App Store. Please upgrade the version before operating.'
ErrAppNameExist: 'The application name already exists'
AppStoreIsSyncing: 'The App Store is syncing, please try again later'
ErrGetCompose: 'Failed to obtain the docker-compose.yml file! {{ .detail }}'
ErrAppWarn: 'Abnormal status, please check the log'
ErrAppParamKey: 'Parameter {{ .name }} field is abnormal'
ErrAppUpgrade: 'Application {{ .name }} upgrade failed {{ .err }}'
AppRecover: 'Rollback application {{ .name }}'
PullImageStart: 'Start pulling image {{ .name }}'
PullImageSuccess: 'Image pull successful'
AppStoreIsLastVersion: 'The App Store is already the latest version'
AppStoreSyncSuccess: 'App Store synchronization successful'
SyncAppDetail: 'Synchronize application configuration'
AppVersionNotMatch: '{{ .name }} application requires a higher 1Panel version, skipping synchronization'
MoveSiteDir: 'The current upgrade requires migration of the OpenResty website directory'
MoveSiteToDir: 'Migrate site directory to {{ .name }}'
ErrMoveSiteDir: 'Failed to migrate site directory'
MoveSiteDirSuccess: 'Successful migration of website directory'
DeleteRuntimePHP: 'Delete PHP runtime'
CustomAppStoreFileValid: 'App store packages need to be in .tar.gz format'
PullImageTimeout: 'Pull image timeout, please try to increase the image acceleration or change to another image acceleration'
ErrAppIsDown: '{{ .name }} application status is abnormal, please check'
ErrCustomApps: 'There is an installed application, please uninstall it first'
ErrCustomRuntimes: 'There is an installed runtime environment, please delete it first'
ErrAppVersionDeprecated: "The {{ .name }} application is not compatible with the current 1Panel version, skipped"
ErrDockerFailed: "The state of Docker is abnormal, please check the service status"
ErrDockerComposeCmdNotFound: "The Docker Compose command does not exist, please install this command on the host machine first"

#file
ErrFileCanNotRead: 'This file does not support preview'
ErrFileToLarge: 'The file is larger than 10M and cannot be opened'
ErrPathNotFound: 'Directory does not exist'
ErrMovePathFailed: 'The target path cannot contain the original path!'
ErrLinkPathNotFound: 'The target path does not exist!'
ErrFileIsExist: 'The file or folder already exists!'
ErrFileUpload: '{{ .name }} failed to upload file {{ .detail }}'
ErrFileDownloadDir: 'Download folder is not supported'
ErrCmdNotFound: '{{ .name}} command does not exist, please install this command on the host first'
ErrSourcePathNotFound: 'Source directory does not exist'
ErrFavoriteExist: 'This path has already been favorited'
ErrInvalidChar: 'Illegal characters are not allowed'
ErrPathNotDelete: 'The selected directory cannot be deleted'
ErrLogFileToLarge: "The log file exceeds 500MB and cannot be opened"

#website
ErrAliasIsExist: 'Alias already exists'
ErrBackupMatch: 'The backup file does not match some of the current website data {{ .detail }}'
ErrBackupExist: 'The corresponding part of the source data in the backup file does not exist {{ .detail }}'
ErrPHPResource: 'The local operating environment does not support switching! '
ErrPathPermission: 'A folder with non-1000:1000 permissions was detected in the index directory, which may cause an Access denied error on the website. Please click the Save button above'
ErrDomainIsUsed: 'The domain name is already used by the website [{{ .name }}]'
ErrDomainFormat: '{{ .name }} domain name format is incorrect'
ErrDefaultAlias: 'default is a reserved code, please use another code'
ErrParentWebsite: 'You need to delete the subsite {{ .name }} first'
ErrBuildDirNotFound: 'The build directory does not exist'
ErrImageNotExist: 'The operating environment {{ .name }} image does not exist, please re-edit the operating environment'
ErrProxyIsUsed: "Load balancing has been used by reverse proxy, cannot be deleted"
ErrSSLValid: 'Certificate file is abnormal, please check the certificate status!'

#ssl
ErrSSLCannotDelete: 'The {{ .name }} certificate is being used by a website and cannot be deleted'
ErrAccountCannotDelete: 'The account is associated with a certificate and cannot be deleted'
ErrSSLApply: 'Certificate renewal successful, openresty reload failed, please check the configuration!'
ErrEmailIsExist: 'The mailbox already exists'
ErrSSLKeyNotFound: 'The private key file does not exist'
ErrSSLCertificateNotFound: 'The certificate file does not exist'
ErrSSLKeyFormat: 'Private key file verification failed'
ErrSSLCertificateFormat: 'The certificate file format is incorrect, please use pem format'
ErrEabKidOrEabHmacKeyCannotBlank: 'EabKid or EabHmacKey cannot be blank'
ErrOpenrestyNotFound: 'Http mode requires Openresty to be installed first'
ApplySSLStart: 'Start applying for a certificate, domain name [{{ .domain }}] application method [{{ .type }}] '
dnsAccount: 'DNS Auto'
dnsManual: 'DNS Manual'
http: 'HTTP'
ApplySSLFailed: 'Application for [{{ .domain }}] certificate failed, {{ .detail }} '
ApplySSLSuccess: 'Apply for [{{ .domain }}] certificate successfully! !'
DNSAccountName: 'DNS account [{{ .name }}] vendor [{{ .type }}]'
PushDirLog: 'Certificate pushed to directory [{{ .path }}] {{ .status }}'
ErrDeleteCAWithSSL: 'The current organization has a certificate that has been issued and cannot be deleted.'
ErrDeleteWithPanelSSL: 'Panel SSL configuration uses this certificate and cannot be deleted'
ErrDefaultCA: 'The default authority cannot be deleted'
ApplyWebSiteSSLLog: 'Starting to renew the {{ .name }} website certificate'
ErrUpdateWebsiteSSL: '{{ .name }} website certificate update failed: {{ .err }}'
ApplyWebSiteSSLSuccess: 'Update website certificate successfully'
ErrExecShell: 'Failed to execute script {{ .err }}'
ExecShellStart: 'Start executing the script'
ExecShellSuccess: 'Script execution successful'
StartUpdateSystemSSL: 'Start updating system certificate'
UpdateSystemSSLSuccess: 'Update system certificate successfully'
ErrWildcardDomain: 'Unable to apply for wildcard domain name certificate in HTTP mode'
ErrApplySSLCanNotDelete: "The certificate {{.name}} being applied for cannot be deleted, please try again later."

#mysql
ErrUserIsExist: 'The current user already exists, please re-enter'
ErrDatabaseIsExist: 'The current database already exists, please re-enter'
ErrExecTimeOut: 'SQL execution timed out, please check the database'
ErrRemoteExist: 'The remote database already exists with this name, please modify it and try again'
ErrLocalExist: 'The name already exists in the local database, please modify it and try again'

#redis
ErrTypeOfRedis: 'The recovery file type does not match the current persistence method, please modify it and try again'

#container
ErrInUsed: '{{ .detail }} is in use and cannot be deleted'
ErrObjectInUsed: 'The object is in use and cannot be deleted'
ErrObjectBeDependent: 'This image depends on other images and cannot be deleted'
ErrPortRules: 'Port number does not match, please re-enter!'
ErrPgImagePull: 'Image pull timed out, please configure image acceleration or manually pull the {{ .name }} image and try again'
PruneHelper: "This cleanup removed {{ .name }} {{ .count }} items, freeing {{ .size }} disk space"
ImageRemoveHelper: "Deleted image {{ .name }}, freeing {{ .size }} disk space"
BuildCache: "Build cache"
Volume: "Storage volume"
Network: "Network"

#runtime
ErrFileNotExist: '{{ .detail }} file does not exist! Please check the integrity of the source file!'
ErrImageBuildErr: 'Image build failed'
ErrImageExist: "Image already exists! Please modify the image name."
ErrDelWithWebsite: 'The operating environment is already associated with a website and cannot be deleted'
ErrRuntimeStart: 'Startup failed'
ErrPackageJsonNotFound: 'package.json file does not exist'
ErrScriptsNotFound: 'The scripts configuration item was not found in package.json'
ErrContainerNameNotFound: 'Unable to get container name, please check .env file'
ErrNodeModulesNotFound: 'The node_modules folder does not exist! Please edit the runtime environment or wait for the runtime environment to start successfully'
ErrContainerNameIsNull: 'Container name does not exist'
ErrPHPPortIsDefault: "Port 9000 is the default port, please modify and try again"
ErrPHPRuntimePortFailed: "The port {{ .name }} is already used by the current runtime environment, please modify and try again"

#tool
ErrConfigNotFound: 'Configuration file does not exist'
ErrConfigParse: 'The configuration file format is incorrect'
ErrConfigIsNull: 'Configuration file cannot be empty'
ErrConfigDirNotFound: 'The running directory does not exist'
ErrConfigAlreadyExist: 'A configuration file with the same name already exists'
ErrUserFindErr: 'User {{ .name }} search failed {{ .err }}'

#cronjob
CutWebsiteLogSuccess: '{{ .name }} website log cut successfully, backup path {{ .path }}'
HandleShell: 'Execute script {{ .name }}'
HandleNtpSync: 'System time synchronization'
HandleSystemClean: 'System cache cleanup'
SystemLog: 'System Log'
CutWebsiteLog: 'Rotate Website Log'
FileOrDir: 'Directory / File'
UploadFile: 'Uploading backup file {{ .file }} to {{ .backup }}'
IgnoreBackupErr: 'Backup failed, error: {{ .detail }}, ignoring this error...'
IgnoreUploadErr: 'Upload failed, error: {{ .detail }}, ignoring this error...'

#toolbox
ErrNotExistUser: 'The current user does not exist, please modify and try again!'
ErrBanAction: 'Setting failed. The current {{ .name }} service is unavailable. Please check and try again!'
ErrClamdscanNotFound: 'The clamdscan command was not detected, please refer to the documentation to install it!'

#waf
ErrScope: 'Modifying this configuration is not supported'
ErrStateChange: 'State change failed'
ErrRuleExist: 'Rule already exists'
ErrRuleNotExist: 'Rule does not exist'
ErrParseIP: 'Wrong IP format'
ErrDefaultIP: 'default is a reserved name, please change it to another name'
ErrGroupInUse: 'IP group is used by blacklist/whitelist and cannot be deleted'
ErrIPGroupAclUse: "IP group is used by custom rules of website {{ .name }}, cannot be deleted"
ErrGroupExist: 'IP group name already exists'
ErrIPRange: 'Wrong IP range'
ErrIPExist: 'IP already exists'
urlDefense: 'URL rules'
urlHelper: 'Prohibited URL'
dirFilter: 'Directory filter'
xss: 'XSS'
phpExec: 'PHP script execution'
oneWordTrojan: 'One Word Trojan'
appFilter: 'Apply dangerous directory filtering'
webshell: 'Webshell'
args: 'Parameter rules'
protocolFilter: 'Protocol filtering'
javaFileter: 'Java dangerous file filter'
scannerFilter: 'Scanner filter'
escapeFilter: 'escape filter'
customRule: 'Custom rule'
httpMethod: 'HTTP method filtering'
fileExt: 'File upload restrictions'
defaultIpBlack: 'Malicious IP group'
cookie: 'Cookie Rules'
urlBlack: 'URL blacklist'
uaBlack: 'User-Agent blacklist'
attackCount: 'Attack frequency limit'
fileExtCheck: 'File upload restrictions'
geoRestrict: 'Regional access restrictions'
unknownWebsite: 'Unauthorized domain name access'
notFoundCount: '404 Rate Limit'
headerDefense: 'Header rules'
defaultUaBlack: 'User-Agent rules'
methodWhite: 'HTTP rules'
captcha: 'human-machine verification'
fiveSeconds: '5 seconds verification'
vulnCheck: 'Supplementary rules'
acl: 'Custom rules'
sql: 'SQL injection'
cc: 'Access frequency limit'
defaultUrlBlack: 'URL rules'
sqlInject: 'SQL injection'
ErrDBNotExist: 'Database does not exist'
allow: 'allow'
deny: 'deny'
OpenrestyNotFound: 'Openresty is not installed'
remoteIpIsNull: "IP list is empty"
OpenrestyVersionErr: "Openresty version is too low, please upgrade Openresty to ********-2-2-focal"

#task
TaskStart: '{{ .name }} task starts [START]'
TaskEnd: '{{ .name }} Task completed [COMPLETED]'
TaskFailed: '{{ .name }} task failed'
TaskTimeout: '{{ .name }} timed out'
TaskSuccess: '{{ .name }} Task succeeded'
TaskRetry: 'Start {{ .name }}th retry'
SubTaskSuccess: '{{ .name }} succeeded'
SubTaskFailed: '{{ .name }} failed: {{ .err }}'
TaskInstall: 'Install'
TaskUninstall: 'Uninstall'
TaskCreate: 'Create'
TaskDelete: 'Delete'
TaskUpgrade: 'Upgrade'
TaskUpdate: 'Update'
TaskRestart: 'Restart'
TaskBackup: 'Backup'
TaskRecover: 'Recover'
TaskRollback: 'Rollback'
TaskPull: 'Pull'
TaskCommit: 'Commit'
TaskBuild: 'Build'
TaskPush: 'Push'
TaskClean: "Cleanup"
TaskHandle: 'Execute'
Website: 'Website'
App: 'Application'
Runtime: 'Runtime environment'
Database: 'Database'
ConfigFTP: 'Create FTP user {{ .name }}'
ConfigOpenresty: 'Create Openresty configuration file'
InstallAppSuccess: 'Application {{ .name }} installed successfully'
ConfigRuntime: 'Configure the runtime environment'
ConfigApp: 'Configuration Application'
SuccessStatus: '{{ .name }} succeeded'
FailedStatus: '{{ .name }} failed {{ .err }}'
HandleLink: 'Handle application association'
HandleDatabaseApp: 'Handling application parameters'
ExecShell: 'Execute {{ .name }} script'
PullImage: 'Pull image'
Start: 'Start'
Run: 'Start'
Stop: 'Stop'
Image: 'Mirror'
Compose: 'Orchestration'
Container: 'Container'
AppLink: 'Linked Application'
EnableSSL: 'Enable HTTPS'
AppStore: 'App Store'
TaskSync: 'Synchronize'
LocalApp: 'Local Application'
SubTask: 'Subtask'
RuntimeExtension: 'Runtime Environment Extension'
TaskIsExecuting: 'Task is running'
CustomAppstore: 'Custom application warehouse'

# task - ai
OllamaModelPull: 'Pull Ollama model {{ .name }}'
OllamaModelSize: 'Get the size of Ollama model {{ .name }}'

# task-snapshot
Snapshot: 'Snapshot'
SnapDBInfo: 'Write 1Panel database information'
SnapCopy: 'Copy files & directories {{ .name }}'
SnapNewDB: 'Initialize database {{ .name }} connection'
SnapDeleteOperationLog: 'Delete operation log'
SnapDeleteLoginLog: 'Delete access log'
SnapDeleteMonitor: 'Delete monitoring data'
SnapRemoveSystemIP: 'Remove system IP'
SnapBaseInfo: 'Write 1Panel basic information'
SnapInstallAppImageEmpty: 'No application images selected, skipping...'
SnapInstallApp: 'Backup 1Panel installed applications'
SnapDockerSave: 'Compress installed applications'
SnapLocalBackup: 'Backup 1Panel local backup directory'
SnapCompressBackup: 'Compress local backup directory'
SnapPanelData: 'Backup 1Panel data directory'
SnapCompressPanel: 'Compressed Data Directory'
SnapWebsite: 'Backup 1Panel website directory'
SnapCloseDBConn: 'Close the database connection'
SnapCompress: 'Make snapshot files'
SnapCompressFile: 'Compress snapshot file'
SnapCheckCompress: 'Check snapshot compression file'
SnapCompressSize: 'Snapshot file size {{ .name }}'
SnapUpload: 'Upload snapshot file'
SnapLoadBackup: 'Get backup account information'
SnapUploadTo: 'Upload snapshot file to {{ .name }}'
SnapUploadRes: 'Upload snapshot file to {{ .name }}'

SnapshotRecover: 'Snapshot Restore'
RecoverDownload: 'Download snapshot file'
Download: 'Download'
RecoverDownloadAccount: 'Get snapshot download backup account {{ .name }}'
RecoverDecompress: 'Decompress snapshot compressed files'
Decompress: 'Decompression'
BackupBeforeRecover: 'Backup system-related data before snapshot'
Readjson: 'Read the Json file in the snapshot'
ReadjsonPath: 'Get the Json file path in the snapshot'
ReadjsonContent: 'Read Json file'
ReadjsonMarshal: 'Json escape processing'
RecoverApp: 'Restore installed apps'
RecoverWebsite: 'Recover website directory'
RecoverAppImage: 'Restore snapshot image backup'
RecoverCompose: 'Restore other composer content'
RecoverComposeList: 'Get all composers to be restored'
RecoverComposeItem: 'Recover compose {{ .name }}'
RecoverAppEmpty: 'No application image backup was found in the snapshot file'
RecoverBaseData: 'Recover base data and files'
RecoverDaemonJsonEmpty: 'Both the snapshot file and the current machine do not have the container configuration daemon.json file'
RecoverDaemonJson: 'Restore container configuration daemon.json file'
RecoverDBData: 'Recover database data'
RecoverBackups: 'Restore local backup directory'
RecoverPanelData: 'Recovery data directory'

# task - container
ContainerNewCliet: 'Initialize Docker Client'
ContainerImagePull: 'Pull container image {{ .name }}'
ContainerRemoveOld: 'Remove the original container {{ .name }}'
ContainerImageCheck: 'Check if the image is pulled normally'
ContainerLoadInfo: 'Get basic container information'
ContainerRecreate: 'Container update failed, now starting to restore the original container'
ContainerCreate: 'Create a new container {{ .name }}'
ContainerCreateFailed: 'Container creation failed, delete the failed container'
ContainerStartCheck: 'Check if the container has been started'

# task - image
ImageBuild: 'Image Build'
ImageBuildStdoutCheck: 'Parse image output content'
ImageBuildRes: 'Image build output: {{ .name }}'
ImagePull: 'Pull image'
ImageRepoAuthFromDB: 'Get repository authentication information from the database'
ImaegPullRes: 'Image pull output: {{ .name }}'
ImagePush: 'Push image'
ImageRenameTag: 'Modify image tag'
ImageNewTag: 'New image tag {{ .name }}'
ImaegPushRes: 'Image push output: {{ .name }}'
ComposeCreate: 'Create a composition'
ComposeCreateRes: 'Compose create output: {{ .name }}'

# task - website
BackupNginxConfig: 'Backup website OpenResty configuration file'
CompressFileSuccess: 'Compress directory successfully, compressed to {{ .name }}'
CompressDir: 'Compression directory'
DeCompressFile: 'Decompress file {{ .name }}'
ErrCheckValid: 'Backup file verification failed, {{ .name }}'
Rollback: 'Rollback'
websiteDir: 'Website Directory'
RecoverFailedStartRollBack: 'Recovery failed, start rollback'
AppBackupFileIncomplete: 'The backup file is incomplete and lacks app.json or app.tar.gz files'
AppAttributesNotMatch: 'Application type or name does not match'

#alert
ErrAlert: 'The format of the warning message is incorrect, please check and try again!'
ErrAlertPush: 'Error in pushing alert information, please check and try again!'
ErrAlertSave: 'Error saving the alarm information, please check and try again!'
ErrAlertSync: 'Alarm information synchronization error, please check and try again!'
ErrAlertRemote: 'Alarm message remote error, please check and try again!'

#task - runtime
ErrInstallExtension: "An installation task is already in progress, please wait for the task to finish"

# alert mail template
PanelAlertTitle: "Panel Alert Notification"
TestAlertTitle: "Test Email - Verify Email Connectivity"
TestAlert: "This is a test email to verify that your email sending configuration is correct."
LicenseExpirationAlert: "Your 1Panel license will expire in {{ .day }} days. Please log in to the panel for details."
CronJobFailedAlert: "Your 1Panel scheduled task '{{ .name }}' has failed. Please log in to the panel for details."
ClamAlert: "Your 1Panel virus scan task has detected {{ .num }} infected files. Please log in to the panel for details."
WebSiteAlert: "There are {{ .num }} websites on your 1Panel that will expire in {{ .day }} days. Please log in to the panel for details."
SSLAlert: "There are {{ .num }} SSL certificates on your 1Panel that will expire in {{ .day }} days. Please log in to the panel for details."
DiskUsedAlert: "Your 1Panel disk '{{ .name }}' has used {{ .used }}. Please log in to the panel for details."
ResourceAlert: "The average {{ .name }} usage over {{ .time }} minutes on your 1Panel is {{ .used }}. Please log in to the panel for details."
PanelVersionAlert: "A new version of 1Panel is available. Please log in to the panel to upgrade."
PanelPwdExpirationAlert: "Your 1Panel password will expire in {{ .day }} days. Please log in to the panel for details."