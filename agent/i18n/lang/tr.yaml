ErrInvalidParams: 'İstek parametre hatası: {{ .detail }}'
ErrTokenParse: 'Token oluşturma hatası: {{ .detail }}'
ErrInitialPassword: 'Orijinal şifre yanlış'
ErrInternalServer: '<PERSON><PERSON><PERSON> sunucu hatası: {{ .detail }}'
ErrRecordExist: 'Kayıt zaten mevcut'
ErrRecordNotFound: 'Kayıt bulunamadı'
ErrStructTransform: 'Tür dönüştürme başarısız: {{ .err }}'
ErrNotLogin: 'Kullanıc<PERSON> giriş yapmamış: {{ .detail }}'
ErrPasswordExpired: 'Mevcut şifrenin süresi dolmuş: {{ .detail }}'
ErrNotSupportType: 'Sistem mevcut türü desteklemiyor: {{ .name }}'
ErrProxy: 'İstek hatası, lütfen düğüm durumunu kontrol edin: {{ .detail }}'
ErrApiConfigStatusInvalid: 'API arayüzüne erişim yasak: {{ .detail }}'
ErrApiConfigKeyInvalid: 'API arayüz anahtarı hatası: {{ .detail }}'
ErrApiConfigIPInvalid: 'API arayüzünü çağırmak için kullanılan IP beyaz listede değil: {{ .detail }}'
ErrApiConfigDisable: 'Bu arayüz API arayüz çağrılarının kullanımını yasaklıyor: {{ .detail }}'
ErrApiConfigKeyTimeInvalid: 'API arayüz zaman damgası hatası: {{ .detail }}'

#common
ErrUsernameIsExist: 'Kullanıcı adı zaten mevcut'
ErrNameIsExist: 'İsim zaten mevcut'
ErrDemoEnvironment: 'Demo sunucu, bu işlem yasak!'
ErrCmdTimeout: 'Komut yürütme zaman aşımına uğradı!'
ErrCmdIllegal: 'Yürütme komutunda yasak karakterler var, lütfen değiştirin ve tekrar deneyin!'
ErrPortExist: '{{ .port }} portu zaten {{ .type }} [{{ .name }}] tarafından kullanılıyor'
TYPE_APP: 'Uygulama'
TYPE_RUNTIME: 'Çalışma ortamı'
TYPE_DOMAIN: 'Alan Adı'
ErrTypePort: 'Port {{ .name }} formatı yanlış'
ErrTypePortRange: 'Port aralığının 1-65535 arasında olması gerekiyor'
Success: 'Başarılı'
Failed: 'Başarısız'
SystemRestart: 'Sistem yeniden başlatması nedeniyle görev kesildi'
ErrGroupIsDefault: 'Varsayılan grup, silinemez'
ErrGroupIsInWebsiteUse: 'Grup başka bir web sitesi tarafından kullanılıyor ve silinemez.'

#backup
ErrBackupInUsed: 'Yedekleme hesabı zamanlanmış görevde kullanıldı ve silinemez.'
ErrBackupCheck: 'Yedekleme hesabı test bağlantısı başarısız {{ .err }}'
ErrBackupLocalDelete: 'Yerel sunucu yedekleme hesabını silme henüz desteklenmiyor'
ErrBackupLocalCreate: 'Yerel sunucu yedekleme hesapları oluşturma henüz desteklenmiyor'

#app
ErrPortInUsed: '{{ .detail }} portu zaten kullanılıyor!'
ErrAppLimit: 'Yüklenen uygulama sayısı sınırı aştı'
ErrNotInstall: 'Uygulama yüklenmedi'
ErrPortInOtherApp: '{{ .port }} portu zaten {{ .apps }} uygulaması tarafından kullanılıyor!'
ErrDbUserNotValid: 'Mevcut veritabanı, kullanıcı adı ve şifre eşleşmiyor!'
ErrUpdateBuWebsite: 'Uygulama başarıyla güncellendi, ancak web sitesi yapılandırma dosyası değiştirme başarısız. Lütfen yapılandırmayı kontrol edin! '
Err1PanelNetworkFailed: 'Varsayılan konteyner ağ oluşturma başarısız! {{ .detail }}'
ErrFileParse: 'Uygulama docker-compose dosya ayrıştırma başarısız!'
ErrInstallDirNotFound: 'Kurulum dizini mevcut değil. Kaldırmanız gerekiyorsa, lütfen Zorla Kaldır seçeneğini seçin'
AppStoreIsUpToDate: 'Uygulama mağazası zaten en son sürüm'
LocalAppVersionNull: '{{ .name }} uygulaması sürüme senkronize edilmedi! Uygulama listesine eklenemez'
LocalAppVersionErr: '{{ .name }} senkronizasyon sürümü {{ .version }} başarısız! {{ .err }}'
ErrFileNotFound: '{{ .name }} dosyası mevcut değil'
ErrFileParseApp: '{{ .name }} dosya ayrıştırma başarısız {{ .err }}'
ErrAppDirNull: 'Sürüm klasörü mevcut değil'
LocalAppErr: 'Uygulama {{ .name }} senkronizasyonu başarısız! {{ .err }}'
ErrContainerName: 'Konteyner adı zaten mevcut'
ErrCreateHttpClient: 'İstek oluşturma başarısız {{ .err }}'
ErrHttpReqTimeOut: 'İstek zaman aşımı {{ .err }}'
ErrHttpReqFailed: 'İstek başarısız {{ .err }}'
ErrNoSuchHost: 'İstenen sunucu bulunamıyor {{ .err }}'
ErrHttpReqNotFound: 'İstenen kaynak {{ .err }} bulunamadı'
ErrContainerNotFound: '{{ .name }} konteyneri mevcut değil'
ErrContainerMsg: '{{ .name }} konteyneri anormal. Lütfen ayrıntılar için konteyner sayfasındaki günlüğü kontrol edin'
ErrAppBackup: '{{ .name }} uygulama yedeklemesi başarısız {{ .err }}'
ErrVersionTooLow: 'Mevcut 1Panel sürümü Uygulama Mağazasını güncellemek için çok düşük. Lütfen işlemden önce sürümü yükseltin.'
ErrAppNameExist: 'Uygulama adı zaten mevcut'
AppStoreIsSyncing: 'Uygulama Mağazası senkronize ediliyor, lütfen daha sonra tekrar deneyin'
ErrGetCompose: 'docker-compose.yml dosyası alınamadı! {{ .detail }}'
ErrAppWarn: 'Anormal durum, lütfen günlüğü kontrol edin'
ErrAppParamKey: 'Parametre {{ .name }} alanı anormal'
ErrAppUpgrade: 'Uygulama {{ .name }} yükseltmesi başarısız {{ .err }}'
AppRecover: 'Uygulama {{ .name }} geri alma'
PullImageStart: 'Image {{ .name }} çekmeye başla'
PullImageSuccess: 'Image çekme başarılı'
AppStoreIsLastVersion: 'Uygulama Mağazası zaten en son sürüm'
AppStoreSyncSuccess: 'Uygulama Mağazası senkronizasyonu başarılı'
SyncAppDetail: 'Uygulama yapılandırmasını senkronize et'
AppVersionNotMatch: '{{ .name }} uygulaması daha yüksek 1Panel sürümü gerektiriyor, senkronizasyon atlanıyor'
MoveSiteDir: 'Mevcut yükseltme OpenResty web sitesi dizininin taşınmasını gerektiriyor'
MoveSiteToDir: 'Site dizinini {{ .name }} konumuna taşı'
ErrMoveSiteDir: 'Site dizini taşıma başarısız'
MoveSiteDirSuccess: 'Web sitesi dizini taşıma başarılı'
DeleteRuntimePHP: 'PHP çalışma zamanını sil'
CustomAppStoreFileValid: 'Uygulama mağazası paketlerinin .tar.gz formatında olması gerekiyor'
PullImageTimeout: 'Image çekme zaman aşımı, lütfen image hızlandırmayı artırmayı deneyin veya başka bir image hızlandırmaya geçin'
ErrAppIsDown: '{{ .name }} uygulama durumu anormal, lütfen kontrol edin'
ErrCustomApps: 'Yüklü bir uygulama var, lütfen önce kaldırın'
ErrCustomRuntimes: 'Yüklü bir çalışma ortamı var, lütfen önce silin'
ErrAppVersionDeprecated: "{{ .name }} uygulaması mevcut 1Panel sürümü ile uyumlu değil, atlandı"
ErrDockerFailed: "Docker durumu anormal, lütfen servis durumunu kontrol edin"
ErrDockerComposeCmdNotFound: "Docker Compose komutu mevcut değil, lütfen önce bu komutu host makinesine yükleyin"

#file
ErrFileCanNotRead: 'Bu dosya önizlemeyi desteklemiyor'
ErrFileToLarge: 'Dosya 10M'dan büyük ve açılamıyor'
ErrPathNotFound: 'Dizin mevcut değil'
ErrMovePathFailed: 'Hedef yol orijinal yolu içeremez!'
ErrLinkPathNotFound: 'Hedef yol mevcut değil!'
ErrFileIsExist: 'Dosya veya klasör zaten mevcut!'
ErrFileUpload: '{{ .name }} dosya yükleme başarısız {{ .detail }}'
ErrFileDownloadDir: 'Klasör indirme desteklenmiyor'
ErrCmdNotFound: '{{ .name}} komutu mevcut değil, lütfen önce bu komutu host'a yükleyin'
ErrSourcePathNotFound: 'Kaynak dizin mevcut değil'
ErrFavoriteExist: 'Bu yol zaten favorilere eklendi'
ErrInvalidChar: 'Yasak karakterlere izin verilmiyor'
ErrPathNotDelete: 'Seçilen dizin silinemez'
ErrLogFileToLarge: "Günlük dosyası 500MB'ı aşıyor ve açılamıyor"

#website
ErrAliasIsExist: 'Takma ad zaten mevcut'
ErrBackupMatch: 'Yedekleme dosyası mevcut web sitesi verilerinin bir kısmıyla eşleşmiyor {{ .detail }}'
ErrBackupExist: 'Yedekleme dosyasındaki kaynak verilerin karşılık gelen kısmı mevcut değil {{ .detail }}'
ErrPHPResource: 'Yerel işletim ortamı değiştirmeyi desteklemiyor! '
ErrPathPermission: 'Dizin dizininde 1000:1000 olmayan izinlere sahip bir klasör tespit edildi, bu web sitesinde Erişim reddedildi hatasına neden olabilir. Lütfen yukarıdaki Kaydet düğmesine tıklayın'
ErrDomainIsUsed: 'Alan adı zaten [{{ .name }}] web sitesi tarafından kullanılıyor'
ErrDomainFormat: '{{ .name }} alan adı formatı yanlış'
ErrDefaultAlias: 'default ayrılmış bir kod, lütfen başka bir kod kullanın'
ErrParentWebsite: 'Önce {{ .name }} alt sitesini silmeniz gerekiyor'
ErrBuildDirNotFound: 'Yapı dizini mevcut değil'
ErrImageNotExist: 'İşletim ortamı {{ .name }} image mevcut değil, lütfen işletim ortamını yeniden düzenleyin'
ErrProxyIsUsed: "Yük dengeleme ters proxy tarafından kullanıldı, silinemez"
ErrSSLValid: 'Sertifika dosyası anormal, lütfen sertifika durumunu kontrol edin!'

#ssl
ErrSSLCannotDelete: '{{ .name }} sertifikası bir web sitesi tarafından kullanılıyor ve silinemez'
ErrAccountCannotDelete: 'Hesap bir sertifikayla ilişkili ve silinemez'
ErrSSLApply: 'Sertifika yenileme başarılı, openresty yeniden yükleme başarısız, lütfen yapılandırmayı kontrol edin!'
ErrEmailIsExist: 'Posta kutusu zaten mevcut'
ErrSSLKeyNotFound: 'Özel anahtar dosyası mevcut değil'
ErrSSLCertificateNotFound: 'Sertifika dosyası mevcut değil'
ErrSSLKeyFormat: 'Özel anahtar dosyası doğrulama başarısız'
ErrSSLCertificateFormat: 'Sertifika dosyası formatı yanlış, lütfen pem formatını kullanın'
ErrEabKidOrEabHmacKeyCannotBlank: 'EabKid veya EabHmacKey boş olamaz'
ErrOpenrestyNotFound: 'Http modu önce Openresty yüklenmesini gerektiriyor'
ApplySSLStart: 'Sertifika başvurusu başlatılıyor, alan adı [{{ .domain }}] başvuru yöntemi [{{ .type }}] '
dnsAccount: 'DNS Otomatik'
dnsManual: 'DNS Manuel'
http: 'HTTP'
ApplySSLFailed: '[{{ .domain }}] sertifika başvurusu başarısız, {{ .detail }} '
ApplySSLSuccess: '[{{ .domain }}] sertifika başvurusu başarılı! !'
DNSAccountName: 'DNS hesabı [{{ .name }}] sağlayıcı [{{ .type }}]'
PushDirLog: 'Sertifika [{{ .path }}] dizinine gönderildi {{ .status }}'
ErrDeleteCAWithSSL: 'Mevcut kuruluşun verilmiş bir sertifikası var ve silinemez.'
ErrDeleteWithPanelSSL: 'Panel SSL yapılandırması bu sertifikayı kullanıyor ve silinemez'
ErrDefaultCA: 'Varsayılan otorite silinemez'
ApplyWebSiteSSLLog: '{{ .name }} web sitesi sertifikası yenilemeyi başlatıyor'
ErrUpdateWebsiteSSL: '{{ .name }} web sitesi sertifika güncelleme başarısız: {{ .err }}'
ApplyWebSiteSSLSuccess: 'Web sitesi sertifikası güncelleme başarılı'
ErrExecShell: 'Betik yürütme başarısız {{ .err }}'
ExecShellStart: 'Betik yürütmeyi başlat'
ExecShellSuccess: 'Betik yürütme başarılı'
StartUpdateSystemSSL: 'Sistem sertifikası güncellemeyi başlat'
UpdateSystemSSLSuccess: 'Sistem sertifikası güncelleme başarılı'
ErrWildcardDomain: 'HTTP modunda joker alan adı sertifikası başvurusu yapılamıyor'
ErrApplySSLCanNotDelete: "İşlemde olan sertifika {{.name}} silinemez, lütfen daha sonra tekrar deneyin."

#mysql
ErrUserIsExist: 'Mevcut kullanıcı zaten mevcut, lütfen yeniden girin'
ErrDatabaseIsExist: 'Mevcut veritabanı zaten mevcut, lütfen yeniden girin'
ErrExecTimeOut: 'SQL yürütme zaman aşımı, lütfen veritabanını kontrol edin'
ErrRemoteExist: 'Uzak veritabanında bu adla zaten mevcut, lütfen değiştirin ve tekrar deneyin'
ErrLocalExist: 'Yerel veritabanında bu ad zaten mevcut, lütfen değiştirin ve tekrar deneyin'

#redis
ErrTypeOfRedis: 'Kurtarma dosyası türü mevcut kalıcılık yöntemiyle eşleşmiyor, lütfen değiştirin ve tekrar deneyin'

#container
ErrInUsed: '{{ .detail }} kullanımda ve silinemez'
ErrObjectInUsed: 'Nesne kullanımda ve silinemez'
ErrObjectBeDependent: 'Bu image diğer imagelere bağlı ve silinemez'
ErrPortRules: 'Port numarası eşleşmiyor, lütfen yeniden girin!'
ErrPgImagePull: 'Image çekme zaman aşımı, lütfen image hızlandırma yapılandırın veya manuel olarak {{ .name }} imageını çekin ve tekrar deneyin'
PruneHelper: "Bu temizleme {{ .name }} {{ .count }} öğe kaldırdı, {{ .size }} disk alanı boşalttı"
ImageRemoveHelper: "{{ .name }} imajı silindi, {{ .size }} disk alanı boşalttı"
BuildCache: "Derleme önbelleği"
Volume: "Depolama hacmi"
Network: "Ağ"

#runtime
ErrFileNotExist: '{{ .detail }} dosyası mevcut değil! Lütfen kaynak dosyanın bütünlüğünü kontrol edin!'
ErrImageBuildErr: 'Image yapı başarısız'
ErrImageExist: "Görüntü zaten var! Lütfen görüntü adını değiştirin."
ErrDelWithWebsite: 'İşletim ortamı zaten bir web sitesiyle ilişkili ve silinemez'
ErrRuntimeStart: 'Başlatma başarısız'
ErrPackageJsonNotFound: 'package.json dosyası mevcut değil'
ErrScriptsNotFound: 'package.json içinde scripts yapılandırma öğesi bulunamadı'
ErrContainerNameNotFound: 'Konteyner adı alınamıyor, lütfen .env dosyasını kontrol edin'
ErrNodeModulesNotFound: 'node_modules klasörü mevcut değil! Lütfen çalışma ortamını düzenleyin veya çalışma ortamının başarıyla başlatılmasını bekleyin'
ErrContainerNameIsNull: 'Konteyner adı mevcut değil'
ErrPHPPortIsDefault: "9000 portu varsayılan port, lütfen değiştirin ve tekrar deneyin"
ErrPHPRuntimePortFailed: "{{ .name }} portu mevcut çalışma ortamı tarafından zaten kullanılıyor, lütfen değiştirin ve tekrar deneyin"

#tool
ErrConfigNotFound: 'Yapılandırma dosyası mevcut değil'
ErrConfigParse: 'Yapılandırma dosyası formatı yanlış'
ErrConfigIsNull: 'Yapılandırma dosyası boş olamaz'
ErrConfigDirNotFound: 'Çalışma dizini mevcut değil'
ErrConfigAlreadyExist: 'Aynı ada sahip bir yapılandırma dosyası zaten mevcut'
ErrUserFindErr: 'Kullanıcı {{ .name }} arama başarısız {{ .err }}'

#cronjob
CutWebsiteLogSuccess: '{{ .name }} web sitesi günlüğü başarıyla kesildi, yedekleme yolu {{ .path }}'
HandleShell: 'Betik {{ .name }} yürüt'
HandleNtpSync: 'Sistem zaman senkronizasyonu'
HandleSystemClean: 'Sistem önbellek temizliği'
SystemLog: 'Sistem Günlüğü'
CutWebsiteLog: 'Web Sitesi Günlüğünü Döndür'
FileOrDir: 'Dizin / Dosya'
UploadFile: 'Yedekleme dosyası {{ .file }} {{ .backup }} konumuna yükleniyor'

#toolbox
ErrNotExistUser: 'Mevcut kullanıcı mevcut değil, lütfen değiştirin ve tekrar deneyin!'
ErrBanAction: 'Ayarlama başarısız. Mevcut {{ .name }} servisi kullanılamıyor. Lütfen kontrol edin ve tekrar deneyin!'
ErrClamdscanNotFound: 'clamdscan komutu tespit edilmedi, lütfen yüklemek için belgeleri inceleyin!'

#waf
ErrScope: 'Bu yapılandırmayı değiştirme desteklenmiyor'
ErrStateChange: 'Durum değiştirme başarısız'
ErrRuleExist: 'Kural zaten mevcut'
ErrRuleNotExist: 'Kural mevcut değil'
ErrParseIP: 'Yanlış IP formatı'
ErrDefaultIP: 'default ayrılmış bir isim, lütfen başka bir isme değiştirin'
ErrGroupInUse: 'IP grubu kara liste/beyaz liste tarafından kullanılıyor ve silinemez'
ErrIPGroupAclUse: "IP grubu {{ .name }} web sitesinin özel kuralları tarafından kullanılıyor, silinemez"
ErrGroupExist: 'IP grup adı zaten mevcut'
ErrIPRange: 'Yanlış IP aralığı'
ErrIPExist: 'IP zaten mevcut'
urlDefense: 'URL kuralları'
urlHelper: 'Yasak URL'
dirFilter: 'Dizin filtresi'
xss: 'XSS'
phpExec: 'PHP betik yürütme'
oneWordTrojan: 'Tek Kelime Truva Atı'
appFilter: 'Tehlikeli dizin filtreleme uygula'
webshell: 'Webshell'
args: 'Parametre kuralları'
protocolFilter: 'Protokol filtreleme'
javaFileter: 'Java tehlikeli dosya filtresi'
scannerFilter: 'Tarayıcı filtresi'
escapeFilter: 'kaçış filtresi'
customRule: 'Özel kural'
httpMethod: 'HTTP yöntem filtreleme'
fileExt: 'Dosya yükleme kısıtlamaları'
defaultIpBlack: 'Kötü niyetli IP grubu'
cookie: 'Cookie Kuralları'
urlBlack: 'URL kara listesi'
uaBlack: 'User-Agent kara listesi'
attackCount: 'Saldırı sıklığı sınırı'
fileExtCheck: 'Dosya yükleme kısıtlamaları'
geoRestrict: 'Bölgesel erişim kısıtlamaları'
unknownWebsite: 'Yetkisiz alan adı erişimi'
notFoundCount: '404 Hız Sınırı'
headerDefense: 'Başlık kuralları'
defaultUaBlack: 'User-Agent kuralları'
methodWhite: 'HTTP kuralları'
captcha: 'insan-makine doğrulaması'
fiveSeconds: '5 saniye doğrulaması'
vulnCheck: 'Tamamlayıcı kurallar'
acl: 'Özel kurallar'
sql: 'SQL enjeksiyonu'
cc: 'Erişim sıklığı sınırı'
defaultUrlBlack: 'URL kuralları'
sqlInject: 'SQL enjeksiyonu'
ErrDBNotExist: 'Veritabanı mevcut değil'
allow: 'izin ver'
deny: 'reddet'
OpenrestyNotFound: 'Openresty yüklü değil'
remoteIpIsNull: "IP listesi boş"
OpenrestyVersionErr: "Openresty sürümü çok düşük, lütfen Openresty'i ********-2-2-focal olarak güncelleyin"

#task
TaskStart: '{{ .name }} görevi başlıyor [BAŞLAT]'
TaskEnd: '{{ .name }} Görev tamamlandı [TAMAMLANDI]'
TaskFailed: '{{ .name }} görev başarısız'
TaskTimeout: '{{ .name }} zaman aşımına uğradı'
TaskSuccess: '{{ .name }} Görev başarılı'
TaskRetry: '{{ .name }}. yeniden denemeyi başlat'
SubTaskSuccess: '{{ .name }} başarılı'
SubTaskFailed: '{{ .name }} başarısız: {{ .err }}'
TaskInstall: 'Yükle'
TaskUninstall: 'Kaldır'
TaskCreate: 'Oluştur'
TaskDelete: 'Sil'
TaskUpgrade: 'Yükselt'
TaskUpdate: 'Güncelle'
TaskRestart: 'Yeniden Başlat'
TaskBackup: 'Yedekle'
TaskRecover: 'Kurtar'
TaskRollback: 'Geri Al'
TaskPull: 'Çek'
TaskCommit: 'işleme'
TaskBuild: 'Yapı'
TaskPush: 'Gönder'
TaskClean: "Temizleme"
TaskHandle: 'Yürüt'
Website: 'Web Sitesi'
App: 'Uygulama'
Runtime: 'Çalışma ortamı'
Database: 'Veritabanı'
ConfigFTP: 'FTP kullanıcısı {{ .name }} oluştur'
ConfigOpenresty: 'Openresty yapılandırma dosyası oluştur'
InstallAppSuccess: 'Uygulama {{ .name }} başarıyla yüklendi'
ConfigRuntime: 'Çalışma ortamını yapılandır'
ConfigApp: 'Uygulama Yapılandırması'
SuccessStatus: '{{ .name }} başarılı'
FailedStatus: '{{ .name }} başarısız {{ .err }}'
HandleLink: 'Uygulama ilişkisini işle'
HandleDatabaseApp: 'Uygulama parametrelerini işle'
ExecShell: '{{ .name }} betiğini yürüt'
PullImage: 'Image çek'
Start: 'Başlat'
Run: 'Başlat'
Stop: 'Durdur'
Image: 'Ayna'
Compose: 'Düzenleme'
Container: 'Konteyner'
AppLink: 'Bağlantılı Uygulama'
EnableSSL: 'HTTPS Etkinleştir'
AppStore: 'Uygulama Mağazası'
TaskSync: 'Senkronize Et'
LocalApp: 'Yerel Uygulama'
SubTask: 'Alt görev'
RuntimeExtension: 'Çalışma Ortamı Uzantısı'
TaskIsExecuting: 'Görev çalışıyor'
CustomAppstore: 'Özel uygulama deposu'

# task - ai
OllamaModelPull: 'Ollama modeli {{ .name }} çek'
OllamaModelSize: 'Ollama modeli {{ .name }} boyutunu al'

# task-snapshot
Snapshot: 'Anlık Görüntü'
SnapDBInfo: '1Panel veritabanı bilgilerini yaz'
SnapCopy: 'Dosya ve dizinleri {{ .name }} kopyala'
SnapNewDB: 'Veritabanı {{ .name }} bağlantısını başlat'
SnapDeleteOperationLog: 'İşlem günlüğünü sil'
SnapDeleteLoginLog: 'Erişim günlüğünü sil'
SnapDeleteMonitor: 'İzleme verilerini sil'
SnapRemoveSystemIP: 'Sistem IP'sini kaldır'
SnapBaseInfo: '1Panel temel bilgilerini yaz'
SnapInstallAppImageEmpty: 'Uygulama imageı seçilmedi, atlanıyor...'
SnapInstallApp: '1Panel yüklü uygulamaları yedekle'
SnapDockerSave: 'Yüklü uygulamaları sıkıştır'
SnapLocalBackup: '1Panel yerel yedekleme dizinini yedekle'
SnapCompressBackup: 'Yerel yedekleme dizinini sıkıştır'
SnapPanelData: '1Panel veri dizinini yedekle'
SnapCompressPanel: 'Sıkıştırılmış Veri Dizini'
SnapWebsite: '1Panel web sitesi dizinini yedekle'
SnapCloseDBConn: 'Veritabanı bağlantısını kapat'
SnapCompress: 'Anlık görüntü dosyaları oluştur'
SnapCompressFile: 'Anlık görüntü dosyasını sıkıştır'
SnapCheckCompress: 'Anlık görüntü sıkıştırma dosyasını kontrol et'
SnapCompressSize: 'Anlık görüntü dosya boyutu {{ .name }}'
SnapUpload: 'Anlık görüntü dosyasını yükle'
SnapLoadBackup: 'Yedekleme hesabı bilgilerini al'
SnapUploadTo: 'Anlık görüntü dosyasını {{ .name }} konumuna yükle'
SnapUploadRes: 'Anlık görüntü dosyasını {{ .name }} konumuna yükle'

SnapshotRecover: 'Anlık Görüntü Geri Yükleme'
RecoverDownload: 'Anlık görüntü dosyasını indir'
Download: 'İndir'
RecoverDownloadAccount: 'Anlık görüntü indirme yedek hesabı al {{ .name }}'
RecoverDecompress: 'Anlık görüntü sıkıştırılmış dosyalarını açma'
Decompress: 'Açma'
BackupBeforeRecover: 'Anlık görüntü öncesi sistem ilgili verileri yedekleme'
Readjson: 'Anlık görüntüdeki Json dosyasını oku'
ReadjsonPath: 'Anlık görüntüdeki Json dosya yolunu al'
ReadjsonContent: 'Json dosyasını oku'
ReadjsonMarshal: 'Json kaçış işlemesi'
RecoverApp: 'Yüklü uygulamaları geri yükle'
RecoverWebsite: 'Web sitesi dizinini kurtar'
RecoverAppImage: 'Anlık görüntü imaj yedeklemesini geri yükle'
RecoverCompose: 'Diğer besteci içeriğini geri yükle'
RecoverComposeList: 'Geri yüklenecek tüm bestecileri al'
RecoverComposeItem: 'Besteci geri yükle {{ .name }}'
RecoverAppEmpty: 'Anlık görüntü dosyasında uygulama imaj yedeklemesi bulunamadı'
RecoverBaseData: 'Temel veri ve dosyaları kurtar'
RecoverDaemonJsonEmpty: 'Hem anlık görüntü dosyası hem de mevcut makine konteyner yapılandırma daemon.json dosyasına sahip değil'
RecoverDaemonJson: 'Konteyner yapılandırma daemon.json dosyasını geri yükle'
RecoverDBData: 'Veritabanı verilerini kurtar'
RecoverBackups: 'Yerel yedekleme dizinini geri yükle'
RecoverPanelData: 'Veri dizinini kurtar'

# task - container
ContainerNewCliet: 'Docker İstemcisini Başlat'
ContainerImagePull: 'Konteyner imajını çek {{ .name }}'
ContainerRemoveOld: 'Orijinal konteyneri kaldır {{ .name }}'
ContainerImageCheck: 'İmajın normal çekilip çekilmediğini kontrol et'
ContainerLoadInfo: 'Temel konteyner bilgilerini al'
ContainerRecreate: 'Konteyner güncellemesi başarısız, şimdi orijinal konteyneri geri yüklemeye başlıyor'
ContainerCreate: 'Yeni konteyner oluştur {{ .name }}'
ContainerCreateFailed: 'Konteyner oluşturma başarısız, başarısız konteyneri sil'
ContainerStartCheck: 'Konteynerin başlatılıp başlatılmadığını kontrol et'

# task - image
ImageBuild: 'İmaj Oluşturma'
ImageBuildStdoutCheck: 'İmaj çıktı içeriğini ayrıştır'
ImageBuildRes: 'İmaj oluşturma çıktısı: {{ .name }}'
ImagePull: 'İmaj çek'
ImageRepoAuthFromDB: 'Veritabanından depo kimlik doğrulama bilgilerini al'
ImaegPullRes: 'İmaj çekme çıktısı: {{ .name }}'
ImagePush: 'İmaj gönder'
ImageRenameTag: 'İmaj etiketini değiştir'
ImageNewTag: 'Yeni imaj etiketi {{ .name }}'
ImaegPushRes: 'İmaj gönderme çıktısı: {{ .name }}'
ComposeCreate: 'Kompozisyon oluştur'
ComposeCreateRes: 'Compose oluşturma çıktısı: {{ .name }}'

# task - website
BackupNginxConfig: 'Web sitesi OpenResty yapılandırma dosyasını yedekle'
CompressFileSuccess: 'Dizin başarıyla sıkıştırıldı, şuraya sıkıştırıldı {{ .name }}'
CompressDir: 'Sıkıştırma dizini'
DeCompressFile: 'Dosyayı aç {{ .name }}'
ErrCheckValid: 'Yedekleme dosyası doğrulaması başarısız, {{ .name }}'
Rollback: 'Geri alma'
websiteDir: 'Web Sitesi Dizini'
RecoverFailedStartRollBack: 'Kurtarma başarısız, geri almayı başlat'
AppBackupFileIncomplete: 'Yedekleme dosyası eksik ve app.json veya app.tar.gz dosyalarından yoksun'
AppAttributesNotMatch: 'Uygulama türü veya adı eşleşmiyor'

#alert
ErrAlert: 'Uyarı mesajının formatı yanlış, lütfen kontrol edin ve tekrar deneyin!'
ErrAlertPush: 'Uyarı bilgisi gönderiminde hata, lütfen kontrol edin ve tekrar deneyin!'
ErrAlertSave: 'Alarm bilgisini kaydetmede hata, lütfen kontrol edin ve tekrar deneyin!'
ErrAlertSync: 'Alarm bilgisi senkronizasyon hatası, lütfen kontrol edin ve tekrar deneyin!'
ErrAlertRemote: 'Alarm mesajı uzak hata, lütfen kontrol edin ve tekrar deneyin!'

#task - runtime
ErrInstallExtension: "Zaten devam eden bir kurulum görevi var, görevin bitmesini bekleyin"

# alert mail template
PanelAlertTitle: "Panel Uyarı Bildirimi"
TestAlertTitle: "Test E-postası - E-posta Bağlantısını Doğrula"
TestAlert: "Bu, e-posta gönderim ayarlarınızın doğru yapılandırıldığını doğrulamak için gönderilen bir test e-postasıdır."
LicenseExpirationAlert: "1Panel lisansınız {{ .day }} gün içinde sona erecek. Detaylar için panele giriş yapın."
CronJobFailedAlert: "1Panel zamanlanmış görevi '{{ .name }}' başarısız oldu. Detaylar için panele giriş yapın."
ClamAlert: "1Panel virüs taraması {{ .num }} enfekte dosya tespit etti. Detaylar için panele giriş yapın."
WebSiteAlert: "1Panel üzerindeki {{ .num }} web sitesi {{ .day }} gün içinde sona erecek. Detaylar için panele giriş yapın."
SSLAlert: "1Panel üzerindeki {{ .num }} SSL sertifikası {{ .day }} gün içinde sona erecek. Detaylar için panele giriş yapın."
DiskUsedAlert: "1Panel diski '{{ .name }}' {{ .used }} kullanıldı. Detaylar için panele giriş yapın."
ResourceAlert: "1Panel üzerinde ortalama {{ .time }} dakikalık {{ .name }} kullanım oranı {{ .used }}. Detaylar için panele giriş yapın."
PanelVersionAlert: "1Panel için yeni bir sürüm mevcut. Güncellemek için panele giriş yapın."
PanelPwdExpirationAlert: "1Panel şifreniz {{ .day }} gün içinde sona erecek. Detaylar için panele giriş yapın."
