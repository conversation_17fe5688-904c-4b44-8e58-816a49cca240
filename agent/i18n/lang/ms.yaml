ErrInvalidParams: 'Permintaan ralat parameter: {{ .detail }}'
ErrTokenParse: '<PERSON><PERSON> penjanaan token: {{ .detail }}'
ErrInitialPassword: 'Kata laluan asal tidak betul'
ErrInternalServer: '<PERSON><PERSON> pelayan dalaman: {{ .detail }}'
ErrRecordExist: 'Rekod sudah wujud'
ErrRecordNotFound: 'Rekod tidak ditemui'
ErrStructTransform: 'Penukaran jenis gagal: {{ .err }}'
ErrNotLogin: 'Pengguna tidak log masuk: {{ .detail }}'
ErrPasswordExpired: 'Kata laluan semasa telah tamat tempoh: {{ .detail }}'
ErrNotSupportType: 'Sistem tidak menyokong jenis semasa: {{ .name }}'
ErrProxy: 'Ralat permintaan, sila semak status nod: {{ .detail }}'
ErrApiConfigStatusInvalid: '<PERSON><PERSON><PERSON> kepada antara muka API adalah dilarang: {{ .detail }}'
ErrApiConfigKeyInvalid: 'Ralat kunci antara muka API: {{ .detail }}'
ErrApiConfigIPInvalid: 'IP yang digunakan untuk memanggil antara muka API tiada dalam senarai putih: {{ .detail }}'
ErrApiConfigDisable: 'Antara muka ini melarang penggunaan panggilan antara muka API: {{ .detail }}'
ErrApiConfigKeyTimeInvalid: 'Ralat cap masa antara muka API: {{ .detail }}'

#biasa
ErrUsernameIsExist: 'Nama pengguna sudah wujud'
ErrNameIsExist: 'Nama sudah wujud'
ErrDemoEnvironment: 'Pelayan demo, operasi ini dilarang!'
ErrCmdTimeout: 'Pelaksanaan arahan tamat masa!'
ErrCmdIllegal: 'Terdapat aksara yang menyalahi undang-undang dalam perintah pelaksanaan, sila ubah suainya dan cuba lagi!'
ErrPortExist: '{{ .port }} port sudah diduduki oleh {{ .type }} [{{ .name }}]'
TYPE_APP: 'Aplikasi'
TYPE_RUNTIME: 'Persekitaran masa jalan'
TYPE_DOMAIN: 'Nama Domain'
ErrTypePort: Format 'Port {{ .name }} tidak betul'
ErrTypePortRange: 'Julat port perlu antara 1-65535'
Success: 'Kejayaan'
Failed: 'Gagal'
SystemRestart: 'Tugas terganggu kerana sistem mula semula'
ErrGroupIsDefault: 'Kumpulan lalai, tidak boleh dipadamkan'
ErrGroupIsInWebsiteUse: 'Kumpulan sedang digunakan oleh tapak web lain dan tidak boleh dipadamkan.'

#sandaran
ErrBackupInUsed: 'Akaun sandaran telah digunakan dalam tugas yang dijadualkan dan tidak boleh dipadamkan.'
ErrBackupCheck: 'Sambungan ujian akaun sandaran gagal {{ .err }}'
ErrBackupLocalDelete: 'Memadam akaun sandaran pelayan tempatan belum disokong lagi'
ErrBackupLocalCreate: 'Membuat akaun sandaran pelayan tempatan belum disokong lagi'

#app
ErrPortInUsed: 'Port {{ .detail }} sudah diduduki!'
ErrAppLimit: 'Bilangan aplikasi yang dipasang telah melebihi had'
ErrNotInstall: 'Aplikasi tidak dipasang'
ErrPortInOtherApp: 'Port {{ .port }} sudah diduduki oleh aplikasi {{ .apps }}!'
ErrDbUserNotValid: 'Pangkalan data sedia ada, nama pengguna dan kata laluan tidak sepadan!'
ErrUpdateBuWebsite: 'Aplikasi telah berjaya dikemas kini, tetapi pengubahsuaian fail konfigurasi tapak web gagal. Sila semak konfigurasi! '
Err1PanelNetworkFailed: 'Pembuatan rangkaian kontena lalai gagal! {{ .detail }}'
ErrFileParse: 'Penghuraian fail karang docker aplikasi gagal!'
ErrInstallDirNotFound: 'Direktori pemasangan tidak wujud. Jika anda perlu menyahpasang, sila pilih Paksa Nyahpasang'
AppStoreIsUpToDate: 'Gedung aplikasi sudah pun versi terkini'
LocalAppVersionNull: Aplikasi '{{ .name }} tidak disegerakkan ke versi! Tidak dapat menambah pada senarai apl'
LocalAppVersionErr: '{{ .name }} versi penyegerakan {{ .version }} gagal! {{ .err }}'
ErrFileNotFound: 'Fail {{ .name }} tidak wujud'
ErrFileParseApp: '{{ .name }} penghuraian fail gagal {{ .err }}'
ErrAppDirNull: 'Folder versi tidak wujud'
LocalAppErr: 'Penyegerakan {{ .name }} aplikasi gagal! {{ .err }}'
ErrContainerName: 'Nama kontena sudah wujud'
ErrCreateHttpClient: 'Gagal membuat permintaan {{ .err }}'
ErrHttpReqTimeOut: 'Permintaan tamat masa {{ .err }}'
ErrHttpReqFailed: 'Permintaan gagal {{ .err }}'
ErrNoSuchHost: 'Tidak dapat mencari pelayan yang diminta {{ .err }}'
ErrHttpReqNotFound: 'Sumber yang diminta {{ .err }} tidak dapat ditemui'
ErrContainerNotFound: 'Bekas {{ .name }} tidak wujud'
ErrContainerMsg: Bekas '{{ .name }} adalah tidak normal. Sila semak log pada halaman kontena untuk butiran'
ErrAppBackup: '{{ .name }} sandaran aplikasi gagal {{ .err }}'
ErrVersionTooLow: 'Versi 1Panel semasa terlalu rendah untuk mengemas kini App Store. Sila tingkatkan versi sebelum beroperasi.'
ErrAppNameExist: 'Nama aplikasi sudah wujud'
AppStoreIsSyncing: 'App Store sedang menyegerak, sila cuba sebentar lagi'
ErrGetCompose: 'Gagal mendapatkan fail docker-compose.yml! {{ .detail }}'
ErrAppWarn: 'Status tidak normal, sila semak log'
ErrAppParamKey: 'Parameter {{ .name }} medan tidak normal'
ErrAppUpgrade: 'Peningkatan {{ .name }} aplikasi gagal {{ .err }}'
AppRecover: 'Aplikasi tarik balik {{ .name }}'
PullImageStart: 'Mula tarik imej {{ .name }}'
PullImageSuccess: 'Tarik imej berjaya'
AppStoreIsLastVersion: 'App Store sudah pun versi terkini'
AppStoreSyncSuccess: 'Penyegerakan App Store berjaya'
SyncAppDetail: 'Segerakkan konfigurasi aplikasi'
AppVersionNotMatch: 'Aplikasi {{ .name }} memerlukan versi 1Panel yang lebih tinggi, melangkau penyegerakan'
MoveSiteDir: 'Peningkatan semasa memerlukan penghijrahan direktori tapak web OpenResty'
MoveSiteToDir: 'Pindahkan direktori tapak ke {{ .name }}'
ErrMoveSiteDir: 'Gagal memindahkan direktori tapak'
MoveSiteDirSuccess: 'Penghijrahan direktori tapak web yang berjaya'
DeleteRuntimePHP: 'Padam masa jalan PHP'
CustomAppStoreFileValid: 'Pakej gedung apl perlu dalam format .tar.gz'
PullImageTimeout: 'Tarik tamat masa imej, sila cuba tingkatkan pecutan imej atau tukar kepada pecutan imej lain'
ErrAppIsDown: 'Status permohonan {{ .name }} tidak normal, sila semak'
ErrCustomApps: 'Ada aplikasi yang dipasang, sila nyahpasangnya dahulu'
ErrCustomRuntimes: 'Terdapat persekitaran masa jalan yang dipasang, sila padamkannya dahulu'
ErrAppVersionDeprecated: "Aplikasi {{ .name }} tidak sesuai dengan versi 1Panel saat ini, dilewati"
ErrDockerFailed: "Keadaan Docker tidak normal, sila periksa status perkhidmatan"
ErrDockerComposeCmdNotFound: "Perintah Docker Compose tidak wujud, sila pasang perintah ini di mesin tuan terlebih dahulu"

#fail
ErrFileCanNotRead: 'Fail ini tidak menyokong pratonton'
ErrFileToLarge: 'Fail lebih besar daripada 10M dan tidak boleh dibuka'
ErrPathNotFound: 'Direktori tidak wujud'
ErrMovePathFailed: 'Laluan sasaran tidak boleh mengandungi laluan asal!'
ErrLinkPathNotFound: 'Laluan sasaran tidak wujud!'
ErrFileIsExist: 'Fail atau folder sudah wujud!'
ErrFileUpload: '{{ .name }} gagal memuat naik fail {{ .detail }}'
ErrFileDownloadDir: 'Folder muat turun tidak disokong'
ErrCmdNotFound: 'Arahan {{ .name}} tidak wujud, sila pasang arahan ini pada hos dahulu'
ErrSourcePathNotFound: 'Direktori sumber tidak wujud'
ErrFavoriteExist: 'Laluan ini telah digemari'
ErrInvalidChar: 'Aksara haram tidak dibenarkan'
ErrPathNotDelete: 'Direktori yang dipilih tidak boleh dipadamkan'

#laman web
ErrAliasIsExist: 'Alias sudah wujud'
ErrBackupMatch: 'Fail sandaran tidak sepadan dengan beberapa data tapak web semasa {{ .detail }}'
ErrBackupExist: 'Bahagian sepadan data sumber dalam fail sandaran tidak wujud {{ .detail }}'
ErrPHPResource: 'Persekitaran operasi tempatan tidak menyokong penukaran! '
ErrPathPermission: 'Folder dengan keizinan bukan 1000:1000 telah dikesan dalam direktori indeks, yang mungkin menyebabkan ralat dinafikan Akses pada tapak web. Sila klik butang Simpan di atas'
ErrDomainIsUsed: 'Nama domain telah digunakan oleh tapak web [{{ .name }}]'
ErrDomainFormat: 'Format nama domain {{ .name }} tidak betul'
ErrDefaultAlias: 'lalai ialah kod simpanan, sila gunakan kod lain'
ErrParentWebsite: 'Anda perlu memadamkan subtapak {{ .name }} dahulu'
ErrBuildDirNotFound: 'Direktori binaan tidak wujud'
ErrImageNotExist: 'Imej persekitaran operasi {{ .name }} tidak wujud, sila edit semula persekitaran pengendalian'
ErrProxyIsUsed: "Pengimbang beban telah digunakan oleh pengganti terbalik, tidak boleh dipadamkan"
ErrSSLValid: 'Fail sijil bermasalah, sila periksa status sijil!'

#ssl
ErrSSLCannotDelete: 'Sijil {{ .name }} sedang digunakan oleh tapak web dan tidak boleh dipadamkan'
ErrAccountCannotDelete: 'Akaun dikaitkan dengan sijil dan tidak boleh dipadamkan'
ErrSSSLApply: 'Pembaharuan sijil berjaya, muat semula openresty gagal, sila semak konfigurasi!'
ErrEmailIsExist: 'Peti mel sudah wujud'
ErrSSLKeyNotFound: 'Fail kunci peribadi tidak wujud'
ErrSSLCertificateNotFound: 'Fail sijil tidak wujud'
ErrSSLKeyFormat: 'Pengesahan fail kunci peribadi gagal'
ErrSSLCertificateFormat: 'Format fail sijil tidak betul, sila gunakan format pem'
ErrEabKidOrEabHmacKeyCannot Blank: 'EabKid atau EabHmacKey tidak boleh kosong'
ErrOpenrestyNotFound: 'Mod Http memerlukan Openresty dipasang dahulu'
ApplySSLStart: 'Mula memohon sijil, nama domain [{{ .domain }}] kaedah permohonan [{{ .type }}] '
dnsAccount: 'DNS Auto'
dnsManual: 'Manual DNS'
http: 'HTTP'
ApplySSLFailed: 'Permohonan untuk sijil [{{ .domain }}] gagal, {{ .detail }} '
ApplySSLSuccess: 'Berjaya memohon sijil [{{ .domain }}]! ! '
DNSAccountName: 'Akaun DNS [{{ .name }}] vendor [{{ .type }}]'
PushDirLog: 'Sijil ditolak ke direktori [{{ .path }}] {{ .status }}'
ErrDeleteCAWithSSL: 'Organisasi semasa mempunyai sijil yang telah dikeluarkan dan tidak boleh dipadamkan.'
ErrDeleteWithPanelSSL: 'Konfigurasi SSL Panel menggunakan sijil ini dan tidak boleh dipadamkan'
ErrDefaultCA: 'Pihak berkuasa lalai tidak boleh dipadamkan'
ApplyWebSiteSSLLog: 'Mula memperbaharui sijil tapak web {{ .name }}'
ErrUpdateWebsiteSSL: '{{ .name }} kemas kini sijil tapak web gagal: {{ .err }}'
ApplyWebSiteSSLSuccess: 'Berjaya kemas kini sijil tapak web'
ErrExecShell: 'Gagal melaksanakan skrip {{ .err }}'
ExecShellStart: 'Mula melaksanakan skrip'
ExecShellSuccess: 'Pelaksanaan skrip berjaya'
StartUpdateSystemSSL: 'Mula mengemas kini sijil sistem'
UpdateSystemSSLSuccess: 'Berjaya kemas kini sijil sistem'
ErrWildcardDomain: 'Tidak dapat memohon sijil nama domain kad bebas dalam mod HTTP'
ErrApplySSLCanNotDelete: "Sertifikat {{.name}} yang sedang diajukan tidak dapat dihapus, sila cuba lagi kemudian."

#mysql
ErrUserIsExist: 'Pengguna semasa sudah wujud, sila masukkan semula'
ErrDatabaseIsExist: 'Pangkalan data semasa sudah wujud, sila masukkan semula'
ErrExecTimeOut: 'Pelaksanaan SQL tamat masa, sila semak pangkalan data'
ErrRemoteExist: 'Pangkalan data jauh sudah wujud dengan nama ini, sila ubah suainya dan cuba lagi'
ErrLocalExist: 'Nama sudah wujud dalam pangkalan data tempatan, sila ubah suai dan cuba lagi'

#redis
ErrTypeOfRedis: 'Jenis fail pemulihan tidak sepadan dengan kaedah kegigihan semasa, sila ubah suai dan cuba lagi'

#bekas
ErrInUsed: '{{ .detail }} sedang digunakan dan tidak boleh dipadamkan'
ErrObjectInUsed: 'Objek sedang digunakan dan tidak boleh dipadamkan'
ErrObjectBeDependent: 'Imej ini bergantung pada imej lain dan tidak boleh dipadamkan'
ErrPortRules: 'Nombor port tidak sepadan, sila masukkan semula!'
ErrPgImagePull: 'Tarikh imej tamat masa, sila konfigurasikan pecutan imej atau tarik imej {{ .name }} secara manual dan cuba lagi'
PruneHelper: "Pembersihan ini membuang {{ .name }} {{ .count }} item, membebaskan {{ .size }} ruang cakera"
ImageRemoveHelper: "Padam imej {{ .name }}, membebaskan {{ .size }} ruang cakera"
BuildCache: "Cache binaan"
Volume: "Jilid storan"
Network: "Rangkaian"

#masa berjalan
ErrFileNotExist: 'Fail {{ .detail }} tidak wujud! Sila semak integriti fail sumber!'
ErrImageBuildErr: 'Pembinaan imej gagal'
ErrImageExist: "Imej sudah wujud! Sila ubah nama imej."
ErrDelWithWebsite: 'Persekitaran pengendalian sudah dikaitkan dengan tapak web dan tidak boleh dipadamkan'
ErrRuntimeStart: 'Permulaan gagal'
ErrPackageJsonNotFound: 'fail package.json tidak wujud'
ErrScriptsNotFound: 'Item konfigurasi skrip tidak ditemui dalam package.json'
ErrContainerNameNotFound: 'Tidak dapat mendapatkan nama kontena, sila semak fail .env'
ErrNodeModulesNotFound: 'Folder node_modules tidak wujud! Sila edit persekitaran masa jalan atau tunggu persekitaran masa jalan berjaya dimulakan'
ErrContainerNameIsNull: 'Nama kontena tidak wujud'
ErrPHPPortIsDefault: "Порт 9000 является портом по умолчанию, пожалуйста, измените и попробуйте снова"
ErrPHPRuntimePortFailed: "Порт {{ .name }} уже используется текущей средой выполнения, пожалуйста, измените и попробуйте снова"

#alat
ErrConfigNotFound: 'Fail konfigurasi tidak wujud'
ErrConfigParse: 'Format fail konfigurasi tidak betul'
ErrConfigIsNull: 'Fail konfigurasi tidak boleh kosong'
ErrConfigDirNotFound: 'Direktori berjalan tidak wujud'
ErrConfigAlreadyExist: 'Fail konfigurasi dengan nama yang sama sudah wujud'
ErrUserFindErr: 'Pengguna {{ .name }} carian gagal {{ .err }}'

#cronjob
CutWebsiteLogSuccess: '{{ .name }} log tapak web berjaya dipotong, laluan sandaran {{ .path }}'
HandleShell: 'Laksanakan skrip {{ .name }}'
HandleNtpSync: 'Penyegerakan masa sistem'
HandleSystemClean: 'Pembersihan cache sistem'
SystemLog: 'Log Sistem'
CutWebsiteLog: 'Putar Log Laman Web'
FileOrDir: 'Direktori / Fail'
UploadFile: 'Muat naik fail sandaran {{ .file }} ke {{ .backup }}'
IgnoreBackupErr: 'Sandaran gagal, ralat: {{ .detail }}, abaikan ralat ini...'
IgnoreUploadErr: 'Muat naik gagal, ralat: {{ .detail }}, abaikan ralat ini...'

#kotak alat
ErrNotExistUser: 'Pengguna semasa tidak wujud, sila ubah suai dan cuba lagi!'
ErrBanAction: 'Tetapan gagal. Perkhidmatan {{ .name }} semasa tidak tersedia. Sila semak dan cuba lagi!'
ErrClamdscanNotFound: 'Arahan clamdscan tidak dikesan, sila rujuk dokumentasi untuk memasangnya!'

#waf
ErrScope: 'Mengubah suai konfigurasi ini tidak disokong'
ErrStateChange: 'Perubahan keadaan gagal'
ErrRuleExist: 'Peraturan sudah wujud'
ErrRuleNotExist: 'Peraturan tidak wujud'
ErrParseIP: 'Format IP salah'
ErrDefaultIP: 'default ialah nama simpanan, sila tukar kepada nama lain'
ErrGroupInUse: 'Kumpulan IP digunakan oleh senarai hitam/senarai putih dan tidak boleh dipadamkan'
ErrIPGroupAclUse: "Kumpulan IP digunakan oleh peraturan tersuai tapak web {{ .name }}, tidak boleh dipadamkan"
ErrGroupExist: 'Nama kumpulan IP sudah wujud'
ErrIPRange: 'Julat IP salah'
ErrIPExist: 'IP sudah wujud'
urlDefense: 'Peraturan URL'
urlHelper: 'URL Terlarang'
dirFilter: 'Penapis direktori'
xss: 'XSS'
phpExec: 'Pelaksanaan skrip PHP'
oneWordTrojan: 'One Word Trojan'
appFilter: 'Gunakan penapisan direktori berbahaya'
webshell: 'Webshell'
args: 'Peraturan parameter'
protocolFilter: 'Penapisan protokol'
javaFileter: 'Penapis fail berbahaya Java'
Penapis pengimbas: 'Penapis pengimbas'
escapeFilter: 'penapis melarikan diri'
customRule: 'Peraturan tersuai'
httpMethod: 'Penapisan kaedah HTTP'
fileExt: 'Sekatan muat naik fail'
defaultIpBlack: 'Kumpulan IP berniat jahat'
cookie: 'Peraturan Kuki'
urlBlack: 'senarai hitam URL'
uaBlack: 'Senarai hitam Ejen Pengguna'
attackCount: 'Had kekerapan serangan'
fileExtCheck: 'Sekatan muat naik fail'
geoRestrict: 'Sekatan akses serantau'
unknownWebsite: 'Akses nama domain yang tidak dibenarkan'
notFoundCount: '404 Had Kadar'
headerDefense: 'Peraturan header'
defaultUaBlack: 'Peraturan Ejen Pengguna'
methodWhite: 'Peraturan HTTP'
captcha: 'pengesahan mesin manusia'
fiveSeconds: 'pengesahan 5 saat'
vulnCheck: 'Peraturan tambahan'
acl: 'Peraturan tersuai'
sql: 'suntikan SQL'
cc: 'Had kekerapan akses'
defaultUrlBlack: 'Peraturan URL'
sqlInject: 'Suntikan SQL'
ErrDBNotExist: 'Pangkalan data tidak wujud'
allow: 'membenarkan'
deny: 'menafikan'
OpenrestyNotFound: 'Openresty tidak dipasang'
remoteIpIsNull: "Senarai IP kosong"
OpenrestyVersionErr: "Versi Openresty terlalu rendah, sila naik taraf Openresty ke ********-2-2-focal"

#tugas
TaskStart: '{{ .name }} Task mula [MULA]'
TaskEnd: '{{ .name }} Task selesai [SELESAI]'
TaskFailed: 'tugas {{ .name }} gagal'
TaskTimeout: '{{ .name }} tamat masa'
TaskSuccess: '{{ .name }} Task berjaya'
TaskRetry: 'Mulakan {{ .name }} cuba semula'
SubTaskSuccess: '{{ .name }} berjaya'
SubTaskFailed: '{{ .name }} gagal: {{ .err }}'
TaskInstall: 'Pasang'
TaskUninstall: 'Nyahpasang'
TaskCreate: 'Buat'
TaskDelete: 'Padam'
TaskUpgrade: 'Naik taraf'
TaskUpdate: 'Kemas kini'
TaskRestart: 'Mulakan semula'
TaskBackup: 'Sandaran'
TaskRecover: 'Pulihkan'
TaskRollback: 'Rollback'
TaskPull: 'Tarik'
TaskCommit: 'Komit'
TaskBuild: 'Bina'
TaskPush: 'Tolak'
TaskClean: "Pembersihan"
TaskHandle: 'Laksanakan'
Website: 'Laman web'
App: 'Aplikasi'
Runtime: 'Persekitaran masa jalan'
Database: 'Pangkalan Data'
ConfigFTP: 'Buat pengguna FTP {{ .name }}'
ConfigOpenresty: 'Buat fail konfigurasi Openresty'
InstallAppSuccess: 'Aplikasi {{ .name }} berjaya dipasang'
ConfigRuntime: 'Konfigurasikan persekitaran masa jalan'
ConfigApp: 'Aplikasi Konfigurasi'
SuccessStatus: '{{ .name }} berjaya'
FailedStatus: '{{ .name }} gagal {{ .err }}'
HandleLink: 'Kendalikan persatuan aplikasi'
HandleDatabaseApp: 'Mengendalikan parameter aplikasi'
ExecShell: 'Laksanakan skrip {{ .name }}'
PullImage: 'Tarik imej'
Start: 'Mula'
Run: 'Mula'
Stop: 'Berhenti'
Image: 'Cermin'
Compose: 'Orkestrasi'
Container: 'Bekas'
AppLink: 'Aplikasi Terpaut'
EnableSSL: 'Dayakan HTTPS'
AppStore: 'App Store'
TaskSync: 'Segerakkan'
LocalApp: 'Aplikasi Tempatan'
SubTask: 'Subtugas'
RuntimeExtension: 'Sambungan Persekitaran Runtime'
TaskIsExecuting: 'Tugas sedang berjalan'
CustomAppstore: 'Gudang aplikasi tersuai'

# tugasan - ai
OllamaModelPull: 'Tarik model Ollama {{ .name }}'
OllamaModelSize: 'Dapatkan saiz model Ollama {{ .name }}'

# gambar tugasan
Snapshot: 'Snapshot'
SnapDBInfo: 'Tulis maklumat pangkalan data 1Panel'
SnapCopy: 'Salin fail & direktori {{ .name }}'
SnapNewDB: 'Mulakan sambungan {{ .name }} pangkalan data'
SnapDeleteOperationLog: 'Padam log operasi'
SnapDeleteLoginLog: 'Padam log akses'
SnapDeleteMonitor: 'Padamkan data pemantauan'
SnapRemoveSystemIP: 'Alih keluar IP sistem'
SnapBaseInfo: 'Tulis maklumat asas 1Panel'
SnapInstallAppImageEmpty: 'Tiada imej aplikasi dipilih, dilangkau...'
SnapInstallApp: 'Sandaran aplikasi terpasang 1Panel'
SnapDockerSave: 'Mampatkan aplikasi yang dipasang'
SnapLocalBackup: 'Sandaran 1Panel direktori sandaran tempatan'
SnapCompressBackup: 'Mampatkan direktori sandaran tempatan'
SnapPanelData: 'Sandaran direktori data 1Panel'
SnapCompressPanel: 'Direktori Data Mampat'
SnapWebsite: 'Sandaran direktori tapak web 1Panel'
SnapCloseDBConn: 'Tutup sambungan pangkalan data'
SnapCompress: 'Buat fail syot kilat'
SnapCompressFile: 'Mampatkan fail syot kilat'
SnapCheckCompress: 'Semak fail mampatan syot kilat'
SnapCompressSize: 'Saiz fail syot kilat {{ .name }}'
SnapUpload: 'Muat naik fail syot kilat'
SnapLoadBackup: 'Dapatkan maklumat akaun sandaran'
SnapUploadTo: 'Muat naik fail syot kilat ke {{ .name }}'
SnapUploadRes: 'Muat naik fail syot kilat ke {{ .name }}'

SnapshotRecover: 'Snapshot Restore'
RecoverDownload: 'Muat turun fail syot kilat'
Download: 'Muat turun'
RecoverDownloadAccount: 'Dapatkan syot kilat muat turun akaun sandaran {{ .name }}'
RecoverDecompress: 'Nyahmampatkan fail termampat syot kilat'
Decompress: 'Penyahmampatan'
BackupBeforeRecover: 'Sandarkan data berkaitan sistem sebelum syot kilat'
Readjson: 'Baca fail Json dalam petikan'
ReadjsonPath: 'Dapatkan laluan fail Json dalam petikan'
ReadjsonContent: 'Baca fail Json'
ReadjsonMarshal: 'Pemprosesan melarikan diri Json'
RecoverApp: 'Pulihkan apl yang dipasang'
RecoverWebsite: 'Pulihkan direktori tapak web'
RecoverAppImage: 'Pulihkan sandaran imej syot kilat'
RecoverCompose: 'Pulihkan kandungan komposer lain'
RecoverComposeList: 'Dapatkan semua komposer untuk dipulihkan'
RecoverComposeItem: 'Recover compose {{ .name }}'
RecoverAppEmpty: 'Tiada sandaran imej aplikasi ditemui dalam fail syot kilat'
RecoverBaseData: 'Pulihkan data asas dan fail'
RecoverDaemonJsonEmpty: 'Kedua-dua fail syot kilat dan mesin semasa tidak mempunyai fail daemon.json konfigurasi bekas'
RecoverDaemonJson: 'Pulihkan fail daemon.json konfigurasi bekas'
RecoverDBData: 'Pulihkan data pangkalan data'
RecoverBackups: 'Pulihkan direktori sandaran tempatan'
RecoverPanelData: 'Direktori data pemulihan'

# tugas - bekas
ContainerNewCliet: 'Memulakan Klien Docker'
ContainerImagePull: 'Tarik imej bekas {{ .name }}'
ContainerRemoveOld: 'Alih keluar bekas asal {{ .name }}'
ContainerImageCheck: 'Periksa sama ada imej ditarik seperti biasa'
ContainerLoadInfo: 'Dapatkan maklumat bekas asas'
ContainerRecreate: 'Kemas kini kontena gagal, kini mula memulihkan bekas asal'
ContainerCreate: 'Buat bekas baharu {{ .name }}'
ContainerCreateFailed: 'Penciptaan bekas gagal, padamkan bekas yang gagal'
ContainerStartCheck: 'Periksa sama ada bekas telah dimulakan'

# tugas - imej
ImageBuild: 'Membina Imej'
ImageBuildStdoutCheck: 'Menghuraikan kandungan output imej'
ImageBuildRes: 'Output binaan imej: {{ .name }}'
ImagePull: 'Tarik imej'
ImageRepoAuthFromDB: 'Dapatkan maklumat pengesahan repositori daripada pangkalan data'
ImaegPullRes: 'Output tarik imej: {{ .name }}'
ImagePush: 'Tolak imej'
ImageRenameTag: 'Ubah suai teg imej'
ImageNewTag: 'Teg imej baharu {{ .name }}'
ImaegPushRes: 'Output tolak imej: {{ .name }}'
ComposeCreate: 'Buat gubahan'
ComposeCreateRes: 'Karang cipta output: {{ .name }}'

# tugas - laman web
BackupNginxConfig: 'Fail konfigurasi OpenResty tapak web sandaran'
CompressFileSuccess: 'Mampatkan direktori berjaya, dimampatkan ke {{ .name }}'
CompressDir: 'Direktori mampatan'
DeCompressFile: 'Nyahmampatkan fail {{ .name }}'
ErrCheckValid: 'Pengesahan fail sandaran gagal, {{ .name }}'
Rollback: 'Undur'
websiteDir: 'Direktori Laman Web'
RecoverFailedStartRollBack: 'Pemulihan gagal, mulakan rollback'
AppBackupFileIncomplete: 'Fail sandaran tidak lengkap dan tidak mempunyai fail app.json atau app.tar.gz'
AppAttributesNotMatch: 'Jenis atau nama aplikasi tidak sepadan'

#alert
ErrAlert: 'Format mesej amaran tidak betul, sila semak dan cuba lagi!'
ErrAlertPush: 'Ralat dalam menolak maklumat amaran, sila semak dan cuba lagi!'
ErrAlertSave: 'Ralat menyimpan maklumat penggera, sila semak dan cuba lagi!'
ErrAlertSync: 'Ralat penyegerakan maklumat penggera, sila semak dan cuba lagi!'
ErrAlertRemote: 'Ralat jauh mesej penggera, sila semak dan cuba lagi!'

#task - runtime
ErrInstallExtension: "Tugas pemasangan sudah sedang berjalan, silakan tunggu tugas selesai"

# alert mail template
PanelAlertTitle: "Notifikasi Amaran Panel"
TestAlertTitle: "E-mel Ujian - Sahkan Sambungan E-mel"
TestAlert: "Ini adalah e-mel ujian untuk mengesahkan konfigurasi penghantaran e-mel anda betul."
LicenseExpirationAlert: "Lesen 1Panel anda akan tamat dalam {{ .day }} hari. Sila log masuk ke panel untuk maklumat lanjut."
CronJobFailedAlert: "Tugas berjadual 1Panel '{{ .name }}' gagal dilaksanakan. Sila log masuk ke panel untuk butiran lanjut."
ClamAlert: "Tugas imbasan virus 1Panel menemui {{ .num }} fail yang dijangkiti. Sila log masuk ke panel untuk maklumat lanjut."
WebSiteAlert: "Terdapat {{ .num }} laman web dalam 1Panel anda yang akan tamat dalam {{ .day }} hari. Sila log masuk ke panel untuk maklumat lanjut."
SSLAlert: "Terdapat {{ .num }} sijil SSL dalam 1Panel anda yang akan tamat dalam {{ .day }} hari. Sila log masuk ke panel untuk maklumat lanjut."
DiskUsedAlert: "Cakera '{{ .name }}' dalam 1Panel anda telah menggunakan {{ .used }}. Sila log masuk ke panel untuk maklumat lanjut."
ResourceAlert: "Penggunaan purata {{ .name }} selama {{ .time }} minit dalam 1Panel anda ialah {{ .used }}. Sila log masuk ke panel untuk maklumat lanjut."
PanelVersionAlert: "Versi baru 1Panel tersedia. Sila log masuk ke panel untuk menaik taraf."
PanelPwdExpirationAlert: "Kata laluan 1Panel anda akan tamat dalam {{ .day }} hari. Sila log masuk ke panel untuk maklumat lanjut."