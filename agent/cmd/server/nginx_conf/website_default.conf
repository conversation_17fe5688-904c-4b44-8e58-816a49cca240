server {
    listen 80;
    server_name ko.wp-1.com;

    index index.php index.html index.htm default.php default.htm default.html;

    access_log /www/sites/domain/log/access.log main;
    error_log /www/sites/domain/log/error.log;
    
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md){
        return 404;
    }
    location ^~ /.well-known{
        allow all;
        root /usr/share/nginx/html;
    }
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }

}
