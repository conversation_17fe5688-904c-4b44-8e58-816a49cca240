if (!-e $request_filename) {
        rewrite ^/index.php(.*)$ /index.php?s=$1 break;
        # MacCMS要求强制修改后台文件名称 所以需要手动修改下方这条重写规则 将admin修改为你修改后的文件名即可
        rewrite ^/admin.php(.*)$ /admin.php?s=$1 break;
        rewrite ^/api.php(.*)$ /api.php?s=$1 break;
        rewrite ^/(.*)$ /index.php?s=$1 break;
        rewrite ^/vod-(.*)$ /index.php?m=vod-$1 break;
        rewrite ^/art-(.*)$ /index.php?m=art-$1 break;
        rewrite ^/gbook-(.*)$ /index.php?m=gbook-$1 break;
        rewrite ^/label-(.*)$ /index.php?m=label-$1 break;
        rewrite ^/map-(.*)$ /index.php?m=map-$1 break;
    }
try_files $uri $uri/ /index.php?$query_string;