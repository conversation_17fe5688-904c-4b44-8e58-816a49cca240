if (!-e $request_filename)
{
rewrite "^/index\.html" /index.php last;
rewrite "^/category$" /index.php last;
rewrite "^/feed-c([0-9]+)\.xml$" /feed.php?cat=$1 last;
rewrite "^/feed-b([0-9]+)\.xml$" /feed.php?brand=$1 last;
rewrite "^/feed\.xml$" /feed.php last;
rewrite "^/category-([0-9]+)-b([0-9]+)-min([0-9]+)-max([0-9]+)-attr([^-]*)-([0-9]+)-(.+)-([a-zA-Z]+)(.*)\.html$" /category.php?id=$1&brand=$2&price_min=$3&price_max=$4&filter_attr=$5&page=$6&sort=$7&order=$8 last;
rewrite "^/category-([0-9]+)-b([0-9]+)-min([0-9]+)-max([0-9]+)-attr([^-]*)(.*)\.html$" /category.php?id=$1&brand=$2&price_min=$3&price_max=$4&filter_attr=$5 last;
rewrite "^/category-([0-9]+)-b([0-9]+)-([0-9]+)-(.+)-([a-zA-Z]+)(.*)\.html$" /category.php?id=$1&brand=$2&page=$3&sort=$4&order=$5 last;
rewrite "^/category-([0-9]+)-b([0-9]+)-([0-9]+)(.*)\.html$" /category.php?id=$1&brand=$2&page=$3 last;
rewrite "^/category-([0-9]+)-b([0-9]+)(.*)\.html$" /category.php?id=$1&brand=$2 last;
rewrite "^/category-([0-9]+)(.*)\.html$" /category.php?id=$1 last;
rewrite "^/goods-([0-9]+)(.*)\.html" /goods.php?id=$1 last;
rewrite "^/article_cat-([0-9]+)-([0-9]+)-(.+)-([a-zA-Z]+)(.*)\.html$" /article_cat.php?id=$1&page=$2&sort=$3&order=$4 last;
rewrite "^/article_cat-([0-9]+)-([0-9]+)(.*)\.html$" /article_cat.php?id=$1&page=$2 last;
rewrite "^/article_cat-([0-9]+)(.*)\.html$" /article_cat.php?id=$1 last;
rewrite "^/article-([0-9]+)(.*)\.html$" /article.php?id=$1 last;
rewrite "^/brand-([0-9]+)-c([0-9]+)-([0-9]+)-(.+)-([a-zA-Z]+)\.html" /brand.php?id=$1&cat=$2&page=$3&sort=$4&order=$5 last;
rewrite "^/brand-([0-9]+)-c([0-9]+)-([0-9]+)(.*)\.html" /brand.php?id=$1&cat=$2&page=$3 last;
rewrite "^/brand-([0-9]+)-c([0-9]+)(.*)\.html" /brand.php?id=$1&cat=$2 last;
rewrite "^/brand-([0-9]+)(.*)\.html" /brand.php?id=$1 last;
rewrite "^/tag-(.*)\.html" /search.php?keywords=$1 last;
rewrite "^/snatch-([0-9]+)\.html$" /snatch.php?id=$1 last;
rewrite "^/group_buy-([0-9]+)\.html$" /group_buy.php?act=view&id=$1 last;
rewrite "^/auction-([0-9]+)\.html$" /auction.php?act=view&id=$1 last;
rewrite "^/exchange-id([0-9]+)(.*)\.html$" /exchange.php?id=$1&act=view last;
rewrite "^/exchange-([0-9]+)-min([0-9]+)-max([0-9]+)-([0-9]+)-(.+)-([a-zA-Z]+)(.*)\.html$" /exchange.php?cat_id=$1&integral_min=$2&integral_max=$3&page=$4&sort=$5&order=$6 last;
rewrite ^/exchange-([0-9]+)-([0-9]+)-(.+)-([a-zA-Z]+)(.*)\.html$" /exchange.php?cat_id=$1&page=$2&sort=$3&order=$4 last;
rewrite "^/exchange-([0-9]+)-([0-9]+)(.*)\.html$" /exchange.php?cat_id=$1&page=$2 last;
rewrite "^/exchange-([0-9]+)(.*)\.html$" /exchange.php?cat_id=$1 last;
}