# yaml-language-server: $schema=https://goreleaser.com/static/schema.json
# vim: set ts=2 sw=2 tw=0 fo=jcroql
version: 2

before:
  hooks:
    # - export NODE_OPTIONS="--max-old-space-size=8192"
    # - make build_web
    - chmod +x ./ci/script.sh
    - ./ci/script.sh
    - sed -i 's@ORIGINAL_VERSION=.*@ORIGINAL_VERSION=v{{ .Version }}@g' 1pctl
    
builds:
  - id: agent
    dir: agent
    main: cmd/server/main.go
    binary: 1panel-agent
    flags:
      - -tags=xpack
      - -trimpath
    ldflags:
      - -w -s
    env:
      - CGO_ENABLED=0
    goos:
      - linux
    goarm:
      - 7
    goarch:
      - amd64
      - arm64
      - arm
      - ppc64le
      - s390x
      - riscv64
    hooks:
      post:
        - ./ci/script.sh compress_binary {{ .Path }} {{ .Arch }}

  - id: core
    dir: core
    main: cmd/server/main.go
    binary: 1panel-core
    flags:
      - -tags=xpack
      - -trimpath
    ldflags:
      - -w -s
    env:
      - CGO_ENABLED=0
    goos:
      - linux
    goarm:
      - 7
    goarch:
      - amd64
      - arm64
      - arm
      - ppc64le
      - s390x
      - riscv64
    hooks:
      post:
        - ./ci/script.sh compress_binary {{ .Path }} {{ .Arch }}

archives:
  - formats: [ 'tar.gz' ]
    ids: [core, agent]
    name_template: "1panel-v{{ .Version }}-{{ .Os }}-{{ .Arch }}{{- if .Arm }}v{{ .Arm }}{{ end }}"
    wrap_in_directory: true
    files:
      - 1pctl
      - 1panel-core.service
      - 1panel-agent.service
      - install.sh
      - GeoIP.mmdb
      - lang/*

checksum:
  name_template: 'checksums.txt'

changelog:
  sort: asc
  filters:
    exclude:
      - "^docs:"
      - "^test:"
