import fit2cloudEnLocale from 'fit2cloud-ui-plus/src/locale/lang/en';

const message = {
    commons: {
        true: 'true',
        false: 'false',
        colon: ': ',
        example: 'For example, ',
        fit2cloud: 'FIT2CLOUD',
        lingxia: 'Lingxia',
        button: {
            run: 'Run',
            prev: 'Previous',
            next: 'Next',
            create: 'Create ',
            add: 'Add ',
            save: 'Save ',
            set: 'Set',
            sync: 'Sync ',
            delete: 'Delete',
            edit: 'Edit ',
            enable: 'Enable',
            disable: 'Disable',
            confirm: 'Confirm',
            cancel: 'Cancel',
            reset: 'Reset',
            setDefault: 'Restore Default',
            restart: 'Restart',
            conn: 'Connect',
            disconn: 'Disconnect',
            clean: 'Clean',
            login: 'Login',
            close: 'Close',
            stop: 'Stop',
            start: 'Start',
            view: 'View',
            watch: 'Watch',
            handle: 'Trigger',
            clone: 'Clone',
            expand: 'Expand',
            collapse: 'Collapse',
            log: 'View logs',
            back: 'Back',
            backup: 'Backup',
            recover: 'Recover',
            retry: 'Retry',
            upload: 'Upload',
            download: 'Download',
            init: 'Initialize',
            verify: 'Verify',
            saveAndEnable: 'Save and enable',
            import: 'Import',
            export: 'Export',
            power: 'Authorization',
            search: 'Search',
            refresh: 'Refresh',
            get: 'Get',
            upgrade: 'Upgrade',
            update: 'Update',
            ignore: 'Ignore upgrade',
            copy: 'Copy',
            random: 'Random',
            install: 'Install',
            uninstall: 'Uninstall',
            fullscreen: 'Enter fullscreen',
            quitFullscreen: 'Exit fullscreen',
            showAll: 'Show All',
            hideSome: 'Hide Some',
            agree: 'Agree',
            notAgree: 'Not Agree',
            preview: 'Preview',
            open: 'Open',
            notSave: 'Not Save',
            createNewFolder: 'Create new folder',
            createNewFile: 'Create new file',
            helpDoc: 'Help Document',
            bind: 'Bind',
            unbind: 'Unbind',
            cover: 'cover',
            skip: 'skip',
            fix: 'Fix',
            down: 'Stop',
            up: 'Start',
            sure: 'Confirm',
            show: 'Show',
            hide: 'Hide',
        },
        operate: {
            start: 'Start',
            stop: 'Stop',
            restart: 'Restart',
            reload: 'Reload',
            rebuild: 'Rebuild',
            sync: 'Sync',
            up: 'Up',
            down: 'Down',
            delete: 'Delete',
        },
        search: {
            timeStart: 'Time start',
            timeEnd: 'Time end',
            timeRange: 'To',
            dateStart: 'Date start',
            dateEnd: 'Date end',
        },
        table: {
            all: 'All',
            total: 'Total {0}',
            name: 'Name',
            type: 'Type',
            status: 'Status',
            statusSuccess: 'Success',
            statusFailed: 'Failed',
            statusWaiting: 'Waiting...',
            records: 'Records',
            group: 'Group',
            default: 'Default',
            createdAt: 'Creation time',
            publishedAt: 'Publish time',
            date: 'Date',
            updatedAt: 'Update time',
            operate: 'Operations',
            message: 'Message',
            description: 'Description',
            interval: 'Interval',
            user: 'Owner',
            title: 'Title',
            port: 'Port',
            forward: 'Forward',
            protocol: 'Protocol',
            tableSetting: 'Table setting',
            refreshRate: 'Refresh rate',
            noRefresh: 'No refresh',
            selectColumn: 'Select column',
            local: 'local',
            serialNumber: 'Serial number',
            manageGroup: 'Manage Groups',
            backToList: 'Back to List',
            keepEdit: 'Continue Editing',
        },
        loadingText: {
            Upgrading: 'System upgrade, please wait...',
            Restarting: 'System restart, please wait...',
            Recovering: 'Recovering from snapshot, please wait...',
            Rollbacking: 'Rollbacking from snapshot, please wait...',
        },
        msg: {
            noneData: 'No data available',
            delete: `This operation delete can't be undone. Do you want to continue?`,
            clean: `This operation clean can't be undone. Do you want to continue?`,
            closeDrawerHelper: 'The system may not save the changes you made. Do you want to continue?',
            deleteSuccess: 'Deleted successfully',
            loginSuccess: 'Logged in successfully',
            operationSuccess: 'Done successfully',
            copySuccess: 'Copied successfully',
            notSupportOperation: `This operation isn't supported`,
            requestTimeout: 'The request timed out, please try again later',
            infoTitle: 'Hint',
            notRecords: 'No execution record is generated for the current task',
            sureLogOut: 'Are you sure you want to log out?',
            createSuccess: 'Created successfully',
            updateSuccess: 'Updated successfully',
            uploadSuccess: 'Uploaded successfully',
            operateConfirm: 'If you are sure about the operation, please input it manually : ',
            inputOrSelect: 'Please select or enter',
            copyFailed: 'Failed to copy',
            operatorHelper: `The operation "{1}" will be performed on "{0}" and can't be undone. Do you want to continue?`,
            notFound: 'Sorry, the page you requested does not exist.',
            unSupportType: `Current file type isn't supported.`,
            unSupportSize: 'The uploaded file exceeds {0}M, please confirm!',
            fileExist: `The file already exists in the current folder. Repeat uploading isn't supported.`,
            fileNameErr:
                'You can upload only files whose name contains 1 to 256 characters, including English, Chinese, digits, or periods (.-_)',
            confirmNoNull: `Make sure the value {0} isn't empty.`,
            errPort: 'Incorrect port information, please confirm!',
            remove: 'Remove',
            backupHelper: 'The current operation will back up {0}. Do you want to proceed?',
            recoverHelper: 'Restoring from {0} file. This operation is irreversible. Do you want to continue?',
            refreshSuccess: 'Refresh successful',
            rootInfoErr: "It's already the root directory",
            resetSuccess: 'Reset successful',
            creatingInfo: 'Creating, no need for this operation',
            installSuccess: 'Install successful',
            uninstallSuccess: 'Uninstall successful',
        },
        login: {
            username: 'Username',
            password: 'Password',
            welcome: 'Welcome back, please enter your username and password to log in!',
            errorAuthInfo: 'The user name or password you entered is incorrect, please re-enter!',
            errorMfaInfo: 'Incorrect authentication information, please try again!',
            captchaHelper: 'Captcha',
            errorCaptcha: 'Captcha code error!',
            notSafe: 'Access Denied',
            safeEntrance1: 'The secure login has been enabled in the current environment',
            safeEntrance2: 'Enter the following command on the SSH terminal to view the panel entry: 1pctl user-info',
            errIP1: 'Authorized IP address access is enabled in the current environment',
            errDomain1: 'Access domain name binding is enabled in the current environment',
            errHelper: 'To reset the binding information, run the following command on the SSH terminal: ',
            codeInput: 'Please enter the 6-digit verification code of the MFA validator',
            mfaTitle: 'MFA Certification',
            mfaCode: 'MFA verification code',
            title: 'Linux Server Management Panel',
            licenseHelper: '<Community License Agreement>',
            errorAgree: 'Click to agree to the Community Software License',
            logout: 'Logout',
            agreeTitle: 'Agreement',
            agreeContent:
                'In order to better protect your legitimate rights and interests, please read and agree to the following agreement &laquo; <a href = "https://www.fit2cloud.com/legal/licenses.html" target = "_blank" > Community License Agreement </a> &raquo;',
        },
        rule: {
            username: 'Enter a username',
            password: 'Enter a password',
            rePassword: 'Confirm password is inconsistent with the password.',
            requiredInput: 'This field is required.',
            requiredSelect: 'Select an item in the list',
            illegalChar: 'Injection of characters & ; $ \' ` ( ) " > < | is currently not supported',
            illegalInput: `This field mustn't contains illegal characters.`,
            commonName:
                'This field must start with non-special characters and must consist of English, Chinese, numbers, ".", "-", and "_" characters with a length of 1-128.',
            userName: 'This field must consist of English, Chinese, numbers and "_" characters with a length of 3-30.',
            simpleName: `This field mustn't start with underscore character and must consist of English, numbers, and "_" characters with a length of 3-30.`,
            simplePassword: `This field mustn't start with underscore character and must consist of English, numbers, and "_" characters with a length of 1-30.`,
            dbName: `This field mustn't start with underscore character and must consist of English, numbers, and "_" characters with a length of 1-64.`,
            imageName:
                'This field must consist of English, numbers, ":", "@", "/", ".", "-", and "_" characters with a length of 1-256.',
            volumeName:
                'This field must consist of English, numbers, ".", "-", and "_" characters with a length of 2-30.',
            supervisorName:
                'This field must start with non-special characters and must consist of English, numbers, "-", and "_" characters with a length of 1-128.',
            composeName:
                'Supports non-special characters at the beginning, lowercase letters, numbers, - and _, length 1-256',
            complexityPassword:
                'This field must consist of English, numbers with a length of 8-30 and contain at least two special characters.',
            commonPassword: 'This field length must be more than 6.',
            linuxName: `This field length must be between 1 and 128. The field mustn't contain these special characters: "{0}".`,
            email: 'This field must be a valid email address.',
            number: 'This field must be a number.',
            integer: 'This field must be a positive integer.',
            ip: 'This field must be a valid IP address.',
            host: 'This field must be a valid IP address or domain name.',
            hostHelper: 'Support input of IP address or domain name',
            port: 'This field must be a valid port number.',
            selectHelper: 'Please select the correct {0} file',
            domain: 'This field must be like: example.com or example.com:8080.',
            databaseName: 'This field must consist of English, numbers, and "_" characters with a length of 1-30.',
            ipErr: 'This field must be a valid IP address.',
            numberRange: 'This field must be a number between {0} and {1}.',
            paramName:
                'This field must consist of English, numbers, ".", "-", and "_" characters with a length of 2-30.',
            paramComplexity: `This field mustn't start and end with special characters and must consist of English, numbers, "{0}" characters with a length of 6-128.`,
            paramUrlAndPort: 'This field must be in the format of "http(s)://(domain name/ip):(port)".',
            nginxDoc: 'This field must consist of English, numbers and "." characters.',
            appName: `This field musnt't start and end with "-" and "_" characters and must consist of English, numbers, "-", and "_" characters with a length of 2-30.`,
            containerName: 'Supports letters, numbers, -, _ and .; cannot start with - _ or .; length: 2-128',
            mirror: 'The mirror acceleration address should start with http(s)://, support English letters (both uppercase and lowercase), numbers, . / and -, and should not contain blank lines.',
            disableFunction: 'Only support letters ,underscores,and,',
            leechExts: 'Only support letters, numbers and,',
            paramSimple: 'Support lowercase letters and numbers, length 1-128',
            filePermission: 'File Permission Error',
            formatErr: 'Format error, please check and retry',
            phpExtension: 'Only supports , _ lowercase English and numbers',
            paramHttp: 'Must start with http:// or https://',
            phone: 'The format of the phone number is incorrect',
            authBasicPassword: 'Supports letters, numbers, and common special characters, length 1-72',
            length128Err: 'Length cannot exceed 128 characters',
            maxLength: 'Length cannot exceed {0} characters',
            alias: 'Supports English, numbers, - and _, length 1-30, and cannot start or end with -_.',
        },
        res: {
            paramError: 'The request failed, please try again later!',
            forbidden: 'The current user has no permission',
            serverError: 'Service exception',
            notFound: 'The resource does not exist',
            commonError: 'The request failed',
        },
        service: {
            serviceNotStarted: `The {0} service isn't started.`,
        },
        status: {
            running: 'Running',
            done: 'Done',
            scanFailed: 'Incomplete',
            success: 'Success',
            waiting: 'Waiting',
            failed: 'Failed',
            stopped: 'Stopped',
            error: 'Error',
            created: 'Created',
            restarting: 'Restarting',
            uploading: 'Uploading',
            unhealthy: 'Unhealthy',
            removing: 'Removing',
            paused: 'Paused',
            exited: 'Exited',
            dead: 'Dead',
            installing: 'Installing',
            enabled: 'Enabled',
            disabled: 'Disabled',
            normal: 'Normal',
            building: 'Building',
            upgrading: 'Upgrading',
            pending: 'Pending Edit',
            rebuilding: 'Rebuilding',
            deny: 'Denied',
            accept: 'Accepted',
            used: 'Used',
            unused: 'Unused',
            starting: 'Starting',
            recreating: 'Recreating',
            creating: 'Creating',
            init: 'Waiting for application',
            ready: 'normal',
            applying: 'Applying',
            uninstalling: 'Uninstalling',
            lost: 'Lost Contact',
            bound: 'Bound',
            unbind: 'Unbound',
            exceptional: 'Exceptional',
            free: 'Free',
            enable: 'Enabled',
            disable: 'Disabled',
            deleted: 'Deleted',
            downloading: 'Downloading',
            packing: 'Packing',
            sending: 'Sending',
            healthy: 'Normal',
            executing: 'Executing',
            installerr: 'Installation failed',
            applyerror: 'Apply failed',
            systemrestart: 'Interrupted',
            starterr: 'Startup failed',
            uperr: 'Startup failed',
        },
        units: {
            second: ' second | second | seconds',
            minute: 'minute | minute | minutes',
            hour: 'hour | hour | hours',
            day: 'day | day | days',
            week: 'week | week | weeks',
            month: 'month | month | months',
            year: 'year | year | years',
            time: 'rqm',
            core: 'core | core | cores',
            millisecond: 'millisecond | milliseconds',
            secondUnit: 's',
            minuteUnit: 'min',
            hourUnit: 'h',
            dayUnit: 'd',
        },
    },
    menu: {
        home: 'Overview',
        apps: 'App Store',
        website: 'Website | Websites',
        project: 'Project | Projects',
        config: 'Configuration | Configurations',
        ssh: 'SSH Settings',
        firewall: 'Firewall',
        ssl: 'Certificate | Certificates',
        database: 'Database | Databases',
        aiTools: 'AI',
        mcp: 'MCP',
        container: 'Container | Containers',
        cronjob: 'Cron Job | Cron Jobs',
        system: 'System',
        security: 'Security',
        files: 'File Browser',
        monitor: 'Monitoring',
        terminal: 'Terminal | Terminals',
        settings: 'Setting | Settings',
        toolbox: 'Toolbox',
        logs: 'Log | Logs',
        runtime: 'Runtime | Runtimes',
        processManage: 'Process | Processes',
        process: 'Process | Processes',
        network: 'Network | Networks',
        supervisor: 'Supervisor',
        tamper: 'Tamper-proof',
        app: 'Application',
        msgCenter: 'Task Center',
    },
    home: {
        recommend: 'recommend',
        dir: 'dir',
        restart_1panel: 'Restart panel',
        restart_system: 'Restart server',
        operationSuccess: 'Operation succeeded, rebooting, please refresh the browser manually later!',
        entranceHelper: `Security entrance isn't enabled. You can enable it in "Settings -> Security" to improve system security.`,
        appInstalled: 'Applications',
        systemInfo: 'System infomation',
        hostname: 'Hostname',
        platformVersion: 'Operating system',
        kernelVersion: 'Kernel',
        kernelArch: 'Architecture',
        network: 'Network',
        io: 'Disk I/O',
        ip: 'Local IP',
        proxy: 'System proxy',
        baseInfo: 'Base info',
        totalSend: 'Total sent',
        totalRecv: 'Total received',
        rwPerSecond: 'I/O operations',
        ioDelay: 'I/O latency',
        uptime: 'Uptime',
        runningTime: 'Up since',
        mem: 'System',
        swapMem: 'Swap Partition',

        runSmoothly: 'Low load',
        runNormal: 'Moderate load',
        runSlowly: 'High load',
        runJam: 'Heavy load',

        core: 'Physical core',
        logicCore: 'Logical core',
        loadAverage: 'Load average in the last 1 minute | Load average in the last {n} minutes',
        load: 'Load',
        mount: 'Mount point',
        fileSystem: 'File system',
        total: 'Total',
        used: 'Used',
        cache: 'Cache',
        free: 'Free',
        shard: 'Sharded',
        available: 'Available',
        percent: 'Utilization',
        goInstall: 'Go install',

        networkCard: 'Network card',
        disk: 'Disk',
    },
    tabs: {
        more: 'More',
        hide: 'Hide',
        closeLeft: 'Close left',
        closeRight: 'Close right',
        closeCurrent: 'Close current',
        closeOther: 'Close other',
        closeAll: 'Close All',
    },
    header: {
        logout: 'Logout',
    },
    database: {
        manage: 'Manage database',
        deleteBackupHelper: 'Delete database backups simultaneously',
        delete: 'Delete operation cannot be rolled back, please input "',
        deleteHelper: '" to delete this database',
        create: 'Create database',
        noMysql: 'Database service (MySQL or MariaDB)',
        noPostgresql: 'Database service PostgreSQL',
        goUpgrade: 'Go to upgrade',
        goInstall: 'Go to install',
        isDelete: 'Deleted',
        permission: 'Change permissions',
        permissionForIP: 'IP',
        permissionAll: 'All of them(%)',
        localhostHelper:
            'Configuring database permissions as "localhost" for container deployment will prevent external access to the container. Please choose carefully!',
        databaseConnInfo: 'View connection info',
        rootPassword: 'Root password',
        serviceName: 'Service Name',
        serviceNameHelper: 'Access between containers in the same network.',
        backupList: 'Backup',
        loadBackup: 'Import',
        remoteAccess: 'Remote access',
        remoteHelper: 'Multiple IP comma-delimited, example: ************1, ************2',
        remoteConnHelper:
            'Remote connection to MySQL as user root may have security risks. Therefore, perform this operation with caution.',
        changePassword: 'Change password',
        changeConnHelper: 'This operation will modify the current database {0}. Do you want to continue?',
        changePasswordHelper:
            'The database has been associated with an application. Changing the password will change the database password of the application at the same time. The change takes effect after the application restarts.',

        confChange: 'Configuration',
        confNotFound:
            'The configuration file could not be found. Please upgrade the application to the latest version in the app store and try again!',

        portHelper:
            'This port is the exposed port of the container. You need to save the modification separately and restart the container!',

        loadFromRemote: 'Sync from server',
        userBind: 'Bind user',
        pgBindHelper: `This operation is used to create a new user and bind it to the target database. Currently, selecting users already existing in the database isn't supported.`,
        pgSuperUser: 'Super User',
        loadFromRemoteHelper:
            'This will synchronize the database info on the server to 1Panel. Do you want to continue?',
        passwordHelper: 'Unable to retrieve, please modify',
        remote: 'Remote',
        remoteDB: 'Remote server | Remote servers',
        createRemoteDB: 'Bind @.lower:database.remoteDB',
        unBindRemoteDB: 'Unbind @.lower:database.remoteDB',
        unBindForce: 'Force unbind',
        unBindForceHelper: 'Ignore all errors during the unbinding process to ensure the final operation is successful',
        unBindRemoteHelper:
            'Unbinding the remote database will only remove the binding relationship and will not directly delete the remote database',
        editRemoteDB: 'Edit remote server',
        localDB: 'Local database',
        address: 'Database address',
        version: 'Database version',
        userHelper: 'The root user or a database user with root privileges can access the remote database.',
        pgUserHelper: 'Database user with superuser privileges.',
        ssl: 'Use SSL',
        clientKey: 'Client private key',
        clientCert: 'Client certificate',
        caCert: 'CA certificate',
        hasCA: 'Has CA certificate',
        skipVerify: 'Ignore certificate validity check',

        formatHelper:
            'The current database character set is {0}, the character set inconsistency may cause recovery failure',
        selectFile: 'Select file',
        dropHelper: 'You can drag and drop the uploaded file here or',
        clickHelper: 'click to upload',
        supportUpType: 'Only sql, sql.gz, and tar.gz files are supported',
        zipFormat: 'tar.gz compressed package structure: test.tar.gz compressed package must contain test.sql',

        currentStatus: 'Current state',
        baseParam: 'Basic parameter',
        performanceParam: 'Performance parameter',
        runTime: 'Startup time',
        connections: 'Total connections',
        bytesSent: 'Send bytes',
        bytesReceived: 'Received bytes',
        queryPerSecond: 'Query per second',
        txPerSecond: 'Tx per second',
        connInfo: 'active/peak connections',
        connInfoHelper: 'If the value is too large, increase "max_connections".',
        threadCacheHit: 'Thread cache hit',
        threadCacheHitHelper: 'If it is too low, increase "thread_cache_size".',
        indexHit: 'Index hit',
        indexHitHelper: 'If it is too low, increase "key_buffer_size".',
        innodbIndexHit: 'Innodb index hit rate',
        innodbIndexHitHelper: 'If it is too low, increase "innodb_buffer_pool_size".',
        cacheHit: 'Querying the cache hit',
        cacheHitHelper: 'If it is too low, increase "query_cache_size".',
        tmpTableToDB: 'Temporary table to disk',
        tmpTableToDBHelper: 'If it is too large, try increasing "tmp_table_size".',
        openTables: 'Open tables',
        openTablesHelper: 'The configuration value of "table_open_cache" must be greater than or equal to this value.',
        selectFullJoin: 'Select full join',
        selectFullJoinHelper: `If the value isn't 0, check whether the index of the data table is correct.`,
        selectRangeCheck: 'The number of joins with no index',
        selectRangeCheckHelper: `If the value isn't 0, check whether the index of the data table is correct.`,
        sortMergePasses: 'Number of sorted merges',
        sortMergePassesHelper: 'If the value is too large, increase "sort_buffer_size".',
        tableLocksWaited: 'Lock table number',
        tableLocksWaitedHelper: 'If the value is too large, consider increasing your database performance.',

        performanceTuning: 'Performance tuning',
        optimizationScheme: 'Optimization scheme',
        keyBufferSizeHelper: 'Buffer size for index',
        queryCacheSizeHelper: 'Query cache. If this function is disabled, set this parameter to 0.',
        tmpTableSizeHelper: 'Temporary table cache size',
        innodbBufferPoolSizeHelper: 'Innodb buffer size',
        innodbLogBufferSizeHelper: 'Innodb log buffer size',
        sortBufferSizeHelper: '* connections, buffer size per thread sort',
        readBufferSizeHelper: '* connections, read buffer size',
        readRndBufferSizeHelper: '* connections, random read buffer size',
        joinBufferSizeHelper: '* connections, association table cache size',
        threadStackelper: '* connections, stack size per thread',
        binlogCacheSizeHelper: '* onnections, binary log cache size (multiples of 4096)',
        threadCacheSizeHelper: 'Thread pool size',
        tableOpenCacheHelper: 'Table cache',
        maxConnectionsHelper: 'Max connections',
        restart: 'Restart',

        slowLog: 'Slow logs',
        noData: 'No slow logs yet.',

        isOn: 'On',
        longQueryTime: 'threshold(s)',
        thresholdRangeHelper: 'Please enter the correct threshold (1 - 600).',

        timeout: 'Timeout(s)',
        timeoutHelper: 'Idle connection timeout period. 0 indicates that the connection is on continuously.',
        maxclients: 'Max clients',
        requirepassHelper:
            'Leave this blank to indicate that no password has been set. Changes need to be saved separately and the container restarted!',
        databases: 'Number of databases',
        maxmemory: 'Maximum memory usage',
        maxmemoryHelper: '0 indicates no restriction.',
        tcpPort: 'Current listening port.',
        uptimeInDays: 'Days in operation.',
        connectedClients: 'Number of connected clients.',
        usedMemory: 'Current memory usage of Redis.',
        usedMemoryRss: 'Memory size requested from the operating system.',
        usedMemoryPeak: 'Peak memory consumption of Redis.',
        memFragmentationRatio: 'Memory fragmentation ratio.',
        totalConnectionsReceived: 'Total number of clients connected since run.',
        totalCommandsProcessed: 'The total number of commands executed since the run.',
        instantaneousOpsPerSec: 'Number of commands executed by the server per second.',
        keyspaceHits: 'The number of times a database key was successfully found.',
        keyspaceMisses: 'Number of failed attempts to find the database key.',
        hit: 'Find the database key hit ratio.',
        latestForkUsec: 'The number of microseconds spent on the last fork() operation.',
        redisCliHelper: `"redis-cli" service isn't detected. Enable the service first.`,
        redisQuickCmd: 'Redis quick commands',
        recoverHelper: 'This will overwrite the data with [{0}]. Do you want to continue?',
        submitIt: 'Overwrite the data',

        baseConf: 'Basic',
        allConf: 'All',
        restartNow: 'Restart now',
        restartNowHelper1:
            'You need to restart the system after the configuration changes take effect. If your data needs to be persisted, perform the save operation first.',
        restartNowHelper: 'This will take effect only after the system restarts.',

        persistence: 'Persistence',
        rdbHelper1: 'second(s), insert',
        rdbHelper2: 'pieces of data',
        rdbHelper3: 'Meeting any of the conditions will trigger RDB persistence.',
        rdbInfo: 'Ensure that the value in the rule list ranges from 1 to 100000',

        containerConn: 'Container connection',
        connAddress: 'Address',
        containerConnHelper:
            'This connection address is used by applications running on the PHP execution environment/container installation.',
        remoteConn: 'External connection',
        remoteConnHelper2:
            'This connnection address can be used by applications running on non-container or external applications.',
        remoteConnHelper3:
            'The default access address is the host IP. To modify it, go to the "Default Access Address" configuration item in the panel settings page.',
        localIP: 'Local IP',
    },
    aiTools: {
        model: {
            model: 'Model',
            create: 'Add Model',
            create_helper: 'Pull "{0}"',
            ollama_doc: 'You can visit the Ollama official website to search and find more models.',
            container_conn_helper: 'Use this address for inter-container access or connection',
            ollama_sync: 'Syncing Ollama model found the following models do not exist, do you want to delete them?',
            from_remote: 'This model was not downloaded via 1Panel, no related pull logs.',
            no_logs: 'The pull logs for this model have been deleted and cannot be viewed.',
        },
        proxy: {
            proxy: 'AI Proxy Enhancement',
            proxyHelper1: 'Bind domain and enable HTTPS for enhanced transmission security',
            proxyHelper2: 'Limit IP access to prevent exposure on the public internet',
            proxyHelper3: 'Enable streaming',
            proxyHelper4: 'Once created, you can view and manage it in the website list',
            proxyHelper5:
                'After enabling, you can disable external access to the port in the App Store - Installed - Ollama - Parameters to improve security.',
            proxyHelper6: 'To disable proxy configuration, you can delete it from the website list.',
            whiteListHelper: 'Restrict access to only IPs in the whitelist',
        },
        gpu: {
            gpu: 'GPU Monitor',
            base: 'Basic Information',
            gpuHelper: 'NVIDIA-SMI or XPU-SMI command not detected on the current system. Please check and try again!',
            driverVersion: 'Driver Version',
            cudaVersion: 'CUDA Version',
            process: 'Process Information',
            type: 'Type',
            typeG: 'Graphics',
            typeC: 'Compute',
            typeCG: 'Compute + Graphics',
            processName: 'Process Name',
            processMemoryUsage: 'Memory Usage',
            temperatureHelper: 'High GPU temperature can cause GPU frequency throttling',
            performanceStateHelper: 'From P0 (maximum performance) to P12 (minimum performance)',
            busID: 'Bus ID',
            persistenceMode: 'Persistence Mode',
            enabled: 'Enabled',
            disabled: 'Disabled',
            persistenceModeHelper:
                'Persistence mode allows quicker task responses but increases standby power consumption.',
            displayActive: 'Graphics Card Initialized',
            displayActiveT: 'Yes',
            displayActiveF: 'No',
            ecc: 'Error Correction and Check Technology',
            computeMode: 'Compute Mode',
            default: 'Default',
            exclusiveProcess: 'Exclusive Process',
            exclusiveThread: 'Exclusive Thread',
            prohibited: 'Prohibited',
            defaultHelper: 'Default: Processes can execute concurrently',
            exclusiveProcessHelper:
                'Exclusive Process: Only one CUDA context can use the GPU, but can be shared by multiple threads',
            exclusiveThreadHelper: 'Exclusive Thread: Only one thread in a CUDA context can use the GPU',
            prohibitedHelper: 'Prohibited: Processes are not allowed to execute simultaneously',
            migModeHelper: 'Used to create MIG instances for physical isolation of the GPU at the user level.',
            migModeNA: 'Not Supported',
        },
        mcp: {
            server: 'MCP Server',
            create: 'Add MCP Server',
            edit: 'Edit MCP Server',
            commandHelper: 'For example: npx -y {0}',
            baseUrl: 'External Access Path',
            baseUrlHelper: 'For example: http://***********:8000',
            ssePath: 'SSE Path',
            ssePathHelper: 'For example: /sse, note not to duplicate with other servers',
            environment: 'Environment Variables',
            envKey: 'Variable Name',
            envValue: 'Variable Value',
            externalUrl: 'External Connection Address',
            operatorHelper: 'Will perform {1} operation on {0}, continue?',
            domain: 'Default Access Address',
            domainHelper: 'For example: *********** or example.com',
            bindDomain: 'Bind Website',
            commandPlaceHolder: 'Currently only supports npx and binary startup commands',
            importMcpJson: 'Import MCP Server Configuration',
            importMcpJsonError: 'mcpServers structure is incorrect',
            bindDomainHelper:
                'After binding the website, it will modify the access address of all installed MCP Servers and close external access to the ports',
            outputTransport: 'Output Type',
            streamableHttpPath: 'Streaming Path',
            streamableHttpPathHelper: 'For example: /mcp, note that it should not overlap with other Servers',
        },
    },
    container: {
        create: 'Create',
        createByCommand: 'Create by command',
        commandInput: 'Command input',
        commandRule: 'Please enter the correct docker run container creation command!',
        commandHelper: 'This command will be executed on the server to create the container. Do you want to continue?',
        edit: 'Edit container',
        updateHelper1: 'Detected that this container comes from the app store. Please note the following two points:',
        updateHelper2:
            '1. The current modifications will not be synchronized to the installed applications in the app store.',
        updateHelper3:
            '2. If you modify the application on the installed page, the currently edited content will become invalid.',
        updateHelper4:
            'Editing the container requires rebuilding, and any non-persistent data will be lost. Do you want to continue?',
        containerList: 'Container list',
        operatorHelper: '{0} will be performed on the following container, Do you want to continue?',
        operatorAppHelper:
            'The "{0}" operation will be performed on the following container(s) and may affect the running services. Do you want to continue?',
        start: 'Start',
        stop: 'Stop',
        restart: 'Restart',
        kill: 'Kill',
        pause: 'Pause',
        unpause: 'Resume',
        rename: 'Rename',
        remove: 'Remove',
        removeAll: 'Remove all',
        containerPrune: 'Prune',
        containerPruneHelper1: 'This will delete all containers that are in stopped state.',
        containerPruneHelper2:
            'If the containers are from the app store, you need to go to "App Store -> Installed" and click the "Rebuild" button to reinstall them after performing the cleanup.',
        containerPruneHelper3: `This operation can't be undone. Do you want to continue?`,
        imagePrune: 'Prune',
        imagePruneSome: 'Clean unlabeled',
        imagePruneSomeEmpty: 'No images with the "none" tag can be cleaned.',
        imagePruneSomeHelper: 'Clean the images with the tag "none" that are not used by any containers.',
        imagePruneAll: 'Clean unused',
        imagePruneAllEmpty: 'No unused images can be cleaned.',
        imagePruneAllHelper: 'Clean the images that are not used by any containers.',
        networkPrune: 'Prune',
        networkPruneHelper: 'This will remove all unused networks. Do you want to continue?',
        volumePrune: 'Prune',
        volumePruneHelper: 'This will remove all unused local volumes. Do you want to continue?',
        cleanSuccess: 'The operation is successful, the number of this cleanup: {0}!',
        cleanSuccessWithSpace:
            'The operation is successful. The number of disks cleaned this time is {0}. The disk space freed is {1}!',
        unExposedPort: 'The current port mapping address is 127.0.0.1, which cannot enable external access.',
        upTime: 'Uptime',
        fetch: 'Fetch',
        lines: 'Lines',
        linesHelper: 'Please enter the correct number of logs to retrieve!',
        lastDay: 'Last day',
        last4Hour: 'Last 4 hours',
        lastHour: 'Last hour',
        last10Min: 'Last 10 minutes',
        cleanLog: 'Clean log',
        downLogHelper1: 'This will download all logs from container {0}. Do you want to continue?',
        downLogHelper2: 'This will download the recent {0} logs from container {0}. Do you want to continue?',
        cleanLogHelper: `This will require restarting the container and can't be undone. Do you want to continue?`,
        newName: 'New name',
        workingDir: 'Working Dir',
        source: 'Resource usage',
        cpuUsage: 'CPU usage',
        cpuTotal: 'CPU total',
        core: 'Core',
        memUsage: 'Memory usage',
        memTotal: 'Memory limit',
        memCache: 'Memory cache',
        ip: 'IP address',
        cpuShare: 'CPU shares',
        cpuShareHelper:
            'Container engine uses a base value of 1024 for CPU shares. You can increase it to give the container more CPU time.',
        inputIpv4: 'Example: ***********',
        inputIpv6: 'Example: 2001:0db8:85a3:0000:0000:8a2e:0370:7334',

        containerFromAppHelper:
            'Detected that this container originates from the app store. App operations may cause current edits to be invalidated.',
        containerFromAppHelper1:
            'Click the [Param] button in the installed applications list to enter the editing page and modify the container name.',
        command: 'Command',
        console: 'Container interaction',
        tty: 'Allocate a pseudo-TTY (-t)',
        openStdin: 'Keep STDIN open even if not attached (-i)',
        custom: 'Custom',
        emptyUser: 'When empty, you will log in as  default',
        privileged: 'Privileged',
        privilegedHelper:
            'Allow the container to perform certain privileged operations on the host, which may increase container risks. Use with caution!',
        editComposeHelper:
            'Note: The environment variables set will be written to the 1panel.env file by default.\nIf you want to use these parameters in the container, you also need to manually add an env_file reference in the compose file.',

        upgradeHelper: 'Repository Name/Image Name: Image Version',
        upgradeWarning2:
            'The upgrade operation requires rebuilding the container, any unpersisted data will be lost. Do you wish to continue?',
        oldImage: 'Current image',
        sameImageContainer: 'Same-image containers',
        sameImageHelper: 'Containers using the same image can be batch upgraded after selection',
        targetImage: 'Target image',
        imageLoadErr: 'No image name detected for the container',
        appHelper: 'The container comes from the app store, and upgrading may make the service unavailable.',

        resource: 'Resource',
        input: 'Manually input',
        forcePull: 'Always pull image ',
        forcePullHelper: 'This will ignore existing images on the server and pull the latest image from the registry.',
        server: 'Host',
        serverExample: '80, 80-88, ip:80 or ip:80-88',
        containerExample: '80 or 80-88',
        exposePort: 'Expose port',
        exposeAll: 'Expose all',
        cmdHelper: 'Example: nginx -g "daemon off;"',
        entrypointHelper: 'Example: docker-entrypoint.sh',
        autoRemove: 'Auto remove',
        cpuQuota: 'Number of CPU cores',
        memoryLimit: 'Memory',
        limitHelper: `If set to 0, it means that there is no limitation. The maximum value is {0}`,
        macAddr: 'MAC Address',
        mount: 'Mount',
        volumeOption: 'Volume',
        hostOption: 'Host',
        serverPath: 'Server path',
        containerDir: 'Container path',
        volumeHelper: 'Ensure that the content of the storage volume is correct',
        modeRW: 'RW',
        modeR: 'R',
        mode: 'Mode',
        env: 'Environments',
        restartPolicy: 'Restart policy',
        always: 'always',
        unlessStopped: 'unless-stopped',
        onFailure: 'on-failure (five times by default)',
        no: 'never',

        refreshTime: 'Refresh interval',
        cache: 'Cache',

        image: 'Image | Images',
        imagePull: 'Pull',
        imagePush: 'Push',
        imageDelete: 'Image delete',
        imageTagDeleteHelper: 'Remove other tags associated with this image ID',
        repoName: 'Container registry',
        imageName: 'Image name',
        pull: 'Pull',
        path: 'Path',
        importImage: 'Import',
        build: 'Build',
        imageBuild: 'Build',
        pathSelect: 'Path',
        label: 'Label',
        imageTag: 'Image tag',
        push: 'Push',
        fileName: 'Filename',
        export: 'Export',
        exportImage: 'Image export',
        size: 'Size',
        tag: 'Tags',
        tagHelper: 'One per line. For example,\nkey1=value1\nkey2=value2',
        imageNameHelper: 'Image name and Tag, for example: nginx:latest',
        cleanBuildCache: 'Clean build cache',
        delBuildCacheHelper: `This will delete all cached artifacts that are generated during builds and can't be undone. Do you want to continue?`,
        urlWarning: 'The URL prefix does not need to include http:// or https://. Please modify.',

        network: 'Network | Networks',
        networkHelper:
            'This may cause some applications and runtime environments to not work properly. Do you want to continue?',
        createNetwork: 'Create',
        networkName: 'Name',
        driver: 'Driver',
        option: 'Option',
        attachable: 'Attachable',
        subnet: 'Subnet',
        scope: 'IP scope',
        gateway: 'Gateway',
        auxAddress: 'Exclude IP',

        volume: 'Volume | Volumes',
        volumeDir: 'Volume directory',
        nfsEnable: 'Enable NFS storage',
        nfsAddress: 'Address',
        mountpoint: 'Mountpoint',
        mountpointNFSHelper: 'e.g. /nfs, /nfs-share',
        options: 'Options',
        createVolume: 'Create',

        repo: 'Container registry | Container registries',
        createRepo: 'Add',
        httpRepoHelper: 'Operating an HTTP-type repository requires restarting the Docker service.',
        httpRepo: 'Choosing HTTP protocol requires restarting the Docker service to add it into insecure registries.',
        delInsecure: 'Deletion of credit',
        delInsecureHelper:
            'This will restart Docker service to remove it from insecure registries. Do you want to continue?',
        downloadUrl: 'Server',
        imageRepo: 'Image repo',
        repoHelper: 'Does it include a mirror repository/organization/project?',
        auth: 'Require authentication',
        mirrorHelper:
            'If there are multiple mirrors, newlines must be displayed, for example:\nhttp://xxxxxx.m.daocloud.io \nhttps://xxxxxx.mirror.aliyuncs.com',
        registrieHelper:
            'If multiple private repositories exist, newlines must be displayed, for example:\n************1:8081 \n************2:8081',

        compose: 'Compose | Composes',
        fromChangeHelper: 'Switching the source will clean the current edited content. Do you want to continue?',
        composePathHelper: 'Configuration file save path: {0}',
        composeHelper:
            'The composition created through 1Panel editor or template will be saved in the {0}/docker/compose directory.',
        deleteFile: 'Delete file',
        deleteComposeHelper:
            'Delete all files related to container compose, including configuration files and persistent files. Please proceed with caution!',
        deleteCompose: '" Delete this composition.',
        createCompose: 'Create',
        composeDirectory: 'Compose directory',
        template: 'Template',
        composeTemplate: 'Compose template | Compose templates',
        createComposeTemplate: 'Create',
        content: 'Content',
        contentEmpty: 'Compose content cannot be empty, please enter and try again!',
        containerNumber: 'Container number',
        containerStatus: 'Container status',
        exited: 'Exited',
        running: 'Running ( {0} / {1} )',
        composeDetailHelper:
            'The compose is created external to 1Panel. The start and stop operations are not supported.',
        composeOperatorHelper: '{1} operation will be performed on {0}. Do you want to continue?',
        composeDownHelper:
            'This will stop and remove all containers and networks under the {0} compose. Do you want to continue?',

        setting: 'Setting | Settings',
        goSetting: 'Go to edit',
        operatorStatusHelper: 'This will "{0}" Docker service. Do you want to continue?',
        dockerStatus: 'Docker Service',
        daemonJsonPathHelper: 'Ensure that the configuration path is the same as that specified in docker.service.',
        mirrors: 'Registry mirrors',
        mirrorsHelper: '',
        mirrorsHelper2: 'For details, see the official documents. ',
        registries: 'Insecure registries',
        ipv6Helper:
            'When enabling IPv6, you need to add an IPv6 container network. Refer to the official documentation for specific configuration steps.',
        ipv6CidrHelper: 'IPv6 address pool range for containers',
        ipv6TablesHelper: 'Automatic configuration of Docker IPv6 for iptables rules.',
        experimentalHelper:
            'Enabling ip6tables requires this configuration to be turned on; otherwise, ip6tables will be ignored',
        cutLog: 'Log option',
        cutLogHelper1: 'The current configuration will only affect newly created containers.',
        cutLogHelper2: 'Existing containers need to be recreated for the configuration to take effect.',
        cutLogHelper3:
            'Please note that recreating containers may result in data loss. If your containers contain important data, make sure to backup before performing the rebuilding operation.',
        maxSize: 'Max size',
        maxFile: 'Max file',
        liveHelper:
            'By default, when the Docker daemon terminates, it shuts down running containers. You can configure the daemon so that containers remain running if the daemon becomes unavailable. This functionality is called live restore. The live restore option helps reduce container downtime due to daemon crashes, planned outages, or upgrades.',
        liveWithSwarmHelper: 'live-restore daemon configuration is incompatible with swarm mode.',
        iptablesDisable: 'Close iptables',
        iptablesHelper1: 'Automatic configuration of iptables rules for Docker.',
        iptablesHelper2:
            'Disabling iptables will result in the containers being unable to communicate with external networks.',
        daemonJsonPath: 'Conf Path',
        serviceUnavailable: `Docker service isn't started at present.`,
        startIn: ' to start',
        sockPath: 'Unix domain socket',
        sockPathHelper: 'Communication channel between Docker daemon and the client.',
        sockPathHelper1: 'Default path: /var/run/docker-x.sock',
        sockPathMsg:
            'Saving the Socket Path setting may result in Docker service being unavailable. Do you want to continue?',
        sockPathErr: 'Please select or enter the correct Docker sock file path',
        related: 'Related',
        includeAppstore: 'Show containers from the app store',
        excludeAppstore: 'Hide App Store Container',

        cleanDockerDiskZone: 'Clean up disk space used by Docker',
        cleanImagesHelper: '( Clean up all images that are not used by any containers )',
        cleanContainersHelper: '( Clean up all stopped containers )',
        cleanVolumesHelper: '( Clean up all unused local volumes )',

        makeImage: 'Create image',
        newImageName: 'New image name',
        commitMessage: 'Commit message',
        author: 'Author',
        ifPause: 'Pause Container During Creation',
        ifMakeImageWithContainer: 'Create New Image from This Container?',
    },
    cronjob: {
        create: 'Create cron job',
        edit: 'Edit cron job',
        errImport: 'File content exception:',
        errImportFormat: 'The scheduled task data or format is abnormal. Please check and try again!',
        importHelper:
            'Duplicate scheduled tasks will be automatically skipped during import. Tasks will be set to [Disabled] status by default, and set to [Pending Edit] status when data association is abnormal.',
        changeStatus: 'Change status',
        disableMsg: 'This will stop the scheduled task from automatically executing. Do you want to continue?',
        enableMsg: 'This will allow the scheduled task to automatically execute. Do you want to continue?',
        taskType: 'Type',
        nextTime: 'Next 5 executions',
        record: 'Records',
        viewRecords: 'View records',
        shell: 'Shell',
        log: 'Backup logs',
        logHelper: 'Backup system log',
        ogHelper1: '1.1Panel System log ',
        logHelper2: '2. SSH login log of the server ',
        logHelper3: '3. All site logs ',
        containerCheckBox: 'In container (no need to enter the container command)',
        containerName: 'Container name',
        ntp: 'Time synchronization',
        ntp_helper: 'You can configure the NTP server on the Quick Setup page of the Toolbox.',
        app: 'Backup app',
        website: 'Backup website',
        rulesHelper:
            'When there are multiple compression exclusion rules, they need to be displayed with line breaks. For example,\n*.log \n*.sql',
        lastRecordTime: 'Last execution time',
        all: 'All',
        failedRecord: 'Failure records',
        successRecord: 'Successful records',
        database: 'Backup database',
        missBackupAccount: 'The backup account could not be found',
        syncDate: 'Synchronization time ',
        clean: 'Cache clean',
        curl: 'Access URL',
        taskName: 'Name',
        cronSpec: 'Trigger cycle',
        cronSpecDoc:
            'Custom execution cycles only support the [minute hour day month week] format, e.g., 0 0 * * *. For details, please refer to the official documentation.',
        cronSpecHelper: 'Enter the correct execution period',
        cleanHelper:
            'This operation records all job execution records, backup files, and log files. Do you want to continue?',
        backupContent: 'Backup content',
        directory: 'Backup directory',
        sourceDir: 'Backup directory',
        snapshot: 'System snapshot',
        allOptionHelper: `The current task plan is to back up all [{0}]. Direct download isn't supported at the moment. You can check the backup list of [{0}] menu.`,
        exclusionRules: 'Exclusive rule',
        exclusionRulesHelper: 'The exclusion rules will apply to all compression operations of this backup.',
        default_download_path: 'Default download link',
        saveLocal: 'Retain local backups (the same as the number of cloud storage copies)',
        url: 'URL Address',
        targetHelper: 'Backup accounts are maintained in panel settings.',
        withImageHelper: 'Backup app store images, but this will increase the snapshot file size.',
        ignoreApp: 'Exclude apps',
        withImage: 'Backup all app images',
        retainCopies: 'Retain records',
        retryTimes: 'Retry Attempts',
        timeout: 'Timeout',
        ignoreErr: 'Ignore errors',
        ignoreErrHelper: 'Ignore errors during backup to ensure all backup tasks complete',
        retryTimesHelper: '0 means no retry after failure',
        retainCopiesHelper: 'Number of copies to retain for execution records and logs',
        retainCopiesHelper1: 'Number of copies to retain for backup files',
        retainCopiesUnit: ' copies (View)',
        cronSpecRule: 'The execution period format in line {0} is incorrect. Please check and try again!',
        cronSpecRule2: 'Execution period format is incorrect, please check and try again!',
        perMonthHelper: 'Execute on the {0} day of every month at {1}:{2}',
        perWeekHelper: 'Execute every week on {0} at {1}:{2}',
        perDayHelper: 'Execute every day at {0}:{1}',
        perHourHelper: 'Execute every hour at {0} minutes',
        perNDayHelper: 'Execute every {0} days at {1}:{2}',
        perNHourHelper: 'Execute every {0} hours at {1}',
        perNMinuteHelper: 'Execute every {0} minutes',
        perNSecondHelper: 'Execute every {0} seconds',
        perMonth: 'Every month',
        perWeek: 'Every week',
        perHour: 'Every hour',
        perNDay: 'Every N day(s)',
        perDay: 'Every day',
        perNHour: 'Every N hour(s)',
        perNMinute: 'Every N minute(s)',
        perNSecond: 'Every N second(s)',
        day: 'day(s)',
        monday: 'Monday',
        tuesday: 'Tuesday',
        wednesday: 'Wednesday',
        thursday: 'Thursday',
        friday: 'Friday',
        saturday: 'Saturday',
        sunday: 'Sunday',
        shellContent: 'Script',
        executor: 'Executor',
        errRecord: 'Incorrect logging',
        errHandle: 'Cronjob execution failure',
        noRecord: 'Trigger the Cron Job, and you will see the records here.',
        cleanData: 'Clean data',
        cleanRemoteData: 'Delete remote data',
        cleanDataHelper: 'Delete the backup file generated during this task.',
        noLogs: 'No task output yet...',
        errPath: 'Backup path [{0}] error, cannot download!',
        cutWebsiteLog: 'Website log rotation',
        cutWebsiteLogHelper: 'The rotated log files will be backed up to the backup directory of 1Panel.',

        requestExpirationTime: 'Upload request expiration time(Hours)',
        unitHours: 'Unit: Hours',
        alertTitle: 'Planned Task - {0} 「{1}」 Task Failure Alert',
        library: {
            script: 'Script',
            isInteractive: 'Interactive',
            interactive: 'Interactive script',
            interactiveHelper: 'Requires user input during execution and cannot be used in scheduled tasks.',
            library: 'Script Library',
            create: 'Add Script',
            edit: 'Edit Script',
            groupHelper:
                'Set different groups based on script characteristics, which allows for faster script filtering operations.',
            handleHelper: 'Execute script {1} on {0}, continue?',
            noSuchApp: 'The {0} service was not detected. Please install it quickly using the script library first!',
            syncHelper: 'About to sync system script library. This operation only affects system scripts. Continue?',
        },
    },
    monitor: {
        globalFilter: 'Global Filter',
        enableMonitor: 'Enable',
        storeDays: 'Expiration days',
        defaultNetwork: 'Default Network Adapter',
        defaultNetworkHelper: 'Network adapter option displayed in the default monitoring and overview interface',
        cleanMonitor: 'Clean monitoring records',

        avgLoad: 'Load average',
        loadDetail: 'Load detail',
        resourceUsage: 'Utilization',
        networkCard: 'Network interface',
        read: 'Read',
        write: 'Write',
        readWriteCount: 'I/O operations',
        readWriteTime: 'I/O latency',
        today: 'Today',
        yesterday: 'Yesterday',
        lastNDay: 'Last {0} day(s)',
        lastNMonth: 'Last {0} months',
        lastHalfYear: 'Last half year',
        memory: 'Memory',
        cache: 'Cache',
        disk: 'Disk',
        network: 'Network',
        up: 'Up',
        down: 'Down',
        interval: 'Interval(minute)',

        gpuUtil: 'GPU Utilization',
        temperature: 'Temperature',
        performanceState: 'Performance state',
        powerUsage: 'Power usage',
        memoryUsage: 'Memory usage',
        fanSpeed: 'Fan speed',
    },
    terminal: {
        local: 'Local',
        localHelper: 'The `local` name is used only for system local identification',
        connLocalErr: 'Unable to automatically authenticate, please fill in the local server login information.',
        testConn: 'Test connection',
        saveAndConn: 'Save and connect',
        connTestOk: 'Connection information available',
        connTestFailed: 'Connection unavailable, please check connection information.',
        host: 'Host | Hosts',
        createConn: 'New connection',
        noHost: 'No host',
        groupChange: 'Change group',
        expand: 'Expand all',
        fold: 'All contract',
        batchInput: 'Batch processing',
        quickCommand: 'Quick command | Quick commands',
        quickCommandHelper: 'You can use the quick commands at the bottom of the "Terminals -> Terminals".',
        groupDeleteHelper:
            'After the group is removed, all connections in the group will be migrated to the default group. Do you want to continue?',
        command: 'Command',
        quickCmd: 'Quick command',
        addHost: 'Add',
        localhost: 'Localhost',
        ip: 'Address',
        authMode: 'Authentication',
        passwordMode: 'Password',
        rememberPassword: 'Remember authentication information',
        keyMode: 'PrivateKey',
        key: 'Private key',
        keyPassword: 'Private key password',
        emptyTerminal: 'No terminal is currently connected.',
        lineHeight: 'Line Height',
        letterSpacing: 'Letter Spacing',
        fontSize: 'Font Size',
        cursorBlink: 'Cursor Blink',
        cursorStyle: 'Cursor Style',
        cursorUnderline: 'Underline',
        cursorBlock: 'Block',
        cursorBar: 'Bar',
        scrollback: 'Scrollback',
        scrollSensitivity: 'Scroll Sensitivity',
        saveHelper: 'Are you sure you want to save the current terminal configuration?',
    },
    toolbox: {
        common: {
            toolboxHelper: 'For some installation and usage issues, please refer to',
        },
        swap: {
            swap: 'Swap Partition',
            swapHelper1:
                'The size of the swap should be 1 to 2 times the physical memory, adjustable based on specific requirements;',
            swapHelper2:
                'Before creating a swap file, ensure that the system disk has sufficient available space, as the swap file size will occupy the corresponding disk space;',
            swapHelper3:
                'Swap can help alleviate memory pressure, but it is only an alternative. Excessive reliance on swap may lead to a decrease in system performance. It is recommended to prioritize increasing memory or optimizing application memory usage;',
            swapHelper4: 'It is advisable to regularly monitor the usage of swap to ensure normal system operation.',
            swapDeleteHelper:
                'This operation will remove the Swap partition {0}. For system security reasons, the corresponding file will not be automatically deleted. If deletion is required, please proceed manually!',
            saveHelper: 'Please save the current settings first!',
            saveSwap:
                'Saving the current configuration will adjust the Swap partition {0} size to {1}. Do you want to continue?',
            swapMin: 'The minimum partition size is 40 KB. Please modify and try again!',
            swapMax: 'The maximum value for partition size is {0}. Please modify and try again!',
            swapOff: 'The minimum partition size is 40 KB. Setting it to 0 will disable the Swap partition.',
        },
        device: {
            dnsHelper: 'DNS server',
            dnsAlert:
                'Attention! Modifying the configuration of /etc/resolv.conf file will restore the file to its default values after system restart.',
            dnsHelper1:
                'When there are multiple DNS entries, they should be displayed on new lines. e.g.\n114.114.114.114\n8.8.8.8',
            hostsHelper: 'Hostname resolution',
            hosts: 'Domain',
            hostAlert: 'Hidden commented records, please click the All configuration button to view or set',
            toolbox: 'Quick settings',
            hostname: 'Hostname',
            passwd: 'System password',
            passwdHelper: 'Input characters cannot include $ and &',
            timeZone: 'System time zone',
            localTime: 'Server time',
            timeZoneChangeHelper: 'Modifying the system time zone requires restarting the service. Continue?',
            timeZoneHelper: `If you don't install "timedatectl" command, you may not change the time zone. Because system uses that command to change time zone.`,
            timeZoneCN: 'Beijing',
            timeZoneAM: 'Los Angeles',
            timeZoneNY: 'New York',
            ntpALi: 'Alibaba',
            ntpGoogle: 'Google',
            syncSite: 'NTP server',
            hostnameHelper: `Hostname modification depends on the "hostnamectl" command. If the command isn't installed, the modification may fail.`,
            userHelper: `The username depends on the "whoami" command for retrieval. If the command isn't installed, retrieval may fail.`,
            passwordHelper: `Password modification depends on the "chpasswd" command. If the command isn't installed, the modification may fail.`,
            hostHelper:
                'There is an empty value in the provided content. Please check and try again after modification!',
            dnsCheck: 'Test Availability',
            dnsOK: 'DNS configuration information is available!',
            dnsTestFailed: `DNS configuration information isn't available.`,
        },
        fail2ban: {
            sshPort: 'Listen to SSH port',
            sshPortHelper: 'Current Fail2ban listens to the SSH connection port of the host',
            unActive: `The Fail2ban service isn't enabled at present.`,
            operation: 'You will perform operation "{0}" on Fail2ban service. Do you want to continue?',
            fail2banChange: 'Fail2ban Configuration Modification',
            ignoreHelper: 'The IP list in the allowlist will be ignored for blocking. Do you want to continue?',
            bannedHelper: 'The IP list in the blocklist will be blocked by the server. Do you want to continue?',
            maxRetry: 'Maximum retry attempts',
            banTime: 'Ban time',
            banTimeHelper: 'Default ban time is 10 minutes, -1 indicates permanent ban',
            banTimeRule: 'Please enter a valid ban time or -1',
            banAllTime: 'Permanent ban',
            findTime: 'Discovery period',
            banAction: 'Ban action',
            banActionOption: 'Ban specified IP addresses using {0}',
            allPorts: ' (All Ports)',
            ignoreIP: 'IP allowlist',
            bannedIP: 'IP blocklist',
            logPath: 'Log path',
            logPathHelper: 'Default is /var/log/secure or /var/log/auth.log',
        },
        ftp: {
            ftp: 'FTP account | FTP accounts',
            notStart: 'FTP service is currently not running, please start it first!',
            operation: 'This will perform "{0}" operation on FTP service. Do you want to continue?',
            noPasswdMsg: 'Can not get the current FTP account password, please set the password and try again! ',
            enableHelper:
                'Enabling the selected FTP account will restore its access permissions. Do you want to continue?',
            disableHelper:
                'Disabling the selected FTP account will revoke its access permissions. Do you want to continue?',
            syncHelper: 'Sync FTP account data between server and database. Do you want to continue?',
            dirSystem:
                'This directory is system-reserved. Modification may cause system crash, please modify and try again!',
            dirHelper: 'Enabling FTP requires directory permission changes - please choose carefully',
            dirMsg: 'Enabling FTP will modify permissions for the entire {0} directory. Continue?',
        },
        clam: {
            clam: 'Virus scan',
            cron: 'Scheduled scan',
            cronHelper: 'Professional version supports scheduled scan feature',
            specErr: 'Execution schedule format error, please check and retry!',
            disableMsg:
                'Stopping scheduled execution will prevent this scan task from running automatically. Do you want to continue?',
            enableMsg:
                'Enabling scheduled execution will allow this scan task to run automatically at regular intervals. Do you want to continue?',
            showFresh: 'Show signature updater service',
            hideFresh: 'Hide signature updater service',
            clamHelper:
                'The minimum recommended configuration for ClamAV is: 3 GiB of RAM or more, single-core CPU with 2.0 GHz or higher, and at least 5 GiB of available hard disk space.',
            notStart: 'ClamAV service is currently not running, please start it first!',
            removeRecord: 'Delete peport files',
            noRecords: 'Click the "Trigger" button to start the scan and you will see records here.',
            removeResultHelper: 'Delete report files generated during task execution to free up storage space.',
            removeInfected: 'Delete virus files',
            removeInfectedHelper:
                'Delete virus files detected during the task to ensure server security and normal operation.',
            clamCreate: 'Create scan rule',
            infectedStrategy: 'Infected strategy',
            removeHelper: 'Delete virus files, choose carefully!',
            move: 'Move',
            moveHelper: 'Move virus files to specified directory',
            copyHelper: 'Copy virus files to specified directory',
            none: 'Do nothing',
            noneHelper: 'Take no action on virus files',
            scanDir: 'Scan directory',
            infectedDir: 'Infected directory',
            scanDate: 'Scan Date',
            scanResult: 'Scan logs tail',
            tail: 'Lines',
            infectedFiles: 'Infected files',
            log: 'Details',
            clamConf: 'Clam AV daemon',
            clamLog: '@:toolbox.clam.clamConf logs',
            freshClam: 'FreshClam',
            freshClamLog: '@:toolbox.clam.freshClam logs',
            alertHelper: 'Professional version supports scheduled scan and SMS alert',
            alertTitle: 'Virus scan task 「{0}」 detected infected file alert',
        },
    },
    logs: {
        core: 'Panel Service',
        agent: 'Node Monitoring',
        panelLog: 'Panel logs',
        operation: 'Operation logs',
        login: 'Login logs',
        loginIP: 'Login IP',
        loginAddress: 'Login address',
        loginAgent: 'Login agent',
        loginStatus: 'Status',
        system: 'System logs',
        deleteLogs: 'Clean logs',
        resource: 'Resource',
        detail: {
            ai: 'AI',
            groups: 'Group',
            hosts: 'Host',
            apps: 'App',
            websites: 'Website',
            containers: 'Container',
            files: 'File',
            runtimes: 'Runtime',
            process: 'Process',
            toolbox: 'Toolbox',
            backups: 'Backup / Restore',
            tampers: 'Tamper',
            xsetting: 'Interface Settings',
            logs: 'Log',
            settings: 'Setting',
            cronjobs: 'Cronjob',
            databases: 'Database',
            waf: 'WAF',
            licenses: 'License',
            nodes: 'Node',
            commands: 'Quick Commands',
        },
        websiteLog: 'Website logs',
        runLog: 'Run logs',
        errLog: 'Error logs',
        task: 'Task Log',
        taskName: 'Task Name',
        taskRunning: 'Running',
    },
    file: {
        fileDirNum: '{0} directories, {1} files,',
        currentDir: 'Directory',
        dir: 'Folder',
        fileName: 'File name',
        search: 'Search',
        mode: 'Permissions',
        editPermissions: 'Edit @.lower:file.mode',
        owner: 'Owner',
        file: 'File',
        remoteFile: 'Download from remote',
        share: 'Share',
        sync: 'Data Synchronization',
        size: 'Size',
        updateTime: 'Modified',
        rename: 'Rename',
        role: 'Permissions',
        info: 'View attributes',
        linkFile: 'Soft link',
        shareList: 'Share list',
        zip: 'Compressed',
        group: 'Group',
        path: 'Path',
        public: 'Others',
        setRole: 'Set permissions',
        link: 'File link',
        rRole: 'Read',
        wRole: 'Write',
        xRole: 'Executable',
        name: 'Name',
        compress: 'Compress',
        deCompress: 'Decompress',
        compressType: 'Compress format',
        compressDst: 'Compress path',
        replace: 'Overwrite existing files',
        compressSuccess: 'Compressed successfully',
        deCompressSuccess: 'Decompress succeeded',
        deCompressDst: 'Decompress path',
        linkType: 'Link type',
        softLink: 'Soft link',
        hardLink: 'Hard link',
        linkPath: 'Link path',
        selectFile: 'Select file',
        downloadUrl: 'Remote URL',
        downloadStart: 'Download started',
        moveSuccess: 'Successfully moved',
        copySuccess: 'Successfully copied',
        move: 'Move',
        calculate: 'Calculate',
        canNotDeCompress: 'Cannot decompress this file',
        uploadSuccess: 'Successfully upload',
        downloadProcess: 'Download progress',
        downloading: 'Downloading...',
        infoDetail: 'File properties',
        root: 'Root directory',
        list: 'File list',
        sub: 'Include subdirectories',
        downloadSuccess: 'Successfully downloaded',
        theme: 'Theme',
        language: 'Language',
        eol: 'End of line',
        copyDir: 'Copy',
        paste: 'Paste',
        changeOwner: 'Modify user and user group',
        containSub: 'Apply the permission change recursively',
        ownerHelper:
            'The default user of the PHP operating environment: the user group is 1000:1000, it is normal that the users inside and outside the container show inconsistencies',
        searchHelper: 'Support wildcards such as *',
        uploadFailed: '[{0}] File upload file',
        fileUploadStart: 'Uploading [{0}]....',
        currentSelect: 'Current select: ',
        unsupportedType: 'Unsupported file type',
        deleteHelper:
            'Are you sure you want to delete the following files? By default, it will enter the recycle bin after deletion',
        fileHelper: `Note:\n1. Search results can't be sorted.\n2. Folders can't be sorted by size.`,
        forceDeleteHelper: 'Permanently delete the file (without entering the recycle bin, delete it directly)',
        recycleBin: 'Recycle bin',
        sourcePath: 'Original path',
        deleteTime: 'Delete time',
        confirmReduce: 'Are you sure you want to restore the following files?',
        reduceSuccess: 'Restore successful',
        reduce: 'Reduction',
        reduceHelper:
            'If a file or directory with the same name exists in the original path, it will be overwritten. Do you want to continue?',
        clearRecycleBin: 'Clean',
        clearRecycleBinHelper: 'Do you want to clean the recycle bin?',
        favorite: 'Favorites',
        removeFavorite: 'Remove from favorites?',
        addFavorite: 'Add/Remove to Favorites',
        clearList: 'Clean list',
        deleteRecycleHelper: 'Are you sure you want to permanently delete the following files?',
        typeErrOrEmpty: '[{0}] file type is wrong or empty folder',
        dropHelper: 'Drag the files you want to upload here',
        fileRecycleBin: 'Enable recycle bin',
        fileRecycleBinMsg: '{0} recycle bin',
        wordWrap: 'Automatically wrap',
        deleteHelper2:
            'Are you sure you want to delete the selected file? The deletion operation cannot be rolled back',
        ignoreCertificate: 'Allow insecure server connections',
        ignoreCertificateHelper:
            'Allowing inscure server connections may lead to data leak or tampering. Use this option only when trusting the download source.',
        uploadOverLimit: 'The number of files exceeds 1000! Please compress and upload',
        clashDitNotSupport: 'File names are prohibited from containing .1panel_clash',
        clashDeleteAlert: `The "Recycle Bin" folder can't be deleted`,
        clashOpenAlert: 'Please click the "Recycle Bin" button to open the recycle bin directory',
        right: 'Forward',
        back: 'Back',
        top: 'Go Back',
        up: 'Go back',
        openWithVscode: 'Open with VS Code',
        vscodeHelper: 'Please make sure that VS Code is installed locally and the SSH Remote plugin is configured',
        saveContentAndClose: 'The file has been modified, do you want to save and close it?',
        saveAndOpenNewFile: 'The file has been modified, do you want to save and open the new file?',
        noEdit: 'The file has not been modified, no need to do this!',
        noNameFolder: 'Untitled folder',
        noNameFile: 'Untitled file',
        minimap: 'Code mini map',
        fileCanNotRead: 'File can not read',
        panelInstallDir: `1Panel installation directory can't be deleted`,
        wgetTask: 'Download Task',
        existFileTitle: 'Same name file prompt',
        existFileHelper: 'The uploaded file contains a file with the same name, do you want to overwrite it?',
        existFileSize: 'File size (new -> old)',
        existFileDirHelper: 'The selected file/folder has a duplicate name. Please proceed with caution! \n',
        coverDirHelper: 'The selected folders to be replaced will be copied to the destination path!',
        noSuchFile: 'The file or directory was not found. Please check and try again.',
        setting: 'Setting',
        showHide: 'Show hidden files',
        noShowHide: 'Don’t show hidden files',
        cancelUpload: 'Cancel Upload',
        cancelUploadHelper: 'Whether to cancel the upload, after cancellation the upload list will be cleared.',
    },
    ssh: {
        autoStart: 'Auto start',
        enable: 'Enable Autostart',
        disable: 'Disable Autostart',
        sshAlert:
            'The list data is sorted based on login date. Changing time zone or performing other operations may cause deviations in the date of login logs.',
        sshAlert2:
            'You can use "Fail2ban" in the "Toolbox" to block IP addresses that attempt brute force attacks, and this will enhance the security of the host.',
        sshOperate: 'Operation "{0}" on the SSH service will be performed. Do you want to continue?',
        sshChange: 'SSH Setting',
        sshChangeHelper: 'This action changed "{0}" to "{1}". Do you want to continue?',
        sshFileChangeHelper:
            'Modifying the configuration file may cause service availability. Exercise caution when performing this operation. Do you want to continue?',
        port: 'Port',
        portHelper: 'Specific the port that SSH service listens on.',
        listenAddress: 'Listen address',
        allV4V6: '0.0.0.0:{0}(IPv4) and :::{0}(IPv6)',
        listenHelper:
            'Leaving both of IPv4 and IPv6 settings blank will listen on "0.0.0.0:{0}(IPv4)" and ":::{0}(IPv6)".',
        addressHelper: 'Specify the address that SSH service listens on.',
        permitRootLogin: 'Permit root user login',
        rootSettingHelper: 'The default login method for root user is "Allow SSH login".',
        rootHelper1: 'Allow SSH login',
        rootHelper2: 'Disable SSH login',
        rootHelper3: 'Only key login is allowed',
        rootHelper4: 'Only predefined commands can be executed. No other operations can be performed.',
        passwordAuthentication: 'Password auth',
        pwdAuthHelper: 'Whether to enable password authentication. This parameter is enabled by default.',
        pubkeyAuthentication: 'Key auth',
        privateKey: 'Private Key',
        publicKey: 'Public Key',
        password: 'Password',
        createMode: 'Creation Method',
        generate: 'Auto-generate',
        unSyncPass: 'Key password cannot be synchronized',
        syncHelper: 'The sync operation will clean invalid keys and sync new complete key pairs. Continue?',
        input: 'Manual Input',
        import: 'File Upload',
        pubkey: 'Key info',
        pubKeyHelper: 'The current key information only takes effect for user {0}',
        encryptionMode: 'Encryption mode',
        passwordHelper: 'Can contain 6 to 10 digits and English cases',
        reGenerate: 'Regenerate key',
        keyAuthHelper: 'Whether to enable key authentication.',
        useDNS: 'useDNS',
        dnsHelper:
            'Control whether the DNS resolution function is enabled on the SSH server to verify the identity of the connection.',
        analysis: 'Statistical information',
        denyHelper:
            "Performing a 'deny' operation on the following addresses. After setting, the IP will be prohibited from accessing the server. Do you want to continue?",
        acceptHelper:
            "Performing an 'accept' operation on the following addresses. After setting, the IP will regain normal access. Do you want to continue?",
        noAddrWarning: 'No [{0}] addresses are currently selected. Please check and try again!',
        loginLogs: 'Login logs',
        loginMode: 'Mode',
        authenticating: 'Key',
        publickey: 'Key',
        belong: 'Belong',
        local: 'Local',
        session: 'Session | Sessions',
        loginTime: 'Login time',
        loginIP: 'Login IP',
        stopSSHWarn: 'Whether to disconnect this SSH connection',
    },
    setting: {
        panel: 'Panel',
        user: 'Panel user',
        userChange: 'Change panel user',
        userChangeHelper: 'Changing the panel user will log you out. Continue?',
        passwd: 'Panel password',
        emailHelper: 'For password retrieval',
        title: 'Panel alias',
        panelPort: 'Panel port',
        titleHelper:
            'Supports a length of 3 to 30 characters, including English letters, Chinese characters, numbers, spaces, and common special characters',
        portHelper:
            'The recommended port range is 8888 to 65535. Note: If the server has a security group, permit the new port from the security group in advance',
        portChange: 'Port change',
        portChangeHelper: 'Modify the service port and restart the service. Do you want to continue?',
        theme: 'Theme',
        menuTabs: 'Menu tabs',
        dark: 'Dark',
        darkGold: 'Dark Gold',
        light: 'Light',
        auto: 'Follow System',
        language: 'Language',
        languageHelper:
            'By default, it follows the browser language. This parameter takes effect only on the current browser',
        sessionTimeout: 'Session timeout',
        sessionTimeoutError: 'The minimum session timeout is 300 seconds',
        sessionTimeoutHelper:
            'The panel will automatically be logged out if there is no operation for more than {0} second(s).',
        systemIP: 'System address',
        proxy: 'Server proxy',
        proxyHelper: 'It will be effective in the following scenarios after you set up the proxy server:',
        proxyHelper1:
            'Installation package download and synchronization from the app store (Professional edition only)',
        proxyHelper2: 'System update and update information retrieval (Professional edition only)',
        proxyHelper3: 'System license verification and synchronization',
        proxyHelper4: 'Docker network will be accessed through a proxy server (Professional edition only)',
        proxyHelper5: 'Unified download and sync for system-type script libraries (Professional)',
        proxyHelper6: 'Apply for certificate (Professional)',
        proxyType: 'Proxy type',
        proxyUrl: 'Proxy Address',
        proxyPort: 'Proxy Port',
        proxyPasswdKeep: 'Remember Password',
        proxyDocker: 'Docker Proxy',
        proxyDockerHelper:
            'Synchronize proxy server configuration to Docker, support offline server image pulling and other operations',
        syncToNode: 'Sync to Node',
        syncToNodeHelper: 'Sync settings to other nodes',
        nodes: 'Node',
        selectNode: 'Select Node',
        selectNodeError: 'Please select a node',
        apiInterface: 'Enable API',
        apiInterfaceClose: 'Once closed, API interfaces cannot be accessed. Do you want to continue?',
        apiInterfaceHelper: 'Allow third-party applications to access the API.',
        apiInterfaceAlert1: `Don't enable it in production environments because it may increase server security risks.`,
        apiInterfaceAlert2: `Don't use third-party applications to call the API to prevent potential security threats.`,
        apiInterfaceAlert3: 'API document:',
        apiInterfaceAlert4: 'Usage document:',
        apiKey: 'API key',
        apiKeyHelper: 'API key is used for third-party applications to access the API.',
        ipWhiteList: 'IP allowlist',
        ipWhiteListEgs: 'One per line. For example,\n172.161.10.111\n172.161.10.0/24',
        ipWhiteListHelper: 'IPs within the allowlist can access the API,0.0.0.0/0 (all IPv4), ::/0 (all IPv6)',
        apiKeyValidityTime: 'Validity period of interface key',
        apiKeyValidityTimeEgs: 'Validity period of interface key (in minutes)',
        apiKeyValidityTimeHelper:
            'The interface timestamp is valid if its difference from the current timestamp (in minutes) is within the allowed range. A value of 0 disables verification.',
        apiKeyReset: 'Interface key reset',
        apiKeyResetHelper: 'the associated key service will become invalid. Please add a new key to the service',
        confDockerProxy: 'Configure docker proxy',
        restartNowHelper: 'Configuring Docker proxy requires restarting the Docker service.',
        restartNow: 'Restart immediately',
        restartLater: 'Restart manually later',
        systemIPWarning: `The server address isn't currently set. Set it in the control panel first.`,
        systemIPWarning1: `The current server address is set to {0}, and quick redirection isn't possible!`,
        syncTime: 'Server Time',
        timeZone: 'Time Zone',
        timeZoneChangeHelper: 'Changing the time zone requires restarting the service. Do you want to continue?',
        timeZoneHelper:
            'Timezone modification depends on the system timedatectl service. take effect after restart the 1Panel service.',
        timeZoneCN: 'Bei Jing',
        timeZoneAM: 'Los Angeles',
        timeZoneNY: 'New York',
        ntpALi: 'Alibaba',
        ntpGoogle: 'Google',
        syncSite: 'Ntp Server',
        syncSiteHelper:
            'This operation will use {0} as the source for system time synchronization. Do you want to continue?',
        changePassword: 'Change Password',
        oldPassword: 'Original password',
        newPassword: 'New password',
        retryPassword: 'Confirm password',
        noSpace: 'Input information cannot include space characters',
        duplicatePassword: 'The new password cannot be the same as the original password, please re-enter!',
        diskClean: 'Cache clean',
        developerMode: 'Preview Program',
        developerModeHelper: `You will get to expirence new features and fixes before they're released broadly and give early feedback.`,
        thirdParty: 'Third-party accounts',
        scope: 'Scope',
        public: 'Public',
        publicHelper:
            'Public type backup accounts will be synchronized to each sub-node, and sub-nodes can use them together',
        private: 'Private',
        privateHelper:
            'Private type backup accounts are only created on the current node and are for the use of the current node only',
        noTypeForCreate: 'No backup type is currently created',
        LOCAL: 'Server disk',
        OSS: 'Ali OSS',
        S3: 'Amazon S3',
        mode: 'Mode',
        MINIO: 'MinIO',
        SFTP: 'SFTP',
        WebDAV: 'WebDAV',
        WebDAVAlist: 'WebDAV connect Alist can refer to the official documentation',
        UPYUN: 'UPYUN',
        ALIYUN: 'Aliyun Drive',
        ALIYUNHelper:
            'The current maximum limit for non-client downloads on Aliyun Drive is 100 MB. Exceeding this limit requires downloading through the client.',
        ALIYUNRecover:
            'The current maximum limit for non-client downloads on Aliyun Drive is 100 MB. Exceeding this limit requires downloading through the client to the local device, then synchronizing the snapshot for recovery.',
        GoogleDrive: 'Google Drive',
        analysis: 'Analysis',
        analysisHelper:
            'Paste the entire token content to automatically parse the required parts. For specific operations, please refer to the official documentation.',
        serviceName: 'Service Name',
        operator: 'Operator',
        OneDrive: 'Microsoft OneDrive',
        isCN: 'Century Internet',
        isNotCN: 'International Version',
        client_id: 'Client ID',
        client_secret: 'Client secret',
        redirect_uri: 'Redirect URL',
        onedrive_helper: 'Custom configuration can be referred to in the official documentation',
        clickToRefresh: 'Click to refresh',
        refreshTime: 'Token Refresh Time',
        refreshStatus: 'Token Refresh Status',
        backupDir: 'Backup directory',
        codeWarning: 'The current authorization code format is incorrect, please confirm again!',
        code: 'Auth code',
        codeHelper:
            'Please click on the "Acquire" button, then login to {0} and copy the content after "code" in the redirected link. Paste it into this input box. For specific instructions, please refer to the official documentation.',
        loadCode: 'Acquire',
        COS: 'Tencent COS',
        ap_beijing_1: 'Beijing Zone 1',
        ap_beijing: 'Beijing',
        ap_nanjing: 'Nanjing',
        ap_shanghai: 'Shanghai',
        ap_guangzhou: 'Guangzhou',
        ap_chengdu: 'Chengdu',
        ap_chongqing: 'Chongqing',
        ap_shenzhen_fsi: 'Shenzhen Financial',
        ap_shanghai_fsi: 'Shanghai Financial',
        ap_beijing_fsi: 'Beijing Financial',
        ap_hongkong: 'Hong Kong, China',
        ap_singapore: 'Singapore',
        ap_mumbai: 'Mumbai',
        ap_jakarta: 'Jakarta',
        ap_seoul: 'Seoul',
        ap_bangkok: 'Bangkok',
        ap_tokyo: 'Tokyo',
        na_siliconvalley: 'Silicon Valley (US West)',
        na_ashburn: 'Ashburn (US East)',
        na_toronto: 'Toronto',
        sa_saopaulo: 'Sao Paulo',
        eu_frankfurt: 'Frankfurt',
        KODO: 'Qiniu Kodo',
        scType: ' Storage type',
        typeStandard: 'Standard',
        typeStandard_IA: 'Standard_IA',
        typeArchive: 'Archive',
        typeDeep_Archive: 'Deep_Archive',
        scLighthouse: 'Default, Lightweight object storage only supports this storage type',
        scStandard:
            'Standard storage, suitable for business scenarios with large amounts of frequently accessed hot files and frequent data interactions.',
        scStandard_IA:
            'Infrequent access storage, suitable for business scenarios with lower access frequency (e.g., average monthly access frequency of 1-2 times), minimum storage duration of 30 days.',
        scArchive: 'Archival storage is suitable for business scenarios with extremely low access frequency.',
        scDeep_Archive: 'Durable cold storage is suitable for business scenarios with extremely low access frequency.',
        archiveHelper:
            'Archival storage files cannot be downloaded directly and must first be restored through the corresponding cloud service provider`s website. Please use with caution!',
        backupAlert:
            'If a cloud provider is compatible with the S3 protocol, you can directly use Amazon S3 for backup. ',
        domain: 'Accelerate domain',
        backupAccount: 'Backup account | Backup accounts',
        loadBucket: 'Get bucket',
        accountName: 'Account name',
        accountKey: 'Account key',
        address: 'Address',
        path: 'Path',

        safe: 'Security',
        bindInfo: 'Bind info',
        bindAll: 'Listen All',
        bindInfoHelper:
            'Changing the service listening address or protocol may result in service unavailability. Do you want to continue?',
        ipv6: 'Listen IPv6',
        bindAddress: 'Listen address',
        entrance: 'Entrance',
        showEntrance: 'Show disabled alert in "Overview" page',
        entranceHelper:
            'Enabling security entrance will only allow logging in to the panel through specified security entrance.',
        entranceError:
            'Please enter a secure login entry point of 5-116 characters, only numbers or letters are supported.',
        entranceInputHelper: 'Leave it blank to disable the security entrance.',
        randomGenerate: 'Random',
        expirationTime: 'Expiration Date',
        unSetting: 'Not set',
        noneSetting:
            'Set the expiration time for the panel password. After the expiration, you need to reset the password',
        expirationHelper: 'If the password expiration time is [0] days, the password expiration function is disabled',
        days: 'Expiration Days',
        expiredHelper: 'The current password has expired. Please change the password again.',
        timeoutHelper:
            '[ {0} days ] The panel password is about to expire. After the expiration, you need to reset the password',
        complexity: 'Complexity validation',
        complexityHelper: `After you enable it, the password validation rule will be: 8-30 characters, including English, numbers, and at least two special characters.`,
        bindDomain: 'Bind domain',
        unBindDomain: 'Unbind domain',
        panelSSL: 'Panel SSL',
        panelSSLHelper:
            'After the automatic renewal of the panel SSL, you need to manually restart the 1Panel service for the changes to take effect.',
        unBindDomainHelper:
            'The action of unbinding a domain name may cause system insecurity. Do you want to continue?',
        bindDomainHelper: 'After you bind the domain, only that domain can access 1Panel service.',
        bindDomainHelper1: 'Leave it blank to disable the domain name binding.',
        bindDomainWarning:
            'After domain binding, you will be logged out and can only access 1Panel service through the domain name specified in the settings. Do you want to continue?',
        allowIPs: 'Authorized IP',
        unAllowIPs: 'Unauthorized IP',
        unAllowIPsWarning:
            'Authorizing an empty IP will allow all IPs to access the system, which may cause system insecurity. Do you want to continue?',
        allowIPsHelper:
            'After you set the authorized IP address list, only the IP address in the list can access the panel service.',
        allowIPsWarning:
            'After you set the authorized IP address list, only the IP address in the list can access the panel service. Do you want to continue?',
        allowIPsHelper1: `Leave it blank to disable the IP address restriction.`,
        allowIPEgs: 'One per line. For example,\n************1\n17*********/24',
        mfa: 'Two-factor authentication (2FA)',
        mfaClose: 'Disabling MFA will reduce the security of the service. Do you want to continue?',
        secret: 'Secret',
        mfaInterval: 'Refresh interval(s)',
        mfaTitleHelper:
            'The title is used to distinguish different 1Panel hosts. Scan again or manually add the secret key after you modify the title.',
        mfaIntervalHelper: 'Scan again or manually add the secret key after you modify the refresh time.',
        mfaAlert:
            'One-time token is dynamically generated 6-digit number and based on the current time. Make sure that the server time is synchronized.',
        mfaHelper: 'After you enabled it, the one-time token needs to be verified.',
        mfaHelper1: 'Download an authenticator app, for example,',
        mfaHelper2:
            'To obtain the one-time token, scan the following QR code using your authenticator app or copy the secret key into your authentication app.',
        mfaHelper3: 'Enter six digits from the app',
        mfaCode: 'One-time token',
        sslChangeHelper: 'Modify the https setting and restart the service. Do you want to continue?',
        sslDisable: 'Disable',
        sslDisableHelper:
            'If the https service is disabled, you need to restart the panel for it to take effect. Do you want to continue?',
        noAuthSetting: 'Unauthorized setting',
        noAuthSettingHelper: `When users don't log in with specified security entrance, or don't access the panel from specified IP or domain name, this response can hid panel characteristic.`,
        responseSetting: 'Response setting',
        help200: 'Help Page',
        error400: 'Bad Request',
        error401: 'Unauthorized',
        error403: 'Forbidden',
        error404: 'Not Found',
        error408: 'Request Timeout',
        error416: 'Range Not Satisfiable',
        error444: 'Connection Closed',
        error500: 'Internal Server Error',

        https: 'Setting up HTTPS protocol access for the panel can enhance the security of panel access.',
        certType: 'Certificate type',
        selfSigned: 'Self signed',
        selfSignedHelper: `Browsers may not trust self-signed certificates and may display security warnings.`,
        select: 'Select',
        domainOrIP: 'Domain or IP:',
        timeOut: 'Timeout',
        rootCrtDownload: 'Root certificate download',
        primaryKey: 'Primary key',
        certificate: 'Certificate',
        backupJump:
            'Backup files not in the current backup list, please try downloading from the file directory and importing for backup.',

        snapshot: 'Snapshot | Snaphshots',
        noAppData: 'No system applications available for selection',
        noBackupData: 'No backup data available for selection',
        stepBaseData: 'Base Data',
        stepAppData: 'System Application',
        stepPanelData: 'System Data',
        stepBackupData: 'Backup Data',
        stepOtherData: 'Other Data',
        operationLog: 'Retain Operation Log',
        loginLog: 'Retain Access Log',
        systemLog: 'Retain System Log',
        taskLog: 'Retain Task Log',
        monitorData: 'Retain Monitoring Data',
        dockerConf: 'Retain Docker Configuration',
        selectAllImage: 'Backup All Application Images',
        logLabel: 'Log',
        agentLabel: 'Node Configuration',
        appDataLabel: 'Application Data',
        appImage: 'Application Image',
        appBackup: 'Application Backup',
        backupLabel: 'Backup Directory',
        confLabel: 'Configuration File',
        dockerLabel: 'Container',
        taskLabel: 'Scheduled Task',
        resourceLabel: 'Application Resource Directory',
        runtimeLabel: 'Runtime Environment',
        appLabel: 'Application',
        databaseLabel: 'Database',
        snapshotLabel: 'Snapshot File',
        websiteLabel: 'Website',
        directoryLabel: 'Directory',
        appStoreLabel: 'Application Store',
        shellLabel: 'Script',
        tmpLabel: 'Temporary Directory',
        sslLabel: 'Certificate Directory',
        reCreate: 'Failed to create snapshot',
        reRollback: 'Rollback snapshot failed',
        deleteHelper:
            'All snapshot files including those in the third-party backup account will be deleted. Do you want to continue?',
        status: 'Snapshot status',
        ignoreRule: 'Ignore rule',
        editIgnoreRule: '@:commons.button.edit @.lower:setting.ignoreRule',
        ignoreHelper:
            'This rule will be used to compress and backup the 1Panel data directory during creating snapshot. By default, socket files are ignored.',
        ignoreHelper1: 'One per line. For example,\n*.log\n/opt/1panel/cache',
        panelInfo: 'Write 1Panel basic information',
        panelBin: 'Backup 1Panel system files',
        daemonJson: 'Backup Docker configuration file',
        appData: 'Backup installed apps from 1Panel',
        panelData: 'Backup 1Panel data directory',
        backupData: 'Backup local backup directory for 1Panel',
        compress: 'Create Snapshot file',
        upload: 'Upload snapshot file',
        recoverDetail: 'Recover detail',
        createSnapshot: 'Create snapshot',
        importSnapshot: 'Sync snapshot',
        importHelper: 'Snapshot directory: ',
        lastRecoverAt: 'Last recovery time',
        lastRollbackAt: 'Last rollback time',
        reDownload: 'Download the backup file again',
        recoverErrArch: `Snapshot recovery between different server architectures isn't supported!`,
        recoverErrSize: 'Detected insufficient disk space, please check or clean up and try again!',
        recoverHelper:
            'Starting recovery from snapshot {0}, please confirm the following information before proceeding:',
        recoverHelper1: 'Recovery requires restarting Docker and 1Panel services',
        recoverHelper2:
            'Please ensure there is sufficient disk space on the server (Snapshot file size: {0}, Available space: {1})',
        recoverHelper3:
            'Please ensure the server architecture matches the architecture of the server where the snapshot was created (Current server architecture: {0})',
        rollback: 'Rollback',
        rollbackHelper:
            'Rolling back this recovery will replace all files from this recovery, and may require restarting Docker and 1Panel services. Do you want to continue?',

        upgradeRecord: 'Upgrade record',
        upgrading: ' Upgrading, please wait...',
        upgradeHelper: 'The upgrade requires restarting the 1Panel service. Do you want to continue?',
        noUpgrade: 'It is currently the latest version',
        versionHelper:
            'Name rules: [major version].[functional version].[Bug fix version], as shown in the following example:',
        rollbackLocalHelper:
            'The primary node does not support direct rollback. Please manually execute the [1pctl restore] command to rollback!',
        upgradeCheck: 'Check for updates',
        upgradeNotes: 'Release note',
        upgradeNow: 'Upgrade now',
        source: 'Download source',
        versionNotSame: 'Node version mismatch with the main node. Please upgrade in Node Management before retrying.',
        versionCompare:
            'Detected that node {0} is already at the latest upgradable version. Please check the primary node version and try again!',

        about: 'About',
        project: 'GitHub',
        issue: 'Feedback',
        doc: 'Official document',
        star: 'Star',
        description: 'Linux Server Panel',
        forum: 'Discussions',
        doc2: 'Docs',
        currentVersion: 'Version',

        license: 'License',
        bindNode: 'Bind Node',
        menuSetting: 'Menu Settings',
        menuSettingHelper: 'When only 1 submenu exists, the menu bar will display only that submenu',
        showAll: 'Show All',
        hideALL: 'Hide All',
        ifShow: 'Whether to Show',
        menu: 'Menu',
        confirmMessage: 'The page will be refreshed to update the advanced menu list. Continue?',
        compressPassword: 'Compression password',
        backupRecoverMessage: 'Please enter the compression or decompression password (leave blank to not set)',
    },
    license: {
        community: 'OSS',
        oss: 'Open Source Software',
        pro: 'Pro',
        trial: 'Trial',
        add: 'Add Community Edition',
        licenseAlert:
            'Community Edition nodes can only be added when the license is properly bound to a node. Only nodes properly bound to the license support switching.',
        licenseUnbindHelper: 'Community Edition nodes detected for this license. Please unbind and try again!',
        subscription: 'Subscription',
        perpetual: 'Perpetual',
        versionConstraint: '{0} Version Buyout',
        forceUnbind: 'Force Unbind',
        forceUnbindHelper:
            'Forcing unbind will ignore any errors that occur during the unbinding process and ultimately release the license binding.',
        updateForce: 'Force update (ignore all errors during unbinding to ensure final operation succeeds)',
        trialInfo: 'Version',
        authorizationId: 'Subscription authorization ID',
        authorizedUser: 'Authorized user',
        lostHelper:
            'The license has reached the maximum number of retry attempts. Please manually click the sync button to ensure the professional version features are functioning properly.',
        exceptionalHelper:
            'License synchronization verification is abnormal. Please manually click the sync button to ensure the professional version functions properly. detail: ',
        quickUpdate: 'Quick update',
        import: 'Import',
        power: 'Authorize',
        unbindHelper: 'All Pro related Settings will be cleaned after unbinding. Do you want to continue? ',
        importLicense: 'Import license',
        importHelper: 'Please click or drag the license file here',
        levelUpPro: 'Upgrade to Professional Edition',
        licenseSync: 'License Sync',
        knowMorePro: 'Learn More',
        closeAlert: 'The current page can be closed in the panel settings',
        introduce: 'Feature Introduction',
        waf: 'Upgrading to the professional version can provide features such as interception map, logs, block records, geographical location blocking, custom rules, custom interception pages, etc.',
        tamper: 'Upgrading to the professional version can protect websites from unauthorized modifications or tampering.',
        tamperHelper: 'Operation failed, the file or folder has tamper protection enabled. Please check and try again!',
        setting:
            'Upgrading to the professional version allows customization of panel logo, welcome message, and other information.',
        monitor:
            'Upgrade to the professional version to view the real-time status of the website, visitor trends, visitor sources, request logs and other information. ',
        alert: 'Upgrade to the professional version to receive alarm information via SMS and view alarm logs, fully control various key events, and ensure worry-free system operation',
        node: 'Upgrading to the professional version allows you to manage multiple Linux servers with 1Panel.',
        fileExchange: 'Upgrade to the Professional Edition to quickly transfer files between multiple servers.',
        app: 'Upgrade to the professional version to view service information, abnormal monitoring, etc. through the mobile APP. ',
        cluster:
            'Upgrading to the Professional Edition allows you to manage MySQL/Postgres/Reids master-slave clusters.',
    },
    clean: {
        scan: 'Start scanning',
        scanHelper: 'Easily clean up junk files produced during 1Panel runtime',
        clean: 'Clean now',
        reScan: 'Rescan',
        cleanHelper: `This will clean up the selected system junk files and can't be undone. Do you want to continue?`,
        statusSuggest: '(Recommended Cleaning)',
        statusClean: '(Very clean)',
        statusEmpty: 'Very clean, no cleaning needed!',
        statusWarning: '(Proceed with Caution)',
        lastCleanTime: 'Last Cleaned: {0}',
        lastCleanHelper: 'Files and directories cleaned: {0}, total cleaned: {1}',
        cleanSuccessful: 'Successfully cleaned',
        currentCleanHelper: 'Files and directories cleaned in this session: {0}, Total cleaned: {1}',
        suggest: '(Recommended)',
        totalScan: 'Total junk files to be cleaned: ',
        selectScan: 'Total selected junk files: ',

        system: 'System Junk Files',
        systemHelper:
            'Temporary files generated during snapshots, upgrades, and obsolete file contents during version iterations',
        panelOriginal: 'System snapshot recovery backup files',
        backup: 'Temporary backup directory',
        upgrade: 'System upgrade backup files',
        upgradeHelper: '(Recommend keeping the latest upgrade backup for system rollback)',
        cache: 'System cache files',
        cacheHelper: '(Proceed with caution, cleaning requires a service restart)',
        snapshot: 'System snapshot temporary files',
        rollback: 'Backup files before recover',

        upload: 'Temporary Upload Files',
        uploadHelper: 'Temporary files uploaded from the system backup list',
        download: 'Temporary Download Files',
        downloadHelper: 'Temporary files downloaded from third-party backup accounts by the system',
        directory: 'Directory',

        systemLog: 'System Log Files',
        systemLogHelper:
            'System log information, container build or image pull log information, and log files generated in scheduled tasks',
        dockerLog: 'Container operation log files',
        taskLog: 'Scheduled task execution log files',
        shell: 'Shell script scheduled tasks',
        containerShell: 'Container internal Shell script scheduled tasks',
        curl: 'CURL scheduled tasks',

        containerTrash: 'Container Trash',
        volumes: 'Volumes',
        buildCache: 'Container Build Cache',
    },
    app: {
        app: 'Application | Applications',
        installName: 'Name',
        installed: 'Installed',
        all: 'All',
        version: 'Version',
        detail: 'Details',
        params: 'Edit parameters',
        author: 'Author',
        source: 'Source',
        appName: 'Application Name',
        deleteWarn:
            'The delete operation will delete all data and backups together. This operation cannot be rolled back. Do you want to continue? ',
        syncSuccess: 'Synchronized successfully',
        canUpgrade: 'Updates',
        backupName: 'File Name',
        backupPath: 'File Path',
        backupdate: 'Backup time',
        versionSelect: 'Please select a version',
        operatorHelper: 'Operation {0} will be performed on the selected application. Do you want to continue?',
        startOperatorHelper: 'The application will be started. Do you want to continue?',
        stopOperatorHelper: 'The application will be stopped. Do you want to continue?',
        restartOperatorHelper: 'The application will be restarted. Do you want to continue?',
        reloadOperatorHelper: 'The application will be reloaded. Do you want to continue?',
        checkInstalledWarn: `"{0}" isn't detected. Go to "App Store" to install.`,
        gotoInstalled: 'Go to install',
        limitHelper: 'The application has already been installed.',
        deleteHelper: `"{0}" has been associated with the following resource(s) and can't be deleted`,
        checkTitle: 'Hint',
        defaultConfig: 'Default configuration',
        defaultConfigHelper: 'It has been restored to the default configuration, it will take effect after saving',
        forceDelete: 'Force delete',
        forceDeleteHelper:
            'Force deletion will ignore errors during the deletion process and eventually delete metadata.',
        deleteBackup: 'Delete backup',
        deleteBackupHelper: 'Also delete the application backup',
        deleteDB: 'Delete database',
        deleteDBHelper: 'Also delete the database',
        noService: 'No {0}',
        toInstall: 'Go to install',
        param: 'Parameters',
        alreadyRun: 'Age',
        syncAppList: 'Sync',
        less1Minute: 'Less than 1 minute',
        appOfficeWebsite: 'Website',
        github: 'Github',
        document: 'Document',
        updatePrompt: 'No updates available',
        installPrompt: 'No apps installed yet',
        updateHelper: 'Editing parameters may cause the application to fail to start. Please proceed with caution.',
        updateWarn: 'Update parameters need to rebuild the application, Do you want to continue? ',
        busPort: 'Port',
        syncStart: 'Start syncing! Please refresh the app store later',
        advanced: 'Advanced settings',
        cpuCore: 'core(s)',
        containerName: 'Container name',
        containerNameHelper: 'The container name will be automatically generated when not set',
        allowPort: 'External access',
        allowPortHelper: 'Allowing external port access will release the firewall port',
        appInstallWarn: `The application dosn't expose the external access port by default. Click "Advanced settings" to expose it.`,
        upgradeStart: 'Start upgrading! Please refresh the page later',
        toFolder: 'Open the installation directory',
        editCompose: 'Edit compose file',
        editComposeHelper: 'Editing the compose file may cause the software installation to fail',
        composeNullErr: 'compose cannot be empty',
        takeDown: 'TakeDown',
        allReadyInstalled: 'Installed',
        installHelper: 'If you have image pull issues, configure image acceleration.',
        installWarn: `The external access isn't checked, and it will make the application unable to access through external network. Do you want to continue?`,
        showIgnore: 'View ignored applications',
        cancelIgnore: 'Cancel ignore',
        ignoreList: 'Ignored applications',
        appHelper: 'Go to application details page to learn installation instruction for some special applications.',
        backupApp: 'Backup application before upgrade',
        backupAppHelper:
            'If the upgrade fails, the backup will be automatically rolled back. Please check the failure reason in the log audit-system log',
        openrestyDeleteHelper: 'Forced delete of OpenResty will delete all websites. Do you want to continue?',
        downloadLogHelper1: 'All logs of {0} application are about to be downloaded. Do you want to continue? ',
        downloadLogHelper2:
            'The latest {1} logs of {0} application are about to be downloaded. Do you want to continue? ',
        syncAllAppHelper: 'All applications will be synchronized. Do you want to continue? ',
        hostModeHelper:
            'The current application network mode is host mode. If you need to open the port, please open it manually on the firewall page.',
        showLocal: 'Show local applications',
        reload: 'Reload',
        upgradeWarn:
            'Upgrading the application will replace the docker-compose.yml file. If there are any changes, you can click to view the file comparison',
        newVersion: 'New version',
        oldVersion: 'Current version',
        composeDiff: 'File comparison',
        showDiff: 'View comparison',
        useNew: 'Use custom version',
        useDefault: 'Use default version',
        useCustom: 'Customize docker-compose.yml',
        useCustomHelper: `Using a custom docker-compose.yml file may cause the application upgrade to fail. If it isn't necessary, don't check it.`,
        diffHelper:
            'The left side is the old version, the right side is the new version. After editing, click to save the custom version',
        pullImage: 'Pull Image',
        pullImageHelper: 'Execute docker pull to pull the image before the application starts',
        deleteImage: 'Delete Image',
        deleteImageHelper: 'Delete the image related to the application. The task will not stop if deletion fails',
        requireMemory: 'Memory',
        supportedArchitectures: 'Architectures',
        link: 'Link',
        showCurrentArch: 'Architecture',
        syncLocalApp: 'Sync Local App',
        memoryRequiredHelper: 'Current application memory requirement {0}',
        gpuConfig: 'Enable GPU Support',
        gpuConfigHelper:
            'Please ensure the machine has an NVIDIA GPU and that NVIDIA drivers and the NVIDIA Docker Container Toolkit are installed',
        webUI: 'Web Access Address',
        webUIPlaceholder: 'For example: example.com:8080/login',
        defaultWebDomain: 'Default Access Address',
        defaultWebDomainHepler:
            'If the application port is 8080, the redirect address will be http(s)://default access address:8080',
        webUIConfig:
            'The current node has no default access address configured. Please set it in application parameters or go to panel settings to configure!',
        toLink: 'Open',
        customAppHelper:
            'Before installing a custom app store package, please ensure that there are no installed apps.',
        forceUninstall: 'Force Uninstall',
        syncCustomApp: 'Sync Custom App',
        ignoreAll: 'Ignore all subsequent versions',
        ignoreVersion: 'Ignore specified version',
        specifyIP: 'Bind Host IP',
        specifyIPHelper:
            'Set the host address/network interface to bind the port (if you are not sure about this, please do not fill it in)',
        uninstallDeleteBackup: 'Uninstall App - Delete Backup',
        uninstallDeleteImage: 'Uninstall App - Delete Image',
        upgradeBackup: 'Backup App Before Upgrade',
    },
    website: {
        primaryDomain: 'Primary domain',
        otherDomains: 'Other domains',
        static: 'Static',
        deployment: 'Deployment',
        supportUpType: 'Only .tar.gz files are supported',
        zipFormat: '.tar.gz compressed package structure: test.tar.gz compressed package must contain {0} file',
        proxy: 'Reverse proxy',
        alias: 'Alias',
        ftpUser: 'FTP account',
        ftpPassword: 'FTP password',
        ftpHelper:
            'After creating a website, a corresponding FTP account will be created and the FTP directory will link to the website directory.',
        remark: 'Remark',
        groupSetting: 'Group Management',
        createGroup: 'Create group',
        appNew: 'New Application',
        appInstalled: 'Installed application',
        create: 'Create',
        delete: 'Delete Website',
        deleteApp: 'Delete Application',
        deleteBackup: 'Delete Backup',
        domain: 'Domain',
        domainHelper: 'One domain per line.\nSupport wildcard "*" and IP address.\nSupport adding port.',
        addDomain: 'Add',
        domainConfig: 'Domains',
        defaultDoc: 'Document',
        perserver: 'Concurrency',
        perserverHelper: 'Limit the maximum concurrency of the current site',
        perip: 'Single IP',
        peripHelper: 'Limit the maximum number of concurrent access to a single IP',
        rate: 'Traffic limits',
        rateHelper: 'Limit the flow of each request (unit: KB)',
        limitHelper: 'Enable flow control',
        other: 'Other',
        currentSSL: 'Current Certificate',
        dnsAccount: 'DNS account',
        applySSL: 'Certificate Application',
        SSLList: 'Certificate List',
        createDnsAccount: 'DNS account',
        aliyun: 'Aliyun DNS',
        manual: 'Manual parsing',
        key: 'Key',
        check: 'View',
        acmeAccountManage: 'Manage ACME accounts',
        email: 'Email',
        acmeAccount: 'ACME account',
        provider: 'Verification method',
        dnsManual: 'Manual Resolution',
        expireDate: 'Expiration date',
        brand: 'Organization',
        deploySSL: 'Deployment',
        deploySSLHelper: 'Are you sure to deploy the certificate? ',
        ssl: 'Certificate | Certificates',
        dnsAccountManage: 'Manage DNS providers',
        renewSSL: 'Renew',
        renewHelper: 'Are you sure to renew the certificate? ',
        renewSuccess: 'Renew certificate',
        enableHTTPS: 'Enable',
        aliasHelper: 'Alias is the directory name of the website',
        lastBackupAt: 'last backup time',
        null: 'none',
        nginxConfig: 'Nginx configuration',
        websiteConfig: 'Website settings',
        basic: 'Basic',
        source: 'Configuration',
        security: 'Security',
        nginxPer: 'Performance tuning',
        neverExpire: 'Never',
        setDefault: 'Set as default',
        deleteHelper: 'Related application status is abnormal, please check',
        toApp: 'Go to the installed list',
        cycle: 'Cycle',
        frequency: 'Frequency',
        ccHelper:
            'Accumulatively request the same URL more than {1} times within {0} seconds, trigger CC defense, block this IP',
        mustSave: 'The modification needs to be saved to take effect',
        fileExt: 'file extension',
        fileExtBlock: 'file extension blocklist',
        value: 'value',
        enable: 'Enable',
        proxyAddress: 'Proxy Address',
        proxyHelper: 'Example: 127.0.0.1:8080',
        forceDelete: 'Force Delete',
        forceDeleteHelper:
            'Force deletion will ignore errors during the deletion process and eventually delete metadata.',
        deleteAppHelper: 'Delete associated applications and application backups at the same time',
        deleteBackupHelper: 'Also delete website backups.',
        deleteConfirmHelper: `The delete operation can't be undone. Enter <span style="color:red"> "{0}" </span> to confirm deletion.`,
        staticPath: 'The corresponding main directory is ',
        limit: 'Scheme',
        blog: 'Forum/Blog',
        imageSite: 'Picture Site',
        downloadSite: 'Download Site',
        shopSite: 'Mall',
        doorSite: 'Portal',
        qiteSite: 'Enterprise',
        videoSite: 'Video',
        errLog: 'Error log',
        accessLog: 'Website log',
        stopHelper:
            'After stopping the site, it will not be able to access normally, and the user will display the stop page of the current site when visiting. Do you want to continue? ',
        startHelper:
            'After enabling the site, users can access the content of the site normally, do you want to continue? ',
        sitePath: 'Directory',
        siteAlias: 'Site alias',
        primaryPath: 'Root directory',
        folderTitle: 'The website mainly contains the following folders',
        wafFolder: 'Firewall rules',
        indexFolder: 'Website root directory',
        logFolder: 'Website log',
        sslFolder: 'Website Certificate',
        enableOrNot: 'Enable',
        oldSSL: 'Existing certificate',
        manualSSL: 'Import certificate',
        select: 'Select',
        selectSSL: 'Select Certificate',
        privateKey: 'Key (KEY)',
        certificate: 'Certificate (PEM format)',
        HTTPConfig: 'HTTP Options',
        HTTPSOnly: 'Block HTTP requests',
        HTTPToHTTPS: 'Redirect to HTTPS',
        HTTPAlso: 'Allow direct HTTP requests',
        sslConfig: 'SSL options',
        disableHTTPS: 'Disable HTTPS',
        disableHTTPSHelper:
            'Disabling HTTPS will delete the certificate related configuration, Do you want to continue?',
        SSLHelper:
            "Note: Do not use SSL certificates for illegal websites.\nIf HTTPS access can't be used after opening, check whether the security group has correctly released port 443.",
        SSLConfig: 'Certificate settings',
        SSLProConfig: 'Protocol settings',
        supportProtocol: 'Protocol version',
        encryptionAlgorithm: 'Encryption algorithm',
        notSecurity: '(not safe)',
        encryptHelper:
            "Let's Encrypt has a frequency limit for issuing certificates, but it is sufficient to meet normal needs. Too frequent operations will cause issuance failure. For specific restrictions, please see <a target='_blank' href='https://letsencrypt.org/zh-cn/docs /rate-limits/'>official document</a> ",
        ipValue: 'Value',
        ext: 'file extension',
        wafInputHelper: 'Input data by line, one line',
        data: 'data',
        ever: 'permanent',
        nextYear: 'One year later',
        noLog: 'No logs found',
        defaultServer: 'Set default site',
        noDefaultServer: 'Not set',
        defaultServerHelper:
            'After setting the default site, all unbinded domain names and IPs will be redirected to the default site\nThis can effectively prevent malicious resolution\nHowever, it will also cause the WAF unauthorized domain name interception to fail',
        restoreHelper: 'Are you sure to restore using this backup?',
        websiteDeploymentHelper: 'Use an installed application or create a new application to create a website.',
        websiteStatictHelper: 'Create a website directory on the host.',
        websiteProxyHelper:
            'Use reverse proxy to proxy existing service. For example, if a service is installed and running on port 8080, the proxy address will be "http://127.0.0.1:8080".',
        runtimeProxyHelper: 'Use a website runtime to create a website.',
        runtime: 'Runtime',
        deleteRuntimeHelper:
            'The Runtime application needs to be deleted together with the website, please handle it with caution',
        proxyType: 'Network Type',
        unix: 'Unix Network',
        tcp: 'TCP/IP Network',
        phpFPM: 'FPM Config',
        phpConfig: 'PHP Config',
        updateConfig: 'Update Config',
        isOn: 'On',
        isOff: 'Off',
        rewrite: 'Pseudo-static',
        rewriteMode: 'Scheme',
        current: 'Current',
        rewriteHelper:
            'If setting pseudo-static causes the website to become inaccessible, try to revert to the default settings.',
        runDir: 'Run Directory',
        runUserHelper:
            'For websites deployed through the PHP container runtime environment, you need to set the owner and user group of all files and folders under index and subdirectories to 1000. For the local PHP environment, refer to the local PHP-FPM user and user group settings',
        userGroup: 'User/Group',
        uGroup: 'Group',
        proxyPath: 'Proxy path',
        proxyPass: 'Target URL',
        cache: 'Cache',
        cacheTime: 'Cache duration',
        enableCache: 'Cache',
        proxyHost: 'Proxy host',
        disabled: 'Stopped',
        startProxy: 'This will start reverse proxy. Do you want to continue?',
        stopProxy: 'This will stop the reverse proxy. Do you want to continue?',
        sourceFile: 'View source',
        proxyHelper1: 'When accessing this directory, the content of the target URL will be returned and displayed.',
        proxyPassHelper: 'The target URL must be valid and accessible.',
        proxyHostHelper: 'Pass the domain name in the request header to the proxy server.',
        modifier: 'Matching rules',
        modifierHelper:
            'Example: "=" is exact match, "~" is regular match, "^~" matches the beginning of the path, etc.',
        replace: 'Text replacements',
        addReplace: 'Add',
        replaced: 'Search String (cannot be empty)',
        replaceText: 'Replace with string',
        replacedErr: 'The Search String cannot be empty',
        replacedErr2: 'The Search String cannot be repeated',
        basicAuth: 'Basic authentication',
        editBasicAuthHelper:
            'The password is asymmetrically encrypted and cannot be echoed. Editing needs to reset the password',
        antiLeech: 'Anti-leech',
        extends: 'Extension',
        browserCache: 'Cache',
        leechLog: 'Record anti-leech log',
        accessDomain: 'Allowed domains',
        leechReturn: 'Response resource',
        noneRef: 'Allow empty referrer',
        disable: 'not enabled',
        disableLeechHelper: 'Whether to disable the anti-leech',
        disableLeech: 'Disable anti-leech',
        ipv6: 'Listen IPv6',
        leechReturnError: 'Please fill in the HTTP status code',
        selectAcme: 'Select Acme account',
        imported: 'Created manually',
        importType: 'Import type',
        pasteSSL: 'Paste code',
        localSSL: 'Select server file',
        privateKeyPath: 'Private key file',
        certificatePath: 'Certificate file',
        ipWhiteListHelper: 'The role of IP allowlist: all rules are invalid for IP allowlist',
        redirect: 'Redirect',
        sourceDomain: 'Source domain',
        targetURL: 'Target URL address',
        keepPath: 'URI params',
        path: 'path',
        redirectType: 'redirection type',
        redirectWay: 'Way',
        keep: 'keep',
        notKeep: 'Do not keep',
        redirectRoot: 'Redirect to the homepage',
        redirectHelper: '301 permanent redirection, 302 temporary redirection',
        changePHPVersionWarn: 'This operation cannot be rolled back, do you want to continue?',
        changeVersion: 'Switch version',
        retainConfig: 'Whether to keep php-fpm.conf and php.ini files',
        runDirHelper2: 'Please ensure that the secondary running directory is under the index directory',
        openrestyHelper:
            'OpenResty default HTTP port: {0} HTTPS port: {1}, which may affect website domain name access and HTTPS forced redirect',
        primaryDomainHelper: 'Exmaple: example.com or example.com:8080',
        acmeAccountType: 'Account type',
        keyType: 'Key algorithm',
        tencentCloud: 'Tencent Cloud',
        containWarn: 'The domain name contains the main domain, please re-enter',
        rewriteHelper2:
            'Applications like WordPress installed from the app store typically come with pseudo-static configuration preset. Reconfiguring them may lead to errors.',
        websiteBackupWarn:
            'Only supports importing local backups, importing backups from other machines may cause recovery failure',
        ipWebsiteWarn: 'Websites with IP as domain names need to be set as default site to be accessed normally.',
        hstsHelper: 'Enabling HSTS can increase website security',
        includeSubDomains: 'SubDomains',
        hstsIncludeSubDomainsHelper:
            'Once enabled, the HSTS policy will apply to all subdomains of the current domain.',
        defaultHtml: 'Set default page',
        website404: 'Website 404 error page',
        domain404: 'Website page does not exist',
        indexHtml: 'Static website default page',
        stopHtml: 'Website stop page',
        indexPHP: 'PHP website default page',
        sslExpireDate: 'SSL Expiration Date',
        website404Helper: 'Website 404 error page only supports PHP runtime environment websites and static websites',
        sni: 'Origin SNI',
        sniHelper:
            "When the reverse proxy backend is HTTPS, you might need to set the origin SNI. Please refer to the CDN service provider's documentation for details.",
        huaweicloud: 'Huawei Cloud',
        createDb: 'Create Database',
        enableSSLHelper: 'Failure to enable will not affect the creation of the website',
        batchAdd: 'Batch Add Domains',
        generateDomain: 'Generate',
        global: 'Global',
        subsite: 'Subsite',
        subsiteHelper: 'A subsite can select an existing PHP or static website directory as the main directory.',
        parentWbeiste: 'Parent Website',
        deleteSubsite: 'To delete the current website, you must first delete the subsite(s) {0}',
        loadBalance: 'Load Balancing',
        server: 'Server',
        algorithm: 'Algorithm',
        ipHash: 'IP Hash',
        ipHashHelper:
            'Distributes requests to a specific server based on the client IP address, ensuring that a particular client is always routed to the same server.',
        leastConn: 'Least Connections',
        leastConnHelper: 'Sends requests to the server with the fewest active connections.',
        leastTime: 'Least Time',
        leastTimeHelper: 'Sends requests to the server with the shortest active connection time.',
        defaultHelper:
            'Default method, requests are evenly distributed to each server. If servers have weights configured, requests are distributed based on the specified weights, with higher-weighted servers receiving more requests.',
        weight: 'Weight',
        maxFails: 'Max Fails',
        maxConns: 'Max Connections',
        strategy: 'Strategy',
        strategyDown: 'Down',
        strategyBackup: 'Backup',
        staticChangePHPHelper: 'Currently a static website, you can switch to a PHP website',
        proxyCache: 'Reverse Proxy Cache',
        cacheLimit: 'Cache Space Limit',
        shareCahe: 'Cache Count Memory Size',
        cacheExpire: 'Cache Expiration Time',
        shareCaheHelper: 'Approximately 8000 cache objects can be stored per 1M of memory',
        cacheLimitHelper: 'Old cache will be automatically deleted when the limit is exceeded',
        cacheExpireJHelper: 'Cache will be deleted if it misses after the expiration time',
        realIP: 'Real IP',
        ipFrom: 'IP Source',
        ipFromHelper:
            "By configuring trusted IP sources, OpenResty will analyze IP information in HTTP headers, accurately identify and record visitors' real IP addresses, including in access logs",
        ipFromExample1: "If the frontend is a tool like Frp, you can enter Frp's IP address, such as 127.0.0.1",
        ipFromExample2: "If the frontend is a CDN, you can enter the CDN's IP address range",
        ipFromExample3:
            'If unsure, you can enter 0.0.0.0/0 (ipv4) ::/0 (ipv6) [Note: Allowing any source IP is not secure]',
        http3Helper:
            'HTTP/3 is an upgrade to HTTP/2, offering faster connection speeds and better performance, but not all browsers support HTTP/3. Enabling it may cause some browsers to be unable to access the site.',

        changeDatabase: 'Change Database',
        changeDatabaseHelper1: 'Database association is used for backing up and restoring the website.',
        changeDatabaseHelper2: 'Switching to another database will cause previous backups to be unrecoverable.',
        saveCustom: 'Save as Template',
        rainyun: 'Rain Yun',
        volcengine: 'Volcengine',
        runtimePortHelper: 'The current runtime environment has multiple ports. Please select a proxy port.',
        runtimePortWarn: 'The current runtime environment has no ports, unable to proxy',
        cacheWarn: 'Please turn off the cache switch in the reverse proxy first',
        loadBalanceHelper:
            'After creating the load balancing, please go to "Reverse Proxy", add a proxy and set the backend address to: http://<load balancing name>',
        favorite: 'Favorite',
        cancelFavorite: 'Cancel Favorite',
        useProxy: 'Use Proxy',
        useProxyHelper: 'Use the proxy server address in the panel settings',
        westCN: 'West Digital',
        openBaseDir: 'Prevent Cross-Site Attacks',
        openBaseDirHelper:
            'open_basedir is used to restrict the PHP file access path, which helps prevent cross-site access and enhance security',
        serverCacheTime: 'Server Cache Time',
        serverCacheTimeHelper:
            'The time a request is cached on the server. During this period, identical requests will return the cached result directly without requesting the origin server.',
        browserCacheTime: 'Browser Cache Time',
        browserCacheTimeHelper:
            'The time static resources are cached locally in the browser, reducing redundant requests. Users will use the local cache directly before it expires when refreshing the page.',
        donotLinkeDB: 'Do Not Link Database',
        toWebsiteDir: 'Enter Website Directory',
    },
    php: {
        short_open_tag: 'Short tag support',
        max_execution_time: 'Maximum script execution time',
        max_input_time: 'Maximum input time',
        memory_limit: 'Script memory limit',
        post_max_size: 'POST data maximum size',
        file_uploads: 'Whether to allow uploading files',
        upload_max_filesize: 'The maximum size allowed to upload files',
        max_file_uploads: 'The maximum number of files allowed to be uploaded at the same time',
        default_socket_timeout: 'Socket timeout',
        error_reporting: 'Error level',
        display_errors: 'Whether to output detailed error information',
        cgi_fix_pathinfo: 'Whether to open pathinfo',
        date_timezone: 'Time zone',
        disableFunction: 'Disable function',
        disableFunctionHelper: 'Enter the function to be disabled, such as exec, please use multiple, split',
        uploadMaxSize: 'Upload limit',
        indexHelper:
            'In order to ensure the normal operation of the PHP website, please place the code in the index directory and avoid renaming',
        extensions: 'Manage extension templates',
        extension: 'Extension',
        extensionHelper: 'Please use multiple extensions, split',
        toExtensionsList: 'View extension list',
        containerConfig: 'Container Configuration',
        containerConfigHelper:
            'Environment variables and other information can be modified in Configuration - Container Configuration after creation',
        dateTimezoneHelper: 'Example: TZ=Asia/Shanghai (Please add as needed)',
    },
    nginx: {
        serverNamesHashBucketSizeHelper: 'The hash table size of the server name',
        clientHeaderBufferSizeHelper: 'The header buffer size requested by the client',
        clientMaxBodySizeHelper: 'Maximum Upload File',
        keepaliveTimeoutHelper: 'Connection Timeout',
        gzipMinLengthHelper: 'Minimum Compressed File',
        gzipCompLevelHelper: 'Compression Rate',
        gzipHelper: 'Enable compression for transmission',
        connections: 'Active connections',
        accepts: 'Accepts',
        handled: 'Handled',
        requests: 'Requests',
        reading: 'Reading',
        writing: 'Writing',
        waiting: 'Waiting',
        status: 'Current Status',
        configResource: 'Configuration',
        saveAndReload: 'Save and reload',
        clearProxyCache: 'Clean reverse proxy cache',
        clearProxyCacheWarn: 'This action will delete all files in the cache directory. Do you want to continue?',
        create: 'Add a new module',
        update: 'Edit a module',
        params: 'Parameters',
        packages: 'Packages',
        script: 'Scripts',
        module: 'Modules',
        build: 'Build',
        buildWarn:
            'Building OpenResty requires reserving a certain amount of CPU and memory, which may take a long time, please be patient',
        mirrorUrl: 'Software Source',
        paramsHelper: 'For example: --add-module=/tmp/ngx_brotli',
        packagesHelper: 'For example: git, curl (separated by commas)',
        scriptHelper:
            'Scripts to execute before compilation, usually for downloading module source code, installing dependencies, etc.',
        buildHelper:
            'Click build after adding/modifying a module. OpenResty will automatically restart upon successful build.',
        defaultHttps: 'HTTPS Anti-tampering',
        defaultHttpsHelper1: 'Enabling this can resolve HTTPS tampering issues.',
    },
    ssl: {
        create: 'Request',
        provider: 'Type',
        manualCreate: 'Created manually',
        acmeAccount: 'ACME account',
        resolveDomain: 'Resolve domain name',
        err: 'Error',
        value: 'record value',
        dnsResolveHelper: 'Please go to the DNS resolution service provider to add the following resolution records:',
        detail: 'View details',
        msg: 'Information',
        ssl: 'Certificate',
        key: 'Private key',
        startDate: 'Effective time',
        organization: 'issuing organization',
        renewConfirm: 'This will renew a new certificate for domain name {0}. Do you want to continue?',
        autoRenew: 'Automatic renewal',
        autoRenewHelper: 'Automatically renew 30 days before expiration',
        renewSuccess: 'Renewal successful',
        renewWebsite:
            'This certificate has been associated with the following websites, and the application will be applied to these websites simultaneously',
        createAcme: 'Create Account',
        acmeHelper: 'Acme Account is used to apply for free certificates',
        upload: 'Import',
        applyType: 'Application method',
        apply: 'Renew',
        applyStart: 'Certificate application starts',
        getDnsResolve: 'Getting DNS resolution value, please wait...',
        selfSigned: 'Manage Self-signed CA',
        ca: 'Certificate authority',
        commonName: 'Common name',
        caName: 'Certificate authority name',
        company: 'Organization name',
        department: 'Organizational unit name',
        city: 'Locality name',
        province: 'State or province name',
        country: 'Country name (2 letter code)',
        commonNameHelper: 'For example, ',
        selfSign: 'Issue certificate',
        days: 'validity period',
        domainHelper: 'One domain name per line, supports * and IP address',
        pushDir: 'Push the certificate to the local directory',
        dir: 'Directory',
        pushDirHelper:
            'Certificate file "fullchain.pem" and key file "privkey.pem" will be generated in this directory.',
        organizationDetail: 'Organization details',
        fromWebsite: 'From website',
        dnsMauanlHelper:
            'In manual resolution mode, you need to click the apply button after creation to obtain the DNS resolution value',
        httpHelper:
            'Using HTTP mode requires installing OpenResty and does not support applying for wildcard domain certificates.',
        buypassHelper: `Buypass isn't accessible in mainland China`,
        googleHelper: 'How to get EAB HmacKey and EAB kid',
        googleCloudHelper: `Google Cloud API isn't accessible in most parts of mainland China`,
        skipDNSCheck: 'Skip DNS check',
        skipDNSCheckHelper: 'Check here only if you encounter a timeout issue during certification request.',
        cfHelper: 'Do not use Global API Key',
        deprecated: 'will be deprecated',
        deprecatedHelper:
            'Maintenance has been stopped and may be abandoned in a future version. Please use Tencent Cloud method for analysis',
        disableCNAME: 'Disable CNAME',
        disableCNAMEHelper: 'Check here if the domain name has a CNAME record and the request fails.',
        nameserver: 'DNS server',
        nameserverHelper: 'Use a custom DNS server to verify domain names.',
        edit: 'Edit certificate',
        execShell: 'Execute the script after certification request.',
        shell: 'Script content',
        shellHelper:
            'The default execution directory of the script is the 1Panel installation directory. If a certificate is pushed into local directory, the execution directory will be the certificate push directory. The default execution timeout is 30 minutes.',
        customAcme: 'Custom ACME Service',
        customAcmeURL: 'ACME Service URL',
        baiduCloud: 'Baidu Cloud',
    },
    firewall: {
        create: 'Create rule',
        edit: 'Edit rule',
        ccDeny: 'CC Protection',
        ipWhiteList: 'IP allowlist',
        ipBlockList: 'IP blocklist',
        fileExtBlockList: 'File extension blocklist',
        urlWhiteList: 'URL allowlist',
        urlBlockList: 'URL blocklist',
        argsCheck: 'GET parameter check',
        postCheck: 'POST parameter verification',
        cookieBlockList: 'Cookie blocklist',

        dockerHelper: `Linux firewall "{0}" can't disable Docker port mapping. The application can edit the parameters on the "App Store -> Installed" page to control whether the port is released.`,
        quickJump: 'Quick access',
        used: 'Used',
        unUsed: 'Unused',
        firewallHelper: '{0} system firewall',
        firewallNotStart: `The system firewall isn't enabled at present. Enable it first.`,
        restartFirewallHelper: 'This operation will restart the current firewall. Do you want to continue?',
        stopFirewallHelper: 'This will make the server lose security protection. Do you want to continue?',
        startFirewallHelper:
            'After the firewall is enabled, the server security can be better protected. Do you want to continue?',
        noPing: 'Disable ping',
        noPingTitle: 'Disable ping',
        noPingHelper: `This will disable ping, and the server won't echo ICMP response. Do you want to continue?`,
        onPingHelper: 'This will enable ping, and hackers may discover your server. Do you want to continue?',
        changeStrategy: 'Change the {0} strategy',
        changeStrategyIPHelper1:
            'Change the IP address strategy to [deny]. After the IP address is set, access to the server is prohibited. Do you want to continue?',
        changeStrategyIPHelper2:
            'Change the IP address strategy to [allow]. After the IP address is set, normal access is restored. Do you want to continue?',
        changeStrategyPortHelper1:
            'Change the port policy to [drop]. After the port policy is set, external access is denied. Do you want to continue?',
        changeStrategyPortHelper2:
            'Change the port policy to [accept]. After the port policy is set, normal port access will be restored. Do you want to continue?',
        stop: 'Stop',
        portFormatError: 'This field must be a valid port.',
        portHelper1: 'Multiple ports, e.g. 8080 and 8081',
        portHelper2: 'Range port, e.g. 8080-8089',
        changeStrategyHelper:
            'Change [{1}] {0} strategy to [{2}]. After setting, {0} will access {2} externally. Do you want to continue?',
        portHelper: 'Multiple ports can be entered, e.g. 80,81, or range ports, e.g. 80-88',
        strategy: 'Strategy',
        accept: 'Accept',
        drop: 'Drop',
        anyWhere: 'Any',
        address: 'Specified IPs',
        addressHelper: 'Support IP address or IP segment',
        allow: 'Allow',
        deny: 'Deny',
        addressFormatError: 'This field must be a valid IP address.',
        addressHelper1: 'Support IP address or IP range. For example, "************" or "17*********/24".',
        addressHelper2: 'For multiple IP addresses, separate with comma. For example, "************, **********/24".',
        allIP: 'All IP',
        portRule: 'Rule | Rules',
        createPortRule: '@:commons.button.create @.lower:firewall.portRule',
        forwardRule: 'Port-Forward rule | Port-Forward rules',
        createForwardRule: '@:commons.button.create @:firewall.forwardRule',
        ipRule: 'IP rule | IP rules',
        createIpRule: '@:commons.button.create @:firewall.ipRule',
        userAgent: 'User-Agent filter',
        sourcePort: 'Source port',
        targetIP: 'Destination IP',
        targetPort: 'Destination port',
        forwardHelper1: 'If you want to forward to the local port, the destination IP should be set to "127.0.0.1".',
        forwardHelper2: 'Leave the destination IP blank to forward to the local port.',
        forwardHelper3: 'Only support IPv4 port forwarding.',
    },
    runtime: {
        runtime: 'Runtime',
        workDir: 'Working directory',
        create: 'Create',
        localHelper: 'The local operating environment needs to be installed by itself',
        versionHelper: 'PHP version, e.g. v8.0',
        buildHelper: `If more extensions are selected, the CPU usage will be higher during the image creation process. Avoid selecting all extensions.`,
        openrestyWarn: 'PHP needs to be upgraded to OpenResty to version ******** or later to use',
        toupgrade: 'To Upgrade',
        edit: 'Edit runtime',
        extendHelper: `If the extensions you need are not in the list, you can manually input the extension name. For example, input "sockets", then select the first one.`,
        rebuildHelper: 'After editing the extension, you need to rebuild the PHP application to take effect',
        rebuild: 'Rebuild PHP App',
        source: 'PHP extension source',
        ustc: 'University of Science and Technology of China',
        netease: 'Netease',
        aliyun: 'Alibaba Cloud',
        tsinghua: 'Tsinghua University',
        xtomhk: 'XTOM Mirror Station (Hong Kong)',
        xtom: 'XTOM Mirror Station (Global)',
        phpsourceHelper: 'Choose a proper source according to your network environment.',
        appPort: 'App port',
        externalPort: 'External port',
        packageManager: 'Package manager',
        codeDir: 'Code directory',
        appPortHelper: 'The port used by the application.',
        externalPortHelper: 'The port exposed to the outside world.',
        runScript: 'Run script',
        runScriptHelper: 'The startup command list is parsed from the package.json file in the source directory.',
        open: 'Open',
        operatorHelper:
            'The {0} operation will be performed on the selected operating environment. Do you want to continue? ',
        taobao: 'Taobao',
        tencent: 'Tencent',
        imageSource: 'Image source',
        moduleManager: 'Module Management',
        module: 'Module',
        nodeOperatorHelper:
            'Is {0} {1} module? The operation may cause abnormality in the operating environment, please confirm before proceeding',
        customScript: 'Custom startup command',
        customScriptHelper: 'Provide a full startup command. For example, "npm run start".',
        portError: `Don't repeat the same port.`,
        systemRestartHelper: 'Status description: Interruption - status acquisition failed due to system restart',
        javaScriptHelper: 'Provide a full startup command. For example, "java -jar halo.jar -Xmx1024M -Xms256M".',
        javaDirHelper: 'The directory must contain jar files, subdirectories are also acceptable',
        goHelper: 'Provide a full startup command. For example, "go run main.go" or "./main".',
        goDirHelper: 'The directory or subdirectory must contain Go or binary files.',
        extension: 'Extension',
        installExtension: 'Do you confirm to install the extension {0}',
        loadedExtension: 'Loaded Extension',
        popularExtension: 'Popular Extension',
        uninstallExtension: 'Are you sure you want to uninstall the extension {0}',
        phpConfigHelper:
            'Modifying the configuration requires restarting the operating environment, do you want to continue',
        operateMode: 'operation mode',
        dynamic: 'dynamic',
        static: 'static',
        ondemand: 'on-demand',
        dynamicHelper:
            'dynamically adjust the number of processes, high flexibility, suitable for websites with large traffic fluctuations or low memory',
        staticHelper:
            'fixed number of processes, suitable for websites with high concurrency and stable traffic, high resource consumption',
        ondemandHelper:
            'processes are started and destroyed on demand, resource utilization is optimal, but the initial response may be slow',
        max_children: 'maximum number of processes allowed to be created',
        start_servers: 'number of processes created at startup',
        min_spare_servers: 'minimum number of idle processes',
        max_spare_servers: 'maximum number of idle processes',
        envKey: 'Name',
        envValue: 'Value',
        environment: 'Environment Variable',
        pythonHelper:
            'Provide a full startup command. For example, "pip install -r requirements.txt && python manage.py runserver 0.0.0.0:5000".',
        dotnetHelper: 'Provide a full startup command. For example, "dotnet MyWebApp.dll".',
        dirHelper: 'Note: Please fill in the directory path inside the container',
        concurrency: 'Concurrency Scheme',
        loadStatus: 'Load Status',
    },
    process: {
        pid: 'Process ID',
        ppid: 'Parent process ID',
        numThreads: 'Threads',
        memory: 'Memory',
        diskRead: 'Disk read',
        diskWrite: 'Disk write',
        netSent: 'uplink',
        netRecv: 'downstream',
        numConnections: 'Connections',
        startTime: 'Start time',
        running: 'Running',
        sleep: 'sleep',
        stop: 'stop',
        idle: 'idle',
        zombie: 'zombie process',
        wait: 'waiting',
        lock: 'lock',
        blocked: 'blocked',
        cmdLine: 'Start command',
        basic: 'Basic',
        mem: 'Memory',
        openFiles: 'Open files',
        env: 'Environments',
        noenv: 'None',
        net: 'Network connections',
        laddr: 'Local address/port',
        raddr: 'Remote address/port',
        stopProcess: 'End',
        viewDetails: 'View details',
        stopProcessWarn: 'Are you sure you want to end this process (PID:{0})?',
        processName: 'Process name',
    },
    tool: {
        supervisor: {
            loadStatusErr: 'Failed to retrieve process status, please check the status of the supervisor service.',
            notSupport: 'Supervisor service not detected, please go to the script library page to install it manually',
            list: 'Daemon process',
            config: 'Supervisor configuration',
            primaryConfig: 'Main configuration file location',
            notSupportCtl: `The supervisorctl isn't detected, please go to the script library page to install it manually`,
            user: 'User',
            command: 'Command',
            dir: 'Directory',
            numprocs: 'Number of process',
            initWarn:
                'This will modify "files" value in "[include"] section in the main configuration file. The directory of other configuration file will be: "{1Panel installation directory}/1panel/tools/supervisord/supervisor.d/".',
            operatorHelper: 'Operation {1} will be performed on {0}, continue? ',
            uptime: 'Running time',
            notStartWarn: `Supervisor isn't started. Start it first.`,
            serviceName: 'Service name',
            initHelper:
                'Supervisor service is detected but not initialized. Please click the initialization button in the top status bar to configure it.',
            serviceNameHelper: 'Supervisor service name managed by systemctl, usually supervisor or supervisord',
            restartHelper:
                'This will restart the service after initialization, which causes all the existing daemon processes to stop.',
            RUNNING: 'Running',
            STOPPED: 'Stopped',
            STOPPING: 'Stopping',
            STARTING: 'Starting',
            FATAL: 'Failed to start',
            BACKOFF: 'Start exception',
            ERROR: 'Error',
            statusCode: 'Status code',
            manage: 'Management',
            autoRestart: 'Auto Restart',
            EXITED: 'Exited',
            autoRestartHelper: 'Whether to automatically restart the program after it crashes',
            autoStart: 'Auto Start',
            autoStartHelper: 'Whether to automatically start the service after Supervisor starts',
        },
    },
    xpack: {
        expiresTrialAlert:
            'Friendly reminder: Your Pro trial will expire in {0} days, and all Pro features will no longer be accessible. Please renew or upgrade to the full version in a timely manner.',
        expiresAlert:
            'Friendly reminder: Your Pro license will expire in {0} days, and all Pro features will no longer be accessible. Please renew promptly to ensure continued usage.',
        menu: 'Pro',
        upage: 'AI Website Builder',
        app: {
            app: 'APP',
            title: 'Panel Alias',
            titleHelper: 'The panel alias is used for display in the APP (default panel alias)',
            qrCode: 'QR Code',
            apiStatusHelper: 'The Panel APP needs to enable the API interface feature',
            apiInterfaceHelper:
                'Supports panel API interface access (this feature needs to be enabled for the panel app)',
            apiInterfaceHelper1:
                "Panel app access requires adding the visitor to the whitelist; for non-fixed IPs, it's recommended to add 0.0.0.0/0 (all IPv4), ::/0 (all IPv6)",
            qrCodeExpired: 'Refresh time',
            apiLeakageHelper: 'Do not disclose the QR code. Ensure it is used only in trusted environments.',
        },
        waf: {
            name: 'WAF',
            blackWhite: 'Black and White List',
            globalSetting: 'Global Settings',
            websiteSetting: 'Website Settings',
            blockRecords: 'Blocked Records',
            world: 'World',
            china: 'China',
            intercept: 'Interception',
            request: 'Requests',
            count4xx: '4xx Quantity',
            count5xx: '5xx Quantity',
            todayStatus: "Today's Status",
            reqMap: 'Attack Map (Last 30 days)',
            resource: 'Source',
            count: 'Quantity',
            hight: 'High',
            low: 'Low',
            reqCount: 'Requests',
            interceptCount: 'Interception Number',
            requestTrends: 'Request Trends (Last 7 Days)',
            interceptTrends: 'Interception Trends (Last 7 Days)',
            whiteList: 'Whitelist',
            blackList: 'Blacklist',
            ipBlackListHelper: 'IP addresses in the blacklist are blocked from accessing the website',
            ipWhiteListHelper: 'IP addresses in the whitelist bypass all restrictions',
            uaBlackListHelper: 'Requests with User-Agent values in the blacklist will be blocked',
            uaWhiteListHelper: 'Requests with User-Agent values in the whitelist bypass all restrictions',
            urlBlackListHelper: 'Requests to URLs in the blacklist will be blocked',
            urlWhiteListHelper: 'Requests to URLs in the whitelist bypass all restrictions',
            ccHelper:
                'If a website receives more than {1} requests from the same IP within {0} seconds, the IP will be blocked for {2}',
            blockTime: 'Blocking Duration',
            attackHelper: 'If cumulative interceptions exceed {1} within {0} seconds, the IP will be blocked for {2}',
            notFoundHelper:
                'If cumulative requests return 404 errors more than {1} times within {0} seconds, the IP will be blocked for {2}',
            frequencyLimit: 'Frequency Limit',
            regionLimit: 'Region Limit',
            defaultRule: 'Default Rules',
            accessFrequencyLimit: 'Access Frequency Limit',
            attackLimit: 'Attack Frequency Limit',
            notFoundLimit: '404 Frequency Limit',
            urlLimit: 'URL Frequency Limit',
            urlLimitHelper: 'Set access frequency for a single URL',
            sqliDefense: 'SQL Injection Protection',
            sqliHelper: 'Detect SQL injection in requests and block them',
            xssHelper: 'Detect XSS in requests and block them',
            xssDefense: 'XSS Protection',
            uaDefense: 'Malicious User-Agent Rules',
            uaHelper: 'Includes rules to identify common malicious bots',
            argsDefense: 'Malicious Parameter Rules',
            argsHelper: 'Blocks requests containing malicious parameters',
            cookieDefense: 'Malicious Cookie Rules',
            cookieHelper: 'Prohibit malicious cookies from being carried in requests',
            headerDefense: 'Malicious Header Rules',
            headerHelper: 'Prohibit requests from containing malicious headers',
            httpRule: 'HTTP Request Method Rules',
            httpHelper:
                'Set the method types that are allowed to access. If you want to restrict certain types of access, please turn off this type of button. For example: only GET type access is allowed, then you need to turn off other types of buttons except GET',
            geoRule: 'Regional Access Restrictions',
            geoHelper:
                'Restrict access to your website from certain regions, for example: if access is allowed from mainland China, then requests from outside mainland China will be blocked',
            ipLocation: 'IP Location',
            action: 'Action',
            ruleType: 'Attack Type',
            ipHelper: 'Enter the IP address',
            attackLog: 'Attack Log',
            rule: 'Rule',
            ipArr: 'IPV4 Range',
            ipStart: 'Start IP',
            ipEnd: 'End IP',
            ipv4: 'IPv4',
            ipv6: 'IPv6',
            urlDefense: 'URL Rules',
            urlHelper: 'Forbidden URL',
            dirFilter: 'Directory Filter',
            sqlInject: 'SQL Injection',
            xss: 'XSS',
            phpExec: 'PHP Script Execution',
            oneWordTrojan: 'One word Trojan',
            appFilter: 'Dangerous Directory Filtering',
            webshell: 'Webshell',
            args: 'Malicious Parameters',
            protocolFilter: 'Protocol Filter',
            javaFilter: 'Java Dangerous File Filtering',
            scannerFilter: 'Scanner Filter',
            escapeFilter: 'Escape Filter',
            customRule: 'Custom Rules',
            httpMethod: 'HTTP Method Filter',
            fileExt: 'File Upload Limit',
            fileExtHelper: 'Prohibited file extensions for upload',
            deny: 'Forbidden',
            allow: 'Allow',
            field: 'Object',
            pattern: 'Condition',
            ruleContent: 'Content',
            contain: 'include',
            equal: 'equal',
            regex: 'regular expression',
            notEqual: 'Not equal to',
            customRuleHelper: 'Take actions based on specified conditions',
            actionAllow: 'Allow',
            blockIP: 'Block IP',
            code: 'Return Status Code',
            noRes: 'Disconnect (444)',
            badReq: 'Invalid Parameters (400)',
            forbidden: 'Access Forbidden (403)',
            serverErr: 'Server Error (500)',
            resHtml: 'Response Page',
            allowHelper: 'Allowing access will skip subsequent WAF rules, please use with caution',
            captcha: 'human-machine verification',
            fiveSeconds: '5-Seconds verification',
            location: 'Region',
            redisConfig: 'Redis Configuration',
            redisHelper: 'Enable Redis to persist temporarily blocked IPs',
            wafHelper: 'All websites will lose protection after closing',
            attackIP: 'Attacking IP',
            attackParam: 'Attack Details',
            execRule: 'Hit Rule',
            acl: 'ACL',
            sql: 'SQL Injection',
            cc: 'Access Frequency Limit',
            isBlocking: 'Blocked',
            isFree: 'Unblocked',
            unLock: 'Unlock',
            unLockHelper: 'Do you want to unblock IP: {0}?',
            saveDefault: 'Save Default',
            saveToWebsite: 'Apply to Website',
            saveToWebsiteHelper: 'Apply current settings to all websites? ',
            websiteHelper:
                'Here are the default settings for creating a website. Modifications need to be applied to the website to take effect',
            websiteHelper2:
                'Here are the default settings for creating a website. Please modify the specific configuration at the website',
            ipGroup: 'IP Group',
            ipGroupHelper:
                'One IP or IP segment per line, supports IPv4 and IPv6, for example: *********** or ***********/24',
            ipBlack: 'IP blacklist',
            openRestyAlert: 'OpenResty version needs to be higher than {0}',
            initAlert:
                'Initialization is required for first-time use, the website configuration file will be modified, and the original WAF configuration will be lost. Please be sure to back up OpenResty in advance',
            initHelper:
                'The initialization operation will clear the existing WAF configuration. Are you sure you want to initialize? ',
            mainSwitch: 'Main Switch',
            websiteAlert: 'Please create a website first',
            defaultUrlBlack: 'URL Rules',
            htmlRes: 'Intercept Page',
            urlSearchHelper: 'Please enter the URL to support fuzzy search',
            toCreate: 'Create',
            closeWaf: 'Close WAF',
            closeWafHelper: 'Closing WAF will cause the website to lose protection, do you want to continue',
            addblack: 'Black',
            addwhite: 'Add white',
            addblackHelper: 'Add IP:{0} to the default blacklist?',
            addwhiteHelper: 'Add IP:{0} to the default whitelist?',
            defaultUaBlack: 'User-Agent rule',
            defaultIpBlack: 'Malicious IP Group',
            cookie: 'Cookie Rules',
            urlBlack: 'URL Blacklist',
            uaBlack: 'User-Agent blacklist',
            attackCount: 'Attack frequency limit',
            fileExtCheck: 'File upload limit',
            geoRestrict: 'Regional access restriction',
            attacklog: 'Interception Record',
            unknownWebsite: 'Unauthorized domain name access',
            geoRuleEmpty: 'Region cannot be empty',
            unknown: 'Website Not Exist',
            geo: 'Region Restriction',
            revertHtml: 'Do you want to restore {0} as the default page?',
            five_seconds: '5-Second verification',
            header: 'Header rules',
            methodWhite: 'HTTP rules',
            expiryDate: 'Expiration date',
            expiryDateHelper:
                'After passing the verification, it will no longer be verified within the validity period',
            defaultIpBlackHelper: 'Some malicious IPs collected from the Internet to prevent access',
            notFoundCount: '404 Frequency Limit',
            matchValue: 'Match value',
            headerName: 'Supports non-special characters starting with English, numbers, -, length 3-30',
            cdnHelper: 'Websites using CDN can open here to obtain the correct source IP',
            clearLogWarn: 'Clearing the log will not be possible, do you want to continue?',
            commonRuleHelper: 'Rule is fuzzy matching',
            blockIPHelper:
                'Blocked IPs are temporarily stored in OpenResty and will be unblocked when you restart OpenResty. They can be permanently blocked through the blocking function',
            addWhiteUrlHelper: 'Add URL {0} to the whitelist?',
            dashHelper: 'The community version can also use the functions in global settings and website settings',
            wafStatusHelper: 'WAF is not enabled, please enable it in global settings',
            ccMode: 'Mode',
            global: 'Global Mode',
            uriMode: 'URL Mode',
            globalHelper:
                'Global Mode: Triggered when the total number of requests to any URL within a unit of time exceeds the threshold',
            uriModeHelper:
                'URL Mode: Triggered when the number of requests to a single URL within a unit of time exceeds the threshold',

            ip: 'IP BlackList',
            globalSettingHelper:
                'Settings with the [Website] tag need to be enabled in [Website Settings], and global settings are only the default settings for newly created websites',
            globalSettingHelper2:
                'Settings need to be enabled in both [Global Settings] and [Website Settings] at the same time',
            urlCCHelper: 'More than {1} requests to this URL within {0} seconds, blocking this IP {2}',
            urlCCHelper2: 'URL cannot contain parameters',
            notContain: 'Not contain',
            urlcc: 'URL frequency limit',
            method: 'Request type',
            addIpsToBlock: 'Batch block IP',
            addUrlsToWhite: 'Batch add URL to white list',
            noBlackIp: 'IP is already blocked, no need to block again',
            noWhiteUrl: 'URL is already in the white list, no need to add again',
            spiderIpHelper:
                'Includes Baidu, Bing, Google, 360, Shenma, Sogou, ByteDance, DuckDuckGo, Yandex. Closing this will block all spider access.',
            spiderIp: 'Spider IP Pool',
            geoIp: 'IP Address Library',
            geoIpHelper: 'Used to confirm the geolocation of the IP',
            stat: 'Attack Report',
            statTitle: 'Report',
            attackIp: 'IP',
            attackCountNum: 'Counts',
            percent: 'Percentage',
            addblackUrlHelper: 'Whether to add URL: {0} to the default blacklist?',
            rce: 'Remote Code Execution',
            software: 'Software',
            cveHelper: 'Contains vulnerabilities of common software and frameworks',
            vulnCheck: 'Supplementary Rules',
            ssrf: 'SSRF Vulnerability',
            afr: 'Arbitrary File Read',
            ua: 'Unauthorized Access',
            id: 'Information Disclosure',
            aa: 'Authentication Bypass',
            dr: 'Directory Traversal',
            xxe: 'XXE Vulnerability',
            suid: 'Serialization Vulnerability',
            dos: 'Denial of Service Vulnerability',
            afd: 'Arbitrary File Download',
            sqlInjection: 'SQL Injection',
            afw: 'Arbitrary File Write',
            il: 'Information Leak',
            clearAllLog: 'Clear all logs',
            exportLog: 'Export logs',
            appRule: 'Application Rules',
            appRuleHelper:
                'Common application rules, enabling can reduce false positives, one website can only use one rule',
            logExternal: 'Exclude Record Types',
            ipWhite: 'IP White List',
            urlWhite: 'URL White List',
            uaWhite: 'User-Agent White List',
            logExternalHelper:
                'Excluded record types will not be recorded in logs, blacklist/whitelist, regional access restrictions, and custom rules will generate a lot of logs, it is recommended to exclude',
            ssti: 'SSTI Attack',
            crlf: 'CRLF Injection',
            strict: 'Strict Mode',
            strictHelper: 'Use stricter rules to validate requests',
            saveLog: 'Save Log',
            remoteURLHelper: 'The remote URL needs to ensure one IP per line and no other characters',
            notFound: 'Not Found (404)',
            serviceUnavailable: 'Service Unavailable (503)',
            gatewayTimeout: 'Gateway Timeout (504)',
            belongToIpGroup: 'Belongs to IP Group',
            notBelongToIpGroup: 'Does not belong to IP Group',
            unknownWebsiteKey: 'Unknown Domain',
            special: 'Special',
        },
        monitor: {
            name: 'Website Monitoring',
            pv: 'Page Views',
            uv: 'Unique Visitors',
            flow: 'Traffic Flow',
            ip: 'IP',
            spider: 'Spider',
            visitors: 'Visitor Trends',
            today: 'Today',
            last7days: 'Last 7 Days',
            last30days: 'Last 30 Days',
            uvMap: 'Visitor Map (30th)',
            qps: 'Real-time Requests (per minute)',
            flowSec: 'Real-time Traffic (per minute)',
            excludeCode: 'Exclude Status Codes',
            excludeUrl: 'Exclude URLs',
            excludeExt: 'Exclude Extensions',
            cdnHelper: 'Obtain the real IP from the CDN-provided Header',
            reqRank: 'Visit Ranking',
            refererDomain: 'Referrer Domain',
            os: 'System',
            browser: 'Browser/Client',
            device: 'Device',
            showMore: 'More',
            unknown: 'Other',
            pc: 'Computer',
            mobile: 'Mobile Device',
            wechat: 'WeChat',
            machine: 'machine',
            tencent: 'Tencent Browser',
            ucweb: 'UC Browser',
            '2345explorer': '2345 browser',
            huaweibrowser: 'Huawei Browser',
            log: 'Request Logs',
            statusCode: 'Status Code',
            requestTime: 'Response Time',
            flowRes: 'Response Traffic',
            method: 'Request Method',
            statusCodeHelper: 'Enter the status code above',
            statusCodeError: 'Invalid status code type',
            methodHelper: 'Enter the request method above',
            all: 'All',
            baidu: 'Baidu',
            google: 'Google',
            bing: 'Bing',
            bytes: 'Today headlines',
            sogou: 'Sogou',
            failed: 'Error',
            ipCount: 'IP Count',
            spiderCount: 'Spider Requests',
            averageReqTime: 'Average Response Time',
            totalFlow: 'Total Flow',
            logSize: 'Log File Size',
            realIPType: 'Real IP acquisition method',
            fromHeader: 'Get from HTTP Header',
            fromHeaders: 'Get from Header list',
            header: 'HTTP Header',
            cdnConfig: 'CDN Configuration',
            xff1: 'First-level Proxy from X-Forwarded-For',
            xff2: 'Second-level Proxy from X-Forwarded-For',
            xff3: 'Third-level Proxy from X-Forwarded-For',
            xffHelper:
                'For example: X-Forwarded-For: <client>,<proxy1>,<proxy2>,<proxy3> The upper level proxy will take the last IP <proxy3>',
            headersHelper:
                'Obtain the real IP from commonly used CDN HTTP headers, selecting the first available value',
            monitorCDNHelper:
                'Modifying the CDN configuration for website monitoring will also update the WAF CDN settings',
            wafCDNHelper: 'Modifying the WAF CDN configuration will also update the website monitoring CDN settings',
            statusErr: 'Invalid status code format',
            shenma: 'Shenma Search',
            duckduckgo: 'DuckDuckGo',
            '360': '360 Search',
            excludeUri: 'Exclude URIs',
            top100Helper: 'Show the top 100 data',
            logSaveDay: 'Log Retention Period (days)',
            cros: 'Chrome OS',
            theworld: 'TheWorld Browser',
            edge: 'Microsoft Edge',
            maxthon: 'Maxthon Browser',
            monitorStatusHelper: 'Monitoring is not enabled, please enable it in settings',
            excludeIp: 'Exclude IP Addresses',
            excludeUa: 'Exclude User-Agent',
            remotePort: 'Remote Port',
            unknown_browser: 'Unknown',
            unknown_os: 'Unknown',
            unknown_device: 'Unknown',
            logSaveSize: 'Maximum Log Save Size',
            logSaveSizeHelper: 'This is the log save size for a single website',
            '360se': '360 Security Browser',
            websites: 'Website List',
            trend: 'Trend Statistics',
            reqCount: 'Request Count',
            uriHelper: 'You can use /test/* or /*/index.php to exclude Uri',
        },
        tamper: {
            tamper: 'Website Tamper Protection',
            ignoreTemplate: 'Exclude Directory Template',
            protectTemplate: 'Protect File Template',
            templateContent: 'Template Content',
            template: 'Template',
            tamperHelper1:
                'For one-click deployment type websites, it is recommended to enable the application directory tamper protection feature; if the website is not functioning properly or there are backup and recovery failures, please first disable the tamper protection feature;',
            tamperHelper2:
                'Operations for reading, writing, deleting, changing permissions, and modifying owners of protected files outside the excluded directories will be restricted',
            tamperPath: 'Protected Directory',
            tamperPathEdit: 'Edit Path',
            log: 'Intercept Log',
            totalProtect: 'Total Protection',
            todayProtect: 'Today’s Protection',
            addRule: 'Add Rule',
            ignore: 'Exclude Directory',
            ignoreHelper: 'One per line, e.g.: \ntmp\n./tmp',
            ignoreTemplateHelper: 'Add folder names to ignore, separated by commas, e.g.: tmp,cache',
            templateRule: 'Length 1-512, name cannot contain symbols like {0}',
            ignoreHelper1: 'Add folder names or specific paths to ignore',
            ignoreHelper2: 'To ignore a specific folder, use a relative path starting with ./',
            protect: 'Protect File',
            protectHelper: 'One per line, e.g.: \npng\n./test.css',
            protectTemplateHelper: 'Add file names or extensions to ignore, separated by commas, e.g.: conf,.css',
            protectHelper1: 'You can specify file names, extensions, or specific files to protect',
            protectHelper2: 'To protect a specific file, use a relative path starting with ./',
            enableHelper:
                'The tamper protection feature for the following websites will be enabled to enhance website security. Do you want to continue?',
            disableHelper:
                'The tamper protection feature for the following websites will be disabled. Do you want to continue?',
        },
        setting: {
            setting: 'Panel Settings',
            title: 'Panel Description',
            titleHelper:
                'Will be displayed on the user login page (e.g. Linux server operation and maintenance management panel, recommended 8-15 characters)',
            logo: 'Logo (Without Text)',
            logoHelper:
                'Will be displayed in the upper left corner of the management page when the menu is collapsed (recommended image size: 82px*82px)',
            logoWithText: 'Logo (With Text)',
            logoWithTextHelper:
                'Will be displayed in the upper left corner of the management page when the menu is expanded (recommended image size: 185px*55px)',
            favicon: 'Website Icon',
            faviconHelper: 'Website icon (recommended image size: 16px*16px)',
            reUpload: 'Choose File',
            setDefault: 'Restore Default',
            setHelper: 'The current settings will be saved. Do you want to continue?',
            setDefaultHelper: 'All panel settings will be restored to default. Do you want to continue?',
            logoGroup: 'Logo',
            imageGroup: 'Image',
            loginImage: 'Login Page Image',
            loginImageHelper: 'Displayed on the login page (recommended image size: 500*416px)',
            loginBgType: 'Login Page Background Type',
            loginBgImage: 'Login Page Background Image',
            loginBgImageHelper: 'Displayed as background image on the login page (recommended image size: 1920*1080px)',
            loginBgColor: 'Login Page Background Color',
            loginBgColorHelper: 'Displayed as background color on the login page',
            image: 'Image',
            bgColor: 'Background Color',
            loginGroup: 'Login Page',
            loginBtnLinkColor: 'Button/Link Color',
            loginBtnLinkColorHelper: 'Will be displayed as the button/link color on the login page',
        },
        helper: {
            wafTitle1: 'Interception Map',
            wafContent1: 'Displays the geographical distribution of interceptions over the past 30 days',
            wafTitle2: 'Regional Access Restrictions',
            wafContent2: 'Restrict website access sources according to geographical locations',
            wafTitle3: 'Custom interception page',
            wafContent3: 'Create a custom page to display after a request is intercepted',
            wafTitle4: 'Custom Rules (ACL)',
            wafContent4: 'Intercept requests according to custom rules',

            tamperTitle1: 'File Integrity Monitoring',
            tamperContent1:
                'Monitor the integrity of website files, including core files, script files, and configuration files.',
            tamperTitle2: 'Real-time Scanning and Detection',
            tamperContent2: 'Detect abnormal or tampered files by real-time scanning the website file system.',
            tamperTitle3: 'Security Permission Settings',
            tamperContent3:
                'Restrict access to website files through proper permission settings and access control policies, reducing potential attack surface.',
            tamperTitle4: 'Logging and Analysis',
            tamperContent4:
                'Record file access and operation logs for subsequent auditing and analysis by administrators, as well as to identify potential security threats.',

            settingTitle1: 'Custom Welcome Message',
            settingContent1: 'Set a custom welcome message on the 1Panel login page.',
            settingTitle2: 'Custom Logo',
            settingContent2: 'Allow uploading logo images containing brand names or other text.',
            settingTitle3: 'Custom Website Icon',
            settingContent3:
                'Allow uploading custom icons to replace the default browser icon, improving user experience.',

            monitorTitle1: 'Visitor Trend',
            monitorContent1: 'Statistics and displays website visitor trends',
            monitorTitle2: 'Visitor Map',
            monitorContent2: 'Statistics and displays the geographical distribution of visitors to the website',
            monitorTitle3: 'Access Statistics',
            monitorContent3:
                'Statistics on website request information, including spiders, access devices, request status, etc.',
            monitorTitle4: 'Real-time monitoring',
            monitorContent4:
                'Real-time monitoring of website request information, including number of requests, traffic, etc.',

            alertTitle1: 'SMS Alerts',
            alertContent1:
                'When abnormal server resource usage, website and certificate expiration, new version update, password expiration, etc. occur, users will be notified via SMS alarm to ensure timely processing.',
            alertTitle2: 'Alert log',
            alertContent2:
                'Provide users with the function of viewing alarm logs to facilitate tracking and analysis of historical alarm events.',
            alertTitle3: 'Alert Settings',
            alertContent3:
                'Provide users with custom phone numbers, daily push frequency, and daily push time configurations, making it easier for users to set up more reasonable push alerts.',

            nodeTitle1: 'One-Click Node Addition',
            nodeContent1: 'Quickly integrate multiple server nodes',
            nodeTitle2: 'Batch Upgrade',
            nodeContent2: 'Synchronize and upgrade all nodes with one operation',
            nodeTitle3: 'Node Status Monitoring',
            nodeContent3: "Real-time monitoring of each node's operational status",
            nodeTitle4: 'Quick Remote Connection',
            nodeContent4: 'One-click direct connection to node remote terminals',

            fileExchangeTitle1: 'Key Authentication Transmission',
            fileExchangeContent1: 'Authenticate via SSH keys to ensure transmission security.',
            fileExchangeTitle2: 'Efficient File Synchronization',
            fileExchangeContent2:
                'Only synchronize changed content to significantly improve transmission speed and stability.',
            fileExchangeTitle3: 'Support Multi-Node Intercommunication',
            fileExchangeContent3:
                'Easily transfer project files between different nodes, flexible management of multiple servers.',

            appTitle1: 'Flexible Panel Management',
            appContent1: 'Easily manage your 1Panel server anytime, anywhere.',
            appTitle2: 'Comprehensive Service Information',
            appContent2:
                'Manage basic applications, websites, Docker, databases, etc., and quickly create applications and websites via the mobile app.',
            appTitle3: 'Real-Time Abnormal Monitoring',
            appContent3:
                'View real-time server status, WAF security monitoring, website traffic statistics, and process health status on the mobile app.',

            clusterTitle1: 'Master-Slave Deployment',
            clusterContent1:
                'Supports creating MySQL/Postgres/Redis master-slave instances on different nodes, automatically completing master-slave association and initialization',
            clusterTitle2: 'Master-Slave Management',
            clusterContent2:
                'Unified page to centrally manage multiple master-slave nodes, view their roles, running status, etc.',
            clusterTitle3: 'Replication Status',
            clusterContent3:
                'Displays master-slave replication status and delay information, assisting in troubleshooting synchronization issues',
        },
        node: {
            master: 'Main Node',
            masterBackup: 'Master Node Backup',
            backupNode: 'Backup Node',
            backupFrequency: 'Backup Frequency (hours)',
            backupCopies: 'Backup Retention Copies',
            noBackupNode: 'The backup node is currently empty. Please select a backup node to save and try again!',
            masterBackupAlert:
                'Master node backup is not currently configured. To ensure data security, please set up a backup node as soon as possible to facilitate manual switching to a new master node in case of failure.',
            node: 'Node',
            addr: 'Address',
            nodeUnhealthy: 'Node status abnormal',
            deletedNode: 'Deleted node {0} does not currently support upgrade operations!',
            nodeUnhealthyHelper: 'Abnormal node status detected. Please check in [Node Management] and try again!',
            nodeUnbind: 'Node not bound to license',
            nodeUnbindHelper:
                'Detected that this node is not bound to a license. Please bind it in [Panel Settings - License] menu and try again!',
            memTotal: 'Total Memory',
            nodeManagement: 'Node Management',
            addNode: 'Add Node',
            connInfo: 'Connection Information',
            nodeInfo: 'Node Information',
            syncInfo: 'Sync',
            syncHelper: 'When master node data changes, it synchronizes to this child node in real-time',
            syncBackupAccount: 'Backup account settings',
            syncWithMaster:
                'After upgrading to Pro, all data will be synced by default. Sync policies can be manually adjusted in node management.',
            syncProxy: 'System proxy settings',
            syncProxyHelper: 'Syncing system proxy settings requires Docker restart',
            syncProxyHelper1: 'Restarting Docker may affect currently running container services.',
            syncProxyHelper2: 'You can manually restart in the Containers - Configuration page.',
            syncProxyHelper3:
                'Syncing system proxy settings requires Docker restart, which may affect currently running container services',
            syncProxyHelper4:
                'Syncing system proxy settings requires Docker restart. You can manually restart later in the Containers - Configuration page.',
            syncCustomApp: 'Sync Custom App Repository',
            syncAlertSetting: 'System alert settings',
            syncNodeInfo: 'Node basic data,',
            nodeSyncHelper: 'Node information synchronization will sync the following information:',
            nodeSyncHelper1: '1. Public backup account information',
            nodeSyncHelper2: '2. Connection information between the main node and sub-nodes',

            nodeCheck: 'Availability check',
            checkSSH: 'Check node SSH connection',
            checkUserPermission: 'Check node user permissions',
            isNotRoot: 'Detected that password-less sudo is not supported on this node and current user is non-root',
            checkLicense: 'Check node license status',
            checkService: 'Check existing service information on node',
            checkPort: 'Check node port reachability',
            panelExist:
                'Detected that this node is running 1Panel V1 service. Please upgrade to V2 using the migration script before adding.',
            coreExist:
                'The current node is already enabled as a master node and cannot be directly added as a slave node. Please downgrade it to a slave node first before adding, refer to the documentation for details.',
            agentExist:
                'Detected that 1panel-agent is already installed on this node. Continuing will retain existing data and only replace the 1panel-agent service.',
            oldDataExist:
                'Detected historical 1Panel V2 data on this node. The following information will be used to overwrite current settings:',
            errLicense: 'The license bound to this node is unavailable. Please check and try again!',
            errNodePort:
                'Node port [ {0} ] is detected as inaccessible. Please check if the firewall or security group has allowed this port.',

            reinstallHelper: 'Reinstall node {0}, do you want to continue?',
            unhealthyCheck: 'Abnormal Check',
            fixOperation: 'Fix Operation',
            checkName: 'Check Item',
            checkSSHConn: 'Check SSH Connection Availability',
            fixSSHConn: 'Manually edit the node to confirm connection information',
            checkConnInfo: 'Check Agent Connection Information',
            checkStatus: 'Check Node Service Availability',
            fixStatus: 'Run "systemctl status 1panel-agent.service" to check if the service is running.',
            checkAPI: 'Check Node API Availability',
            fixAPI: 'Check the node logs and verify if the firewall ports are properly opened.',
            forceDelete: 'Force Delete',
            operateHelper: 'The following nodes will undergo {0} operation, do you want to continue?',
            forceDeleteHelper: 'Force delete will ignore node deletion errors and delete database metadata',
            uninstall: 'Delete node data',
            uninstallHelper: 'This will delete all 1Panel related data of the node. Proceed with caution!',
            baseDir: 'Installation Directory',
            baseDirHelper:
                'When the installation directory is empty, it will be installed in the /opt directory by default',
            nodePort: 'Node Port',
            offline: 'Offline mode',
            freeCount: 'Free quota [{0}]',
            offlineHelper: 'Used when the node is in an offline environment',
        },
        customApp: {
            name: 'Custom App Repository',
            appStoreType: 'App Store Package Source',
            appStoreUrl: 'Repository URL',
            local: 'Local Path',
            remote: 'Remote Link',
            imagePrefix: 'Image Prefix',
            imagePrefixHelper:
                'Function: Customize the image prefix and modify the image field in the compose file. For example, when the image prefix is set to 1panel/custom, the image field for MaxKB will change to 1panel/custom/maxkb:v1.10.0',
            closeHelper: 'Cancel using custom app repository',
            appStoreUrlHelper: 'Only .tar.gz format is supported',
            postNode: 'Sync to sub-node',
            postNodeHelper:
                'Sync the custom store package to tmp/customApp/apps.tar.gz in the installation directory of the sub-node',
            nodes: 'Nodes',
            selectNode: 'Select Node',
            selectNodeError: 'Please select a node',
            licenseHelper: 'The Pro version supports the custom application repository feature',
        },
        alert: {
            isAlert: 'Alert',
            alertCount: 'Alert Count',
            clamHelper: 'Trigger alert when scanning infected files',
            cronJobHelper: 'Trigger alert when task execution fails',
            licenseHelper: 'Professional version supports SMS alert',
            alertCountHelper: 'Maximum daily alarm frequency',
            alert: 'SMS Alert',
            logs: 'Alert Logs',
            list: 'Alert List',
            addTask: 'Create Alert',
            editTask: 'Edit Alert',
            alertMethod: 'Method',
            alertMsg: 'Alert Message',
            alertRule: 'Alert Rules',
            titleSearchHelper: 'Enter alert title for fuzzy search',
            taskType: 'Type',
            ssl: 'Certificate (SSL) Expiry',
            siteEndTime: 'Website Expiry',
            panelPwdEndTime: 'Panel Password Expiry',
            panelUpdate: 'New Panel Version Available',
            cpu: 'Server CPU Alert',
            memory: 'Server Memory Alert',
            load: 'Server Load Alert',
            disk: 'Server Disk Alert',
            website: 'Website',
            certificate: 'SSL Certificate',
            remainingDays: 'Remaining Days',
            sendCount: 'Send Count',
            sms: 'SMS',
            wechat: 'WeChat',
            dingTalk: 'DingTalk',
            feiShu: 'FeiShu',
            mail: 'Email',
            weCom: 'WeCom',
            sendCountRulesHelper: 'Total alerts sent before expiry (once daily)',
            panelUpdateRulesHelper: 'Total alerts sent for new panel version (once daily)',
            oneDaySendCountRulesHelper: 'Maximum alerts sent per day',
            siteEndTimeRulesHelper: 'Websites that never expire will not trigger alerts',
            autoRenewRulesHelper:
                'Certificates with auto-renew enabled and remaining days less than 31 will not trigger alerts',
            panelPwdEndTimeRulesHelper: 'Panel password expiry alerts are unavailable if no expiration is set',
            sslRulesHelper: 'All SSL Certificates',
            diskInfo: 'Disk',
            monitoringType: 'Monitoring Type',
            autoRenew: 'Auto-Renew',
            useDisk: 'Disk Usage',
            usePercentage: 'Usage Percentage',
            changeStatus: 'Change Status',
            disableMsg:
                'Stopping the alert task will prevent this task from sending alert messages. Do you want to continue?',
            enableMsg: 'Enabling the alert task will allow this task to send alert messages. Do you want to continue?',
            useExceed: 'Usage Exceeds',
            useExceedRulesHelper: 'Trigger alert when usage exceeds the set value',
            cpuUseExceedAvg: 'The average cpu usage exceeds the specified value',
            memoryUseExceedAvg: 'The average memory usage exceeds the specified value',
            loadUseExceedAvg: 'The average load usage exceeds the specified value',
            cpuUseExceedAvgHelper: 'The average cpu usage within the specified time exceeds the specified value',
            memoryUseExceedAvgHelper: 'The average memory usage within the specified time exceeds the specified value',
            loadUseExceedAvgHelper: 'The average load usage within the specified time exceeds the specified value',
            resourceAlertRulesHelper: 'Note: Continuous alerts within 30 minutes will send only one SMS',
            specifiedTime: 'Specified Time',
            deleteTitle: 'Delete Alert',
            deleteMsg: 'Are you sure you want to delete the alert task?',

            allSslTitle: 'All Website SSL Certificate Expiry Alerts',
            sslTitle: 'SSL Certificate Expiry Alert for Website {0}',
            allSiteEndTimeTitle: 'All Website Expiry Alerts',
            siteEndTimeTitle: 'Website {0} Expiry Alert',
            panelPwdEndTimeTitle: 'Panel Password Expiry Alert',
            panelUpdateTitle: 'New Panel Version Notification',
            cpuTitle: 'High CPU Usage Alert',
            memoryTitle: 'High Memory Usage Alert',
            loadTitle: 'High Load Alert',
            diskTitle: 'High Disk Usage Alert for Mount Directory {0}',
            allDiskTitle: 'High Disk Usage Alert',

            timeRule: 'Remaining time less than {0} days (if not handled, will resend the next day)',
            panelUpdateRule:
                'Send an alert once when a new panel version is detected (if not handled, will resend the next day)',
            avgRule: 'Average {1} usage exceeds {2}% within {0} minutes, triggers alert, sends {3} times per day',
            diskRule: 'Disk usage for mount directory {0} exceeds {1}{2}, triggers alert, sends {3} times per day',
            allDiskRule: 'Disk usage exceeds {0}{1}, triggers alert, sends {2} times per day',

            cpuName: ' CPU ',
            memoryName: 'Memory',
            loadName: 'Load',
            diskName: 'Disk',

            syncAlertInfo: 'Manual Push',
            syncAlertInfoMsg: 'Do you want to manually push the alert task?',
            pushError: 'Push Failed',
            pushSuccess: 'Push Successful',
            syncError: 'Sync Failed',
            success: 'Alert Successful',
            pushing: 'Pushing...',
            error: 'Alert Failed',
            cleanLog: 'Clean Logs',
            cleanAlertLogs: 'Clean Alert Logs',
            daily: 'Daily Alert Count: {0}',
            cumulative: 'Cumulative Alert Count: {0}',
            clams: 'Virus scan',
            taskName: 'Task Name',
            cronJobType: 'Task Type',
            clamPath: 'Scan Directory',
            cronjob: 'Cronjob',
            app: 'Backup App',
            web: 'Backup Website',
            database: 'Backup Database',
            directory: 'Backup Directory',
            log: 'Backup Logs',
            snapshot: 'System Snapshot',
            clamsRulesHelper: 'Virus scanning tasks that require alerts',
            cronJobRulesHelper: 'This type of scheduled task needs to be configured',
            clamsTitle: 'Virus scan task 「 {0} 」 detected infected file alert',
            cronJobAppTitle: 'Cronjob - Backup App 「 {0} 」 Task Failure Alert',
            cronJobWebsiteTitle: 'Cronjob - Backup Website「 {0} 」Task Failure Alert',
            cronJobDatabaseTitle: 'Cronjob - Backup Database「 {0} 」Task Failure Alert',
            cronJobDirectoryTitle: 'Cronjob - Backup Directory「 {0} 」Task Failure Alert',
            cronJobLogTitle: 'Cronjob - Backup Logs「 {0} 」Task Failure Alert',
            cronJobSnapshotTitle: 'Cronjob - Backup Snapshot「 {0} 」Task Failure Alert',
            cronJobShellTitle: 'Cronjob - Shell script 「 {0} 」Task Failure Alert',
            cronJobCurlTitle: 'Cronjob - URL access「 {0} 」Task Failure Alert',
            cronJobCutWebsiteLogTitle: 'Cronjob - Cut website log「 {0} 」Task Failure Alert',
            cronJobCleanTitle: 'Cronjob - Cache cleaning「 {0} 」Task Failure Alert',
            cronJobNtpTitle: 'Cronjob - Synchronization server time「 {0} 」Task Failure Alert',
            clamsRule: 'Virus scan detected infected file alert，sent {0} times per day',
            cronJobAppRule: 'Backup app task failed alert，sent {0} times per day',
            cronJobWebsiteRule: 'Backup website task failed alert，sent {0} times per day',
            cronJobDatabaseRule: 'Backup database task failed alert，sent {0} times per day',
            cronJobDirectoryRule: 'Backup directory task failed alert，sent {0} times per day',
            cronJobLogRule: 'Backup logs task failed alert，sent {0} times per day',
            cronJobSnapshotRule: 'Backup snapshot task failed alert，sent {0} times per day',
            cronJobShellRule: 'Shell script task failed alert，sent {0} times per day',
            cronJobCurlRule: 'URL access task failed alert，sent {0} times per day',
            cronJobCutWebsiteLogRule: 'Cut website log task failed alert，sent {0} times per day',
            cronJobCleanRule: 'Cache cleaning task failed alert，sent {0} times per day',
            cronJobNtpRule: 'Synchronization server time task failed alert，sent {0} times per day',
            alertSmsHelper: 'SMS limit: total of {0} messages, {1} already used',
            goBuy: 'Purchase More',
            phone: 'Phone',
            phoneHelper: 'Provide real phone number for alert messages',
            dailyAlertNum: 'Daily Alert Limit',
            dailyAlertNumHelper: 'Maximum number of alerts per day (up to 100)',
            timeRange: 'Time Range',
            sendTimeRange: 'Send time range',
            sendTimeRangeHelper: 'Can push {0} time range',
            to: 'to',
            startTime: 'Start Time',
            endTime: 'End Time',
            defaultPhone: 'Default to license-bound account phone number',
            noticeAlert: 'Notice Alert',
            resourceAlert: 'Resource Alert',
            agentOfflineAlertHelper:
                'When offline alert is enabled for the node, the main node will scan every 30 minutes to execute alert tasks.',
            offline: 'Offline Alert',
            offlineHelper:
                'When set to offline alert, the main node will scan every 30 minutes to execute alert tasks.',
            offlineOff: 'Enable Offline Alert',
            offlineOffHelper:
                'Enabling offline alert will make the main node scan every 30 minutes to execute alert tasks.',
            offlineClose: 'Disable Offline Alert',
            offlineCloseHelper:
                'Disabling offline alert requires sub-nodes to handle alerts independently. Please ensure network connectivity to avoid alert failure.',
            alertNotice: 'Alert Notification',
            methodConfig: 'Notification Method Configuration',
            commonConfig: 'Global Configuration',
            smsConfig: 'SMS',
            smsConfigHelper: 'Configure phone numbers for SMS notifications',
            emailConfig: 'Email',
            emailConfigHelper: 'Configure SMTP email sending service',
            deleteConfigTitle: 'Delete Alert Configuration',
            deleteConfigMsg: 'Are you sure you want to delete the alert configuration?',
            test: 'Test',
            alertTestOk: 'Test notification succeeded',
            alertTestFailed: 'Test notification failed',
            displayName: 'Display Name',
            sender: 'Sender Address',
            password: 'Password',
            host: 'SMTP Server',
            port: 'Port',
            encryption: 'Encryption Method',
            recipient: 'Recipient',
            licenseTime: 'License Expiration Reminder',
            licenseTimeTitle: 'License Expiration Reminder',
            displayNameHelper: 'Sender display name for emails',
            senderHelper: 'Email address used to send messages',
            passwordHelper: 'Authorization code for the email service',
            hostHelper: 'SMTP server address, e.g., smtp.qq.com',
            portHelper: 'SSL usually uses 465, TLS usually uses 587',
            sslHelper: 'If the SMTP port is 465, SSL is usually required',
            tlsHelper: 'If the SMTP port is 587, TLS is usually required',
        },
        theme: {
            lingXiaGold: 'Ling Xia Gold',
            classicBlue: 'Classic Blue',
            freshGreen: 'Fresh Green',
            customColor: 'Custom Color',
            setDefault: 'Default',
            setDefaultHelper:
                'The theme color scheme is about to be restored to its initial state. Do you want to continue?',
            setHelper: 'The currently selected theme color scheme is about to be saved. Do you want to continue?',
        },
        exchange: {
            exchange: 'File Exchange',
            exchangeConfirm: "Do you want to transfer the file/folder {1} from {0} node to {2} node's {3} directory?",
        },
        cluster: {
            cluster: 'Application High Availability',
            name: 'Cluster Name',
            addCluster: 'Add Cluster',
            installNode: 'Install Node',
            master: 'Master Node',
            slave: 'Slave Node',
            replicaStatus: 'Master-Slave Status',
            unhealthyDeleteError: 'The installation node status is abnormal, please check the node list and try again!',
            replicaStatusError: 'Status acquisition is abnormal, please check the master node.',
            masterHostError: 'The master node IP cannot be 127.0.0.1',
        },
    },
};

export default {
    ...fit2cloudEnLocale,
    ...message,
};
