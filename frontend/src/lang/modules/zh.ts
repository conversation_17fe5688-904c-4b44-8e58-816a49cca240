import fit2cloudZhLocale from 'fit2cloud-ui-plus/src/locale/lang/zh-cn';

const message = {
    commons: {
        true: '是',
        false: '否',
        example: '例：',
        fit2cloud: '飞致云',
        lingxia: '凌霞',
        colon: '：',
        button: {
            run: '运行',
            prev: '上一步',
            next: '下一步',
            create: '创建',
            add: '添加',
            save: '保存',
            set: '设置',
            sync: '同步',
            delete: '删除',
            edit: '编辑',
            enable: '启用',
            disable: '停用',
            confirm: '确认',
            cancel: '取消',
            reset: '重置',
            setDefault: '恢复默认',
            restart: '重启',
            conn: '连接',
            disConn: '断开',
            clean: '清空',
            login: '登录',
            close: '关闭',
            stop: '关闭',
            start: '开启',
            view: '详情',
            watch: '追踪',
            handle: '执行',
            clone: '克隆',
            expand: '展开',
            collapse: '收起',
            log: '日志',
            back: '返回',
            backup: '备份',
            recover: '恢复',
            retry: '重试',
            upload: '上传',
            download: '下载',
            init: '初始化',
            verify: '验证',
            saveAndEnable: '保存并启用',
            import: '导入',
            export: '导出',
            power: '授权',
            search: '搜索',
            refresh: '刷新',
            get: '获取',
            upgrade: '升级',
            update: '更新',
            ignore: '忽略升级',
            copy: '复制',
            random: '随机密码',
            install: '安装',
            uninstall: '卸载',
            fullscreen: '网页全屏',
            quitFullscreen: '退出网页全屏',
            showAll: '显示所有',
            hideSome: '隐藏部分',
            agree: '同意',
            notAgree: '不同意',
            preview: '预览',
            open: '打开',
            notSave: '不保存',
            createNewFolder: '新建文件夹',
            createNewFile: '新建文件',
            helpDoc: '帮助文档',
            bind: '绑定',
            unbind: '解绑',
            cover: '覆盖',
            skip: '跳过',
            fix: '修复',
            down: '停止',
            up: '启动',
            sure: '确定',
            show: '显示',
            hide: '隐藏',
        },
        operate: {
            start: '启动',
            stop: '停止',
            restart: '重启',
            reload: '重载',
            rebuild: '重建',
            sync: '同步',
            up: '启动',
            down: '停止',
            delete: '删除',
        },
        search: {
            timeStart: '开始时间',
            timeEnd: '结束时间',
            timeRange: '至',
            dateStart: '开始日期',
            dateEnd: '结束日期',
        },
        table: {
            all: '所有',
            total: '共 {0} 条',
            name: '名称',
            type: '类型',
            status: '状态',
            records: '任务输出',
            group: '分组',
            default: '默认',
            createdAt: '创建时间',
            publishedAt: '发布时间',
            date: '时间',
            updatedAt: '更新时间',
            operate: '操作',
            message: '信息',
            description: '描述',
            interval: '耗时',
            user: '用户',
            title: '标题',
            port: '端口',
            forward: '转发',
            protocol: '协议',
            tableSetting: '列表设置',
            refreshRate: '刷新频率',
            noRefresh: '不刷新',
            selectColumn: '选择列',
            local: '本地',
            serialNumber: '序号',
            manageGroup: '管理分组',
            backToList: '返回列表',
            keepEdit: '继续编辑',
        },
        loadingText: {
            Upgrading: '系统升级中，请稍候...',
            Restarting: '系统重启中，请稍候...',
            Recovering: '快照恢复中，请稍候...',
            Rollbacking: '快照回滚中，请稍候...',
        },
        msg: {
            noneData: '暂无数据',
            delete: '删除 操作不可回滚，是否继续？',
            clean: '清空 操作不可回滚，是否继续？',
            closeDrawerHelper: '系统可能不会保存您所做的更改，是否继续？',
            deleteSuccess: '删除成功',
            loginSuccess: '登录成功',
            operationSuccess: '操作成功',
            copySuccess: '复制成功',
            notSupportOperation: '不支持的当前操作',
            requestTimeout: '请求超时,请稍后重试',
            infoTitle: '提示',
            notRecords: '当前任务未产生执行记录',
            sureLogOut: '您是否确认退出登录?',
            createSuccess: '创建成功',
            updateSuccess: '更新成功',
            uploadSuccess: '上传成功',
            operateConfirm: '如果确认操作，请手动输入 ',
            inputOrSelect: '请选择或输入',
            copyFailed: '复制失败',
            operatorHelper: '将对以下{0}进行 {1} 操作，是否继续？',
            backupSuccess: '备份成功',
            restoreSuccess: '备份成功',
            notFound: '抱歉，您访问的页面不存在',
            unSupportType: '不支持当前文件类型！',
            unSupportSize: '上传文件超过 {0}M，请确认！',
            fileExist: '当前文件夹已存在该文件，不支持重复上传！',
            fileNameErr: '仅支持上传名称包含英文、中文、数字或者 .-_ ,长度 1-256 位的文件',
            confirmNoNull: '请确认 {0} 值不为空',
            errPort: '错误的端口信息，请确认！',
            remove: '移出',
            backupHelper: '当前操作将对 {0} 进行备份，是否继续？',
            recoverHelper: '将从 {0} 文件进行恢复，该操作不可回滚，是否继续？',
            refreshSuccess: '刷新成功',
            rootInfoErr: '已经是根目录了',
            resetSuccess: '重置成功',
            creatingInfo: '正在创建，无需此操作',
            installSuccess: '安装成功',
            uninstallSuccess: '卸载成功',
        },
        login: {
            username: '用户名',
            password: '密码',
            welcome: '欢迎回来，请输入用户名和密码登录！',
            errorAuthInfo: '您输入的用户名或密码不正确，请重新输入！',
            errorMfaInfo: '错误的验证信息，请重试！',
            captchaHelper: '验证码',
            errorCaptcha: '验证码错误！',
            notSafe: '暂无权限访问',
            safeEntrance1: '当前环境已经开启了安全入口登录',
            safeEntrance2: '在 SSH 终端输入以下命令来查看面板入口: 1pctl user-info',
            errIP1: '当前环境已经开启了授权 IP 访问',
            errDomain1: '当前环境已经开启了访问域名绑定',
            errHelper: '可在 SSH 终端输入以下命令来重置绑定信息: ',
            codeInput: '请输入 MFA 验证器的 6 位验证码',
            mfaTitle: 'MFA 认证',
            mfaCode: 'MFA 验证码',
            title: 'Linux 服务器运维管理面板',
            licenseHelper: '《飞致云社区软件许可协议》',
            errorAgree: '请点击同意社区软件许可协议',
            agreeTitle: '服务协议及隐私保护',
            agreeContent:
                '为了更好的保障您的合法权益，请您阅读并同意以下协议 &laquo; <a href="https://www.fit2cloud.com/legal/licenses.html" target="_blank"> 飞致云社区软件许可协议 </a> &raquo;',
            logout: '退出登录',
        },
        rule: {
            username: '请输入用户名',
            password: '请输入密码',
            rePassword: '密码不一致，请检查后重新输入',
            requiredInput: '请填写必填项',
            requiredSelect: '请选择必选项',
            illegalChar: '暂不支持注入字符 & ; $ \' ` ( ) " > < |',
            illegalInput: '输入框中存在不合法字符',
            commonName: '支持非特殊字符开头,英文、中文、数字、.-和_,长度1-128',
            userName: '支持非特殊字符开头、英文、中文、数字和_,长度3-30',
            simpleName: '支持非下划线开头，英文、数字、_,长度3-30',
            simplePassword: '支持非下划线开头，英文、数字、_,长度1-30',
            dbName: '支持非特殊字符开头，英文、中文、数字、.-_,长度1-64',
            composeName: '支持非特殊字符开头，小写英文、数字、-和_,长度1-256',
            imageName: '支持非特殊字符开头、英文、数字、:@/.-_,长度1-256',
            volumeName: '支持英文、数字、.-和_,长度2-30',
            supervisorName: '支持非特殊字符开头,英文、数字、-和_,长度1-128',
            complexityPassword: '请输入长度为 8-30 位且包含字母、数字、特殊字符至少两项的密码组合',
            commonPassword: '请输入 6 位以上长度密码',
            linuxName: '长度1-128，名称不能含有{0}等符号',
            email: '请输入正确的邮箱',
            number: '请输入正确的数字',
            integer: '请输入正确的正整数',
            ip: '请输入正确的 IP 地址',
            host: '请输入正确的 IP 或者域名',
            hostHelper: '支持输入 ip 或者域名',
            port: '请输入正确的端口,1-65535',
            domain: '域名格式错误',
            databaseName: '支持英文、数字、_,长度1-30',
            numberRange: '数字范围: {0} - {1}',
            paramName: '支持英文、数字、.-和_,长度2-64',
            paramComplexity: '支持英文、数字、{0},长度6-128，特殊字符不能在首尾',
            paramUrlAndPort: '格式为 http(s)://(域名/ip):(端口)',
            nginxDoc: '仅支持英文大小写，数字，和.',
            appName: '支持英文、数字、-和_,长度2-30,并且不能以-_开头和结尾',
            containerName: '支持字母、数字、_-和.,不能以-_或.开头,长度2-128',
            mirror: '支持以 http(s):// 开头，英文大小写，数字，. / 和 - 的镜像加速地址，且不能有空行',
            disableFunction: '仅支持字母、下划线和,',
            leechExts: '仅支持字母数字和,',
            paramSimple: '支持小写字母和数字,长度1-128',
            filePermission: '权限错误',
            formatErr: '格式错误，检查后重试',
            phpExtension: '仅支持 , _ 小写英文和数字',
            paramHttp: '必须以 http:// 或 https:// 开头',
            phone: '手机号码格式不正确',
            authBasicPassword: '支持字母、数字以及常见特殊字符，长度1-72',
            length128Err: '长度不能超过128位',
            maxLength: '长度不能超过 {0} 位',
            alias: '支持英文、数字、-和_,长度1-30,并且不能以-_开头和结尾',
        },
        res: {
            paramError: '请求失败,请稍后重试!',
            forbidden: '当前用户无权限',
            serverError: '服务异常',
            notFound: '资源不存在',
            commonError: '请求失败',
        },
        service: {
            serviceNotStarted: '当前未启动 {0} 服务',
        },
        status: {
            running: '已启动',
            done: '已完成',
            scanFailed: '未完成',
            success: '成功',
            waiting: '请等待',
            failed: '失败',
            stopped: '已停止',
            error: '失败',
            created: '已创建',
            restarting: '重启中',
            uploading: '上传中',
            unhealthy: '异常',
            removing: '移除中',
            paused: '已暂停',
            exited: '已停止',
            dead: '已结束',
            installing: '安装中',
            enabled: '已启用',
            disabled: '已停止',
            normal: '正常',
            building: '制作镜像中',
            upgrading: '升级中',
            pending: '待编辑',
            rebuilding: '重建中',
            deny: '已屏蔽',
            accept: '已放行',
            used: '已使用',
            unused: '未使用',
            starting: '启动中',
            recreating: '重建中',
            creating: '创建中',
            init: '等待申请',
            ready: '正常',
            applying: '申请中',
            uninstalling: '卸载中',
            lost: '已失联',
            bound: '已绑定',
            unbind: '未绑定',
            exceptional: '异常',
            free: '空闲',
            enable: '已启用',
            disable: '已停止',
            deleted: '已删除',
            downloading: '下载中',
            packing: '打包中',
            sending: '下发中',
            healthy: '正常',
            executing: '执行中',
            installerr: '安装失败',
            applyerror: '申请失败',
            systemrestart: '中断',
            starterr: '启动失败',
            uperr: '启动失败',
        },
        units: {
            second: '秒',
            minute: '分钟',
            hour: '小时',
            day: '天',
            week: '周',
            month: '月',
            year: '年',
            time: '次',
            core: '核',
            secondUnit: '秒',
            minuteUnit: '分钟',
            hourUnit: '小时',
            dayUnit: '天',
            millisecond: '毫秒',
        },
    },
    menu: {
        home: '概览',
        apps: '应用商店',
        website: '网站',
        project: '项目',
        config: '配置',
        ssh: 'SSH 管理',
        firewall: '防火墙',
        ssl: '证书',
        database: '数据库',
        aiTools: 'AI',
        mcp: 'MCP',
        container: '容器',
        cronjob: '计划任务',
        system: '系统',
        files: '文件',
        monitor: '监控',
        terminal: '终端',
        settings: '面板设置',
        toolbox: '工具箱',
        logs: '日志审计',
        runtime: '运行环境',
        processManage: '进程管理',
        process: '进程',
        network: '网络',
        supervisor: '进程守护',
        tamper: '防篡改',
        app: '应用',
        msgCenter: '任务中心',
    },
    home: {
        recommend: '推荐',
        dir: '目录',
        restart_1panel: '重启面板',
        restart_system: '重启服务器',
        operationSuccess: '操作成功，正在重启，请稍后手动刷新浏览器！',
        entranceHelper: '设置安全入口有利于提高系统的安全性，如有需要，前往 面板设置-安全 中，启用安全入口',
        appInstalled: '已安装应用',
        systemInfo: '系统信息',
        hostname: '主机名称',
        platformVersion: '发行版本',
        kernelVersion: '内核版本',
        kernelArch: '系统类型',
        network: '流量',
        io: '磁盘 IO',
        ip: '主机地址',
        proxy: '系统代理',
        baseInfo: '基本信息',
        totalSend: '总发送',
        totalRecv: '总接收',
        rwPerSecond: '读写次数',
        ioDelay: '读写延迟',
        uptime: '启动时间',
        runningTime: '运行时间',
        mem: '系统',
        swapMem: 'Swap 分区',

        runSmoothly: '运行流畅',
        runNormal: '运行正常',
        runSlowly: '运行缓慢',
        runJam: '运行堵塞',

        core: '物理核心',
        logicCore: '逻辑核心',
        loadAverage: '最近 {0} 分钟平均负载',
        load: '负载',
        mount: '挂载点',
        fileSystem: '文件系统',
        total: '总数',
        used: '已用',
        cache: '缓存',
        free: '空闲',
        shard: '共享',
        available: '可用',
        percent: '使用率',
        goInstall: '去安装',

        networkCard: '网卡',
        disk: '磁盘',
    },
    tabs: {
        more: '更多',
        hide: '收起',
        closeLeft: '关闭左侧',
        closeRight: '关闭右侧',
        closeCurrent: '关闭当前',
        closeOther: '关闭其它',
        closeAll: '关闭所有',
    },
    header: {
        logout: '退出登录',
    },
    database: {
        manage: '管理',
        deleteBackupHelper: '同时删除数据库备份',
        delete: '删除操作无法回滚，请输入 "',
        deleteHelper: '" 删除此数据库',
        create: '创建数据库',
        noMysql: '数据库服务 (MySQL 或 MariaDB)',
        noPostgresql: '数据库服务 PostgreSQL',
        goUpgrade: '去应用列表升级',
        goInstall: '去应用商店安装',
        isDelete: '已删除',
        permission: '权限',
        permissionForIP: '指定 IP',
        permissionAll: '所有人(%)',
        localhostHelper: '将容器部署的数据库权限配置为 localhost 会导致容器外部无法访问，请谨慎选择！',
        databaseConnInfo: '连接信息',
        rootPassword: 'root 密码',
        serviceName: '服务名称',
        serviceNameHelper: '用于同一 network 下的容器间访问',
        backupList: '备份列表',
        loadBackup: '导入备份',
        remoteAccess: '远程访问',
        remoteHelper: '多个 ip 以逗号分隔，例：*************,*************',
        remoteConnHelper: 'root 帐号远程连接 MySQL 有安全风险，开启需谨慎！',
        changePassword: '改密',
        changeConnHelper: '此操作将修改当前数据库 {0}，是否继续？',
        changePasswordHelper: '当前数据库已经关联应用，修改密码将同步修改应用中数据库密码，修改后重启生效。',

        portHelper: '该端口为容器对外暴露端口，修改需要单独保存并且重启容器！',

        confChange: '配置修改',
        confNotFound: '未能找到该应用配置文件，请在应用商店升级该应用至最新版本后重试！',

        loadFromRemote: '从服务器同步',
        userBind: '绑定用户',
        pgBindHelper: '该操作用于创建新用户并将其绑定到目标数据库，暂不支持选择已存在于数据库中的用户。',
        pgSuperUser: '超级用户',
        loadFromRemoteHelper: '此操作将同步服务器上数据库信息到 1Panel，是否继续？',
        passwordHelper: '无法获取密码，请修改',
        remote: '远程',
        remoteDB: '远程服务器',
        createRemoteDB: '添加远程服务器',
        unBindRemoteDB: '解绑远程服务器',
        unBindForce: '强制解绑',
        unBindForceHelper: '忽略解绑过程中的所有错误，确保最终操作成功',
        unBindRemoteHelper: '解绑远程数据库只会删除绑定关系，不会直接删除远程数据库',
        editRemoteDB: '编辑远程服务器',
        localDB: '本地数据库',
        address: '数据库地址',
        version: '数据库版本',
        userHelper: 'root 用户或者拥有 root 权限的数据库用户',
        pgUserHelper: '拥有超级管理员权限的数据库用户',
        ssl: '使用 SSL',
        clientKey: '客户端私钥',
        clientCert: '客户端证书',
        hasCA: '拥有 CA 证书',
        caCert: 'CA 证书',
        skipVerify: '忽略校验证书可用性检测',

        formatHelper: '当前数据库字符集为 {0}，字符集不一致可能导致恢复失败',
        selectFile: '选择文件',
        dropHelper: '将上传文件拖拽到此处，或者',
        clickHelper: '点击上传',
        supportUpType: '仅支持 sql、sql.gz、tar.gz 文件',
        zipFormat: 'tar.gz 压缩包结构：test.tar.gz 压缩包内，必需包含 test.sql',

        currentStatus: '当前状态',
        baseParam: '基础参数',
        performanceParam: '性能参数',
        runTime: '启动时间',
        connections: '总连接数',
        bytesSent: '发送',
        bytesReceived: '接收',
        queryPerSecond: '每秒查询',
        txPerSecond: '每秒事务',
        connInfo: '活动/峰值连接数',
        connInfoHelper: '若值过大，增加 max_connections',
        threadCacheHit: '线程缓存命中率',
        threadCacheHitHelper: '若过低,增加 thread_cache_size',
        indexHit: '索引命中率',
        indexHitHelper: '若过低,增加 key_buffer_size',
        innodbIndexHit: 'Innodb 索引命中率',
        innodbIndexHitHelper: '若过低,增加 innodb_buffer_pool_size',
        cacheHit: '查询缓存命中率',
        cacheHitHelper: '若过低,增加 query_cache_size',
        tmpTableToDB: '创建临时表到磁盘',
        tmpTableToDBHelper: '若过大,尝试增加 tmp_table_size',
        openTables: '已打开的表',
        openTablesHelper: 'table_open_cache 配置值应大于等于此值',
        selectFullJoin: '没有使用索引的量',
        selectFullJoinHelper: '若不为0，请检查数据表的索引是否合理',
        selectRangeCheck: '没有索引的 JOIN 量',
        selectRangeCheckHelper: '若不为0，请检查数据表的索引是否合理',
        sortMergePasses: '排序后的合并次数',
        sortMergePassesHelper: '若值过大，增加sort_buffer_size',
        tableLocksWaited: '锁表次数',
        tableLocksWaitedHelper: '若值过大，请考虑增加您的数据库性能',

        performanceTuning: '性能调整',
        optimizationScheme: '优化方案',
        keyBufferSizeHelper: '用于索引的缓冲区大小',
        queryCacheSizeHelper: '查询缓存，不开启请设为0',
        tmpTableSizeHelper: '临时表缓存大小',
        innodbBufferPoolSizeHelper: 'Innodb 缓冲区大小',
        innodbLogBufferSizeHelper: 'Innodb 日志缓冲区大小',
        sortBufferSizeHelper: '* 连接数, 每个线程排序的缓冲大小',
        readBufferSizeHelper: '* 连接数, 读入缓冲区大小',
        readRndBufferSizeHelper: '* 连接数, 随机读取缓冲区大小',
        joinBufferSizeHelper: '* 连接数, 关联表缓存大小',
        threadStackelper: '* 连接数, 每个线程的堆栈大小',
        binlogCacheSizeHelper: '* 连接数, 二进制日志缓存大小(4096的倍数)',
        threadCacheSizeHelper: '线程池大小',
        tableOpenCacheHelper: '表缓存',
        maxConnectionsHelper: '最大连接数',
        restart: '重启数据库',

        slowLog: '慢日志',
        noData: '暂无慢日志...',

        isOn: '开启',
        longQueryTime: '阈值(秒)',
        thresholdRangeHelper: '请输入正确的阈值(1 - 600)',

        timeout: '超时时间(秒)',
        timeoutHelper: '空闲连接超时时间，0表示不断开',
        maxclients: '最大连接数',
        requirepassHelper: '留空代表没有设置密码，修改需要单独保存并且重启容器！',
        databases: '数据库数量',
        maxmemory: '最大内存使用',
        maxmemoryHelper: '0 表示不做限制',
        tcpPort: '当前监听端口',
        uptimeInDays: '已运行天数',
        connectedClients: '连接的客户端数量',
        usedMemory: '当前 Redis 使用的内存大小',
        usedMemoryRss: '向操作系统申请的内存大小',
        usedMemoryPeak: 'Redis 的内存消耗峰值',
        memFragmentationRatio: '内存碎片比率',
        totalConnectionsReceived: '运行以来连接过的客户端的总数量',
        totalCommandsProcessed: '运行以来执行过的命令的总数量',
        instantaneousOpsPerSec: '服务器每秒钟执行的命令数量',
        keyspaceHits: '查找数据库键成功的次数',
        keyspaceMisses: '查找数据库键失败的次数',
        hit: '查找数据库键命中率',
        latestForkUsec: '最近一次 fork() 操作耗费的微秒数',
        redisCliHelper: '未检测到 redis-cli 服务，请先启用服务！',
        redisQuickCmd: 'Redis 快速命令',

        recoverHelper: '即将使用 [{0}] 对数据进行覆盖，是否继续?',
        submitIt: '覆盖数据',

        baseConf: '基础配置',
        allConf: '全部配置',
        restartNow: '立即重启',
        restartNowHelper1: '修改配置后需要重启生效，若您的数据需要持久化请先执行 save 操作。',
        restartNowHelper: '修改配置后需要重启生效。',

        persistence: '持久化',
        rdbHelper1: '秒內,插入',
        rdbHelper2: '条数据',
        rdbHelper3: '符合任意一个条件将会触发RDB持久化',
        rdbInfo: '请确认规则列表中值在 1-100000 之间',

        containerConn: '容器连接',
        connAddress: '地址',
        containerConnHelper: 'PHP 运行环境/容器安装的应用使用此连接地址',
        remoteConn: '外部连接',
        remoteConnHelper2: '非容器环境或外部连接需使用此地址。',
        remoteConnHelper3: '默认访问地址为主机IP，修改请前往面板设置页面的「默认访问地址」配置项。',
        localIP: '本机 IP',
    },
    aiTools: {
        model: {
            model: '模型',
            create: '添加模型',
            create_helper: '拉取 "{0}"',
            ollama_doc: '您可以访问 Ollama 官网，搜索并查找更多模型。',
            container_conn_helper: '容器间访问或连接使用此地址',
            ollama_sync: '同步 Ollama 模型发现下列模型不存在，是否删除？',
            from_remote: '该模型并非通过 1Panel 下载，无相关拉取日志。',
            no_logs: '该模型的拉取日志已被删除，无法查看相关日志。',
        },
        proxy: {
            proxy: 'AI 代理增强',
            proxyHelper1: '绑定域名并开启 HTTPS，增强传输安全性',
            proxyHelper2: '限制 IP 访问，防止在公网暴露',
            proxyHelper3: '开启流式传输',
            proxyHelper4: '创建完成之后可以在网站列表中查看并管理',
            proxyHelper5: '创建完成之后可以在应用商店 - 已安装 - ollama - 参数中取消端口外部访问以提高安全性',
            proxyHelper6: '如需关闭代理配置，可以在网站列表中删除',
            whiteListHelper: '限制仅白名单中的 IP 可访问',
        },
        gpu: {
            gpu: 'GPU 监控',
            base: '基础信息',
            gpuHelper: '当前系统未检测到 NVIDIA-SMI或者XPU-SMI 指令，请检查后重试！',
            driverVersion: '驱动版本',
            cudaVersion: 'CUDA 版本',
            process: '进程信息',
            type: '类型',
            typeG: '图形',
            typeC: '计算',
            typeCG: '计算+图形',
            processName: '进程名称',
            processMemoryUsage: '显存使用',
            temperatureHelper: 'GPU 温度过高会导致 GPU 频率下降',
            performanceStateHelper: '从 P0 (最大性能) 到 P12 (最小性能)',
            busID: '总线地址',
            persistenceMode: '持续模式',
            enabled: '开启',
            disabled: '关闭',
            persistenceModeHelper: '持续模式能更加快速地响应任务，但相应待机功耗也会增加',
            displayActive: '显卡初始化',
            displayActiveT: '是',
            displayActiveF: '否',
            ecc: '是否开启错误检查和纠正技术',
            computeMode: '计算模式',
            default: '默认',
            exclusiveProcess: '进程排他',
            exclusiveThread: '线程排他',
            prohibited: '禁止',
            defaultHelper: '默认: 进程可以并发执行',
            exclusiveProcessHelper: '进程排他: 只有一个 CUDA 上下文可以使用 GPU, 但可以由多个线程共享',
            exclusiveThreadHelper: '线程排他: 只有一个线程在 CUDA 上下文中可以使用 GPU',
            prohibitedHelper: '禁止: 不允许进程同时执行',
            migModeHelper: '用于创建 MIG 实例，在用户层实现 GPU 的物理隔离。',
            migModeNA: '不支持',
            shr: '共享显存',
        },
        mcp: {
            server: 'MCP Server',
            create: '创建 MCP Server',
            edit: '编辑 MCP Server',
            commandHelper: '例如：npx -y {0}',
            baseUrl: '外部访问路径',
            baseUrlHelper: '例如：http://***********:8000',
            ssePath: 'SSE 路径',
            ssePathHelper: '例如：/sse,注意不要与其他 Server 重复',
            environment: '环境变量',
            envKey: '变量名',
            envValue: '变量值',
            externalUrl: '外部连接地址',
            operatorHelper: '将对 {0} 进行 {1} 操作，是否继续？',
            domain: '默认访问地址',
            domainHelper: '例如：*********** 或者 example.com',
            bindDomain: '绑定网站',
            commandPlaceHolder: '当前仅支持 npx 和 二进制启动的命令',
            importMcpJson: '导入 MCP Server 配置',
            importMcpJsonError: 'mcpServers 结构不正确',
            bindDomainHelper: '绑定网站之后会修改所有已安装 MCP Server 的访问地址，并关闭端口的外部访问',
            outputTransport: '输出类型',
            streamableHttpPath: '流式传输路径',
            streamableHttpPathHelper: '例如：/mcp, 注意不要与其他 Server 重复',
        },
    },
    container: {
        create: '创建容器',
        createByCommand: '命令创建',
        commandInput: '命令输入',
        commandRule: '请输入正确的 docker run 容器创建命令！',
        commandHelper: '将在服务器上执行该条命令以创建容器，是否继续？',
        edit: '编辑容器',
        updateHelper1: '检测到该容器来源于应用商店，请注意以下两点：',
        updateHelper2: '1. 当前修改内容不会同步到应用商店的已安装应用。',
        updateHelper3: '2. 如果在已安装页面修改应用，当前编辑的部分内容将失效。',
        updateHelper4: '编辑容器需要重建，任何未持久化的数据将丢失，是否继续操作？',
        containerList: '容器列表',
        operatorHelper: '将对以下容器进行 {0} 操作，是否继续？',
        operatorAppHelper:
            '将对以下容器进行 {0} 操作，\n其中部分来源于应用商店，该操作可能会影响到该服务的正常使用。\n是否继续？',
        start: '启动',
        stop: '停止',
        restart: '重启',
        kill: '强制停止',
        pause: '暂停',
        unpause: '恢复',
        rename: '重命名',
        remove: '删除',
        removeAll: '删除所有',
        containerPrune: '清理容器',
        containerPruneHelper1: '清理容器 将删除所有处于停止状态的容器。',
        containerPruneHelper2:
            '若容器来自于应用商店，在执行清理操作后，您需要前往 [应用商店] 的 [已安装] 列表，点击 [重建] 按钮进行重新安装。',
        containerPruneHelper3: '该操作无法回滚，是否继续？',
        imagePrune: '清理镜像',
        imagePruneSome: '未标签镜像',
        imagePruneSomeEmpty: '暂无待清理的未使用 none 标签镜像',
        imagePruneSomeHelper: '清理下列标签为 none 且未被任何容器使用的镜像',
        imagePruneAll: '未使用镜像',
        imagePruneAllEmpty: '暂无待清理的未使用镜像',
        imagePruneAllHelper: '清理下列未被任何容器使用的镜像',
        networkPrune: '清理网络',
        networkPruneHelper: '清理网络 将删除所有未被使用的网络，该操作无法回滚，是否继续？',
        volumePrune: '清理存储卷',
        volumePruneHelper: '清理存储卷 将删除所有未被使用的本地存储卷，该操作无法回滚，是否继续？',
        cleanSuccess: '操作成功，本次清理数量: {0} 个！',
        cleanSuccessWithSpace: '操作成功，本次清理数量: {0} 个，释放磁盘空间: {1}！',
        unExposedPort: '当前端口映射地址为 127.0.0.1，无法实现外部访问',
        upTime: '运行时长',
        fetch: '过滤',
        lines: '条数',
        linesHelper: '请输入正确的日志获取条数！',
        lastDay: '最近一天',
        last4Hour: '最近 4 小时',
        lastHour: '最近 1 小时',
        last10Min: '最近 10 分钟',
        cleanLog: '清空日志',
        downLogHelper1: '即将下载 {0} 容器所有日志，是否继续？',
        downLogHelper2: '即将下载 {0} 容器最近 {1} 条日志，是否继续？',
        cleanLogHelper: '清空日志需要重启容器，该操作无法回滚，是否继续？',
        newName: '新名称',
        workingDir: '工作目录',
        source: '资源使用率',
        cpuUsage: 'CPU 使用',
        cpuTotal: 'CPU 总计',
        core: '核心数',
        memUsage: '内存使用',
        memTotal: '内存限额',
        memCache: '缓存使用',
        ip: 'IP 地址',
        cpuShare: 'CPU 权重',
        cpuShareHelper: '容器默认份额为 1024 个 CPU，增大可使当前容器获得更多的 CPU 时间',
        inputIpv4: '请输入 IPv4 地址',
        inputIpv6: '请输入 IPv6 地址',

        containerFromAppHelper: '检测到该容器来源于应用商店，应用操作可能会导致当前编辑失效',
        containerFromAppHelper1: '在应用商店的已安装页面，点击 [参数] 按钮，进入编辑页面修改容器名称。',
        command: '命令',
        console: '控制台交互',
        tty: '伪终端 ( -t )',
        openStdin: '标准输入 ( -i )',
        custom: '自定义',
        emptyUser: '为空时，将使用容器默认的用户登录',
        privileged: '特权模式',
        privilegedHelper: '允许容器在主机上执行某些特权操作，可能会增加容器风险，谨慎开启！',
        editComposeHelper:
            '注意：设置的环境变量会默认写入 1panel.env 文件。\n如需在容器中使用这些参数，还需在 compose 文件中手动添加 env_file 引用。',

        upgradeHelper: '仓库名称/镜像名称:镜像版本',
        upgradeWarning2: '升级操作需要重建容器，任何未持久化的数据将会丢失，是否继续？',
        oldImage: '当前镜像',
        sameImageContainer: '同镜像容器',
        sameImageHelper: '同镜像容器可勾选后批量升级',
        targetImage: '目标镜像',
        imageLoadErr: '未检测到容器的镜像名称',
        appHelper: '该容器来源于应用商店，升级可能导致该服务不可用',

        resource: '资源',
        input: '手动输入',
        forcePull: '强制拉取镜像',
        forcePullHelper: '忽略服务器已存在的镜像，重新拉取一次',
        server: '服务器',
        serverExample: '80, 80-88, ip:80 或者 ip:80-88',
        containerExample: '80 或者 80-88',
        exposePort: '暴露端口',
        exposeAll: '暴露所有',
        cmdHelper: '例： nginx -g "daemon off;"',
        entrypointHelper: '例： docker-entrypoint.sh',
        autoRemove: '容器退出后自动删除容器',
        cpuQuota: 'CPU 限制',
        memoryLimit: '内存限制',
        limitHelper: '限制为 0 则关闭限制，最大可用为 {0}',
        macAddr: 'MAC 地址',
        mount: '挂载',
        volumeOption: '挂载卷',
        hostOption: '本机目录',
        serverPath: '服务器目录',
        containerDir: '容器目录',
        volumeHelper: '请确认存储卷内容输入正确',
        modeRW: '读写',
        modeR: '只读',
        mode: '权限',
        env: '环境变量',
        restartPolicy: '重启规则',
        always: '一直重启',
        unlessStopped: '未手动停止则重启',
        onFailure: '失败后重启（默认重启 5 次）',
        no: '不重启',

        refreshTime: '刷新间隔',
        cache: '缓存',

        image: '镜像',
        imagePull: '拉取镜像',
        imagePush: '推送镜像',
        imageDelete: '删除镜像',
        imageTagDeleteHelper: '移除与该镜像 ID 相关联的其他标签',
        repoName: '仓库名',
        imageName: '镜像名',
        httpRepo: 'http 仓库添加授信需要重启 docker 服务',
        delInsecure: '删除授信',
        delInsecureHelper: '删除授信需要重启 docker 服务，是否删除？',
        pull: '拉取',
        path: '路径',
        importImage: '导入镜像',
        imageBuild: '构建镜像',
        build: '构建镜像',
        pathSelect: '路径选择',
        label: '标签',
        imageTag: '镜像标签',
        push: '推送',
        fileName: '文件名',
        export: '导出',
        exportImage: '导出镜像',
        size: '大小',
        tag: '标签',
        tagHelper: '一行一个，例： \nkey1=value1\nkey2=value2',
        imageNameHelper: '镜像名称及 Tag，例：nginx:latest',
        cleanBuildCache: '清理构建缓存',
        delBuildCacheHelper: '清理构建缓存 将删除所有构建产生的缓存，该操作无法回滚，是否继续？',
        urlWarning: '路径前缀不需要添加 http:// 或 https://, 请修改',

        network: '网络',
        networkHelper: '删除 1panel-network 容器网络将影响部分应用和运行环境的正常使用，是否继续？',
        createNetwork: '创建网络',
        networkName: '网络名',
        driver: '模式',
        option: '参数',
        attachable: '可用',
        subnet: '子网',
        scope: 'IP 范围',
        gateway: '网关',
        auxAddress: '排除 IP',

        volume: '存储卷',
        volumeDir: '存储卷目录',
        nfsEnable: '启用 NFS 存储',
        nfsAddress: '地址',
        mountpoint: '挂载点',
        mountpointNFSHelper: '例：/nfs, /nfs-share',
        options: '可选参数',
        createVolume: '创建存储卷',

        repo: '仓库',
        createRepo: '添加仓库',
        httpRepoHelper: '操作 http 类型仓库需要重启 Docker 服务。',
        downloadUrl: '下载地址',
        imageRepo: '镜像仓库',
        repoHelper: '是否包含镜像仓库/组织/项目?',
        auth: '认证',
        mirrorHelper:
            '当存在多个加速器时，需要换行显示，例： \nhttp://xxxxxx.m.daocloud.io \nhttps://xxxxxx.mirror.aliyuncs.com',
        registrieHelper: '当存在多个私有仓库时，需要换行显示，例：\n*************:8081 \n*************:8081',

        compose: '编排',
        fromChangeHelper: '切换来源将清空当前已编辑内容，是否继续？',
        composePathHelper: '配置文件保存路径: {0}',
        composeHelper: '通过 1Panel 编辑或者模版创建的编排，将保存在 {0}/docker/compose 路径下',
        deleteFile: '删除文件',
        deleteComposeHelper: '删除容器编排的所有文件，包括配置文件和持久化文件，请谨慎操作！',
        deleteCompose: '" 删除此编排',
        createCompose: '创建编排',
        composeDirectory: '编排目录',
        template: '模版',
        composeTemplate: '编排模版',
        createComposeTemplate: '创建编排模版',
        content: '内容',
        contentEmpty: '编排内容不能为空，请输入后重试！',
        containerNumber: '容器数量',
        containerStatus: '容器状态',
        exited: '已停止',
        running: '运行中 ( {0} / {1} )',
        composeDetailHelper: '该 compose 为 1Panel 编排外部创建。暂不支持启停操作。',
        composeOperatorHelper: '将对 {0} 进行 {1} 操作，是否继续？',
        composeDownHelper: '将停止并删除 {0} 编排下所有容器及网络，是否继续？',

        setting: '配置',
        goSetting: '去修改',
        operatorStatusHelper: '此操作将{0}Docker 服务，是否继续？',
        dockerStatus: 'Docker 服务',
        daemonJsonPathHelper: '请保证配置路径与 docker.service 中指定的配置路径保持一致。',
        mirrors: '镜像加速',
        mirrorsHelper: '优先使用加速 URL 执行操作，设置为空则取消镜像加速。',
        mirrorsHelper2: '具体操作配置请参照官方文档',
        registries: '私有仓库',
        ipv6Helper: '开启 IPv6 后，需要增加 IPv6 的容器网络，具体操作配置请参照官方文档',
        ipv6CidrHelper: '容器的 IPv6 地址池范围',
        ipv6TablesHelper: 'Docker IPv6 对 iptables 规则的自动配置',
        experimentalHelper: '开启 ip6tables 必须开启此配置，否则 ip6tables 会被忽略',
        cutLog: '日志切割',
        cutLogHelper1: '当前配置只会影响新创建的容器；',
        cutLogHelper2: '已经创建的容器需要重新创建使配置生效；',
        cutLogHelper3:
            '注意，重新创建容器可能会导致数据丢失。如果你的容器中有重要数据，确保在执行重建操作之前进行备份。',
        maxSize: '文件大小',
        maxFile: '保留份数',
        liveHelper: '允许在 Docker 守护进程发生意外停机或崩溃时保留正在运行的容器状态',
        liveWithSwarmHelper: 'live-restore 守护进程配置与 Swarm 模式不兼容',
        iptablesDisable: '关闭 iptables',
        iptablesHelper1: 'Docker 对 iptables 规则的自动配置',
        iptablesHelper2: '关闭 iptables 会导致容器无法与外部网络通信。',
        daemonJsonPath: '配置路径',
        serviceUnavailable: '当前未启动 Docker 服务，请在',
        startIn: '中开启',
        sockPath: 'Socket 路径',
        sockPathHelper: 'Docker 守护进程（Docker Daemon）与客户端之间的通信通道',
        sockPathHelper1: '默认路径：/var/run/docker.sock',
        sockPathMsg: '保存设置 Socket 路径可能导致 Docker 服务不可用，是否继续？',
        sockPathErr: '请选择或输入正确的 Docker sock 文件路径',
        related: '关联资源',
        includeAppstore: '显示应用商店容器',
        excludeAppstore: '隐藏应用商店容器',

        cleanDockerDiskZone: '清理 Docker 使用的磁盘空间',
        cleanImagesHelper: '( 清理所有未被任何容器使用的镜像 )',
        cleanContainersHelper: '( 清理所有处于停止状态的容器 )',
        cleanVolumesHelper: '( 清理所有未被使用的本地存储卷 )',

        makeImage: '制作镜像',
        newImageName: '新镜像名称',
        commitMessage: '提交信息',
        author: '作者',
        ifPause: '制作过程中是否暂停容器',
        ifMakeImageWithContainer: '是否根据此容器制作新镜像？',
    },
    cronjob: {
        create: '创建计划任务',
        edit: '编辑计划任务',
        errImport: '文件内容异常：',
        errImportFormat: '导入的计划任务数据或格式异常，请检查后重试！',
        importHelper:
            '导入时将自动跳过重名计划任务。任务默认设置为【停用】状态，数据关联异常时，设置为【待编辑】状态。',
        changeStatus: '状态修改',
        disableMsg: '停止计划任务会导致该任务不再自动执行。是否继续？',
        enableMsg: '启用计划任务会让该任务定期自动执行。是否继续？',
        taskType: '任务类型',
        nextTime: '近 5 次执行',
        record: '报告',
        viewRecords: '查看报告',
        shell: 'Shell 脚本',
        log: '备份日志',
        logHelper: '备份系统日志',
        logHelper1: '1. 1Panel 系统日志',
        logHelper2: '2. 服务器的 SSH 登录日志',
        logHelper3: '3. 所有网站日志',
        containerCheckBox: '在容器中执行（无需再输入进入容器命令）',
        containerName: '容器名称',
        ntp: '同步服务器时间',
        ntp_helper: '您可以在工具箱的快速设置页面配置 NTP 服务器',
        app: '备份应用',
        website: '备份网站',
        rulesHelper: '支持多个排除规则，使用英文逗号 , 分隔，例如：*.log,*.sql',
        lastRecordTime: '上次执行情况',
        database: '备份数据库',
        missBackupAccount: '未能找到备份账号',
        syncDate: '同步时间 ',
        clean: '缓存清理',
        curl: '访问 URL',
        taskName: '任务名称',
        cronSpec: '执行周期',
        cronSpecDoc: '自定义执行周期仅支持【分时日月周】格式，如 0 0 * * * ，具体可参考官方文档',
        cronSpecHelper: '请输入正确的执行周期',
        cleanHelper: '该操作将所有任务执行记录、备份文件和日志文件，是否继续？',
        backupContent: '备份内容',
        directory: '备份目录 / 文件',
        sourceDir: '备份目录',
        snapshot: '系统快照',
        allOptionHelper: '当前计划任务为备份所有【{0}】，暂不支持直接下载，可在【{0}】备份列表中查看',
        exclusionRules: '排除规则',
        exclusionRulesHelper: '排除规则将对此次备份的所有压缩操作生效',
        default_download_path: '默认下载地址',
        saveLocal: '同时保留本地备份（和云存储保留份数一致）',
        url: 'URL 地址',
        targetHelper: '备份账号可在面板设置中维护',
        withImageHelper: '备份应用商店镜像，但是会增大快照文件体积。',
        ignoreApp: '排除应用',
        withImage: '备份所有应用镜像',
        retainCopies: '保留份数',
        retryTimes: '失败重试次数',
        timeout: '超时时间',
        ignoreErr: '忽略错误',
        ignoreErrHelper: '忽略备份过程中出现的错误，保证所有备份任务执行',
        retryTimesHelper: '为 0 表示失败后不重试',
        retainCopiesHelper: '执行记录及日志保留份数',
        retainCopiesHelper1: '备份文件保留份数',
        retainCopiesUnit: ' 份 (查看)',
        cronSpecRule: '第 {0} 行中执行周期格式错误，请检查后重试！',
        cronSpecRule2: '执行周期格式错误，请检查后重试！',
        perMonthHelper: '每月 {0} 日 {1}:{2} 执行',
        perWeekHelper: '每周 {0} {1}:{2} 执行',
        perDayHelper: '每日 {0}:{1} 执行',
        perHourHelper: '每小时 {0}分 执行',
        perNDayHelper: '每 {0} 日 {1}:{2} 执行',
        perNHourHelper: '每 {0}小时 {1}分 执行',
        perNMinuteHelper: '每 {0}分 执行',
        perNSecondHelper: '每 {0}秒 执行',
        perMonth: '每月',
        perWeek: '每周',
        perHour: '每小时',
        perNDay: '每 N 日',
        perDay: '每天',
        perNHour: '每 N 时',
        perNMinute: '每 N 分钟',
        perNSecond: '每 N 秒',
        day: '日',
        monday: '周一',
        tuesday: '周二',
        wednesday: '周三',
        thursday: '周四',
        friday: '周五',
        saturday: '周六',
        sunday: '周日',
        shellContent: '脚本内容',
        executor: '解释器',
        errRecord: '错误的日志记录',
        errHandle: '任务执行失败',
        noRecord: '当前计划任务暂未产生记录',
        cleanData: '删除备份文件',
        cleanRemoteData: '删除远程备份文件',
        cleanDataHelper: '删除该任务执行过程中产生的备份文件',
        noLogs: '暂无任务输出...',
        errPath: '备份路径 [{0}] 错误，无法下载！',
        cutWebsiteLog: '切割网站日志',
        cutWebsiteLogHelper: '切割的日志文件会备份到 1Panel 的 backup 目录下',

        requestExpirationTime: '上传请求过期时间（小时）',
        unitHours: '单位：小时',
        alertTitle: '计划任务-{0}「 {1} 」任务失败告警',
        library: {
            script: '脚本',
            isInteractive: '交互式',
            interactive: '交互式脚本',
            interactiveHelper: '在脚本执行过程中需要用户输入参数或做出选择，且无法用于计划任务中。',
            library: '脚本库',
            create: '添加脚本',
            edit: '修改脚本',
            groupHelper: '针对脚本特征设置不同的分组，可以更加快速的对脚本进行筛选操作。',
            handleHelper: '将在 {0} 上执行 {1} 脚本，是否继续？',
            noSuchApp: '未检测到 {0} 服务，请前往脚本库页面手动安装！',
            syncHelper: '即将同步系统脚本库，该操作仅针对系统脚本，是否继续？',
        },
    },
    monitor: {
        globalFilter: '全局过滤',
        enableMonitor: '监控状态',
        storeDays: '保存天数',
        defaultNetwork: '默认网卡',
        defaultNetworkHelper: '默认监控和概览界面显示的网卡选项',
        cleanMonitor: '清空监控记录',

        avgLoad: '平均负载',
        loadDetail: '负载详情',
        resourceUsage: '资源使用率',
        networkCard: '网卡',
        read: '读取',
        write: '写入',
        readWriteCount: '读写次数',
        readWriteTime: '读写延迟',
        today: '今天',
        yesterday: '昨天',
        lastNDay: '近 {0} 天',
        lastNMonth: '近 {0} 月',
        lastHalfYear: '近半年',
        memory: '内存',
        cache: '缓存',
        disk: '磁盘',
        network: '网络',
        up: '上行',
        down: '下行',
        interval: '采集间隔（分钟）',

        gpuUtil: 'GPU 使用率',
        temperature: '温度',
        performanceState: '性能状态',
        powerUsage: '功耗',
        memoryUsage: '显存使用率',
        fanSpeed: '风扇转速',
    },
    terminal: {
        local: '本机',
        localHelper: 'local 名称仅用于系统本机标识',
        connLocalErr: '无法自动认证，请填写本地服务器的登录信息！',
        testConn: '连接测试',
        saveAndConn: '保存并连接',
        connTestOk: '连接信息可用',
        connTestFailed: '连接不可用，请检查连接信息！',
        host: '主机',
        createConn: '新建连接',
        noHost: '暂无主机',
        groupChange: '切换分组',
        expand: '全部展开',
        fold: '全部收缩',
        batchInput: '批量输入',
        quickCommand: '快速命令',
        quickCommandHelper: '常用命令列表，用于在终端界面底部快速选择',
        groupDeleteHelper: '移除组后，组内所有连接将迁移到 default 组内，是否继续？',
        command: '命令',
        addHost: '添加主机',
        localhost: '本地服务器',
        ip: '主机地址',
        authMode: '认证方式',
        passwordMode: '密码认证',
        rememberPassword: '记住认证信息',
        keyMode: '私钥认证',
        key: '私钥',
        keyPassword: '私钥密码',
        emptyTerminal: '暂无终端连接',
        lineHeight: '字体行高',
        letterSpacing: '字体间距',
        fontSize: '字体大小',
        cursorBlink: '光标闪烁',
        cursorStyle: '光标样式',
        cursorUnderline: '下划线',
        cursorBlock: '块状',
        cursorBar: '条形',
        scrollback: '滚动行数',
        scrollSensitivity: '滚动速度',
        saveHelper: '是否确认保存当前终端配置？',
    },
    toolbox: {
        common: {
            toolboxHelper: '部分安装和使用问题，可参考',
        },
        swap: {
            swap: 'Swap 分区',
            swapHelper1: 'Swap 的大小应该是物理内存的 1 到 2 倍，可根据具体情况进行调整；',
            swapHelper2: '在创建 Swap 文件之前，请确保系统硬盘有足够的可用空间，Swap 文件的大小将占用相应的磁盘空间；',
            swapHelper3:
                'Swap 可以帮助缓解内存压力，但仅是一个备选项，过多依赖可能导致系统性能下降，建议优先考虑增加内存或者优化应用程序内存使用；',
            swapHelper4: '建议定期监控 Swap 的使用情况，以确保系统正常运行。',
            swapDeleteHelper: '此操作将移除 Swap 分区 {0}，出于系统安全考虑，不会自动删除该文件，如需删除请手动操作！',
            saveHelper: '请先保存当前设置！',
            saveSwap: '保存当前配置将调整 Swap 分区 {0} 大小到 {1}，是否继续？',
            swapMin: '分区大小最小值为 40 KB，请修改后重试！',
            swapMax: '分区大小最大值为 {0}，请修改后重试！',
            swapOff: '分区大小最小值为 40 KB，设置成 0 则关闭 Swap 分区。',
        },
        device: {
            dnsHelper: '服务器地址域名解析',
            dnsAlert: '请注意！修改 /etc/resolv.conf 文件的配置时，重启系统后会将文件恢复为默认值',
            dnsHelper1: '当存在多个 DNS 时，需换行显示，例：\n114.114.114.114\n8.8.8.8',
            hostsHelper: '主机名解析',
            hosts: '域名',
            hostAlert: '隐藏了已注释的记录，请点击 全部配置 按钮以查看或设置',
            toolbox: '快速设置',
            hostname: '主机名',
            passwd: '系统密码',
            passwdHelper: '输入字符不能包含 $ 和 &',
            timeZone: '系统时区',
            localTime: '服务器时间',
            timeZoneChangeHelper: '系统时区修改需要重启服务，是否继续？',
            timeZoneHelper: '时区修改依赖于 timedatectl 命令，如未安装可能导致修改失败',
            timeZoneCN: '北京',
            timeZoneAM: '洛杉矶',
            timeZoneNY: '纽约',
            ntpALi: '阿里',
            ntpGoogle: '谷歌',
            syncSite: 'NTP 服务器',
            syncSiteHelper: '该操作将使用 {0} 作为源进行系统时间同步，是否继续？',
            hostnameHelper: '主机名修改依赖于 hostnamectl 命令，如未安装可能导致修改失败',
            userHelper: '用户名依赖于 whoami 命令获取，如未安装可能导致获取失败。',
            passwordHelper: '密码修改依赖于 chpasswd 命令，如未安装可能导致修改失败',
            hostHelper: '填写的内容中存在空值，请检查修改后重试！',
            dnsCheck: '测试可用性',
            dnsOK: 'DNS 配置信息可用！',
            dnsTestFailed: 'DNS 配置信息不可用，请修改后重试！',
        },
        fail2ban: {
            sshPort: '监听 SSH 端口',
            sshPortHelper: '当前 Fail2ban 监听主机 SSH 连接端口',
            unActive: '当前未开启 Fail2ban 服务，请先开启！',
            operation: '对 Fail2ban 服务进行 [{0}] 操作，是否继续？',
            fail2banChange: 'Fail2ban 配置修改',
            ignoreHelper: '白名单中的 IP 列表将被忽略屏蔽，是否继续？',
            bannedHelper: '黑名单中的 IP 列表将被服务器屏蔽，是否继续？',
            maxRetry: '最大重试次数',
            banTime: '禁用时间',
            banTimeHelper: '默认禁用时间为 10 分钟，禁用时间为 -1 则表示永久禁用',
            banTimeRule: '请输入正确的禁用时间或者 -1',
            banAllTime: '永久禁用',
            findTime: '发现周期',
            banAction: '禁用方式',
            banActionOption: '通过 {0} 来禁用指定的 IP 地址',
            allPorts: ' (所有端口)',
            ignoreIP: 'IP 白名单',
            bannedIP: 'IP 黑名单',
            logPath: '日志路径',
            logPathHelper: '默认 /var/log/secure 或者 /var/log/auth.log',
        },
        ftp: {
            ftp: 'FTP 账户',
            notStart: '当前未开启 FTP 服务，请先开启！',
            operation: '对 FTP 服务进行 [{0}] 操作，是否继续？',
            noPasswdMsg: '无法获取当前 FTP 账号密码，请先设置密码后重试！',
            enableHelper: '启用选中的 FTP 账号后，该 FTP 账号恢复访问权限，是否继续操作？',
            disableHelper: '停用选中的 FTP 账号后，该 FTP 账号将失去访问权限，是否继续操作？',
            syncHelper: '同步服务器与数据库中的 FTP 账户数据，是否继续操作？',
            dirSystem: '该目录为系统保留目录，修改可能导致系统崩溃，请修改后重试！',
            dirHelper: '开启 FTP 需要修改目录权限，请谨慎选择',
            dirMsg: '开启 FTP 将修改整个 {0} 目录权限，是否继续？',
        },
        clam: {
            clam: '病毒扫描',
            cron: '定时扫描',
            cronHelper: '专业版支持定时扫描功能 ',
            specErr: '执行周期格式错误，请检查后重试！',
            disableMsg: '停止定时执行会导致该扫描任务不再自动执行。是否继续？',
            enableMsg: '启用定时执行会让该扫描任务定期自动执行。是否继续？',
            showFresh: '显示病毒库服务',
            hideFresh: '隐藏病毒库服务',
            clamHelper: 'ClamAV 建议最低配置：3 GiB 以上内存、单核 2.0 GHz 以上 CPU，以及至少 5 GiB 可用硬盘空间。',
            notStart: '当前未开启 ClamAV 服务，请先开启！',
            removeRecord: '删除报告文件',
            noRecords: '点击“执行”按钮开始扫描，扫描结果将会记录在这里。',
            removeResultHelper: '删除任务执行过程中生成的报告文件，以清理存储空间。',
            removeInfected: '删除病毒文件',
            removeInfectedHelper: '删除任务检测到的病毒文件，以确保服务器的安全和正常运行。',
            clamCreate: '创建扫描规则',
            infectedStrategy: '感染文件策略',
            removeHelper: '删除病毒文件，请谨慎选择！',
            move: '移动',
            moveHelper: '将病毒文件移动到指定目录下',
            copyHelper: '将病毒文件复制到指定目录下',
            none: '不操作',
            noneHelper: '不对病毒文件采取任何操作',
            scanDir: '扫描目录',
            infectedDir: '隔离目录',
            scanDate: '扫描时间',
            scanResult: '扫描报告条数',
            tail: '日志显示行数',
            infectedFiles: '感染文件数',
            log: '详情',
            clamConf: '扫描配置',
            clamLog: '扫描日志',
            freshClam: '病毒库刷新配置',
            freshClamLog: '病毒库刷新日志',
            alertHelper: '专业版支持定时扫描和短信告警功能',
            alertTitle: '病毒扫描「 {0} 」任务检测到感染文件告警',
        },
    },
    logs: {
        core: '面板服务',
        agent: '节点监控',
        panelLog: '面板日志',
        operation: '操作日志',
        login: '访问日志',
        loginIP: '登录 IP',
        loginAddress: '登录地址',
        loginAgent: '用户代理',
        loginStatus: '登录状态',
        system: '系统日志',
        deleteLogs: '清空日志',
        resource: '资源',
        detail: {
            ai: 'AI',
            groups: '分组',
            hosts: '主机',
            apps: '应用',
            websites: '网站',
            containers: '容器',
            files: '文件管理',
            runtimes: '运行环境',
            process: '进程管理',
            toolbox: '工具箱',
            backups: '备份 / 恢复',
            tampers: '防篡改',
            xsetting: '界面设置',
            logs: '日志审计',
            settings: '面板设置',
            cronjobs: '计划任务',
            databases: '数据库',
            waf: 'WAF',
            licenses: '许可证',
            nodes: '节点',
            commands: '快速命令',
        },
        websiteLog: '网站日志',
        runLog: '运行日志',
        errLog: '错误日志',
        task: '任务日志',
        taskName: '任务名称',
        taskRunning: '执行中',
    },
    file: {
        fileDirNum: '共 {0} 个目录，{1} 个文件，',
        currentDir: '当前目录',
        dir: '文件夹',
        fileName: '文件名',
        search: '在当前目录下查找',
        mode: '权限',
        editPermissions: '编辑@:file.mode',
        owner: '所有者',
        file: '文件',
        remoteFile: '远程下载',
        share: '分享',
        sync: '数据同步',
        size: '大小',
        updateTime: '修改时间',
        rename: '重命名',
        role: '权限',
        info: '属性',
        linkFile: '软连接文件',
        shareList: '分享列表',
        zip: '压缩',
        group: '用户组',
        path: '路径',
        public: '公共',
        setRole: '设置权限',
        link: '是否链接',
        rRole: '读取',
        wRole: '写入',
        xRole: '可执行',
        compress: '压缩',
        deCompress: '解压',
        compressType: '压缩格式',
        compressDst: '压缩路径',
        replace: '覆盖已存在的文件',
        compressSuccess: '压缩成功',
        deCompressSuccess: '解压成功',
        deCompressDst: '解压路径',
        linkType: '链接类型',
        softLink: '软链接',
        hardLink: '硬链接',
        linkPath: '链接路径',
        selectFile: '选择文件',
        downloadSuccess: '下载成功',
        downloadUrl: '下载地址',
        downloadStart: '下载开始!',
        moveSuccess: '移动成功',
        copySuccess: '复制成功',
        move: '移动',
        calculate: '计算',
        canNotDeCompress: '无法解压此文件',
        uploadSuccess: '上传成功!',
        downloadProcess: '下载进度',
        downloading: '正在下载...',
        infoDetail: '文件属性',
        root: '根目录',
        list: '文件列表',
        sub: '子目录',
        theme: '主题',
        language: '语言',
        eol: '行尾符',
        copyDir: '复制路径',
        paste: '粘贴',
        changeOwner: '修改用户和用户组',
        containSub: '同时修改子文件属性',
        ownerHelper: 'PHP 运行环境默认用户:用户组为 1000:1000, 容器内外用户显示不一致为正常现象',
        searchHelper: '支持 * 等通配符',
        uploadFailed: '【{0}】 文件上传失败',
        fileUploadStart: '正在上传【{0}】....',
        currentSelect: '当前选中: ',
        unsupportedType: '不支持的文件类型',
        deleteHelper: '确定删除所选文件？ 默认删除之后将进入回收站',
        fileHelper: '注意：1. 搜索结果不支持排序功能 2. 文件夹无法按大小排序。',
        forceDeleteHelper: '永久删除文件（不进入回收站，直接删除）',
        recycleBin: '回收站',
        sourcePath: '原路径',
        deleteTime: '删除时间',
        reduce: '还原',
        confirmReduce: '确定还原以下文件？',
        reduceSuccess: '还原成功',
        reduceHelper: '如果原路径存在同名文件或目录，将会被覆盖，是否继续？',
        clearRecycleBin: '清空回收站',
        clearRecycleBinHelper: '是否清空回收站？',
        favorite: '收藏夹',
        removeFavorite: '是否从收藏夹移出？',
        addFavorite: '添加/移出收藏夹',
        clearList: '清空列表',
        deleteRecycleHelper: '确定永久删除以下文件？',
        typeErrOrEmpty: '【{0}】 文件类型错误或为空文件夹',
        dropHelper: '将需要上传的文件拖曳到此处',
        fileRecycleBin: '文件回收站',
        fileRecycleBinMsg: '已{0}回收站',
        wordWrap: '自动换行',
        deleteHelper2: '确定删除所选文件？删除操作不可回滚',
        ignoreCertificate: '忽略不可信证书',
        ignoreCertificateHelper:
            '下载时忽略不可信证书可能导致数据泄露或篡改。请谨慎使用此选项，仅在信任下载源的情况下启用',
        uploadOverLimit: '文件数量超过 1000！请压缩后上传',
        clashDitNotSupport: '文件名禁止包含 .1panel_clash',
        clashDeleteAlert: '回收站文件夹不能删除',
        clashOpenAlert: '回收站目录请点击【回收站】按钮打开',
        right: '前进',
        back: '后退',
        top: '返回上一级',
        up: '上一级',
        openWithVscode: 'VS Code 打开',
        vscodeHelper: '请确保本地已安装 VS Code 并配置了 SSH Remote 插件',
        saveContentAndClose: '文件已被修改，是否保存并关闭？',
        saveAndOpenNewFile: '文件已被修改，是否保存并打开新文件？',
        noEdit: '文件未修改,无需此操作！',
        noNameFolder: '未命名文件夹',
        noNameFile: '未命名文件',
        minimap: '缩略图',
        fileCanNotRead: '此文件不支持预览',
        panelInstallDir: '1Panel 安装目录不能删除',
        wgetTask: '下载任务',
        existFileTitle: '同名文件提示',
        existFileHelper: '上传的文件存在同名文件，是否覆盖？',
        existFileSize: '文件大小 (新 -> 旧)',
        existFileDirHelper: '选择的文件/文件夹存在同名，请谨慎操作！',
        coverDirHelper: '选中覆盖的文件夹，将复制到目标路径！',
        noSuchFile: '未能找到该文件或目录，请检查后重试',
        setting: '设置',
        showHide: '显示隐藏文件',
        noShowHide: '不显示隐藏文件',
        cancelUpload: '取消上传',
        cancelUploadHelper: '是否取消上传，取消后将清空上传列表',
    },
    ssh: {
        autoStart: '开机自启',
        enable: '设置开机自启',
        disable: '关闭开机自启',
        sshAlert: '列表数据根据登录时间排序，但请注意，切换时区或其他操作可能导致登录日志的时间出现偏差。',
        sshAlert2: '可通过工具箱中的 Fail2ban 屏蔽暴力破解 IP，从而保护主机安全。',
        sshOperate: '对 SSH 服务进行 [{0}] 操作，是否继续？',
        sshChange: 'SSH 配置修改',
        sshChangeHelper: '此操作将 {0} 修改为 [{1}] ，是否继续？',
        sshFileChangeHelper: '直接修改配置文件可能会导致服务不可用，请谨慎操作，是否继续？',
        port: '连接端口',
        portHelper: '指定 SSH 服务监听的端口号，默认为 22。',
        listenAddress: '监听地址',
        allV4V6: '0.0.0.0:{0}(IPv4) 和 :::{0}(IPv6)',
        listenHelper: '同时取消 IPv4 和 IPv6 设置，将会同时监听 0.0.0.0:{0}(IPv4) 和 :::{0}(IPv6)',
        addressHelper: '指定 SSH 服务监听的 IP 地址',
        permitRootLogin: 'root 用户',
        rootSettingHelper: 'root 用户 SSH 登录方式，默认所有 SSH 登录。',
        rootHelper1: '允许 SSH 登录',
        rootHelper2: '禁止 SSH 登录',
        rootHelper3: '仅允许密钥登录',
        rootHelper4: '仅允许执行预先定义的命令，不能进行其他操作',
        passwordAuthentication: '密码认证',
        pwdAuthHelper: '是否启用密码认证，默认启用。',
        pubkeyAuthentication: '密钥认证',
        privateKey: '私钥',
        publicKey: '公钥',
        password: '密码',
        createMode: '创建方式',
        generate: '自动生成',
        unSyncPass: '密钥密码无法同步',
        syncHelper: '同步操作将清理失效密钥并同步新增的完整密钥对，是否继续？',
        input: '手动输入',
        import: '文件上传',
        pubkey: '密钥信息',
        pubKeyHelper: '当前密钥信息仅对用户 {0} 生效',
        encryptionMode: '加密方式',
        passwordHelper: '支持大小写英文、数字,长度6-10',
        reGenerate: '重新生成密钥',
        keyAuthHelper: '是否启用密钥认证，默认启用。',
        useDNS: '反向解析',
        dnsHelper: '控制 SSH 服务器是否启用 DNS 解析功能，从而验证连接方的身份。',
        analysis: '统计信息',
        denyHelper: '将对下列地址进行【屏蔽】操作，设置后该 IP 将禁止访问服务器，是否继续？',
        acceptHelper: '将对下列地址进行【放行】操作，设置后该 IP 将恢复正常访问，是否继续？',
        noAddrWarning: '当前未选中任何可【{0}】地址，请检查后重试！',
        loginLogs: '登录日志',
        loginMode: '登录方式',
        authenticating: '密钥',
        publickey: '密钥',
        belong: '归属地',
        local: '内网',
        remote: '外网',
        session: '会话',
        loginTime: '登录时间',
        loginIP: '登录IP',
        stopSSHWarn: '是否断开此SSH连接',
    },
    setting: {
        panel: '面板',
        user: '面板用户',
        userChange: '修改面板用户',
        userChangeHelper: '修改面板用户将退出登陆，是否继续？',
        passwd: '面板密码',
        emailHelper: '用于密码找回',
        title: '面板别名',
        titleHelper: '支持长度3-30的英文、中文、数字、空格和常见的特殊字符',
        panelPort: '面板端口',
        portHelper: '建议端口范围8888 - 65535，注意：有安全组的服务器请提前在安全组放行新端口',
        portChange: '端口修改',
        portChangeHelper: '服务端口修改需要重启服务，是否继续？',
        theme: '主题颜色',
        menuTabs: '菜单标签页',
        componentSize: '组件大小',
        dark: '暗色',
        darkGold: '黑金',
        light: '亮色',
        auto: '跟随系统',
        language: '系统语言',
        languageHelper: '默认跟随浏览器语言，设置后只对当前浏览器生效，更换浏览器后需要重新设置',
        sessionTimeout: '超时时间',
        sessionTimeoutError: '最小超时时间为 300 秒',
        sessionTimeoutHelper: '如果用户超过 {0} 秒未操作面板，面板将自动退出登录',
        systemIP: '默认访问地址',
        systemIPHelper: '应用跳转、容器访问等功能将使用此地址进行跳转，每个节点可设置不同地址。',
        proxy: '代理服务器',
        proxyHelper: '设置代理服务器后，将在以下场景中生效：',
        proxyHelper1: '应用商店的安装包下载和同步（专业版功能）',
        proxyHelper2: '系统版本升级及获取更新说明（专业版功能）',
        proxyHelper3: '系统许可证的验证和同步',
        proxyHelper4: 'Docker 的网络访问将通过代理服务器进行（专业版功能）',
        proxyHelper5: '系统类型脚本库的统一下载与同步（专业版功能）',
        proxyHelper6: '申请证书（专业版功能）',
        proxyType: '代理类型',
        proxyUrl: '代理地址',
        proxyPort: '代理端口',
        proxyPasswdKeep: '记住密码',
        proxyDocker: 'Docker 代理',
        proxyDockerHelper: '将代理服务器配置同步至 Docker，支持离线服务器拉取镜像等操作',
        syncToNode: '同步至子节点',
        syncToNodeHelper: '同步设置至所选节点',
        nodes: '节点',
        selectNode: '选择节点',
        selectNodeError: '请选择节点',
        apiInterface: 'API 接口',
        apiInterfaceClose: '关闭后将不能使用 API 接口进行访问，是否继续？',
        apiInterfaceHelper: '提供面板支持 API 接口访问',
        apiInterfaceAlert1: '请不要在生产环境开启，这可能增加服务器安全风险',
        apiInterfaceAlert2: '请不要使用第三方应用调用面板 API，以防止潜在的安全威胁。',
        apiInterfaceAlert3: 'API 接口文档',
        apiInterfaceAlert4: '使用文档',
        apiKey: '接口密钥',
        apiKeyHelper: '接口密钥用于外部应用访问 API 接口',
        ipWhiteList: 'IP 白名单',
        ipWhiteListEgs: '当存在多个 IP 时，需要换行显示，例： \n************* \n172.16.10.0/24',
        ipWhiteListHelper:
            '必需在 IP 白名单列表中的 IP 才能访问面板 API 接口，0.0.0.0/0（所有 IPv4），::/0（所有 IPv6）',
        apiKeyValidityTime: '接口密钥有效期',
        apiKeyValidityTimeEgs: '接口密钥有效期（单位分）',
        apiKeyValidityTimeHelper: '接口时间戳到请求时的当前时间戳之间有效（单位分），设置为 0 时，不做时间戳校验',
        apiKeyReset: '接口密钥重置',
        apiKeyResetHelper: '重置密钥后，已关联密钥服务将失效，请重新添加新密钥至服务。',
        confDockerProxy: '配置 Docker 代理',
        restartNowHelper: '配置 Docker 代理需要重启 Docker 服务。',
        restartNow: '立即重启',
        restartLater: '稍后手动重启',
        systemIPWarning: '当前节点尚未配置默认访问地址，请前往面板设置进行设置！',
        systemIPWarning1: '当前服务器地址设置为 {0}，无法快速跳转！',
        changePassword: '密码修改',
        oldPassword: '原密码',
        newPassword: '新密码',
        retryPassword: '确认密码',
        noSpace: '输入信息不能包含空格符',
        duplicatePassword: '新密码不能与原始密码一致，请重新输入！',
        diskClean: '缓存清理',
        developerMode: '预览体验计划',
        developerModeHelper: '获取 1Panel 的预览版本，以分享有关新功能和更新的反馈',

        thirdParty: '第三方账号',
        scope: '使用范围',
        public: '公有',
        publicHelper: '公有类型的备份账号会同步到各个子节点，子节点可以一起使用',
        private: '私有',
        privateHelper: '私有类型的备份账号只创建在当前节点上，仅供当前节点使用',
        noTypeForCreate: '当前无可创建备份类型',
        LOCAL: '服务器磁盘',
        OSS: '阿里云 OSS',
        S3: '亚马逊 S3 云存储',
        mode: '模式',
        MINIO: 'MINIO',
        SFTP: 'SFTP',
        WebDAV: 'WebDAV',
        WebDAVAlist: 'WebDAV 连接 Alist 可参考官方文档',
        UPYUN: '又拍云',
        ALIYUN: '阿里云盘',
        ALIYUNHelper: '当前阿里云盘非客户端下载最大限制为 100 MB，超过限制需要通过客户端下载',
        ALIYUNRecover:
            '当前阿里云盘非客户端下载最大限制为 100 MB，超过限制需要通过客户端下载到本地后，同步快照进行恢复',
        GoogleDrive: '谷歌云盘',
        analysis: '解析',
        analysisHelper: '粘贴整个 token 内容，自动解析所需部分，具体操作可参考官方文档',
        serviceName: '服务名称',
        operator: '操作员',
        OneDrive: '微软 OneDrive',
        isCN: '世纪互联',
        isNotCN: '国际版',
        client_id: '客户端 ID',
        client_secret: '客户端密钥',
        redirect_uri: '重定向 Url',
        onedrive_helper: '自定义配置可参考官方文档',
        clickToRefresh: '单击可手动刷新',
        refreshTime: '令牌刷新时间',
        refreshStatus: '令牌刷新状态',
        codeWarning: '当前授权码格式错误，请重新确认！',
        backupDir: '备份目录',
        code: '授权码',
        codeHelper:
            '请点击获取按钮，然后登录 {0} 复制跳转链接中 code 后面的内容，粘贴到该输入框中，具体操作可参考官方文档。',
        loadCode: '获取',
        COS: '腾讯云 COS',
        ap_beijing_1: '北京一区',
        ap_beijing: '北京',
        ap_nanjing: '南京',
        ap_shanghai: '上海',
        ap_guangzhou: '广州',
        ap_chengdu: '成都',
        ap_chongqing: '重庆',
        ap_shenzhen_fsi: '深圳金融',
        ap_shanghai_fsi: '上海金融',
        ap_beijing_fsi: '北京金融',
        ap_hongkong: '中国香港',
        ap_singapore: '新加坡',
        ap_mumbai: '孟买',
        ap_jakarta: '雅加达',
        ap_seoul: '首尔',
        ap_bangkok: '曼谷',
        ap_tokyo: '东京',
        na_siliconvalley: '硅谷（美西）',
        na_ashburn: '弗吉尼亚（美东）',
        na_toronto: '多伦多',
        sa_saopaulo: '圣保罗',
        eu_frankfurt: '法兰克福',
        KODO: '七牛云 Kodo',
        scType: '存储类型',
        typeStandard: '标准存储',
        typeStandard_IA: '低频存储',
        typeArchive: '归档存储',
        typeDeep_Archive: '深度归档存储',
        scLighthouse: '默认，轻量对象存储仅支持该存储类型',
        scStandard: '标准存储，适用于实时访问的大量热点文件、频繁的数据交互等业务场景。',
        scStandard_IA: '低频存储，适用于较低访问频率（例如平均每月访问频率1到2次）的业务场景，最少存储30天。',
        scArchive: '归档存储，适用于极低访问频率（例如半年访问1次）的业务场景。',
        scDeep_Archive: '深度归档存储，适用于极低访问频率（例如1年访问1~2次）的业务场景。',
        archiveHelper: '归档存储的文件无法直接下载，需要先在对应的云服务商网站进行恢复操作，请谨慎使用！',
        backupAlert: '理论上只要云厂商兼容 S3 协议，就可以用现有的亚马逊 S3 云存储来备份，具体配置参考',
        domain: '加速域名',
        backupAccount: '备份账号',
        loadBucket: '获取桶',
        accountName: '账户名称',
        accountKey: '账户密钥',
        address: '地址',
        path: '路径',
        backupJump: '未在当前备份列表中的备份文件，请尝试从文件目录中下载后导入备份。',

        snapshot: '快照',
        noAppData: '暂无可选择系统应用',
        noBackupData: '暂无可选择备份数据',
        stepBaseData: '基础数据',
        stepAppData: '系统应用',
        stepPanelData: '系统数据',
        stepBackupData: '备份数据',
        stepOtherData: '其他数据',
        monitorData: '监控数据',
        dockerConf: 'Docker 配置',
        selectAllImage: '备份所有应用镜像',
        logLabel: '日志',
        agentLabel: '节点配置',
        appDataLabel: '应用数据',
        appImage: '应用镜像',
        appBackup: '应用备份',
        backupLabel: '备份目录',
        confLabel: '配置文件',
        dockerLabel: '容器',
        taskLabel: '计划任务',
        resourceLabel: '应用资源目录',
        runtimeLabel: '运行环境',
        appLabel: '应用',
        databaseLabel: '数据库',
        snapshotLabel: '快照文件',
        websiteLabel: '网站',
        directoryLabel: '目录',
        appStoreLabel: '应用商店',
        shellLabel: '脚本',
        tmpLabel: '临时目录',
        sslLabel: '证书目录',
        reCreate: '创建快照失败',
        reRollback: '回滚快照失败',
        deleteHelper: '将删除该快照的所有备份文件，包括第三方备份账号中的文件。',
        ignoreRule: '排除规则',
        editIgnoreRule: '@:commons.button.edit@:setting.ignoreRule',
        ignoreHelper: '快照时将使用该规则对 1Panel 数据目录进行压缩备份，请谨慎修改。',
        ignoreHelper1: '一行一个，例： \n*.log\n/opt/1panel/cache',
        status: '快照状态',
        panelInfo: '写入 1Panel 基础信息',
        panelBin: '备份 1Panel 系统文件',
        daemonJson: '备份 Docker 配置文件',
        appData: '备份 1Panel 已安装应用',
        panelData: '备份 1Panel 数据目录',
        backupData: '备份 1Panel 本地备份目录',
        compress: '制作快照文件',
        upload: '上传快照文件',
        recoverDetail: '恢复详情',
        recoverFailed: '快照恢复失败',
        createSnapshot: '创建快照',
        importSnapshot: '同步快照',
        importHelper: '快照文件目录：',
        lastRecoverAt: '上次恢复时间',
        lastRollbackAt: '上次回滚时间',
        reDownload: '重新下载',
        recoverErrArch: '不支持在不同服务器架构之间进行快照恢复操作!',
        recoverErrSize: '检测到当前磁盘空间不足，请检查或清理后重试!',
        recoverHelper: '即将从快照 {0} 开始恢复，恢复前请确认以下信息：',
        recoverHelper1: '恢复需要重启 Docker 以及 1Panel 服务',
        recoverHelper2: '请确保服务器磁盘空间充足 ( 快照文件大小: {0}, 可用空间: {1} )',
        recoverHelper3: '请确保服务器架构与创建快照服务器架构信息保持一致 (当前服务器架构: {0} )',
        rollback: '回滚',
        rollbackHelper:
            '即将回滚本次恢复，回滚将替换所有本次恢复的文件，过程中可能需要重启 Docker 以及 1Panel 服务，是否继续？',

        upgradeRecord: '更新记录',
        upgrading: '正在升级中，请稍候...',
        upgradeHelper: '升级操作需要重启 1Panel 服务，是否继续？',
        noUpgrade: '当前已经是最新版本',
        versionHelper: '1Panel 版本号命名规则为： [大版本].[功能版本].[Bug 修复版本]，例：',
        rollbackLocalHelper: '主节点暂不支持直接回滚，请手动执行 1pctl restore 命令回滚！',
        upgradeCheck: '检查更新',
        upgradeNotes: '更新内容',
        upgradeNow: '立即更新',
        source: '下载源',
        versionNotSame: '节点版本与主节点不一致，暂不支持切换，请在节点管理中升级后重试！',
        versionCompare: '检测到节点 {0} 版本已是当前可升级最新版本，请检查主节点版本后重试！',

        safe: '安全',
        bindInfo: '监听地址',
        bindAll: '监听所有',
        bindInfoHelper: '修改服务监听地址或协议可能导致服务不可用，是否继续？',
        ipv6: '监听 IPv6',
        bindAddress: '监听地址',
        entrance: '安全入口',
        showEntrance: '启用概览页未开启提醒',
        entranceHelper: '开启安全入口后只能通过指定安全入口登录面板',
        entranceError: '请输入 5-116 位安全登录入口，仅支持输入数字或字母',
        entranceInputHelper: '安全入口设置为空时，则取消安全入口',
        randomGenerate: '随机生成',
        expirationTime: '密码过期时间',
        unSetting: '未设置',
        noneSetting: '为面板密码设置过期时间，过期后需要重新设置密码',
        expirationHelper: '密码过期时间为 [0] 天时，则关闭密码过期功能',
        days: '过期天数',
        expiredHelper: '当前密码已过期，请重新修改密码：',
        timeoutHelper: '【 {0} 天后 】面板密码即将过期，过期后需要重新设置密码',
        complexity: '密码复杂度验证',
        complexityHelper: '开启后密码必须满足长度为 8-30 位且包含字母、数字、特殊字符至少两项',
        bindDomain: '域名绑定',
        unBindDomain: '域名解绑',
        panelSSL: '面板 SSL',
        panelSSLHelper: '面板 SSL 自动续期后需要手动重启 1Panel 服务才可生效',
        unBindDomainHelper: '解除域名绑定可能造成系统不安全，是否继续？',
        bindDomainHelper: '设置域名绑定后，仅能通过设置中域名访问 1Panel 服务',
        bindDomainHelper1: '绑定域名为空时，则取消域名绑定',
        bindDomainWarning: '设置域名绑定后，将退出当前登录，且仅能通过设置中域名访问 1Panel 服务，是否继续？',
        allowIPs: '授权 IP',
        unAllowIPs: '取消授权',
        unAllowIPsWarning: '授权 IP 为空将允许所有 IP 访问系统，可能造成系统不安全，是否继续？',
        allowIPsHelper: '设置授权 IP 后，仅有设置中的 IP 可以访问 1Panel 服务',
        allowIPsWarning: '设置授权 IP 后，仅有设置中的 IP 可以访问 1Panel 服务，是否继续？',
        allowIPsHelper1: '授权 IP 为空时，则取消授权 IP',
        allowIPEgs: '当存在多个授权 IP 时，需要换行显示，例： \n************* \n172.16.10.0/24',
        mfa: '两步验证',
        mfaClose: '关闭两步验证将导致服务安全性降低，是否继续？',
        secret: '密钥',
        mfaAlert: '两步验证密码是基于当前时间生成，请确保服务器时间已同步',
        mfaHelper: '开启后会验证手机应用验证码',
        mfaHelper1: '下载两步验证手机应用 如:',
        mfaHelper2: '使用手机应用扫描以下二维码，获取 6 位验证码',
        mfaHelper3: '输入手机应用上的 6 位数字',
        mfaCode: '验证码',
        mfaInterval: '刷新时间（秒）',
        mfaTitleHelper: '用于区分不同 1Panel 主机，修改后请重新扫描或手动添加密钥信息！',
        mfaIntervalHelper: '修改刷新时间后，请重新扫描或手动添加密钥信息！',
        sslChangeHelper: 'https 设置修改需要重启服务，是否继续？',
        sslDisable: '禁用',
        sslDisableHelper: '禁用 https 服务，需要重启面板才能生效，是否继续？',
        noAuthSetting: '未认证设置',
        noAuthSettingHelper: '用户在未登录且未正确输入安全入口、授权 IP、或绑定域名时，该响应可隐藏面板特征。',
        responseSetting: '响应设置',
        help200: '帮助页面',
        error400: '错误请求',
        error401: '未授权',
        error403: '禁止访问',
        error404: '未找到',
        error408: '请求超时',
        error416: '无效请求',
        error444: '连接被关闭',
        error500: '内部错误',

        https: '为面板设置 https 协议访问，提升面板访问安全性',
        certType: '证书类型',
        selfSigned: '自签名',
        selfSignedHelper: '自签证书，不被浏览器信任，显示不安全是正常现象',
        select: '选择已有',
        domainOrIP: '域名或 IP：',
        timeOut: '过期时间：',
        rootCrtDownload: '根证书下载',
        primaryKey: '密钥',
        certificate: '证书',

        about: '关于',
        project: '项目地址',
        issue: '问题反馈',
        doc: '官方文档',
        star: '点亮 Star',
        description: 'Linux 服务器运维管理面板',
        forum: '论坛求助',
        doc2: '使用手册',
        currentVersion: '当前运行版本：',

        license: '许可证',
        bindNode: '绑定节点',
        menuSetting: '菜单设置',
        menuSettingHelper: '当只存在 1 个子菜单时，菜单栏将仅展示该子菜单',
        showAll: '全部显示',
        hideALL: '全部隐藏',
        ifShow: '是否显示',
        menu: '菜单',
        confirmMessage: '即将刷新页面更新高级功能菜单列表，是否继续？',
        compressPassword: '压缩密码',
        backupRecoverMessage: '请输入压缩或解压缩密码（留空则不设置）',
    },
    license: {
        community: '社区版',
        oss: '社区版',
        pro: '专业版',
        trial: '试用',
        add: '添加社区版',
        licenseAlert: '仅当许可证正常绑定到节点时，该许可证才能添加社区版节点，只有正常绑定到许可证的节点支持切换。',
        licenseUnbindHelper: '检测到该许可证存在社区版节点，请解绑后重试！',
        subscription: '订阅',
        perpetual: '永久授权',
        versionConstraint: '{0} 版本买断',
        forceUnbind: '强制解绑',
        forceUnbindHelper: '强制解绑，会忽略解绑过程中产生的错误并最终解除许可证绑定',
        updateForce: '强制更新（忽略解绑过程中的所有错误，确保最终操作成功）',
        trialInfo: '版本',
        authorizationId: '订阅授权 ID',
        authorizedUser: '被授权方',
        lostHelper: '许可证已达到最大重试次数，请手动点击同步按钮，以确保专业版功能正常使用，详情：',
        exceptionalHelper: '许可证同步验证异常，请手动点击同步按钮，以确保专业版功能正常使用，详情：',
        quickUpdate: '快速更新',
        unbindHelper: '解除绑定后将清除该节点所有专业版相关设置，是否继续？',
        importLicense: '导入许可证',
        importHelper: '请点击或拖动许可文件到此处',
        levelUpPro: '升级专业版',
        licenseSync: '许可证同步',
        knowMorePro: '了解更多',
        closeAlert: '当前页面可在面板设置中关闭显示',
        introduce: '功能介绍',
        waf: '升级专业版可以获得拦截地图、日志、封锁记录、地理位置封禁、自定义规则、自定义拦截页面等功能。',
        tamper: '升级专业版可以保护网站免受未经授权的修改或篡改。',
        tamperHelper: '操作失败，该文件或文件夹已经开启防篡改，请检查后重试！',
        setting: '升级专业版可以自定义面板 Logo、欢迎简介等信息。',
        monitor: '升级专业版可以查看网站的实时状态、访客趋势、访客来源、请求日志等信息。',
        alert: '升级专业版可通过短信接收告警信息，并查看告警日志，全面掌控各类关键事件，确保系统运行无忧。',
        node: '升级专业版可以使用 1Panel 管理多台 linux 服务器。',
        fileExchange: '升级专业版可以在多台服务器之间快速传输文件。',
        app: '升级专业版可通过手机 APP，查看服务信息、异常监控等。',
        cluster: '升级专业版可以管理 MySQL/Postgres/Reids 主从集群。',
    },
    clean: {
        scan: '开始扫描',
        scanHelper: '轻松梳理 1Panel 运行期间积累的垃圾文件',
        clean: '立即清理',
        reScan: '重新扫描',
        cleanHelper: '已勾选文件及目录清理后无法回滚（系统缓存文件清理需要重启服务），是否继续？',
        statusSuggest: '( 建议清理 )',
        statusClean: '( 很干净 )',
        statusEmpty: '非常干净，无需清理！',
        statusWarning: '( 谨慎操作 )',
        lastCleanTime: '上次清理时间: {0}',
        lastCleanHelper: '清理文件及目录：{0} 个， 总计清理：{1}',
        cleanSuccessful: '清理成功！',
        currentCleanHelper: '本次清理文件及目录：{0} 个， 总计清理：{1}',
        totalScan: '待清理垃圾文件共计： ',
        selectScan: '已选中垃圾文件共计： ',

        system: '系统垃圾',
        systemHelper: '快照、升级等过程中产生的临时文件以及版本迭代过程中废弃的文件内容',
        panelOriginal: '系统快照恢复前备份文件',
        backup: '临时备份目录',
        upgrade: '系统升级备份文件',
        upgradeHelper: '( 建议保留最新的升级备份用于系统回滚 )',
        cache: '系统缓存文件',
        cacheHelper: '( 谨慎操作，清理需要重启服务 )',
        snapshot: '系统快照临时文件',
        rollback: '恢复前备份目录',

        upload: '临时上传文件',
        uploadHelper: '系统上传备份列表中上传的临时文件',
        download: '临时下载文件',
        downloadHelper: '系统从第三方备份账号下载的临时文件',
        directory: '文件夹',

        systemLog: '系统日志文件',
        systemLogHelper: '系统日志信息、容器构建或镜像拉取等日志信息以及计划任务中产生的日志文件',
        dockerLog: '容器操作日志文件',
        taskLog: '计划任务执行日志文件',
        shell: 'Shell 脚本计划任务',
        containerShell: '容器内执行 Shell 脚本计划任务',
        curl: 'CURL 计划任务',

        containerTrash: '容器垃圾',
        volumes: '存储卷',
        buildCache: '构建缓存',
    },
    app: {
        app: '应用',
        installName: '安装名称',
        installed: '已安装',
        all: '全部',
        version: '版本',
        detail: '详情',
        params: '参数',
        author: '作者',
        source: '来源',
        appName: '应用名称',
        deleteWarn: '删除操作会把所有数据和备份一并删除，此操作不可回滚，是否继续？',
        syncSuccess: '同步成功',
        canUpgrade: '可升级',
        backupName: '文件名称',
        backupPath: '文件路径',
        backupdate: '备份时间',
        versionSelect: '请选择版本',
        operatorHelper: '将对选中应用进行 {0} 操作，是否继续？',
        checkInstalledWarn: '未检测到 {0} ,请进入应用商店点击安装！',
        gotoInstalled: '去安装',
        limitHelper: '该应用已安装，不支持重复安装',
        deleteHelper: '{0}已经关联以下资源，请检查后重试！',
        checkTitle: '提示',
        defaultConfig: '默认配置',
        defaultConfigHelper: '已恢复为默认配置，保存后生效',
        forceDelete: '强制删除',
        forceDeleteHelper: '强制删除，会忽略删除过程中产生的错误并最终删除元数据',
        deleteBackup: '删除备份',
        deleteBackupHelper: '同时删除应用备份',
        deleteDB: '删除数据库',
        deleteDBHelper: '同时删除与应用关联的数据库',
        noService: '无{0}',
        toInstall: '去安装',
        param: '参数配置',
        syncAppList: '更新远程应用',
        alreadyRun: '已安装',
        less1Minute: '小于1分钟',
        appOfficeWebsite: '官方网站',
        github: '开源社区',
        document: '文档说明',
        updatePrompt: '当前应用均为最新版本',
        installPrompt: '尚未安装任何应用',
        updateHelper: '更新参数可能导致应用无法启动，请提前备份并谨慎操作',
        updateWarn: '更新参数需要重建应用，是否继续？',
        busPort: '服务端口',
        syncStart: '开始同步！请稍后刷新应用商店',
        advanced: '高级设置',
        cpuCore: '核心数',
        containerName: '容器名称',
        containerNameHelper: '可以为空，为空自动生成',
        allowPort: '端口外部访问',
        allowPortHelper: '允许端口外部访问会放开防火墙端口',
        appInstallWarn: '应用端口默认不允许外部访问，可以在下方高级设置中选择放开',
        upgradeStart: '开始升级！请稍后刷新页面',
        toFolder: '进入安装目录',
        editCompose: '编辑 compose 文件',
        editComposeHelper: '编辑 compose 文件可能导致软件安装失败',
        composeNullErr: 'compose 不能为空',
        takeDown: '已废弃',
        allReadyInstalled: '已安装',
        installHelper: '配置镜像加速可以解决镜像拉取失败的问题',
        installWarn: '当前未勾选端口外部访问，将无法通过外网IP:端口访问，是否继续？',
        showIgnore: '查看忽略应用',
        cancelIgnore: '取消忽略',
        ignoreList: '忽略列表',
        appHelper: '部分应用的安装使用说明请在应用详情页查看',
        backupApp: '升级前备份应用',
        backupAppHelper: '升级失败会使用备份自动回滚,请在日志审计-系统日志中查看失败原因',
        openrestyDeleteHelper: '强制删除 OpenResty 会删除所有的网站，请确认风险之后操作',
        downloadLogHelper1: '即将下载 {0} 应用所有日志，是否继续？',
        downloadLogHelper2: '即将下载 {0} 应用最近 {1} 条日志，是否继续？',
        syncAllAppHelper: '即将同步所有应用，是否继续？',
        hostModeHelper: '当前应用网络模式为 host 模式，如需放开端口，请在防火墙页面手动放开',
        showLocal: '本地应用',
        reload: '重载',
        upgradeWarn: '升级应用会替换 docker-compose.yml 文件，如有更改，可以点击查看文件对比',
        newVersion: '新版本',
        oldVersion: '当前版本',
        composeDiff: '文件对比',
        showDiff: '查看对比',
        useNew: '使用自定义版本',
        useDefault: '使用默认版本',
        useCustom: '自定义 docker-compose.yml',
        useCustomHelper: '使用自定义 docker-compose.yml 文件，可能会导致应用升级失败，如无必要，请勿勾选',
        diffHelper: '左侧为旧版本，右侧为新版，编辑之后点击使用自定义版本保存',
        pullImage: '拉取镜像',
        pullImageHelper: '在应用启动之前执行 docker pull 来拉取镜像',
        deleteImage: '删除镜像',
        deleteImageHelper: '删除应用相关镜像，删除失败任务不会终止',
        requireMemory: '内存需求',
        supportedArchitectures: '支持架构',
        link: '链接',
        showCurrentArch: '本服务器架构应用',
        syncLocalApp: '同步本地应用',
        memoryRequiredHelper: '当前应用内存需求 {0}',
        gpuConfig: '开启 GPU 支持',
        gpuConfigHelper: '请确保机器有 NVIDIA GPU 并且安装 NVIDIA 驱动 和 NVIDIA docker Container Toolkit',
        webUI: 'Web 访问地址',
        webUIPlaceholder: '例如：example.com:8080/login',
        defaultWebDomain: '默认访问地址',
        defaultWebDomainHepler: '如果应用端口为 8080，则跳转地址为 http(s)://默认访问地址:8080',
        webUIConfig: '当前应用尚未配置访问地址，请在应用参数或者前往面板设置进行设置！',
        toLink: '跳转',
        customAppHelper: '在使用自定义应用商店仓库之前，请确保没有任何已安装的应用。',
        forceUninstall: '强制卸载',
        syncCustomApp: '同步自定义应用',
        ignoreAll: '忽略后续所有版本',
        ignoreVersion: '忽略指定版本',
        specifyIP: '绑定主机 IP',
        specifyIPHelper: '设置端口绑定的主机地址/网卡（如果你不清楚这个的作用，请不要填写）',
        uninstallDeleteBackup: '卸载应用-删除备份',
        uninstallDeleteImage: '卸载应用-删除镜像',
        upgradeBackup: '应用升级前备份应用',
    },
    website: {
        primaryDomain: '主域名',
        otherDomains: '其他域名',
        static: '静态网站',
        deployment: '一键部署',
        supportUpType: '仅支持 .tar.gz 文件',
        zipFormat: '.tar.gz 压缩包结构：test.tar.gz 压缩包内，必需包含 {0} 文件',
        proxy: '反向代理',
        alias: '代号',
        ftpUser: 'FTP 账号',
        ftpPassword: 'FTP 密码',
        ftpHelper: '创建站点的同时，为站点创建一个对应 FTP 帐户，并且 FTP 目录指向站点所在目录。',
        remark: '备注',
        groupSetting: '分组管理',
        createGroup: '创建分组',
        appNew: '新装应用',
        appInstalled: '已装应用',
        create: '创建网站',
        delete: '删除网站',
        deleteApp: '删除应用',
        deleteBackup: '删除备份',
        domain: '域名',
        domainHelper: '一行一个域名，支持*和IP地址，支持"域名:端口"',
        addDomain: '新增域名',
        domainConfig: '域名设置',
        defaultDoc: '默认文档',
        perserver: '并发限制',
        perserverHelper: '限制当前站点最大并发数',
        perip: '单IP限制',
        peripHelper: '限制单个IP访问最大并发数',
        rate: '流量限制',
        rateHelper: '限制每个请求的流量上(单位:KB)',
        limitHelper: '启用流量控制',
        other: '其他',
        currentSSL: '当前证书',
        dnsAccount: 'DNS账号',
        applySSL: '证书申请',
        SSLList: '证书列表',
        createDnsAccount: 'DNS账户',
        aliyun: '阿里云',
        manual: '手动解析',
        key: '密钥',
        check: '查看',
        acmeAccountManage: 'Acme 账户',
        email: '邮箱',
        acmeAccount: 'Acme 账户',
        provider: '验证方式',
        dnsManual: '手动解析',
        expireDate: '过期时间',
        brand: '颁发组织',
        deploySSL: '部署',
        deploySSLHelper: '确定部署证书？',
        ssl: '证书',
        dnsAccountManage: 'DNS 账户',
        renewSSL: '续签',
        renewHelper: '确定续签证书？',
        renewSuccess: '续签证书',
        enableHTTPS: '启用 HTTPS',
        aliasHelper: '代号是网站目录的文件夹名称',
        lastBackupAt: '上次备份时间',
        null: '无',
        nginxConfig: 'Nginx配置',
        websiteConfig: '网站设置',
        basic: '基本',
        source: '配置文件',
        security: '安全',
        nginxPer: '性能调整',
        neverExpire: '永不过期',
        setDefault: '设为默认',
        deleteHelper: '相关应用状态不正常，请检查',
        toApp: '去已安装列表',
        cycle: '周期',
        frequency: '频率',
        ccHelper: '{0} 秒内累计请求同一URL超过 {1} 次,触发CC防御,封锁此IP',
        mustSave: '修改之后需要保存才能生效',
        fileExt: '文件扩展名',
        fileExtBlock: '文件扩展名黑名单',
        value: '值',
        enable: '开启',
        proxyAddress: '代理地址',
        proxyHelper: '例: 127.0.0.1:8080',
        forceDelete: '强制删除',
        forceDeleteHelper: '强制删除，会忽略删除过程中产生的错误并最终删除元数据',
        deleteAppHelper: '同时删除关联应用、数据库以及应用备份',
        deleteBackupHelper: '同时删除网站备份',
        deleteConfirmHelper: '删除操作无法回滚，请输入 <span style="color:red"> "{0}" </span> 删除',
        staticPath: '对应主目录:',
        limit: '限制方案',
        blog: '论坛/博客',
        imageSite: '图片站',
        downloadSite: '下载站',
        shopSite: '商城',
        doorSite: '门户',
        qiteSite: '企业',
        videoSite: '视频',
        errLog: '错误日志',
        accessLog: '网站日志',
        stopHelper: '停止站点后将无法正常访问，用户访问会显示当前站点停止页面，是否继续操作？',
        startHelper: '启用站点后，用户可以正常访问网站内容，是否继续操作？',
        sitePath: '网站目录',
        siteAlias: '网站代号',
        primaryPath: 'root 目录',
        folderTitle: '网站主要包含以下文件夹',
        wafFolder: '防火墙规则',
        indexFolder: '网站 root 目录（PHP 运行环境 静态网站代码存放目录）',
        logFolder: '网站日志',
        sslFolder: '网站证书',
        enableOrNot: '是否启用',
        oldSSL: '选择已有证书',
        manualSSL: '手动导入证书',
        select: '选择',
        selectSSL: '选择证书',
        privateKey: '私钥(KEY)',
        certificate: '证书(PEM格式)',
        HTTPConfig: 'HTTP 选项',
        HTTPSOnly: '禁止 HTTP',
        HTTPToHTTPS: '访问HTTP自动跳转到HTTPS',
        HTTPAlso: 'HTTP可直接访问',
        sslConfig: 'SSL 选项',
        disableHTTPS: '禁用 HTTPS',
        disableHTTPSHelper: '禁用 HTTPS会删除证书相关配置，是否继续？',
        SSLHelper: '注意：请勿将SSL证书用于非法网站 \n 如开启后无法使用HTTPS访问，请检查安全组是否正确放行443端口',
        SSLConfig: '证书设置',
        SSLProConfig: 'SSL 协议设置',
        supportProtocol: '支持的协议版本',
        encryptionAlgorithm: '加密算法',
        notSecurity: '（不安全）',
        encryptHelper:
            "Let's Encrypt 签发证书有频率限制，但足以满足正常需求，过于频繁操作会导致签发失败。具体限制请看 <a target=“_blank” href='https://letsencrypt.org/zh-cn/docs/rate-limits/'>官方文档</a> ",
        ipValue: '值',
        ext: '文件扩展名',
        wafInputHelper: '按行输入数据，一行一个',
        data: '数据',
        ever: '永久',
        nextYear: '一年后',
        noLog: '当前没有日志...',
        defaultServer: '默认站点',
        noDefaultServer: '未设置',
        defaultServerHelper:
            '设置默认站点后,所有未绑定的域名和IP都被定向到默认站点\n可有效防止恶意解析\n但同时会导致 WAF 未授权域名拦截失败',
        websiteDeploymentHelper: '使用从 1Panel 部署的应用创建网站',
        websiteStatictHelper: '在主机上创建网站目录',
        websiteProxyHelper:
            '代理已有服务。例如本机已安装使用 8080 端口的 halo 服务，那么代理地址为 http://127.0.0.1:8080',
        restoreHelper: '确认使用此备份恢复？',
        wafValueHelper: '值',
        runtimeProxyHelper: '使用从 1Panel 创建的运行环境',
        runtime: '运行环境',
        deleteRuntimeHelper: '运行环境应用需要跟网站一并删除，请谨慎处理',
        proxyType: '监听网络类型',
        unix: 'Unix 网络',
        tcp: 'TCP/IP 网络',
        phpFPM: 'FPM 配置文件',
        phpConfig: 'PHP 配置文件',
        updateConfig: '配置修改',
        isOn: '开启',
        isOff: '关闭',
        rewrite: '伪静态',
        rewriteMode: '方案',
        current: '当前',
        rewriteHelper: '若设置伪静态后，网站无法正常访问，请尝试设置回default',
        runDir: '运行目录',
        runUserHelper:
            '通过 PHP 容器运行环境部署的网站，需要将 index 和子目录下的所有文件、文件夹所有者和用户组设置为 1000，本地 PHP 环境需要参考本地 PHP-FPM 用户和用户组设置',
        userGroup: '运行用户/组',
        uGroup: '用户组',
        proxyPath: '前端请求路径',
        proxyPass: '后端代理地址',
        cache: '缓存',
        cacheTime: '缓存时间',
        enableCache: '开启缓存',
        proxyHost: '后端域名',
        disabled: '已停止',
        startProxy: '开启反向代理',
        stopProxy: '关闭反向代理',
        sourceFile: '源文',
        proxyHelper1: '访问这个目录时将会把目标URL的内容返回并显示',
        proxyPassHelper: '代理的站点，必须为可正常访问的URL',
        proxyHostHelper: '将域名添加到请求头传递到代理服务器',
        modifier: '匹配规则',
        modifierHelper: '例：= 精确匹配，~ 正则匹配，^~ 匹配路径开头 等',
        replace: '文本替换',
        addReplace: '添加文本替换',
        replaced: '搜索字符串（不能为空）',
        replaceText: '替换为字符串',
        replacedErr: '搜索字符串不能为空',
        replacedErr2: '搜索字符串不能重复',
        basicAuth: '密码访问',
        editBasicAuthHelper: '密码为非对称加密，无法回显，编辑需要重新设置密码',
        antiLeech: '防盗链',
        extends: '扩展名',
        browserCache: '浏览器缓存',
        leechLog: '记录防盗链日志',
        accessDomain: '允许的域名',
        leechReturn: '响应资源',
        noneRef: '允许来源为空',
        disable: '未启用',
        disableLeechHelper: '是否禁用防盗链',
        disableLeech: '禁用防盗链',
        ipv6: '监听 IPV6',
        leechReturnError: '请填写 HTTP 状态码',
        selectAcme: '选择 acme 账号',
        imported: '手动创建',
        importType: '导入方式',
        pasteSSL: '粘贴代码',
        localSSL: '选择服务器文件',
        privateKeyPath: '私钥文件',
        certificatePath: '证书文件',
        ipWhiteListHelper: 'IP 白名单的作用：所有规则对IP白名单无效',
        redirect: '重定向',
        sourceDomain: '源域名/路径',
        targetURL: '目标URL地址',
        keepPath: '保留URI参数',
        path: '路径',
        redirectType: '重定向类型',
        redirectWay: '方式',
        keep: '保留',
        notKeep: '不保留',
        redirectRoot: '重定向到首页',
        redirectHelper: '301永久重定向，302临时重定向',
        changePHPVersionWarn: '此操作不可回滚，是否继续',
        changeVersion: '切换版本',
        retainConfig: '是否保留 php-fpm.conf 和 php.ini 文件',
        runDirHelper2: '请确保二级运行目录位于 index 目录下',
        openrestyHelper: 'OpenResty 默认 HTTP 端口：{0} HTTPS 端口 ：{1}，可能影响网站域名访问和 HTTPS 强制跳转',
        primaryDomainHelper: '支持域名:端口',
        acmeAccountType: '账号类型',
        keyType: '密钥算法',
        tencentCloud: '腾讯云',
        containWarn: '其他域名中包含主域名，请重新输入',
        rewriteHelper2: '从应用商店安装的 WordPress 等应用，默认已经配置好伪静态，重复配置可能会报错',
        websiteBackupWarn: '仅支持导入本机备份，导入其他机器备份可能会恢复失败',
        ipWebsiteWarn: 'IP 为域名的网站，需要设置为默认站点才能正常访问',
        hstsHelper: '开启 HSTS 可以增加网站安全性',
        includeSubDomains: '子域',
        hstsIncludeSubDomainsHelper: '启用后，HSTS策略将应用于当前域名的所有子域名',
        defaultHtml: '默认页面',
        website404: '网站 404 错误页',
        domain404: '网站不存在页',
        indexHtml: '静态网站默认页',
        stopHtml: '网站停用页',
        indexPHP: 'PHP 网站默认页',
        sslExpireDate: '证书过期时间',
        website404Helper: '网站 404 错误页仅支持 PHP 运行环境网站和静态网站',
        sni: '回源 SNI',
        sniHelper: '反代后端为 https 的时候可能需要设置回源 SNI，具体需要看 CDN 服务商文档',
        huaweicloud: '华为云',
        createDb: '创建数据库',
        enableSSLHelper: '开启失败不会影响网站创建',
        batchAdd: '批量添加域名',
        generateDomain: '生成',
        global: '全局',
        subsite: '子网站',
        subsiteHelper: '子网站可以选择已存在的 PHP 和静态网站的目录作为主目录',
        parentWbeiste: '父级网站',
        deleteSubsite: '删除当前网站需要先删除子网站 {0}',
        loadBalance: '负载均衡',
        server: '节点',
        algorithm: '算法',
        ipHash: 'IP 哈希',
        ipHashHelper: '基于客户端 IP 地址将请求分配到特定服务器，可以确保特定客户端总是被路由到同一服务器',
        leastConn: '最小连接',
        leastConnHelper: '将请求发送到当前活动连接数最少的服务器',
        leastTime: '最小时间',
        leastTimeHelper: '将请求发送到当前活动连接时间最短的服务器',
        defaultHelper:
            '默认方法，请求被均匀分配到每个服务器，如果服务器有权重配置，则根据指定的权重分配请求，权重越高的服务器接收更多请求',
        weight: '权重',
        maxFails: '最大失败次数',
        maxConns: '最大连接数',
        strategy: '策略',
        strategyDown: '停用',
        strategyBackup: '备用',
        staticChangePHPHelper: '当前为静态网站，可以切换为 PHP 网站',
        proxyCache: '反代缓存',
        cacheLimit: '缓存空间限制',
        shareCahe: '缓存计数内存大小',
        cacheExpire: '缓存过期时间',
        shareCaheHelper: '每1M内存可以存储约8000个缓存对象',
        cacheLimitHelper: '超过限制会自动删除旧的缓存',
        cacheExpireJHelper: '超出时间缓存未命中将会被删除',
        realIP: '真实 IP',
        ipFrom: 'IP 来源',
        ipFromHelper:
            '通过配置可信 IP 来源，OpenResty 会分析 HTTP Header 中的 IP 信息，准确识别并记录访客的真实 IP 地址，包括在访问日志中',
        ipFromExample1: '如果前端是 Frp 等工具，可以填写 Frp 的 IP 地址，类似 127.0.0.1',
        ipFromExample2: '如果前端是 CDN，可以填写 CDN 的 IP 地址段',
        ipFromExample3: '如果不确定，可以填 0.0.0.0/0（ipv4）  ::/0（ipv6） [注意：允许任意来源 IP 不安全]',
        http3Helper:
            'HTTP/3 是 HTTP/2 的升级版本，提供更快的连接速度和更好的性能，但是不是所有浏览器都支持 HTTP/3，开启后可能会导致部分浏览器无法访问',

        changeDatabase: '切换数据库',
        changeDatabaseHelper1: '数据库关联用于备份恢复网站',
        changeDatabaseHelper2: '切换其他数据库会导致以前的备份无法恢复',
        saveCustom: '另存为模版',
        rainyun: '雨云',
        volcengine: '火山引擎',
        runtimePortHelper: '当前运行环境存在多个端口，请选择一个代理端口',
        runtimePortWarn: '当前运行环境没有端口，无法代理',
        cacheWarn: '请先关闭反代中的缓存开关',
        loadBalanceHelper: '创建负载均衡后，请前往‘反向代理’，添加代理并将后端地址设置为：http://<负载均衡名称>。',
        favorite: '收藏',
        cancelFavorite: '取消收藏',
        useProxy: '使用代理',
        useProxyHelper: '使用面板设置中的代理服务器地址',
        westCN: '西部数码',
        openBaseDir: '防跨站攻击',
        openBaseDirHelper: 'open_basedir 用于限制 PHP 文件访问路径，有助于防止跨站访问和提升安全性',
        serverCacheTime: '服务器缓存时间',
        serverCacheTimeHelper: '请求在服务器端缓存的时间，到期前相同请求会直接返回缓存结果，不再请求源站。',
        browserCacheTime: '浏览器缓存时间',
        browserCacheTimeHelper: '静态资源在浏览器本地缓存的时间，减少重复请求。到期前用户刷新页面会直接使用本地缓存。',
        donotLinkeDB: '不关联数据库',
        toWebsiteDir: '进入网站目录',
    },
    php: {
        short_open_tag: '短标签支持',
        max_execution_time: '最大脚本运行时间',
        max_input_time: '最大输入时间',
        memory_limit: ' 脚本内存限制',
        post_max_size: 'POST数据最大尺寸',
        file_uploads: '是否允许上传文件',
        upload_max_filesize: '允许上传文件的最大尺寸',
        max_file_uploads: '允许同时上传文件的最大数量',
        default_socket_timeout: 'Socket超时时间',
        error_reporting: '错误级别',
        display_errors: '是否输出详细错误信息',
        cgi_fix_pathinfo: '是否开启pathinfo',
        date_timezone: '时区',
        disableFunction: '禁用函数',
        disableFunctionHelper: '输入要禁用的函数，例如exec，多个请用,分割',
        uploadMaxSize: '上传限制',
        indexHelper: '为保障 PHP 网站正常运行，请将代码放置于主目录下的 index 目录，并避免重命名',
        extensions: '扩展模版',
        extension: '扩展',
        extensionsHelper: '多个扩展请用,分割',
        toExtensionsList: '查看扩展列表',
        containerConfig: '容器配置',
        containerConfigHelper: '环境变量等信息可以在创建完成之后在配置-容器配置中修改',
        dateTimezoneHelper: '示例：TZ=Asia/Shanghai（请根据需要自行添加）',
    },
    nginx: {
        serverNamesHashBucketSizeHelper: '服务器名字的hash表大小',
        clientHeaderBufferSizeHelper: '客户端请求的头buffer大小',
        clientMaxBodySizeHelper: '最大上传文件',
        keepaliveTimeoutHelper: '连接超时时间',
        gzipMinLengthHelper: '最小压缩文件',
        gzipCompLevelHelper: '压缩率',
        gzipHelper: '是否开启压缩传输',
        connections: '活动连接(Active connections)',
        accepts: '总连接次数(accepts)',
        handled: '总握手次数(handled)',
        requests: '总请求数(requests)',
        reading: '请求数(Reading)',
        writing: '响应数(Writing)',
        waiting: '驻留进程(Waiting)',
        status: '当前状态',
        configResource: '配置修改',
        saveAndReload: '保存并重载',
        clearProxyCache: '清除反代缓存',
        clearProxyCacheWarn: '此操作将删除缓存目录下的所有文件, 是否继续？',
        create: '新增模块',
        update: '编辑模块',
        params: '参数',
        packages: '软件包',
        script: '脚本',
        module: '模块',
        build: '构建',
        buildWarn: '构建 OpenResty 需要预留一定的 CPU 和内存，时间较长，请耐心等待',
        mirrorUrl: '软件源',
        paramsHelper: '例如：--add-module=/tmp/ngx_brotli',
        packagesHelper: '例如：git,curl 按,分割',
        scriptHelper: '编译之前执行的脚本，一般为下载模块源码，安装依赖等',
        buildHelper: '添加/修改模块之后点击构建，构建成功后会自动重启 OpenResty',
        defaultHttps: 'HTTPS 防窜站',
        defaultHttpsHelper1: '开启后可以解决 HTTPS 窜站问题',
    },
    ssl: {
        create: '申请证书',
        provider: '类型',
        manualCreate: '手动创建',
        acmeAccount: 'Acme 账号',
        resolveDomain: '解析域名',
        err: '错误',
        value: '记录值',
        dnsResolveHelper: '请到DNS解析服务商处添加以下解析记录：',
        detail: '详情',
        msg: '证书信息',
        ssl: '证书',
        key: '私钥',
        startDate: '生效时间',
        organization: '签发机构',
        renewConfirm: '是否确定给域名 {0} 申请证书？',
        autoRenew: '自动续签',
        autoRenewHelper: '距离到期时间30天自动续签',
        renewSuccess: '续签成功',
        renewWebsite: '该证书已经和以下网站关联，申请会同步应用到这些网站',
        createAcme: '创建账户',
        acmeHelper: 'Acme 账户用于申请免费证书',
        upload: '上传证书',
        applyType: '申请方式',
        apply: '申请',
        applyStart: '证书申请开始',
        getDnsResolve: '正在获取 DNS 解析值,请稍后 ...',
        selfSigned: '自签证书',
        ca: '证书颁发机构',
        commonName: '证书主体名称(CN)',
        caName: '机构名称',
        company: '公司/组织',
        department: '部门',
        city: '城市',
        province: '省份',
        country: '国家代号',
        commonNameHelper: '例如:',
        selfSign: '签发证书',
        days: '有效期',
        domainHelper: '一行一个域名,支持*和IP地址',
        pushDir: '推送证书到本地目录',
        dir: '目录',
        pushDirHelper: '会在此目录下生成两个文件，证书文件：fullchain.pem 密钥文件：privkey.pem',
        organizationDetail: '机构详情',
        fromWebsite: '从网站中获取',
        dnsMauanlHelper: '手动解析模式需要在创建完之后点击申请按钮获取 DNS 解析值',
        httpHelper: '使用 HTTP 模式需安装 OpenResty，且不支持申请泛域名证书。',
        buypassHelper: 'Buypass 大陆地区无法访问',
        googleHelper: '如何获取 EAB HmacKey 和 EAB kid',
        googleCloudHelper: 'Google Cloud API 大陆大部分地区无法访问',
        skipDNSCheck: '跳过 DNS 校验',
        skipDNSCheckHelper: '如果出现申请超时问题，请勾选此处，其他情况请勿勾选',
        cfHelper: '请勿使用 Global API Key',
        deprecated: '即将废弃',
        deprecatedHelper: '已经停止维护，可能会在以后的某个版本废弃，请使用腾讯云方式解析',
        disableCNAME: '禁用 CNAME',
        disableCNAMEHelper: '有 CNAME 配置的域名，如果申请失败，可以勾选此处',
        nameserver: 'DNS 服务器',
        nameserverHelper: '使用自定义的 DNS 服务器来校验域名',
        edit: '编辑证书',
        execShell: '申请证书之后执行脚本',
        shell: '脚本内容',
        shellHelper:
            '脚本默认执行目录为 1Panel 安装目录，如果有推送证书，那么执行目录为证书推送目录。默认超时时间 30 分钟',
        customAcme: '自定义 ACME 服务',
        customAcmeURL: 'ACME 服务 URL',
        baiduCloud: '百度云',
    },
    firewall: {
        create: '创建规则',
        edit: '编辑规则',
        ccDeny: 'CC 防护',
        ipWhiteList: 'IP 白名单',
        ipBlockList: 'IP 黑名单',
        fileExtBlockList: '文件扩展名黑名单',
        urlWhiteList: 'URL 白名单',
        urlBlockList: 'URL 黑名单',
        argsCheck: 'GET 参数校验',
        postCheck: 'POST 参数校验',
        cookieBlockList: 'Cookie 黑名单',

        dockerHelper: 'Linux 防火墙 {0} 无法禁用 Docker 端口映射，应用可以在 [已安装] 页面编辑参数来控制端口是否放开',
        quickJump: '快速跳转',
        used: '已使用',
        unUsed: '未使用',
        firewallHelper: '{0}系统防火墙',
        firewallNotStart: '当前未开启系统防火墙，请先开启！',
        restartFirewallHelper: '该操作将对当前防火墙进行重启操作，是否继续？',
        stopFirewallHelper: '系统防火墙关闭后，服务器将失去安全防护，是否继续？',
        startFirewallHelper: '系统防火墙开启后，可以更好的防护服务器安全，是否继续？',
        noPing: '禁 ping',
        noPingTitle: '是否禁 ping',
        noPingHelper: '禁 ping 后将无法 ping 通服务器，是否继续？',
        onPingHelper: '解除禁 ping 后您的服务器可能会被黑客发现，是否继续？',
        changeStrategy: '修改{0}策略',
        changeStrategyIPHelper1: 'IP 策略修改为【屏蔽】，设置后该 IP 将禁止访问服务器，是否继续？',
        changeStrategyIPHelper2: 'IP 策略修改为【放行】，设置后该 IP 将恢复正常访问，是否继续？',
        changeStrategyPortHelper1: '端口策略修改为【拒绝】，设置后端口将拒绝外部访问，是否继续？',
        changeStrategyPortHelper2: '端口策略修改为【允许】，设置后端口将恢复正常访问，是否继续？',
        stop: '禁止',
        portFormatError: '请输入正确的端口信息！',
        portHelper1: '多个端口，如：8080,8081',
        portHelper2: '范围端口，如：8080-8089',
        strategy: '策略',
        accept: '允许',
        drop: '拒绝',
        anyWhere: '所有 IP',
        address: '指定 IP',
        addressHelper: '支持输入 IP 或 IP 段',
        allow: '放行',
        deny: '屏蔽',
        addressFormatError: '请输入合法的 ip 地址！',
        addressHelper1: '支持输入 IP 或 IP 段：************ 或 **********/24',
        addressHelper2: '多个 IP 或 IP 段 请用 "," 隔开：************,**********/24',
        allIP: '所有 IP',
        portRule: '端口规则',
        createPortRule: '@:commons.button.create@:firewall.portRule',
        forwardRule: '端口转发',
        createForwardRule: '@:commons.button.create@:firewall.forwardRule',
        ipRule: 'IP 规则',
        createIpRule: '@:commons.button.create @:firewall.ipRule',
        userAgent: 'User-Agent 过滤',
        destination: '目的地',
        sourcePort: '源端口',
        targetIP: '目标 IP',
        targetPort: '目标端口',
        forwardHelper1: '如果是本机端口转发，目标IP为：127.0.0.1',
        forwardHelper2: '如果目标IP不填写，则默认为本机端口转发',
        forwardHelper3: '当前仅支持 IPv4 的端口转发',
    },
    runtime: {
        runtime: '运行环境',
        workDir: '工作目录',
        create: '创建运行环境',
        localHelper: '本地运行环境需要自行安装',
        versionHelper: 'PHP的版本,例 v8.0',
        buildHelper: '扩展越多，制作镜像时占用的 CPU 越高，可在创建环境后再安装扩展',
        openrestyWarn: 'PHP 需要升级  OpenResty 至 ******** 版本以上才能使用',
        toupgrade: '去升级',
        edit: '编辑运行环境',
        extendHelper: '未列出的扩展可手动输入并选择，如：输入 sockets 后选择下拉列表中的第一个',
        rebuildHelper: '编辑扩展后需要【重建】PHP 应用之后才能生效',
        rebuild: '重建 PHP 应用',
        source: 'PHP 扩展源',
        ustc: '中国科学技术大学',
        netease: '网易',
        aliyun: '阿里云',
        tsinghua: '清华大学',
        xtomhk: 'XTOM 镜像站（香港）',
        xtom: 'XTOM 镜像站（全球）',
        phpsourceHelper: '根据你的网络环境选择合适的源',
        appPort: '应用端口',
        externalPort: '外部映射端口',
        packageManager: '包管理器',
        codeDir: '项目目录',
        appPortHelper: '应用端口是指容器内部的端口',
        externalPortHelper: '外部映射端口是指容器对外暴露的端口',
        runScript: '启动命令',
        runScriptHelper: '启动命令列表是从源码目录下的 package.json 文件中解析而来',
        open: '放开',
        operatorHelper: '将对选中的运行环境进行 {0} 操作，是否继续？',
        taobao: '淘宝',
        tencent: '腾讯',
        imageSource: '镜像源',
        moduleManager: '模块管理',
        module: '模块',
        nodeOperatorHelper: '是否{0} {1} 模块？操作可能导致运行环境异常，请确认后操作',
        customScript: '自定义启动命令',
        customScriptHelper: '请填写完整的启动命令，例如：npm run start',
        portError: '不能填写相同端口',
        systemRestartHelper: '状态说明：中断-系统重启导致状态获取失败',
        javaScriptHelper: '请填写完整启动命令，例如：java -jar halo.jar -Xmx1024M -Xms256M',
        javaDirHelper: '目录中要包含 jar 包，子目录中包含也可',
        goHelper: '请填写完整启动命令，例如：go run main.go 或 ./main',
        goDirHelper: '目录中要包含 go 文件或者二进制文件，子目录中包含也可',
        extension: '扩展',
        installExtension: '是否确认安装扩展 {0}',
        loadedExtension: '已加载扩展',
        popularExtension: '常用扩展',
        uninstallExtension: '是否确认卸载扩展 {0}',
        phpConfigHelper: '修改配置需要重启运行环境，是否继续',
        operateMode: '运行模式',
        dynamic: '动态',
        static: '静态',
        ondemand: '按需',
        dynamicHelper: '动态调整进程数，灵活性高，适合流量波动较大或者低内存的网站',
        staticHelper: '固定进程数，适合高并发和稳定流量的网站，资源消耗较高',
        ondemandHelper: '进程按需启动和销毁，资源利用最优，但初始响应可能较慢',
        max_children: '允许创建的最大进程数',
        start_servers: '启动时创建的进程数',
        min_spare_servers: '最小空闲进程数',
        max_spare_servers: '最大空闲进程数',
        envKey: '名称',
        envValue: '值',
        environment: '环境变量',
        pythonHelper:
            '请填写完整启动命令，例如：pip install -r requirements.txt && python  manage.py runserver 0.0.0.0:5000',
        dotnetHelper: '请填写完整启动命令，例如 dotnet MyWebApp.dll',
        dirHelper: '说明：请填写容器内的目录路径',
        concurrency: '并发方案',
        loadStatus: '负载状态',
    },
    process: {
        pid: '进程ID',
        ppid: '父进程ID',
        numThreads: '线程',
        memory: '内存',
        diskRead: '磁盘读',
        diskWrite: '磁盘写',
        netSent: '上行',
        netRecv: '下行',
        numConnections: '连接',
        startTime: '启动时间',
        running: '运行中',
        sleep: '睡眠',
        stop: '停止',
        idle: '空闲',
        zombie: '僵尸进程',
        wait: '等待',
        lock: '锁定',
        blocked: '阻塞',
        cmdLine: '启动命令',
        basic: '基本信息',
        mem: '内存信息',
        openFiles: '文件打开',
        env: '环境变量',
        noenv: '无',
        net: '网络连接',
        laddr: '本地地址/端口',
        raddr: '远程地址/端口',
        stopProcess: '结束',
        viewDetails: '查看详情',
        stopProcessWarn: '是否确定结束此进程 (PID:{0})？',
        processName: '进程名称',
    },
    tool: {
        supervisor: {
            loadStatusErr: '获取进程状态失败，请检查 supervisor 服务状态',
            notSupport: '未检测到 Supervisor 服务，请前往脚本库页面手动安装',
            list: '守护进程',
            config: 'Supervisor 配置',
            primaryConfig: '主配置文件位置',
            notSupportCtl: '未检测到 supervisorctl，请前往脚本库页面手动安装',
            user: '启动用户',
            command: '启动命令',
            dir: '运行目录',
            numprocs: '进程数量',
            initWarn:
                '初始化操作需要修改配置文件的 [include] files 参数，修改后的服务配置文件所在目录: 1panel安装目录/1panel/tools/supervisord/supervisor.d/',
            operatorHelper: '将对 {0} 进行 {1} 操作，是否继续？',
            uptime: '运行时长',
            notStartWarn: '当前未开启 Supervisor ，请先启动',
            serviceName: '服务名称',
            initHelper: '检测到 Supervisor 服务未初始化，请点击顶部状态栏的初始化按钮进行配置',
            serviceNameHelper: 'systemctl 管理的 Supervisor 服务名称，一般为 supervisor、supervisord',
            restartHelper: '初始化会重启服务，导致原有的守护进程全部关闭',
            RUNNING: '运行中',
            STOPPED: '已停止',
            STOPPING: '停止中',
            STARTING: '启动中',
            FATAL: '启动失败',
            BACKOFF: '启动异常',
            ERROR: '错误',
            statusCode: '状态码',
            manage: '管理',
            autoRestart: '自动重启',
            EXITED: '已退出',
            autoRestartHelper: '程序异常退出后是否自动重启',
            autoStart: '自动启动',
            autoStartHelper: 'Supervisor 启动后是否自动启动服务',
        },
    },
    xpack: {
        expiresAlert: '温馨提醒：专业版试用将于 [{0}] 天后到期，届时将停止使用所有专业版功能。',
        name: '专业版',
        menu: '高级功能',
        upage: 'AI 建站',
        waf: {
            name: 'WAF',
            blackWhite: '黑白名单',
            globalSetting: '全局设置',
            websiteSetting: '网站设置',
            blockRecords: '封锁记录',
            world: '世界',
            china: '中国',
            intercept: '拦截',
            request: '请求',
            count4xx: '4xx 数量',
            count5xx: '5xx 数量',
            todayStatus: '今日状态',
            reqMap: '拦截地图（30日）',
            count: '数量',
            hight: '高',
            low: '低',
            reqCount: '请求数',
            interceptCount: '拦截数',
            requestTrends: '请求趋势（7日）',
            interceptTrends: '拦截趋势（7日）',
            whiteList: '白名单',
            blackList: '黑名单',
            ipBlackListHelper: '黑名单中的 IP 无法访问网站',
            ipWhiteListHelper: '白名单中的 IP 不受任何规则限制',
            uaBlackListHelper: '携带黑名单中的 User-Agent 的请求将被拦截',
            uaWhiteListHelper: '携带白名单中的 User-Agent 的请求不受任何规则限制',
            urlBlackListHelper: '请求黑名单中的 URL 将被拦截',
            urlWhiteListHelper: '请求白名单中的 URL 不受任何规则限制',
            ccHelper: '{0} 秒内累计请求任意网站超过 {1} 次，封锁此 IP {2}',
            blockTime: '封禁时间',
            attackHelper: '{0} 秒内累计拦截超过 {1} 次，封锁此 IP {2} ',
            notFoundHelper: '{0} 秒内累计请求返回 404 超过 {1} 次，封锁此 IP {2} ',
            frequencyLimit: '频率限制',
            regionLimit: '地区限制',
            defaultRule: '默认规则',
            accessFrequencyLimit: '访问频率限制',
            attackLimit: '攻击频率限制',
            notFoundLimit: '404 频率限制',
            urlLimit: 'URL 频率限制',
            urlLimitHelper: '为单个 URL 设置访问频率',
            sqliDefense: 'SQL 注入防御',
            sqliHelper: '识别请求中的 SQL 注入并拦截',
            xssHelper: '识别请求中的 XSS 并拦截',
            xssDefense: 'XSS 防御',
            uaDefense: 'User-Agent 规则',
            uaHelper: '包含常见的恶意爬虫规则',
            argsDefense: '参数规则',
            argsHelper: '禁止请求中携带恶意参数',
            cookieDefense: 'Cookie 规则',
            cookieHelper: '禁止请求中携带恶意 Cookie',
            headerDefense: 'Header 规则',
            headerHelper: '禁止请求中携带恶意 Header',
            httpRule: 'HTTP 规则',
            httpHelper:
                '设置允许访问的方法类型，如果想限制某些类型访问，请关闭这个类型的按钮，例如：仅允许 GET 类型访问，那么需要关闭除了 GET 之外的其他类型按钮',
            geoRule: '地区访问限制',
            geoHelper: '限制某些地区访问你的网站，例如：允许中国大陆访问，那么中国大陆以外的请求都会被拦截',
            ipLocation: 'IP 归属地',
            action: '动作',
            ruleType: '攻击类型',
            ipHelper: '请输入 IP',
            attackLog: '攻击日志',
            rule: '规则',
            ipArr: 'IPV4 范围',
            ipStart: '起始 IP',
            ipEnd: '结束 IP',
            ipv4: 'IPV4',
            ipv6: 'IPV6',
            urlDefense: 'URL 规则',
            urlHelper: '禁止访问的 URL',
            dirFilter: '目录过滤',
            sqlInject: 'SQL 注入',
            xss: 'XSS',
            phpExec: 'PHP 脚本执行',
            oneWordTrojan: '一句话木马',
            appFilter: '应用危险目录过滤',
            webshell: 'Webshell',
            args: '参数规则',
            protocolFilter: '协议过滤',
            javaFileter: 'Java 危险文件过滤',
            scannerFilter: '扫描器过滤',
            escapeFilter: '转义过滤',
            customRule: '自定义规则',
            httpMethod: 'HTTP 方法过滤',
            fileExt: '文件上传限制',
            fileExtHelper: '禁止上传的文件扩展名',
            deny: '禁止',
            allow: '允许',
            field: '匹配对象',
            pattern: '匹配条件',
            ruleContent: '匹配内容',
            contain: '包含',
            equal: '等于',
            regex: '正则表达式',
            notEqual: '不等于',
            customRuleHelper: '根据条件匹配执行相应动作',
            actionAllow: '允许',
            blockIP: '封禁 IP',
            code: '返回状态码',
            noRes: '断开连接 (444)',
            badReq: '参数错误 (400)',
            forbidden: '禁止访问 (403)',
            serverErr: '服务器错误 (500)',
            resHtml: '响应页面',
            allowHelper: '允许访问会跳过 WAF 规则，请谨慎使用',
            captcha: '人机验证',
            fiveSeconds: '5 秒验证',
            location: '地区',
            redisConfig: 'Redis 配置',
            redisHelper: '开启 Redis 可以将临时拉黑的 IP 持久化',
            wafHelper: '关闭之后所有网站将失去防护',
            attackIP: '攻击 IP',
            attackParam: '攻击信息',
            execRule: '命中规则',
            acl: '自定义规则',
            sql: 'SQL 注入',
            cc: '访问频率限制',
            isBlocking: '封禁中',
            isFree: '已解封',
            unLock: '解封',
            unLockHelper: '是否解封 IP:{0}?',
            saveDefault: '保存默认',
            saveToWebsite: '应用到网站',
            saveToWebsiteHelper: '是否将当前设置应用到所有网站？',
            websiteHelper: '此处为创建网站的默认设置，修改之后需要应用到网站才能生效',
            websiteHelper2: '此处为创建网站的默认设置，具体配置请在网站处修改',
            ipGroup: 'IP 组',
            ipGroupHelper: '一行一个 IP 或者 IP 段，支持 IPv4 和 IPv6， 例如：*********** 或 ***********/24',
            ipBlack: 'IP 黑名单',
            openRestyAlert: 'OpenResty 版本需要高于 {0}',
            initAlert: '首次使用需要初始化，会修改网站配置文件，原有的 WAF 配置会丢失，请一定提前备份 OpenResty',
            initHelper: '初始化操作将清除现有的 WAF 配置，您确定要进行初始化吗？',
            mainSwitch: '总开关',
            websiteAlert: '请先创建网站',
            defaultUrlBlack: 'URL 规则',
            htmlRes: '拦截页面',
            urlSearchHelper: '请输入 URL，支持模糊搜索',
            toCreate: '去创建',
            closeWaf: '关闭 WAF',
            closeWafHelper: '关闭 WAF 会使网站失去防护，是否继续',
            addblack: '拉黑',
            addwhite: '加白',
            addblackHelper: '是否把 IP:{0} 添加到默认黑名单?',
            addwhiteHelper: '是否把 IP:{0} 添加到默认白名单?',
            defaultUaBlack: 'User-Agent 规则',
            defaultIpBlack: '恶意 IP 组',
            cookie: 'Cookie 规则',
            urlBlack: 'URL 黑名单',
            uaBlack: 'User-Agent 黑名单',
            attackCount: '攻击频率限制',
            fileExtCheck: '文件上传限制',
            geoRestrict: '地区访问限制',
            attacklog: '拦截记录',
            unknownWebsite: '未授权域名访问',
            geoRuleEmpty: '地区不能为空',
            unknown: '网站不存在',
            geo: '地区限制',
            revertHtml: '是否还原{0}为默认页面？',
            five_seconds: '5 秒验证',
            header: 'Header 规则',
            methodWhite: 'HTTP 规则',
            expiryDate: '有效期',
            expiryDateHelper: '验证通过后有效期内不再验证',
            defaultIpBlackHelper: '从互联网收集的一些恶意 IP，阻止其访问',
            notFoundCount: '404 频率限制',
            matchValue: '匹配值',
            headerName: '支持非特殊字符开头，英文、数字、-,长度3-30',
            cdnHelper: '使用 CDN 的网站可以打开此处来获取正确来源 IP',
            clearLogWarn: '清空日志将无法恢复，是否继续？',
            commonRuleHelper: '规则为模糊匹配',
            blockIPHelper: '封锁 IP 临时存储在 OpenResty 中，重启 OpenResty 会解封，可以通过拉黑功能永久拉黑',
            addWhiteUrlHelper: '是否把 URL {0} 添加到白名单?',
            dashHelper: '社区版也可使用全局设置和网站设置中的功能',
            wafStatusHelper: 'WAF 未开启，请在全局设置中打开',
            ccMode: '模式',
            global: '全局模式',
            uriMode: 'URL 模式',
            globalHelper: '全局模式：单位时间请求任意 URL 次数之和超过阈值即触发',
            uriModeHelper: 'URL 模式：单位时间请求单个 URL 次数超过阈值即触发',
            ip: 'IP 黑名单',
            globalSettingHelper: '带有【网站】标签的设置，需要在【网站设置】配置生效，全局设置仅为新建网站的默认设置',
            globalSettingHelper2: '设置生效需要【全局设置】和【网站设置】的开关同时打开',
            urlCCHelper: '{0} 秒内累计请求此 URL 超过 {1} 次，封锁此 IP {2} ',
            urlCCHelper2: 'URL 不能带参数',
            notContain: '不包含',
            urlcc: 'URL 频率限制',
            method: '请求类型',
            addIpsToBlock: '批量拉黑 IP',
            addUrlsToWhite: '批量加白 URL',
            noBlackIp: 'IP 已拉黑，无需再次拉黑',
            noWhiteUrl: 'URL 已加白，无需再次加白',
            spiderIpHelper:
                '包含百度、Bing、谷歌、360、神马、搜狗、字节、DuckDuckGo、Yandex，关闭之后会拦截所有蜘蛛访问',
            spiderIp: '蜘蛛 IP 池',
            geoIp: 'IP 地址库',
            geoIpHelper: '用来确认 IP 的地理位置',
            stat: '攻击报表',
            statTitle: '报表',
            attackIp: '攻击 IP',
            attackCountNum: '攻击次数',
            percent: '占比',
            addblackUrlHelper: '是否把 URL:{0} 添加到默认黑名单?',
            rce: '远程代码执行',
            software: '软件',
            cveHelper: '包含常见软件、框架的漏洞',
            vulnCheck: '补充规则',
            ssrf: 'SSRF 漏洞',
            afr: '任意文件读取',
            ua: '未授权访问',
            id: '信息泄露',
            aa: '认证绕过',
            dr: '目录遍历',
            xxe: 'XXE 漏洞',
            suid: '序列化漏洞',
            dos: '拒绝服务漏洞',
            afd: '任意文件下载',
            sqlInjection: 'SQL 注入',
            afw: '任意文件写入',
            il: '信息泄漏',
            clearAllLog: '清空所有日志',
            exportLog: '导出日志',
            appRule: '应用规则',
            appRuleHelper: '常见应用的规则，开启之后可以减少误报，一个网站只能使用一个规则',
            logExternal: '排除记录类型',
            ipWhite: 'IP 白名单',
            urlWhite: 'URL 白名单',
            uaWhite: 'User-Agent 白名单',
            logExternalHelper:
                '排除记录类型不会被记录到日志中，黑白名单、地区访问限制、自定义规则会产生大量日志，建议排除',
            ssti: 'SSTI 攻击',
            crlf: 'CRLF 注入',
            strict: '严格模式',
            strictHelper: '使用更严格的规则来校验请求',
            saveLog: '保存日志',
            remoteURLHelper: '远程 URL 需要保证每行一个 IP 并且没有其他字符',
            notFound: 'Not Found (404)',
            serviceUnavailable: '服务不可用 (503)',
            gatewayTimeout: '网关超时 (504)',
            belongToIpGroup: '属于 IP 组',
            notBelongToIpGroup: '不属于 IP 组',
            unknownWebsiteKey: '未知域名',
            special: '指定',
        },
        monitor: {
            name: '网站监控',
            pv: '浏览量',
            uv: '访客数',
            flow: '流量',
            ip: '独立 IP',
            spider: '蜘蛛',
            visitors: '访客趋势',
            uvMap: '访客地图 (30日)',
            qps: '实时请求数（1分钟）',
            flowSec: '实时流量（1分钟）',
            excludeCode: '排除状态码',
            excludeUrl: '排除 URL',
            excludeExt: '排除扩展名',
            cdnHelper: '通过 CDN 设置的 Header 来获取真实 IP',
            reqRank: '访问统计',
            refererDomain: '来源',
            os: '操作系统',
            browser: '浏览器/客户端',
            device: '设备',
            showMore: '查看更多',
            unknown: '其他',
            pc: '电脑',
            mobile: '移动端',
            wechat: '微信',
            machine: '机器',
            tencent: '腾讯浏览器',
            ucweb: 'UC 浏览器',
            '2345explorer': '2345 浏览器',
            huaweibrowser: '华为浏览器',
            log: '请求日志',
            statusCode: '状态码',
            requestTime: '响应时间',
            flowRes: '响应流量',
            method: '请求类型',
            statusCodeHelper: '可在上方输入状态码',
            statusCodeError: '状态码类型错误',
            methodHeper: '可在上方输入请求类型',
            baidu: '百度',
            google: '谷歌',
            bing: '必应',
            bytes: '今日头条',
            sogou: '搜狗',
            failed: '错误',
            ipCount: 'IP 数',
            spiderCount: '蜘蛛请求',
            averageReqTime: '平均响应时间',
            totalFlow: '总流量',
            logSize: '日志文件大小',
            realIPType: '真实IP获取方式',
            fromHeader: '从 HTTP Header 中获取',
            fromHeaders: '从 Header 列表中获取',
            header: 'HTTP Header',
            cdnConfig: 'CDN 适配',
            xff1: '获取 X-Forwarded-For 的上一级代理地址',
            xff2: '获取 X-Forwarded-For 的上上一级代理地址',
            xff3: '获取 X-Forwarded-For 的上上上一级代理地址',
            xffHealper: '例如：X-Forwarded-For: <client>,<proxy1>,<proxy2>,<proxy3> 上一级代理会取最后一个 IP <proxy3>',
            headersHelper: '从下列常用的 CDN 携带真实 IP 的 HTTP Header 中获取，取第一个能获取到的值',
            monitorCDNHelper: '修改网站监控的 CDN 配置会同步更新 WAF 的 CDN 配置',
            wafCDNHelper: '修改 WAF 的 CDN 配置会同步更新网站监控的 CDN 配置',
            statusErr: '状态码格式错误',
            shenma: '神马搜索',
            duckduckgo: 'DuckDuckGo',
            '360': '360 搜索',
            excludeUri: '排除 Uri',
            top100Helper: '显示 Top 100 的数据',
            logSaveDay: '日志保存天数',
            cros: 'Chrome OS',
            theworld: '世界之窗浏览器',
            edge: 'Edge',
            maxthon: '遨游浏览器',
            monitorStatusHelper: '监控未开启，请在设置中打开',
            excludeIp: '排除 IP',
            excludeUa: '排除 User-Agent',
            remotePort: '远程端口',
            unknown_browser: '未知',
            unknown_os: '未知',
            unknown_device: '未知',
            logSaveSize: '最大日志保存大小',
            logSaveSizeHelper: '此处为单个网站的日志保存大小',
            '360se': '360 安全浏览器',
            websites: '网站列表',
            trend: '趋势统计',
            reqCount: '请求数',
            uriHelper: '可以使用 /test/* 或者 /*/index.php 来排除 Uri',
        },
        tamper: {
            tamper: '网站防篡改',
            ignoreTemplate: '排除目录模版',
            protectTemplate: '保护文件模版',
            templateContent: '模版内容',
            template: '模版',
            tamperHelper1:
                '一键部署类型的网站，建议启用应用目录防篡改功能；如出现网站无法正常使用或备份、恢复失败的情况，请先关闭防篡改功能；',
            tamperHelper2: '将限制非排除目录下受保护文件的读写、删除、权限和所有者修改操作',
            tamperPath: '防护目录',
            tamperPathEdit: '修改路径',
            log: '拦截日志',
            totalProtect: '总防护',
            todayProtect: '今日防护',
            addRule: '添加规则',
            ignore: '排除目录',
            ignoreHelper: '一行一个，例： \ntmp\n./tmp',
            ignoreTemplateHelper: '添加要忽略的文件夹名，以 , 分隔，例：tmp,cache',
            templateRule: '长度1-512，名称不能含有{0}等符号',
            ignoreHelper1: '添加要忽略的文件夹名或特定路径',
            ignoreHelper2: '要忽略特定文件夹，请使用以 ./ 开头的相对路径',
            protect: '保护文件',
            protectHelper: '一行一个，例： \npng\n./test.css',
            protectTemplateHelper: '添加要忽略的文件名或后缀名，以 , 分隔，例：conf,.css',
            protectHelper1: '可指定文件名、后缀名或特定文件进行保护',
            protectHelper2: '要保护特定文件，请使用以 ./ 开头的相对路径',
            enableHelper: '即将启用下列网站的防篡改功能，以提升网站安全性，是否继续？',
            disableHelper: '即将关闭下列网站的防篡改功能，是否继续？',
        },
        setting: {
            setting: '界面设置',
            title: '面板描述',
            titleHelper: '将会显示在用户登录页面 (例: Linux 服务器运维管理面板，建议 8-15 位)',
            logo: 'Logo (不带文字)',
            logoHelper: '将会显示在菜单收缩时管理页面左上方 (建议图片大小为: 82px*82px)',
            logoWithText: 'Logo (带文字)',
            logoWithTextHelper: '将会显示在菜单展开时管理页面左上方 (建议图片大小为: 185px*55px)',
            favicon: '网站图标',
            faviconHelper: '网站图标 (建议图片大小为: 16px*16px)',
            reUpload: '选择文件',
            setHelper: '即将保存当前界面设置内容，是否继续？',
            setDefaultHelper: '即将恢复所有界面设置到初始状态，是否继续？',
            logoGroup: 'Logo',
            imageGroup: '图片',
            loginImage: '登录页图片',
            loginImageHelper: '将会显示在登录页面(建议图片大小为: 500*416px)',
            loginBgType: '登录页背景类型',
            loginBgImage: '登录页背景图片',
            loginBgImageHelper: '将会显示在登录页面背景图片(建议图片大小为: 1920*1080px)',
            loginBgColor: '登录页背景颜色',
            loginBgColorHelper: '将会显示在登录页面背景颜色',
            image: '图片',
            bgColor: '背景色',
            loginGroup: '登录页',
            loginBtnLinkColor: '按钮/链接颜色',
            loginBtnLinkColorHelper: '将会显示在登录页面按钮/链接颜色',
        },
        helper: {
            wafTitle1: '拦截地图',
            wafContent1: '统计并展示 30 天内的拦截地理位置分布',
            wafTitle2: '地区访问限制',
            wafContent2: '按照地理位置限制网站的访问来源',
            wafTitle3: '自定义拦截页面',
            wafContent3: '自定义请求被拦截之后的显示页面',
            wafTitle4: '自定义规则（ACL）',
            wafContent4: '根据自定义的规则拦截请求',

            tamperTitle1: '文件完整性监控',
            tamperContent1: '监控网站文件的完整性，包括核心文件、脚本文件和配置文件等。',
            tamperTitle2: '实时扫描和检测',
            tamperContent2: '通过实时扫描网站文件系统，检测是否存在异常或被篡改的文件。',
            tamperTitle3: '安全权限设置',
            tamperContent3:
                '通过合理的权限设置和访问控制策略，网站防篡改功能可以限制对网站文件的访问权限，减少潜在的攻击面。',
            tamperTitle4: '日志记录与分析',
            tamperContent4: '记录文件访问和操作日志，以便管理员进行后续的审计和分析，以及发现潜在的安全威胁。',

            settingTitle1: '自定义欢迎语',
            settingContent1: '在 1Panel 登录页上设置自定义的欢迎语。',
            settingTitle2: '自定义 Logo',
            settingContent2: '允许上传包含品牌名称或其他文字的 Logo 图像。',
            settingTitle3: '自定义网站 icon',
            settingContent3: '允许上传自定义的图标，以替代默认的浏览器图标，提升用户体验。',

            monitorTitle1: '访客趋势',
            monitorContent1: '统计并展示网站的访客趋势',
            monitorTitle2: '访客地图',
            monitorContent2: '统计并展示网站的访客地理位置分布',
            monitorTitle3: '访问统计',
            monitorContent3: '统计网站的请求信息，包括蜘蛛，访问设备，请求状态等',
            monitorTitle4: '实时监控',
            monitorContent4: '实时监控网站的请求信息，包括请求数，流量等',

            alertTitle1: '短信告警',
            alertContent1:
                '当服务器资源使用异常、网站及证书过期、新版本更新、密码过期等情况发生时，通过短信告警通知用户，确保及时处理。',
            alertTitle2: '告警日志',
            alertContent2: '为用户提供查看告警日志的功能，方便追踪和分析历史告警事件。',
            alertTitle3: '告警设置',
            alertContent3:
                '为用户提供自定义手机号、每日推送次数、每日推送时间的配置，方便用户的设置来更加合理的进行推送告警。',

            nodeTitle1: '一键添加节点',
            nodeContent1: '快速接入多台服务器节点',
            nodeTitle2: '批量升级',
            nodeContent2: '一次操作同步升级所有节点',
            nodeTitle3: '节点状态监控',
            nodeContent3: '实时掌握各节点运行状态',
            nodeTitle4: '快速远程连接',
            nodeContent4: '一键直连节点远程终端',

            fileExchangeTitle1: '密钥认证传输',
            fileExchangeContent1: '通过 SSH 密钥进行身份验证，确保传输的安全性。',
            fileExchangeTitle2: '高效文件同步',
            fileExchangeContent2: '仅同步变化内容，大幅提高传输速度与稳定性。',
            fileExchangeTitle3: '支持多节点互传',
            fileExchangeContent3: '可在不同节点间便捷传送项目文件，灵活管理多台服务器。',

            appTitle1: '灵活管理面板',
            appContent1: '随时随地轻松管理你的 1Panel 服务器。',
            appTitle2: '全面服务信息',
            appContent2: '在移动端进行应用、网站、Docker、数据库等基础管理，支持快速创建应用与网站。',
            appTitle3: '实时异常监控',
            appContent3: '移动端实时查看服务器状态、WAF 安全监控、网站访问统计与进程健康状况。',

            clusterTitle1: '主从部署',
            clusterContent1: '支持在不同节点创建 MySQL/Postgres/Redis 主从实例，自动完成主从关联与初始化',
            clusterTitle2: '主从管理',
            clusterContent2: '统一页面集中管理多个主从节点，查看其角色、运行状态等信息',
            clusterTitle3: '复制状态',
            clusterContent3: '展示主从复制状态与延迟信息，辅助排查同步异常问题',
        },
        node: {
            master: '主节点',
            masterBackup: '主节点备份',
            backupNode: '备份节点',
            backupFrequency: '备份频率（小时）',
            backupCopies: '备份记录保留份数',
            noBackupNode: '当前备份节点为空，请选择备份节点保存后重试！',
            masterBackupAlert:
                '当前未配置主节点备份，为保障数据安全，请尽快设置备份节点，便于主节点故障时可人工切换新主节点。',
            node: '节点',
            addr: '地址',
            nodeUnhealthy: '节点状态异常',
            deletedNode: '已删除节点 {0} 暂不支持升级操作！',
            nodeUnhealthyHelper: '检测到该节点状态异常，请在 [节点管理] 中检查后重试！',
            nodeUnbind: '节点未绑定许可证',
            nodeUnbindHelper: '检测到该节点未绑定许可证，请在 [ 面板设置 - 许可证 ] 菜单中绑定后重试！',
            memTotal: '内存总计',
            nodeManagement: '节点管理',
            addNode: '添加节点',
            connInfo: '连接信息',
            nodeInfo: '节点信息',
            syncInfo: '数据同步',
            syncHelper: '主节点数据发生变化时，实时同步到该子节点',
            syncBackupAccount: '备份账号设置',
            syncWithMaster: '升级为专业版后，将默认同步所有数据，可在节点管理中手动调整同步策略。',
            syncProxy: '系统代理设置',
            syncProxyHelper: '同步系统代理设置需要重启 Docker',
            syncProxyHelper1: '重启 Docker 可能会影响当前正在运行的容器服务。',
            syncProxyHelper2: '可前往 容器 - 配置 页面手动重启。',
            syncProxyHelper3: '同步系统代理设置需要重启 Docker，重启可能会影响当前正在运行的容器服务',
            syncProxyHelper4: '同步系统代理设置需要重启 Docker，可稍后前往 容器 - 配置 页面手动重启。',
            syncCustomApp: '同步自定义应用仓库',
            syncAlertSetting: '系统告警设置',
            syncNodeInfo: '节点基础数据',
            nodeSyncHelper: '节点信息同步将同步以下信息：',
            nodeSyncHelper1: '1. 公用的备份账号信息',
            nodeSyncHelper2: '2. 主节点与子节点的连接信息',

            nodeCheck: '可用性检查',
            checkSSH: '检查节点 SSH 连接',
            checkUserPermission: '检查节点用户权限',
            isNotRoot: '检查到该节点不支持免密 sudo，且当前为非 root 用户',
            checkLicense: '检查节点许可证状态',
            checkService: '检查节点已存在服务信息',
            checkPort: '检查节点端口可达',
            panelExist: '检查到该节点正在运行 1Panel V1 服务，请先通过迁移脚本升级至 V2 后再进行添加。',
            coreExist:
                '当前节点已作为主节点启用，无法直接作为从节点添加。请先将其降级为从节点后再添加，具体可参考文档。',
            agentExist: '检查到该节点已安装 1panel-agent，继续添加将保留现有数据，仅替换 1panel-agent 服务。',
            oldDataExist: '检查到该节点存在 1Panel V2 历史数据，将使用以下信息覆盖当前设置',
            errLicense: '检查到该节点绑定的许可证不可用，请检查后重试！',
            errNodePort: '检查到节点端口 [ {0} ] 无法访问，请检查防火墙或安全组是否已放行该端口。',

            reinstallHelper: '重新安装节点 {0}, 是否继续？',
            unhealthyCheck: '异常检查',
            fixOperation: '修复方案',
            checkName: '检查项',
            checkSSHConn: '检查 SSH 连接可用性',
            fixSSHConn: '手动编辑节点，确认连接信息',
            checkConnInfo: '检查 Agent 连接信息',
            checkStatus: '检查节点服务可用性',
            fixStatus: 'systemctl status 1panel-agent.service 检查服务是否已启动',
            checkAPI: '检查节点 API 可用性',
            fixAPI: '排查节点日志，检查防火墙端口是否正常放行',
            forceDelete: '强制删除',
            operateHelper: '将对以下节点进行 {0} 操作，是否继续？',
            uninstall: '删除节点数据',
            uninstallHelper: '将删除节点所有 1Panel 相关数据，谨慎选择！',
            forceDeleteHelper: '强制删除将会忽略节点删除错误，删除数据库元数据',
            baseDir: '安装目录',
            baseDirHelper: '安装目录为空时，默认安装在 /opt 目录下',
            nodePort: '节点端口',
            offline: '离线模式',
            freeCount: '免费额度 [{0}] ',
            offlineHelper: '节点为离线环境时使用',
        },
        customApp: {
            name: '自定义仓库',
            appStoreType: '仓库来源',
            appStoreUrl: '仓库地址',
            local: '本地',
            remote: '远程',
            imagePrefix: '镜像前缀',
            imagePrefixHelper:
                '用于自定义镜像前缀，自动修改 Compose 文件中的镜像字段。\n 例如，当镜像前缀设置为 1panel/custom 时，MaxKB 的镜像将变更为 1panel/custom/maxkb:v1.10.0。',
            closeHelper: '是否取消使用自定义仓库',
            appStoreUrlHelper: '仅支持 .tar.gz 格式',
            postNode: '同步至子节点',
            postNodeHelper: '把自定义商店包同步至子节点的安装目录下的 tmp/customApp/apps.tar.gz 中',
            nodes: '节点',
            selectNode: '选择节点',
            selectNodeError: '请选择节点',
            licenseHelper: '专业版支持自定义应用仓库功能',
        },
        alert: {
            isAlert: '是否告警',
            alertCount: '告警次数',
            clamHelper: '扫描到感染文件时触发告警通知',
            cronJobHelper: '定时任务执行失败时将触发告警通知',
            licenseHelper: '专业版支持短信告警功能',
            alertCountHelper: '每日最大告警次数',
            alert: '短信告警',
            logs: '告警日志',
            list: '告警列表',
            addTask: '创建告警',
            editTask: '编辑告警',
            alertMethod: '告警方式',
            alertMsg: '告警内容',
            alertRule: '告警规则',
            titleSearchHelper: '请输入告警标题，支持模糊搜索',
            taskType: '告警类型',
            ssl: '网站证书(SSL)到期',
            siteEndTime: '网站到期',
            panelPwdEndTime: '面板密码到期',
            panelUpdate: '面板新版本提醒',
            cpu: '面板服务器 CPU 过高告警',
            memory: '面板服务器内存过高告警',
            load: '面板服务器负载过高告警',
            disk: '面板服务器磁盘过高告警',
            certificate: '证书',
            remainingDays: '剩余天数',
            sendCount: '告警次数',
            sms: '短信通知',
            wechat: '微信公众号',
            dingTalk: '钉钉通知',
            feiShu: '飞书通知',
            mail: '邮箱通知',
            weCom: '企业微信',
            sendCountRulesHelper: '到期前发送告警的总数（每日仅发送一次）',
            panelUpdateRulesHelper: '新版本发送告警总数（每日仅发送一次）',
            oneDaySendCountRulesHelper: '每日发送告警的总数',
            siteEndTimeRulesHelper: '永不过期的网站，不触发告警',
            autoRenewRulesHelper: '证书开启自动续期，剩余天数小于 31 天，不触发告警',
            panelPwdEndTimeRulesHelper: '面板未设置密码到期时长，不能使用密码到期告警',
            sslRulesHelper: '所有 ssl 证书',
            diskInfo: '磁盘信息',
            monitoringType: '监测类型',
            autoRenew: '自动续签',
            useDisk: '占用磁盘',
            usePercentage: '占用百分比',
            changeStatus: '状态修改',
            disableMsg: '停止告警任务会导致该任务不再发送告警消息。是否继续？',
            enableMsg: '启用告警任务会让该任务发送告警消息。是否继续？',
            useExceed: '使用超过',
            useExceedRulesHelper: '使用超过指定值触发告警',
            cpuUseExceedAvg: 'CPU 平均使用率超过指定值',
            memoryUseExceedAvg: '内存平均使用率超过指定值',
            loadUseExceedAvg: '负载平均使用率超过指定值',
            cpuUseExceedAvgHelper: '指定时间内 CPU 平均使用率超过指定值',
            memoryUseExceedAvgHelper: '指定时间内内存平均使用率超过指定值',
            loadUseExceedAvgHelper: '指定时间内负载平均使用率超过指定值',
            resourceAlertRulesHelper: '注意：30 分钟内持续告警只发送一次短信',
            specifiedTime: '指定时间',
            deleteTitle: '删除告警',
            deleteMsg: '是否确认删除告警任务？',

            allSslTitle: '所有网站证书(SSL)到期告警',
            sslTitle: '网站「 {0} 」证书(SSL)到期告警',
            allSiteEndTimeTitle: '所有网站到期告警',
            siteEndTimeTitle: '网站「 {0} 」到期告警',
            panelPwdEndTimeTitle: '面板密码到期告警',
            panelUpdateTitle: '面板新版本提醒',
            cpuTitle: 'CPU 占用过高告警',
            memoryTitle: '内存占用过高告警',
            loadTitle: '负载占用过高告警',
            diskTitle: '挂载目录「{0}」的磁盘占用过高告警',
            allDiskTitle: '磁盘占用过高告警',

            timeRule: '剩余时间小于 {0} 天 (如未处理，次日会重新发送)',
            panelUpdateRule: '检测到面板有新版本时发送一次 (如未处理，次日会重新发送)',
            avgRule: '{0} 分钟内平均{1}占用超过 {2}% 触发，每天发送 {3} 次',
            diskRule: '挂载目录「 {0} 」的磁盘占用超过 {1}{2} 触发，每天发送 {3} 次',
            allDiskRule: '磁盘占用超过 {0}{1} 触发，每天发送 {2} 次',

            cpuName: ' CPU ',
            memoryName: '内存',
            loadName: '负载',
            diskName: '磁盘',

            syncAlertInfo: '同步告警信息',
            syncAlertInfoMsg: '是否同步告警信息内容状态？',
            pushError: '推送失败',
            pushSuccess: '推送成功',
            syncError: '同步失败',
            success: '告警成功',
            pushing: '推送中...',
            error: '告警失败',
            cleanLog: '清空日志',
            cleanAlertLogs: '清空告警日志',
            daily: '当日第 {0} 次告警',
            cumulative: '累计第 {0} 次告警',
            clams: '病毒扫描',
            taskName: '任务名称',
            cronJobType: '任务类型',
            clamPath: '扫描目录',
            cronjob: '计划任务',
            app: '备份应用',
            web: '备份网站',
            database: '备份数据库',
            directory: '备份目录',
            log: '备份日志',
            snapshot: '系统快照',
            clamsRulesHelper: '需要开启告警通知的病毒扫描任务',
            cronJobRulesHelper: '需要配置此类型的计划任务',
            clamsTitle: '病毒扫描「 {0} 」任务检测到感染文件告警',
            cronJobAppTitle: '计划任务-备份应用「 {0} 」任务失败告警',
            cronJobWebsiteTitle: '计划任务-备份网站「 {0} 」任务失败告警',
            cronJobDatabaseTitle: '计划任务-备份数据库「 {0} 」任务失败告警',
            cronJobDirectoryTitle: '计划任务-备份目录「 {0} 」任务失败告警',
            cronJobLogTitle: '计划任务-备份日志「 {0} 」任务失败告警',
            cronJobSnapshotTitle: '计划任务-系统快照「 {0} 」任务失败告警',
            cronJobShellTitle: '计划任务-Shell 脚本「 {0} 」任务失败告警',
            cronJobCurlTitle: '计划任务-访问 URL「 {0} 」任务失败告警',
            cronJobCutWebsiteLogTitle: '计划任务-切割网站日志「 {0} 」任务失败告警',
            cronJobCleanTitle: '计划任务-缓存清理「 {0} 」任务失败告警',
            cronJobNtpTitle: '计划任务-同步服务器时间「 {0} 」任务失败告警',
            clamsRule: '病毒扫描任务检测到感染文件告警，每天发送 {0} 次',
            cronJobAppRule: '备份应用任务失败告警，每天发送 {0} 次',
            cronJobWebsiteRule: '备份网站任务失败告警，每天发送 {0} 次',
            cronJobDatabaseRule: '备份数据库任务失败告警，每天发送 {0} 次',
            cronJobDirectoryRule: '备份目录任务失败告警，每天发送 {0} 次',
            cronJobLogRule: '备份日志任务失败告警，每天发送 {0} 次',
            cronJobSnapshotRule: '系统快照任务失败告警，每天发送 {0} 次',
            cronJobShellRule: 'Shell 脚本任务失败告警，每天发送 {0} 次',
            cronJobCurlRule: '访问 URL任务失败告警，每天发送 {0} 次',
            cronJobCutWebsiteLogRule: '切割网站日志任务失败告警，每天发送 {0} 次',
            cronJobCleanRule: '缓存清理任务失败告警，每天发送 {0} 次',
            cronJobNtpRule: '同步服务器时间任务失败告警，每天发送 {0} 次',
            alertSmsHelper: '短信额度：总量 {0} 条，已使用 {1} 条',
            goBuy: '去购买',
            phone: '手机号',
            phoneHelper: '请填写真实的手机号，以免不能正常接收告警信息',
            dailyAlertNum: '每日告警次数',
            dailyAlertNumHelper: '每日告警通知的总次数，最多通知 100 次',
            timeRange: '时间范围',
            sendTimeRange: '可发送时间范围',
            sendTimeRangeHelper: '可推送{0}时间范围',
            defaultPhone: '默认使用与许可证绑定的账户手机号',
            noticeAlert: '通知告警',
            resourceAlert: '资源告警',
            agentOfflineAlertHelper: '节点开启离线告警，将通过主节点每半小时扫描执行一次告警任务',
            offline: '离线告警',
            offlineHelper: '设置为离线告警，将通过主节点每半小时扫描执行一次告警任务',
            offlineOff: '开启离线告警',
            offlineOffHelper: '开启离线告警，将通过主节点每半小时扫描执行一次告警任务',
            offlineClose: '关闭离线告警',
            offlineCloseHelper: '关闭离线告警，告警需通过子节点自行告警，请保证子节点网络畅通，以避免告警失败',
            alertNotice: '告警通知',
            methodConfig: '发送方式配置',
            commonConfig: '全局配置',
            smsConfig: '短信通知',
            smsConfigHelper: '配置短信通知号码',
            emailConfig: '邮箱',
            emailConfigHelper: '配置 SMTP 邮件发送服务',
            deleteConfigTitle: '删除告警配置',
            deleteConfigMsg: '是否确认删除告警配置？',
            test: '测试',
            alertTestOk: '测试通知成功',
            alertTestFailed: '测试通知失败',
            displayName: '显示名称',
            sender: '发信地址',
            password: '密码',
            host: 'SMTP 服务器',
            port: '端口号',
            encryption: '加密方式',
            recipient: '收件人',
            licenseTime: '许可证到期提醒',
            licenseTimeTitle: '许可证到期提醒',
            displayNameHelper: '邮件的发件人显示名称',
            senderHelper: '用于发送邮件的邮箱地址',
            passwordHelper: '邮件服务的授权码',
            hostHelper: 'SMTP 服务器地址，例如: smtp.qq.com',
            portHelper: 'SSL 通常为465，TLS 通常为587',
            sslHelper: '如果 SMTP 端口是 465，通常需要启用 SSL',
            tlsHelper: '如果 SMTP 端口是 587，通常需要启用 TLS',
        },
        theme: {
            lingXiaGold: '凌霞金',
            classicBlue: '经典蓝',
            freshGreen: '清新绿',
            customColor: '自定义主题色',
            setDefaultHelper: '即将恢复主题配色到初始状态，是否继续？',
            setHelper: '即将保存当前选定的主题配色，是否继续？',
        },
        app: {
            app: 'APP',
            title: '面板别名',
            titleHelper: '面板别名用于 APP 端的显示（默认面板别名）',
            qrCode: '二维码',
            apiStatusHelper: '面板 APP 需要开启 API 接口功能',
            apiInterfaceHelper: '支持面板 API 接口访问功能（面板 APP 需要开启该功能）',
            apiInterfaceHelper1:
                '面板 APP 访问需将访问者添加至白名单，非固定 IP 建议添加 0.0.0.0/0（所有 IPv4），::/0（所有 IPv6）',
            qrCodeExpired: '刷新时间',
            apiLeakageHelper: '请勿泄露二维码，确保仅在受信任的环境中使用',
        },
        exchange: {
            exchange: '文件对传',
            exchangeConfirm: '是否将 {0} 节点文件/文件夹 {1} 传输到 {2} 节点 {3} 目录？',
        },
        cluster: {
            cluster: '应用高可用',
            name: '集群名称',
            addCluster: '添加集群',
            installNode: '安装节点',
            master: '主节点',
            slave: '从节点',
            replicaStatus: '主从状态',
            unhealthyDeleteError: '安装节点状态异常，请在节点列表检查后重试！',
            replicaStatusError: '状态获取异常 请检查主节点',
            masterHostError: '主节点 IP 不能为 127.0.0.1',
        },
    },
};
export default {
    ...fit2cloudZhLocale,
    ...message,
};
