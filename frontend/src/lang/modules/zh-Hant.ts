import fit2cloudTwLocale from 'fit2cloud-ui-plus/src/locale/lang/zh-tw';

const message = {
    commons: {
        true: '是',
        false: '否',
        example: '例如：',
        fit2cloud: '飞致云',
        lingxia: '凌霞',
        colon: '：',
        button: {
            run: '執行',
            prev: '上一步',
            next: '下一步',
            create: '創建',
            add: '添加',
            save: '保存',
            set: '設置',
            sync: '同步',
            delete: '刪除',
            edit: '編輯',
            enable: '啟用',
            disable: '停用',
            confirm: '確認',
            cancel: '取消',
            reset: '重置',
            setDefault: '恢復預設',
            restart: '重啟',
            conn: '連接',
            disConn: '斷開',
            clean: '清空',
            login: '登錄',
            close: '關閉',
            stop: '關閉',
            start: '開啟',
            view: '詳情',
            watch: '追蹤',
            handle: '執行',
            clone: '克隆',
            expand: '展開',
            collapse: '收起',
            log: '日誌',
            back: '返回',
            backup: '備份',
            recover: '恢復',
            retry: '重試',
            upload: '上傳',
            download: '下載',
            init: '初始化',
            verify: '驗證',
            saveAndEnable: '保存並啟用',
            import: '導入',
            power: '授權',
            export: '導出',
            search: '搜索',
            refresh: '刷新',
            get: '獲取',
            upgrade: '升級',
            update: '更新',
            ignore: '忽略升級',
            install: '安裝',
            copy: '復製',
            random: '隨機密碼',
            uninstall: '卸載',
            fullscreen: '網頁全屏',
            quitFullscreen: '退出網頁全屏',
            showAll: '顯示所有',
            hideSome: '隱藏部分',
            agree: '同意',
            notAgree: '不同意',
            preview: '預覽',
            open: '打開',
            notSave: '不保存',
            createNewFolder: '新建資料夾',
            createNewFile: '新建檔案',
            helpDoc: '幫助文档',
            bind: '綁定',
            unbind: '解綁',
            cover: '覆蓋',
            skip: '跳過',
            fix: '修復',
            down: '停止',
            up: '啟動',
            sure: '確定',
            show: '顯示',
            hide: '隱藏',
        },
        operate: {
            start: '啟動',
            stop: '停止',
            restart: '重新啟動',
            reload: '重新載入',
            rebuild: '重建',
            sync: '同步',
            up: '啟動',
            down: '停止',
            delete: '刪除',
        },
        search: {
            timeStart: '開始時間',
            timeEnd: '結束時間',
            timeRange: '至',
            dateStart: '開始日期',
            dateEnd: '結束日期',
        },
        table: {
            all: '所有',
            total: '共 {0} 條',
            name: '名稱',
            type: '類型',
            status: '狀態',
            records: '任務輸出',
            group: '分組',
            default: '默認',
            createdAt: '創建時間',
            publishedAt: '發布時間',
            date: '時間',
            updatedAt: '更新時間',
            operate: '操作',
            message: '信息',
            description: '描述',
            interval: '耗時',
            user: '用戶',
            title: '標題',
            port: '端口',
            forward: '轉發',
            protocol: '協議',
            tableSetting: '列表設置',
            refreshRate: '刷新頻率',
            noRefresh: '不刷新',
            selectColumn: '選擇列',
            local: '本地',
            serialNumber: '序號',
            manageGroup: '管理群組',
            backToList: '返回列表',
            keepEdit: '繼續編輯',
        },
        loadingText: {
            Upgrading: '系統升級中，請稍候...',
            Restarting: '系統重啟中，請稍候...',
            Recovering: '快照恢復中，請稍候...',
            Rollbacking: '快照回滾中，請稍候...',
        },
        msg: {
            noneData: '暫無數據',
            delete: '刪除 操作不可回滾，是否繼續？',
            clean: '清空 操作不可回滾，是否繼續？',
            closeDrawerHelper: '系統可能不會儲存您所做的變更，是否繼續？',
            deleteSuccess: '刪除成功',
            loginSuccess: '登錄成功',
            operationSuccess: '操作成功',
            copySuccess: '復製成功',
            notSupportOperation: '不支持的當前操作',
            requestTimeout: '請求超時,請稍後重試',
            infoTitle: '提示',
            notRecords: '當前任務未產生執行記錄',
            sureLogOut: '您是否確認退出登錄?',
            createSuccess: '創建成功',
            updateSuccess: '更新成功',
            uploadSuccess: '上傳成功',
            operateConfirm: '如果確認操作，請手動輸入 ',
            inputOrSelect: '請選擇或輸入',
            copyFailed: '復製失敗',
            operatorHelper: '將對以下{0}進行{1}操作，是否繼續？',
            backupSuccess: '備份成功',
            restoreSuccess: '備份成功',
            notFound: '抱歉，您訪問的頁面不存在',
            unSupportType: '不支持當前文件類型！',
            unSupportSize: '上傳文件超過 {0}M，請確認！',
            fileExist: '當前文件夾已存在該文件，不支持重復上傳！',
            fileNameErr: '僅支持上傳名稱包含英文、中文、數字或者 .-_ ,長度 1-256 位的文件',
            confirmNoNull: '請確認 {0} 值不為空',
            errPort: '錯誤的端口信息，請確認！',
            remove: '移出',
            backupHelper: '當前操作將對 {0} 進行備份，是否繼續？',
            recoverHelper: '將從 {0} 文件進行恢復，該操作不可回滾，是否繼續？',
            refreshSuccess: '重繪成功',
            rootInfoErr: '已經是根目錄了',
            resetSuccess: '重置成功',
            creatingInfo: '正在創建，無需此操作',
            installSuccess: '安裝成功',
            uninstallSuccess: '卸載成功',
        },
        login: {
            username: '用戶名',
            password: '密碼',
            welcome: '歡迎回來，請輸入用戶名和密碼登錄！',
            errorAuthInfo: '您輸入的用戶名或密碼不正確，請重新輸入！',
            errorMfaInfo: '錯誤的驗證信息，請重試！',
            captchaHelper: '驗證碼',
            errorCaptcha: '驗證碼錯誤！',
            notSafe: '暫無權限訪問',
            safeEntrance1: '當前環境已經開啟了安全入口登錄',
            safeEntrance2: '在 SSH 終端輸入以下命令來查看面板入口: 1pctl user-info',
            errIP1: '當前環境已經開啟了授權 IP 訪問',
            errDomain1: '當前環境已經開啟了訪問域名綁定',
            errHelper: '可在 SSH 終端輸入以下命令來重置綁定信息: ',
            codeInput: '請輸入 MFA 驗證器的 6 位驗證碼',
            mfaTitle: 'MFA 認證',
            mfaCode: 'MFA 驗證碼',
            title: 'Linux 服務器運維管理面板',
            licenseHelper: '《飛致雲社區軟件許可協議》',
            errorAgree: '請點擊同意社區軟件許可協議',
            agreeTitle: '服務協議及隱私保護',
            agreeContent:
                '為了更好的保障您的合法權益，請您閱讀並同意以下協議 &laquo; <a href="https://www.fit2cloud.com/legal/licenses.html" target="_blank"> 飛致雲社區軟件許可協議 </a> &raquo;',
            logout: '退出登錄',
        },
        rule: {
            username: '請輸入用戶名',
            password: '請輸入密碼',
            rePassword: '密碼不一致，請檢查後重新輸入',
            requiredInput: '請填寫必填項',
            requiredSelect: '請選擇必選項',
            illegalChar: '暫不支援注入字元 & ; $ \' ` ( ) " > < |',
            illegalInput: '輸入框中存在不合法字符',
            commonName: '支持非特殊字元開頭,英文、中文、數字、.-和_,長度1-128',
            userName: '支持非特殊字符開頭、英文、中文、數字和_,長度3-30',
            simpleName: '支持非底線開頭，英文、數字、_,長度3-30',
            simplePassword: '支持非底線開頭，英文、數字、_,長度1-30',
            dbName: '支持非特殊字符開頭，英文、中文、數字、.-_，長度1-64',
            composeName: '支持非特殊字符開頭，小寫英文、數字、-和_，長度1-256',
            imageName: '支持非特殊字符開頭、英文、數字、:@/.-_,長度1-256',
            volumeName: '支持英文、數字、.-和_,長度2-30',
            supervisorName: '支援非特殊字元開頭,英文、數字、-和_,長度1-128',
            complexityPassword: '請輸入長度為 8-30 位，並包含字母、數字、至少兩種特殊字符的密碼組合',
            commonPassword: '請輸入 6 位以上長度密碼',
            linuxName: '長度1-128，名稱不能含有{0}等符號',
            email: '請輸入正確的郵箱',
            number: '請輸入正確的數字',
            integer: '請輸入正確的正整數',
            ip: '請輸入正確的 IP 地址',
            host: '請輸入正確的 IP 或者域名',
            hostHelper: '支持輸入 ip 或者域名',
            port: '請輸入正確的端口,1-65535',
            selectHelper: '請選擇正確的 {0} 文件',
            domain: '域名格式錯誤',
            databaseName: '支持英文、數字、_,長度1-30',
            ipErr: 'IP [{0}] 格式錯誤,請檢查',
            numberRange: '數字範圍: {0} - {1}',
            paramName: '支持英文、數字、.-和_,長度2-64',
            paramComplexity: '支持英文、數字、{0},長度6-128，特殊字符不能在首尾',
            paramUrlAndPort: '格式為 http(s)://(域名/ip):(端口)',
            nginxDoc: '僅支持英文大小寫，數字，和.',
            appName: '支持英文、數字、-和_,長度2-30,並且不能以-_開頭和結尾',
            containerName: '支持字母、數字、_-和.,不能以-_或.開頭,長度2-128',
            mirror: '支持以 http(s):// 開頭，英文大小寫，數字，. / 和 - 的鏡像加速地址，且不能有空行',
            disableFunction: '僅支持字母、下劃線和,',
            leechExts: '僅支持字母數字和,',
            paramSimple: '支持小寫字母和數字,長度 1-128',
            filePermission: '權限錯誤',
            formatErr: '格式錯誤，檢查後重試',
            phpExtension: '僅支持 , _ 小寫英文和數字',
            paramHttp: '必須以 http:// 或 https:// 開頭',
            phone: '手機號碼格式不正確',
            authBasicPassword: '支持字母、數字以及常見特殊字符，長度1-72',
            length128Err: '長度不能超過128位',
            maxLength: '長度不能超過 {0} 位',
            alias: '支持英文、數字、-和_,長度1-30,並且不能以-、_開頭或結尾',
        },
        res: {
            paramError: '請求失敗,請稍後重試!',
            forbidden: '當前用戶無權限',
            serverError: '服務異常',
            notFound: '資源不存在',
            commonError: '請求失敗',
        },
        service: {
            serviceNotStarted: '當前未啟動 {0} 服務',
        },
        status: {
            running: '已啟動',
            done: '已完成',
            scanFailed: '未完成',
            success: '成功',
            waiting: '請等待',
            failed: '失敗',
            stopped: '已停止',
            error: '失敗',
            created: '已創建',
            restarting: '重啟中',
            uploading: '上傳中',
            unhealthy: '異常',
            removing: '移除中',
            paused: '已暫停',
            exited: '已停止',
            dead: '已結束',
            installing: '安裝中',
            enabled: '已啟用',
            disabled: '已停止',
            normal: '正常',
            building: '製作鏡像中',
            upgrading: '升級中',
            pending: '待編輯',
            rebuilding: '重建中',
            deny: '已屏蔽',
            accept: '已放行',
            used: '已使用',
            unused: '未使用',
            starting: '啟動中',
            recreating: '重建中',
            creating: '創建中',
            init: '等待申請',
            ready: '正常',
            applying: '申請中',
            uninstalling: '卸載中',
            lost: '已失聯',
            bound: '已綁定',
            unbind: '未綁定',
            exceptional: '異常',
            free: '空閒',
            enable: '已啟用',
            disable: '已停止',
            deleted: '已刪除',
            downloading: '下載中',
            packing: '打包中',
            sending: '下發中',
            healthy: '正常',
            executing: '執行中',
            installerr: '安裝失敗',
            applyerror: '申請失敗',
            systemrestart: '中斷',
            starterr: '啟動失敗',
            uperr: '啟動失敗',
        },
        units: {
            second: '秒',
            minute: '分鐘',
            hour: '小時',
            day: '天',
            week: '周',
            month: '月',
            year: '年',
            time: '次',
            core: '核',
            secondUnit: '秒',
            minuteUnit: '分鐘',
            hourUnit: '小時',
            dayUnit: '天',
            millisecond: '毫秒',
        },
    },
    menu: {
        home: '概覽',
        apps: '應用商店',
        website: '網站',
        project: '項目',
        config: '配置',
        ssh: 'SSH 配置',
        firewall: '防火墻',
        ssl: '證書',
        database: '數據庫',
        aiTools: 'AI',
        mcp: 'MCP',
        container: '容器',
        cronjob: '計劃任務',
        system: '系統',
        files: '文件',
        monitor: '監控',
        terminal: '終端',
        settings: '面板設置',
        toolbox: '工具箱',
        logs: '日誌審計',
        runtime: '運行環境',
        processManage: '進程管理',
        process: '進程',
        network: '網絡',
        supervisor: '進程守護',
        tamper: '防篡改',
        app: '應用',
        msgCenter: '任務中心',
    },
    home: {
        recommend: '推薦',
        dir: '目錄',
        restart_1panel: '重啟面板',
        restart_system: '重啟服務器',
        operationSuccess: '操作成功，正在重啟，請稍後手動刷新瀏覽器！',
        entranceHelper: '設置安全入口有利於提高系統的安全性，如有需要，前往 面板設置-安全 中，啟用安全入口',
        appInstalled: '已安裝應用',
        systemInfo: '系統信息',
        hostname: '主機名稱',
        platformVersion: '發行版本',
        kernelVersion: '內核版本',
        kernelArch: '系統類型',
        network: '流量',
        io: '磁盤 IO',
        ip: '主機地址',
        proxy: '系統代理',
        baseInfo: '基本信息',
        totalSend: '總發送',
        totalRecv: '總接收',
        rwPerSecond: '讀寫次數',
        ioDelay: '讀寫延遲',
        uptime: '啟動時間',
        runningTime: '運行時間',
        mem: '系統',
        swapMem: 'Swap 分區',

        runSmoothly: '運行流暢',
        runNormal: '運行正常',
        runSlowly: '運行緩慢',
        runJam: '運行堵塞',

        core: '物理核心',
        logicCore: '邏輯核心',
        loadAverage: '最近 1 分钟平均负载 | 最近 {n} 分钟平均负载',
        load: '負載',
        mount: '掛載點',
        fileSystem: '文件系統',
        total: '總計',
        used: '已用',
        cache: '快取',
        free: '空閒',
        shard: '分片',
        available: '可用',
        percent: '使用率',
        goInstall: '去安裝',

        networkCard: '網卡',
        disk: '磁盤',
    },
    tabs: {
        more: '更多',
        hide: '收起',
        closeLeft: '關閉左側',
        closeRight: '關閉右側',
        closeCurrent: '關閉當前',
        closeOther: '關閉其它',
        closeAll: '關閉所有',
    },
    header: {
        logout: '退出登錄',
    },
    database: {
        manage: '管理',
        deleteBackupHelper: '同時刪除數據庫備份',
        delete: '刪除操作無法回滾，請輸入 "',
        deleteHelper: '" 刪除此數據庫',
        create: '創建數據庫',
        noMysql: '數據庫服務 (MySQL 或 MariaDB)',
        noPostgresql: '數據庫服務 PostgreSQL',
        goUpgrade: '去應用商店升級',
        goInstall: '去應用商店安裝',
        isDelete: '已刪除',
        permission: '權限',
        permissionForIP: '指定 IP',
        permissionAll: '所有人(%)',
        localhostHelper: '將容器部署的資料庫權限配置為"localhost"會導致容器外部無法存取，請謹慎選擇！',
        databaseConnInfo: '連接信息',
        rootPassword: 'root 密碼',
        serviceName: '服務名稱',
        serviceNameHelper: '用於同一 network 下的容器間訪問',
        backupList: '備份列表',
        loadBackup: '導入備份',
        remoteAccess: '遠程訪問',
        remoteHelper: '多個 ip 以逗號分隔，例：************1,************2',
        remoteConnHelper: 'root 帳號遠程連接 MySQL 有安全風險，開啟需謹慎！',
        changePassword: '改密',
        changeConnHelper: '此操作將修改當前資料庫 {0}，是否繼續？',
        changePasswordHelper: '當前數據庫已經關聯應用，修改密碼將同步修改應用中數據庫密碼，修改後重啟生效。',

        portHelper: '該端口為容器對外暴露端口，修改需要單獨保存並且重啟容器！',

        confChange: '配置修改',
        confNotFound: '未能找到該應用配置文件，請在應用商店升級該應用至最新版本後重試！',

        loadFromRemote: '從伺服器同步',
        userBind: '綁定使用者',
        pgBindHelper: '此操作用於創建新使用者並將其綁定到目標資料庫，暫不支援選擇已存在於資料庫中的使用者。',
        pgSuperUser: '超級使用者',
        loadFromRemoteHelper: '此操作將同步服務器上數據庫信息到 1Panel，是否繼續？',
        passwordHelper: '無法獲取密碼，請修改',
        remote: '遠程',
        remoteDB: '遠程服務器',
        createRemoteDB: '添加遠程服務器',
        unBindRemoteDB: '解綁遠程服務器',
        unBindForce: '強制解綁',
        unBindForceHelper: '忽略解綁過程中的所有錯誤，確保最終操作成功',
        unBindRemoteHelper: '解綁遠程數據庫只會刪除綁定關係，不會直接刪除遠程數據庫',
        editRemoteDB: '編輯遠程服務器',
        localDB: '本地數據庫',
        address: '數據庫地址',
        version: '數據庫版本',
        userHelper: 'root 用戶或者擁有 root 權限的數據庫用戶',
        pgUserHelper: '具有超級管理員權限的數據庫使用者',
        ssl: '使用 SSL',
        clientKey: '客户端私钥',
        clientCert: '客户端证书',
        caCert: 'CA 证书',
        hasCA: '擁有 CA 證書',
        skipVerify: '忽略校验证书可用性检测',

        formatHelper: '當前資料庫字符集為 {0}，字符集不一致可能導致恢復失敗',
        selectFile: '選擇文件',
        dropHelper: '將上傳文件拖拽到此處，或者',
        clickHelper: '點擊上傳',
        supportUpType: '僅支持 sql、sql.gz、tar.gz 文件',
        zipFormat: 'tar.gz 壓縮包結構：test.tar.gz 壓縮包內，必需包含 test.sql',

        currentStatus: '當前狀態',
        baseParam: '基礎參數',
        performanceParam: '性能參數',
        runTime: '啟動時間',
        connections: '總連接數',
        bytesSent: '發送',
        bytesReceived: '接收',
        queryPerSecond: '每秒查詢',
        txPerSecond: '每秒事務',
        connInfo: '活動/峰值連接數',
        connInfoHelper: '若值過大，增加 max_connections',
        threadCacheHit: '線程緩存命中率',
        threadCacheHitHelper: '若過低,增加 thread_cache_size',
        indexHit: '索引命中率',
        indexHitHelper: '若過低,增加 key_buffer_size',
        innodbIndexHit: 'Innodb 索引命中率',
        innodbIndexHitHelper: '若過低,增加 innodb_buffer_pool_size',
        cacheHit: '查詢緩存命中率',
        cacheHitHelper: '若過低,增加 query_cache_size',
        tmpTableToDB: '創建臨時表到磁盤',
        tmpTableToDBHelper: '若過大,嘗試增加 tmp_table_size',
        openTables: '已打開的表',
        openTablesHelper: 'table_open_cache 配置值應大於等於此值',
        selectFullJoin: '沒有使用索引的量',
        selectFullJoinHelper: '若不為0，請檢查數據表的索引是否合理',
        selectRangeCheck: '沒有索引的 JOIN 量',
        selectRangeCheckHelper: '若不為0，請檢查數據表的索引是否合理',
        sortMergePasses: '排序後的合並次數',
        sortMergePassesHelper: '若值過大，增加sort_buffer_size',
        tableLocksWaited: '鎖表次數',
        tableLocksWaitedHelper: '若值過大，請考慮增加您的數據庫性能',

        performanceTuning: '性能調整',
        optimizationScheme: '優化方案',
        keyBufferSizeHelper: '用於索引的緩沖區大小',
        queryCacheSizeHelper: '查詢緩存，不開啟請設為0',
        tmpTableSizeHelper: '臨時表緩存大小',
        innodbBufferPoolSizeHelper: 'Innodb 緩沖區大小',
        innodbLogBufferSizeHelper: 'Innodb 日誌緩沖區大小',
        sortBufferSizeHelper: '* 連接數, 每個線程排序的緩沖大小',
        readBufferSizeHelper: '* 連接數, 讀入緩沖區大小',
        readRndBufferSizeHelper: '* 連接數, 隨機讀取緩沖區大小',
        joinBufferSizeHelper: '* 連接數, 關聯表緩存大小',
        threadStackelper: '* 連接數, 每個線程的堆棧大小',
        binlogCacheSizeHelper: '* 連接數, 二進製日誌緩存大小(4096的倍數)',
        threadCacheSizeHelper: '線程池大小',
        tableOpenCacheHelper: '表緩存',
        maxConnectionsHelper: '最大連接數',
        restart: '重啟數據庫',

        slowLog: '慢日誌',
        noData: '暫無慢日誌...',

        isOn: '開啟',
        longQueryTime: '閾值(秒)',
        thresholdRangeHelper: '請輸入正確的閾值(1 - 600)',

        timeout: '超時時間(秒)',
        timeoutHelper: '空閑連接超時時間，0表示不斷開',
        maxclients: '最大連接數',
        requirepassHelper: '留空代表沒有設置密碼，修改需要單獨保存並且重啟容器！',
        databases: '數據庫數量',
        maxmemory: '最大內存使用',
        maxmemoryHelper: '0 表示不做限製',
        tcpPort: '當前監聽端口',
        uptimeInDays: '已運行天數',
        connectedClients: '連接的客戶端數量',
        usedMemory: '當前 Redis 使用的內存大小',
        usedMemoryRss: '向操作系统申請的內存大小',
        usedMemoryPeak: 'Redis 的內存消耗峰值',
        memFragmentationRatio: '內存碎片比率',
        totalConnectionsReceived: '運行以來連接過的客戶端的總數量',
        totalCommandsProcessed: '運行以來執行過的命令的總數量',
        instantaneousOpsPerSec: '服務器每秒鐘執行的命令數量',
        keyspaceHits: '查找數據庫鍵成功的次數',
        keyspaceMisses: '查找數據庫鍵失敗的次數',
        hit: '查找數據庫鍵命中率',
        latestForkUsec: '最近一次 fork() 操作耗費的微秒數',
        redisCliHelper: '未檢測到 redis-cli 服務，請先啟用服務！',
        redisQuickCmd: 'Redis 快速命令',

        recoverHelper: '即將使用 [{0}] 對數據進行覆蓋，是否繼續?',
        submitIt: '覆蓋數據',

        baseConf: '基礎配置',
        allConf: '全部配置',
        restartNow: '立即重啟',
        restartNowHelper1: '修改配置後需要重啟生效，若您的數據需要持久化請先執行 save 操作。',
        restartNowHelper: '修改配置後需要重啟生效。',

        persistence: '持久化',
        rdbHelper1: '秒內,插入',
        rdbHelper2: '條數據',
        rdbHelper3: '符合任意一個條件將會觸發RDB持久化',
        rdbInfo: '請確認規則列表中值在 1-100000 之間',

        containerConn: '容器連接',
        connAddress: '地址',
        containerConnHelper: 'PHP 執行環境/容器安裝的應用程式使用此連接地址',
        remoteConn: '外部連接',
        remoteConnHelper2: '非容器環境或外部連接需使用此地址。',
        remoteConnHelper3: '預設存取地址為主機IP，修改請前往面板設定頁面的「預設存取地址」配置項。',
        localIP: '本機 IP',
    },
    aiTools: {
        model: {
            model: '模型',
            create: '新增模型',
            create_helper: '拉取 "{0}"',
            ollama_doc: '您可以瀏覽 Ollama 官方網站，搜尋並查找更多模型。',
            container_conn_helper: '容器間瀏覽或連接使用此地址',
            ollama_sync: '同步 Ollama 模型發現下列模型不存在，是否刪除？',
            from_remote: '該模型並非透過 1Panel 下載，無相關拉取日誌。',
            no_logs: '該模型的拉取日誌已被刪除，無法查看相關日誌。',
        },
        proxy: {
            proxy: 'AI 代理增強',
            proxyHelper1: '綁定域名並啟用 HTTPS，提高傳輸安全性',
            proxyHelper2: '限制 IP 瀏覽，防止在網路上暴露',
            proxyHelper3: '啟用即時串流',
            proxyHelper4: '創建後，您可以在網站列表中查看並管理',
            proxyHelper5: '啟用後，您可以在應用商店 - 已安裝 - Ollama - 參數中取消埠外部瀏覽以提高安全性',
            proxyHelper6: '如需關閉代理配置，可以在網站列表中刪除',
            whiteListHelper: '限制僅白名單中的 IP 可瀏覽',
        },
        gpu: {
            gpu: 'GPU 監控',
            base: '基礎資訊',
            gpuHelper: '目前系統未檢測到 NVIDIA-SMI 或者 XPU-SMI 指令，請檢查後重試！',
            driverVersion: '驅動版本',
            cudaVersion: 'CUDA 版本',
            process: '行程資訊',
            type: '類型',
            typeG: '圖形',
            typeC: '計算',
            typeCG: '計算+圖形',
            processName: '行程名稱',
            processMemoryUsage: '記憶體使用',
            temperatureHelper: 'GPU 溫度過高會導致 GPU 頻率下降',
            performanceStateHelper: '從 P0 (最大性能) 到 P12 (最小性能)',
            busID: '匯流排地址',
            persistenceMode: '持續模式',
            enabled: '開啟',
            disabled: '關閉',
            persistenceModeHelper: '持續模式能更加快速地響應任務，但相應待機功耗也會增加',
            displayActive: '顯卡初始化',
            displayActiveT: '是',
            displayActiveF: '否',
            ecc: '是否開啟錯誤檢查和糾正技術',
            computeMode: '計算模式',
            default: '預設',
            exclusiveProcess: '行程排他',
            exclusiveThread: '執行緒排他',
            prohibited: '禁止',
            defaultHelper: '預設: 行程可以並發執行',
            exclusiveProcessHelper: '行程排他: 只有一個 CUDA 上下文可以使用 GPU， 但可以由多個執行緒共享',
            exclusiveThreadHelper: '執行緒排他: 只有一個執行緒在 CUDA 上下文中可以使用 GPU',
            prohibitedHelper: '禁止: 不允許行程同時執行',
            migModeHelper: '用於建立 MIG 實例，在用戶層實現 GPU 的物理隔離。',
            migModeNA: '不支援',
        },
        mcp: {
            server: 'MCP Server',
            create: '创建 MCP Server',
            edit: '編輯 MCP Server',
            commandHelper: '例如：npx -y {0}',
            baseUrl: '外部訪問路徑',
            baseUrlHelper: '例如：http://192.168.1.2:8000',
            ssePath: 'SSE 路徑',
            ssePathHelper: '例如：/sse,注意不要與其他 Server 重複',
            environment: '環境變數',
            envKey: '變數名',
            envValue: '變數值',
            externalUrl: '外部連接地址',
            operatorHelper: '將對 {0} 進行 {1} 操作，是否繼續？',
            domain: '默認訪問地址',
            domainHelper: '例如：*********** 或者 example.com',
            bindDomain: '綁定網站',
            commandPlaceHolder: '當前僅支持 npx 和 二進制啟動的命令',
            importMcpJson: '導入 MCP Server配置',
            importMcpJsonError: 'mcpServers 結構不正確',
            bindDomainHelper: '綁定網站之後會修改所有已安裝 MCP Server 的訪問地址，並關閉端口的外部訪問',
            outputTransport: '輸出類型',
            streamableHttpPath: '流式傳輸路徑',
            streamableHttpPathHelper: '例如：/mcp, 注意不要與其他 Server 重複',
        },
    },
    container: {
        create: '創建容器',
        createByCommand: '命令創建',
        commandInput: '命令輸入',
        commandRule: '請輸入正確的 docker run 容器創建命令！',
        commandHelper: '將在伺服器上執行該條命令以創建容器，是否繼續？',
        edit: '編輯容器',
        updateHelper1: '檢測到該容器來源於應用商店，請注意以下兩點：',
        updateHelper2: '1. 當前修改內容不會同步到應用商店的已安裝應用。',
        updateHelper3: '2. 如果在已安裝頁面修改應用，當前編輯的部分內容將失效。',
        updateHelper4: '編輯容器需要重建，任何未持久化的數據將丟失，是否繼續操作？',
        containerList: '容器列表',
        operatorHelper: '將對以下容器進行 {0} 操作，是否繼續？',
        operatorAppHelper:
            '將對以下容器進行 {0} 操作，\n其中部分來源於應用商店，該操作可能會影響到該服務的正常使用。\n是否確認？',
        start: '啟動',
        stop: '停止',
        restart: '重啟',
        kill: '強製停止',
        pause: '暫停',
        unpause: '恢復',
        rename: '重命名',
        remove: '刪除',
        removeAll: '删除所有',
        containerPrune: '清理容器',
        containerPruneHelper1: '清理容器 將刪除所有處於停止狀態的容器。',
        containerPruneHelper2:
            '若容器來自於應用商店，在執行清理操作後，您需要前往 [應用商店] 的 [已安裝] 列表，點擊 [重建] 按鈕進行重新安裝。',
        containerPruneHelper3: '該操作無法回滾，是否繼續？',
        imagePrune: '清理鏡像',
        imagePruneSome: '未標簽鏡像',
        imagePruneSomeEmpty: '暫無待清理的未使用 none 標簽鏡像',
        imagePruneSomeHelper: '清理下列標簽為 none 且未被任何容器使用的鏡像',
        imagePruneAll: '未使用鏡像',
        imagePruneAllEmpty: '暫無待清理的未使用鏡像',
        imagePruneAllHelper: '清理下列未被任何容器使用的鏡像',
        networkPrune: '清理網絡',
        networkPruneHelper: '清理網絡 將刪除所有未被使用的網絡，該操作無法回滾，是否繼續？',
        volumePrune: '清理存儲卷',
        volumePruneHelper: '清理存儲卷 將刪除所有未被使用的本地存儲卷，該操作無法回滾，是否繼續？',
        cleanSuccess: '操作成功，本次清理數量: {0} 個！',
        cleanSuccessWithSpace: '操作成功，本次清理數量: {0} 個，釋放磁盤空間: {1}！',
        unExposedPort: '當前端口映射地址為 127.0.0.1，無法實現外部訪問',
        upTime: '運行時長',
        fetch: '過濾',
        lines: '條數',
        linesHelper: '請輸入正確的日誌獲取條數！',
        lastDay: '最近一天',
        last4Hour: '最近 4 小時',
        lastHour: '最近 1 小時',
        last10Min: '最近 10 分鐘',
        cleanLog: '清空日誌',
        downLogHelper1: '即將下載 {0} 容器所有日誌，是否繼續？',
        downLogHelper2: '即將下載 {0} 容器最近 {1} 條日誌，是否繼續？',
        cleanLogHelper: '清空日誌需要重啟容器，該操作無法回滾，是否繼續？',
        newName: '新名稱',
        workingDir: '工作目錄',
        source: '資源使用率',
        cpuUsage: 'CPU 使用',
        cpuTotal: 'CPU 總計',
        core: '核心數',
        memUsage: '內存使用',
        memTotal: '內存限額',
        memCache: '緩存使用',
        ip: 'IP 地址',
        cpuShare: 'CPU 權重',
        cpuShareHelper: '容器默認份額為 1024 個 CPU，增大可使當前容器獲得更多的 CPU 時間',
        inputIpv4: '請輸入 IPv4 地址',
        inputIpv6: '請輸入 IPv6 地址',

        containerFromAppHelper: '檢測到該容器來源於應用商店，應用操作可能會導致當前編輯失效',
        containerFromAppHelper1: '在已安裝應用程式列表點擊 [參數] 按鈕，進入編輯頁面即可修改容器名稱。',
        command: '命令',
        console: '控製臺交互',
        tty: '偽終端 ( -t )',
        openStdin: '標準輸入 ( -i )',
        custom: '自定義',
        emptyUser: '為空時，將使用容器默認的用戶登錄',
        privileged: '特權模式',
        privilegedHelper: '允許容器在主機上執行某些特權操作，可能會增加容器風險，請謹慎開啟！',
        editComposeHelper:
            '注意：設置的環境變數會默認寫入 1panel.env 文件。\n若需在容器中使用這些參數，還需在 compose 文件中手動添加 env_file 引用。',

        upgradeHelper: '倉庫名稱/鏡像名稱:鏡像版本',
        upgradeWarning2: '升級操作需要重建容器，任何未持久化的數據將會丟失，是否繼續？',
        oldImage: '當前鏡像',
        sameImageContainer: '同鏡像容器',
        sameImageHelper: '同鏡像容器可勾選後批量升級',
        targetImage: '目標鏡像',
        imageLoadErr: '未檢測到容器的鏡像名稱',
        appHelper: '該容器來源於應用商店，升級可能導致該服務不可用',

        resource: '資源',
        input: '手動輸入',
        forcePull: '強製拉取鏡像',
        forcePullHelper: '忽略服務器已存在的鏡像，重新拉取一次',
        server: '服務器',
        serverExample: '80, 80-88, ip:80 或者 ip:80-88',
        containerExample: '80 或者 80-88',
        exposePort: '暴露端口',
        exposeAll: '暴露所有',
        cmdHelper: '例： nginx -g "daemon off;"',
        entrypointHelper: '例： docker-entrypoint.sh',
        autoRemove: '容器退出後自動刪除容器',
        cpuQuota: 'CPU 限製',
        memoryLimit: '內存限製',
        limitHelper: '限製為 0 則關閉限製，最大可用為 {0}',
        macAddr: 'MAC 地址',
        mount: '掛載',
        volumeOption: '掛載卷',
        hostOption: '本機目錄',
        serverPath: '服務器目錄',
        containerDir: '容器目錄',
        volumeHelper: '請確認存儲卷內容輸入正確',
        modeRW: '讀寫',
        modeR: '只讀',
        mode: '權限',
        env: '環境變量',
        restartPolicy: '重啟規則',
        always: '一直重啟',
        unlessStopped: '未手動停止則重啟',
        onFailure: '失敗後重啟（默認重啟 5 次）',
        no: '不重啟',

        refreshTime: '刷新間隔',
        cache: '緩存',

        image: '鏡像',
        imagePull: '拉取鏡像',
        imagePush: '推送鏡像',
        imageDelete: '刪除鏡像',
        imageTagDeleteHelper: '移除與該映像 ID 相關聯的其他標籤',
        repoName: '倉庫名',
        imageName: '鏡像名',
        httpRepo: 'http 倉庫添加授信需要重啟 docker 服務',
        delInsecure: '刪除授信',
        delInsecureHelper: '刪除授信需要重啟 docker 服務，是否刪除？',
        pull: '拉取',
        path: '路徑',
        importImage: '導入鏡像',
        imageBuild: '構建鏡像',
        build: '構建鏡像',
        pathSelect: '路徑選擇',
        label: '標簽',
        imageTag: '鏡像標簽',
        push: '推送',
        fileName: '文件名',
        export: '導出',
        exportImage: '導出鏡像',
        size: '大小',
        tag: '標簽',
        tagHelper: '一行一個，例： \nkey1=value1\nkey2=value2',
        imageNameHelper: '鏡像名稱及 Tag，例：nginx:latest',
        cleanBuildCache: '清理建置快取',
        delBuildCacheHelper: '清理建置快取將刪除所有建置所產生的快取，此操作無法回復。是否繼續？',
        urlWarning: '路徑前綴不需要添加 http:// 或 https://，請修改',

        network: '網絡',
        networkHelper:
            'Deleting the 1panel-network container network will affect the normal use of some applications and runtime environments. Do you want to continue?',
        createNetwork: '創建網絡',
        networkName: '網絡名',
        driver: '模式',
        option: '參數',
        attachable: '可用',
        subnet: '子網',
        scope: 'IP 範圍',
        gateway: '網關',
        auxAddress: '排除 IP',

        volume: '存儲卷',
        volumeDir: '存儲卷目錄',
        nfsEnable: '啟用 NFS 存儲',
        nfsAddress: '地址',
        mountpoint: '掛載點',
        mountpointNFSHelper: '例：/nfs, /nfs-share',
        options: '可選參數',
        createVolume: '創建存儲卷',

        repo: '倉庫',
        createRepo: '添加倉庫',
        httpRepoHelper: '操作 HTTP 類型倉庫需要重啟 Docker 服務。',
        downloadUrl: '下載地址',
        imageRepo: '鏡像倉庫',
        repoHelper: '是否包含鏡像倉庫/組織/項目?',
        auth: '認證',
        mirrorHelper:
            '當存在多個加速器時，需要換行顯示，例： \nhttp://xxxxxx.m.daocloud.io \nhttps://xxxxxx.mirror.aliyuncs.com',
        registrieHelper: '當存在多個私有倉庫時，需要換行顯示，例：\n************1:8081 \n************2:8081',

        compose: '編排',
        fromChangeHelper: '切換來源將清空當前已編輯內容，是否繼續？',
        composePathHelper: '配置文件保存路徑: {0}',
        composeHelper: '通過 1Panel 編輯或者模版創建的編排，將保存在 {0}/docker/compose 路徑下',
        deleteFile: '刪除文件',
        deleteComposeHelper: '刪除容器編排的所有檔案，包括配置文件和持久化文件，請謹慎操作！',
        deleteCompose: '" 刪除此編排',
        createCompose: '創建編排',
        composeDirectory: '編排目錄',
        template: '模版',
        composeTemplate: '編排模版',
        createComposeTemplate: '創建編排模版',
        content: '內容',
        contentEmpty: '編排內容不能為空，請輸入後重試！',
        containerNumber: '容器數量',
        containerStatus: '容器狀態',
        exited: '已停止',
        running: '運行中 ( {0} / {1} )',
        composeDetailHelper: '該 compose 為 1Panel 編排外部創建。暫不支持啟停操作。',
        composeOperatorHelper: '將對 {0} 進行 {1} 操作，是否繼續？',
        composeDownHelper: '將停止並刪除 {0} 編排下所有容器及網絡，是否繼續？',

        setting: '配置',
        goSetting: '去修改',
        operatorStatusHelper: '此操作將{0}Docker 服務，是否繼續？',
        dockerStatus: 'Docker 服務',
        daemonJsonPathHelper: '請保證配置路徑與 docker.service 中指定的配置路徑保持一致。',
        mirrors: '鏡像加速',
        mirrorsHelper: '優先使用加速 URL 執行操作，設置為空則取消鏡像加速。',
        mirrorsHelper2: '具體操作配置請參照官方文檔',
        registries: '私有倉庫',
        ipv6Helper: '開啟 IPv6 後，需要增加 IPv6 的容器網路，具體操作配置請參照官方文檔',
        ipv6CidrHelper: '容器的 IPv6 地址池範圍',
        ipv6TablesHelper: 'Docker IPv6 對 iptables 規則的自動配置',
        experimentalHelper: '開啟 ip6tables 必須開啟此配置，否則 ip6tables 會被忽略',
        cutLog: '日誌切割',
        cutLogHelper1: '當前配置只會影響新創建的容器；',
        cutLogHelper2: '已經創建的容器需要重新創建使配置生效；',
        cutLogHelper3:
            '註意，重新創建容器可能會導致數據丟失。如果你的容器中有重要數據，確保在執行重建操作之前進行備份。',
        maxSize: '文件大小',
        maxFile: '保留份數',
        liveHelper: '允許在 Docker 守護進程發生意外停機或崩潰時保留正在運行的容器狀態',
        liveWithSwarmHelper: 'live-restore 守護進程配置與 Swarm 模式不兼容',
        iptablesDisable: '關閉 iptables',
        iptablesHelper1: 'Docker 對 iptables 規則的自動配置',
        iptablesHelper2: '關閉 iptables 會導致容器無法與外部網絡通信。',
        daemonJsonPath: '配置路徑',
        serviceUnavailable: '當前未啟動 Docker 服務，請在',
        startIn: '中開啟',
        sockPath: 'Socket 路徑',
        sockPathHelper: 'Docker 守護進程（Docker Daemon）與客戶端之間的通信通道',
        sockPathHelper1: '默認路徑：/var/run/docker-x.sock',
        sockPathMsg: '保存設定 Socket 路徑可能導致 Docker 服務不可用，是否繼續？',
        sockPathErr: '請選擇或輸入正確的 Docker sock 文件路徑',
        related: '相關資源',
        includeAppstore: '顯示應用程式商店容器',
        excludeAppstore: '隱藏應用商店容器',

        cleanDockerDiskZone: '清理 Docker 使用的磁碟空間',
        cleanImagesHelper: '( 清理所有未被任何容器使用的鏡像 )',
        cleanContainersHelper: '( 清理所有處於停止狀態的容器 )',
        cleanVolumesHelper: '( 清理所有未被使用的本地存儲卷 )',

        makeImage: '製作鏡像',
        newImageName: '新鏡像名稱',
        commitMessage: '提交信息',
        author: '作者',
        ifPause: '製作過程中是否暫停容器',
        ifMakeImageWithContainer: '是否根據此容器製作新鏡像？',
    },
    cronjob: {
        create: '創建計劃任務',
        edit: '編輯計劃任務',
        errImport: '文件內容異常:',
        errImportFormat: '導入的計劃任務數據或格式異常，請檢查後重試！',
        importHelper:
            '導入時將自動跳過重名計劃任務。任務默認設置為【停用】狀態，數據關聯異常時，設置為【待編輯】狀態。',
        changeStatus: '狀態修改',
        disableMsg: '停止計劃任務會導致該任務不再自動執行。是否繼續？',
        enableMsg: '啟用計劃任務會讓該任務定期自動執行。是否繼續？',
        taskType: '任務類型',
        nextTime: '近 5 次執行',
        record: '報告',
        viewRecords: '查看報告',
        shell: 'Shell 腳本',
        log: '備份日誌',
        logHelper: '備份系統日誌',
        logHelper1: '1. 1Panel 系統日誌',
        logHelper2: '2. 服務器的 SSH 登錄日誌',
        logHelper3: '3. 所有網站日誌',
        containerCheckBox: '在容器中執行（無需再輸入進入容器命令）',
        containerName: '容器名稱',
        ntp: '同步服務器時間',
        ntp_helper: '您可以在工具箱的快速設定頁面配置 NTP 伺服器',
        app: '備份應用',
        website: '備份網站',
        rulesHelper: '支援多個排除規則，使用英文逗號 , 分隔，例如：*.log,*.sql',
        lastRecordTime: '上次執行情況',
        database: '備份數據庫',
        missBackupAccount: '未能找到備份賬號',
        syncDate: '同步時間 ',
        clean: '缓存清理',
        curl: '訪問 URL',
        taskName: '任務名稱',
        cronSpec: '執行周期',
        cronSpecDoc: '自定義執行週期僅支援【分 時 日 月 週】格式，例如 0 0 * * *，具體可參考官方文件。',
        cronSpecHelper: '請輸入正確的執行周期',
        cleanHelper: '該操作將所有任務執行記錄、備份文件和日誌文件，是否繼續？',
        backupContent: '備份內容',
        directory: '備份目錄 / 檔案',
        sourceDir: '備份目錄',
        snapshot: '系統快照',
        allOptionHelper: '當前計劃任務為備份所有【{0}】，暫不支持直接下載，可在【{0}】備份列表中查看',
        exclusionRules: '排除規則',
        exclusionRulesHelper: '排除規則將對此次備份的所有壓縮操作生效',
        default_download_path: '默認下載地址',
        saveLocal: '同時保留本地備份（和雲存儲保留份數一致）',
        url: 'URL 地址',
        targetHelper: '備份賬號可在面板設置中維護',
        ignoreApp: '排除應用',
        withImage: '備份所有應用鏡像',
        retainCopies: '保留份數',
        retryTimes: '失敗重試次數',
        timeout: '逾時時間',
        ignoreErr: '忽略錯誤',
        ignoreErrHelper: '忽略備份過程中出現的錯誤，保證所有備份任務執行',
        retryTimesHelper: '為0表示失敗後不重試',
        retainCopiesHelper: '執行記錄及日誌保留份数',
        retainCopiesHelper1: '備份文件保留份数',
        retainCopiesUnit: ' 份 (查看)',
        cronSpecRule: '第 {0} 行中執行週期格式錯誤，請檢查後重試！',
        cronSpecRule2: '執行週期格式錯誤，請檢查後重試！',
        perMonthHelper: '每月 {0} 日 {1}:{2} 執行',
        perWeekHelper: '每週 {0} {1}:{2} 執行',
        perDayHelper: '每日 {0}:{1} 執行',
        perHourHelper: '每小時 {0}分 執行',
        perNDayHelper: '每 {0} 日 {1}:{2} 執行',
        perNHourHelper: '每 {0}小時 {1}分 執行',
        perNMinuteHelper: '每 {0}分 執行',
        perNSecondHelper: '每 {0}秒 執行',
        perMonth: '每月',
        perWeek: '每周',
        perHour: '每小時',
        perNDay: '每 N 日',
        perDay: '每天',
        perNHour: '每 N 時',
        perNMinute: '每 N 分鐘',
        perNSecond: '每 N 秒',
        day: '日',
        monday: '周一',
        tuesday: '周二',
        wednesday: '周三',
        thursday: '周四',
        friday: '周五',
        saturday: '周六',
        sunday: '周日',
        shellContent: '腳本內容',
        executor: '解釋器',
        errRecord: '錯誤的日誌記錄',
        errHandle: '任務執行失敗',
        noRecord: '當前計劃任務暫未產生記錄',
        cleanData: '刪除備份文件',
        cleanRemoteData: '刪除遠端備份檔案',
        cleanDataHelper: '刪除該任務執行過程中產生的備份文件',
        noLogs: '暫無任務輸出...',
        errPath: '備份路徑 [{0}] 錯誤，無法下載！',
        cutWebsiteLog: '切割網站日誌',
        cutWebsiteLogHelper: '切割的日誌文件會備份到 1Panel 的 backup 目錄下',

        requestExpirationTime: '上傳請求過期時間（小時）',
        unitHours: '單位：小時',
        alertTitle: '計畫任務-{0}「{1}」任務失敗告警',
        library: {
            script: '腳本',
            isInteractive: '交互式',
            interactive: '交互式腳本',
            interactiveHelper: '在腳本執行過程中需要用戶輸入參數或做出選擇，且無法用於計劃任務中。',
            library: '腳本庫',
            create: '添加腳本',
            edit: '修改腳本',
            groupHelper: '根據腳本特徵設置不同的分組，可以更快地對腳本進行篩選操作。',
            handleHelper: '將在 {0} 上執行 {1} 腳本，是否繼續？',
            noSuchApp: '未檢測到 {0} 服務，請前往腳本庫頁面手動安裝！',
            syncHelper: '即將同步系統腳本庫，該操作僅針對系統腳本，是否繼續？',
        },
    },
    monitor: {
        globalFilter: '全局過濾',
        enableMonitor: '監控狀態',
        storeDays: '保存天數',
        defaultNetwork: '預設網卡',
        defaultNetworkHelper: '預設監控和概覽介面顯示的網卡選項',
        cleanMonitor: '清空監控記錄',

        avgLoad: '平均負載',
        loadDetail: '負載詳情',
        resourceUsage: '資源使用率',
        networkCard: '網卡',
        read: '讀取',
        write: '寫入',
        readWriteCount: '讀寫次數',
        readWriteTime: '讀寫延遲',
        today: '今天',
        yesterday: '昨天',
        lastNDay: '近 {0} 天',
        lastNMonth: '近 {0} 月',
        lastHalfYear: '近半年',
        memory: '內存',
        cache: '緩存',
        disk: '磁盤',
        network: '網絡',
        up: '上行',
        down: '下行',
        interval: '采集間隔（分鐘）',

        gpuUtil: 'GPU 使用率',
        temperature: '溫度',
        performanceState: '性能狀態',
        powerUsage: '功耗',
        memoryUsage: '顯存使用率',
        fanSpeed: '風扇轉速',
    },
    terminal: {
        local: '本機',
        localHelper: 'local 名稱僅用於系統本機標識',
        connLocalErr: '無法自動認證，請填寫本地服務器的登錄信息！',
        testConn: '連接測試',
        saveAndConn: '保存並連接',
        connTestOk: '連接信息可用',
        connTestFailed: '連接不可用，請檢查連接信息！',
        host: '主機',
        createConn: '新建連接',
        noHost: '暫無主機',
        groupChange: '切換分組',
        expand: '全部展開',
        fold: '全部收縮',
        batchInput: '批量輸入',
        quickCommand: '快速命令',
        quickCommandHelper: '常用命令列表，用於在終端界面底部快速選擇',
        groupDeleteHelper: '移除組後，組內所有連接將遷移到 default 組內，是否繼續？',
        command: '命令',
        addHost: '添加主機',
        localhost: '本地服務器',
        ip: '主機地址',
        authMode: '認證方式',
        passwordMode: '密碼認證',
        rememberPassword: '記住認證資訊',
        keyMode: '私鑰認證',
        key: '私鑰',
        keyPassword: '私鑰密碼',
        emptyTerminal: '暫無終端連接',
        lineHeight: '字體行高',
        letterSpacing: '字體間距',
        fontSize: '字體大小',
        cursorBlink: '光標閃爍',
        cursorStyle: '光標樣式',
        cursorUnderline: '下劃線',
        cursorBlock: '塊狀',
        cursorBar: '條形',
        scrollback: '滾動行數',
        scrollSensitivity: '滾動速度',
        saveHelper: '是否確認保存當前終端配置？',
    },
    toolbox: {
        common: {
            toolboxHelper: '部分安装和使用问题，可参考',
        },
        swap: {
            swap: 'Swap',
            swapHelper1: 'Swap 的大小應該是物理內存的 1 到 2 倍，可根據具體情況進行調整；',
            swapHelper2: '在創建 Swap 文件之前，請確保系統硬盤有足夠的可用空間，Swap 文件的大小將佔用相應的磁盤空間；',
            swapHelper3:
                'Swap 可以幫助緩解內存壓力，但僅是一個備選項，過多依賴可能導致系統性能下降，建議優先考慮增加內存或者優化應用程序內存使用；',
            swapHelper4: '建議定期監控 Swap 的使用情況，以確保系統正常運行。',
            swapDeleteHelper: '此操作將移除 Swap 分區 {0}，出於系統安全考慮，不會自動刪除該文件，如需刪除請手動操作！',
            saveHelper: '請先保存當前設置！',
            saveSwap: '儲存當前配置將調整 Swap 分區 {0} 大小到 {1}，是否繼續？',
            swapMin: '分區大小最小值為 40 KB，請修改後重試！',
            swapMax: '分區大小最大值為 {0}，請修改後重試！',
            swapOff: '分區大小最小值為 40 KB，設置為 0 則關閉 Swap 分區。',
        },
        device: {
            dnsHelper: '伺服器地址域名解析',
            dnsAlert: '請注意！修改 /etc/resolv.conf 文件的配置時，重啟系統後會將文件恢復為預設值',
            dnsHelper1: '當存在多個DNS時，需換行顯示，例：\n114.114.114.114\n8.8.8.8',
            hostsHelper: '主機名解析',
            hosts: '域名',
            hostAlert: '隱藏了已註釋的記錄，請點擊 全部配置 按鈕以查看或設置',
            toolbox: '快速設置',
            hostname: '主機名',
            passwd: '系統密碼',
            passwdHelper: '輸入的字符不能包含 $ 和 &',
            timeZone: '系統時區',
            localTime: '伺服器時間',
            timeZoneChangeHelper: '修改系統時區需要重新啟動服務，是否繼續？',
            timeZoneHelper: '時區修改依賴於 timedatectl 命令，如未安裝可能導致修改失敗',
            timeZoneCN: '北京',
            timeZoneAM: '洛杉磯',
            timeZoneNY: '紐約',
            ntpALi: '阿里',
            ntpGoogle: '谷歌',
            syncSite: 'NTP 伺服器',
            syncSiteHelper: '該操作將使用 {0} 作為源進行系統時間同步，是否繼續？',
            hostnameHelper: '主機名修改依賴於 hostnamectl 命令，如未安裝可能導致修改失敗',
            userHelper: '用戶名依賴於 whoami 命令獲取，如未安裝可能導致獲取失敗。',
            passwordHelper: '密碼修改依賴於 chpasswd 命令，如未安裝可能導致修改失敗',
            hostHelper: '填寫的內容中存在空值，請檢查修改後重試！',
            dnsCheck: '測試可用性',
            dnsOK: 'DNS 配置信息可用！',
            dnsTestFailed: 'DNS 配置信息不可用，請修改後重試！',
        },
        fail2ban: {
            sshPort: '監聽 SSH 端口',
            sshPortHelper: '當前 Fail2ban 監聽主機 SSH 連接端口',
            unActive: '當前未開啟 Fail2ban 服務，請先開啟！',
            operation: '對 Fail2ban 服務進行 [{0}] 操作，是否繼續？',
            fail2banChange: 'Fail2ban 配置修改',
            ignoreHelper: '白名單中的 IP 列表將被忽略屏蔽，是否繼續？',
            bannedHelper: '黑名單中的 IP 列表將被伺服器屏蔽，是否繼續？',
            banTimeRule: '請輸入正確的禁用時間或 -1',
            banAllTime: '永久禁用',
            maxRetry: '最大重試次數',
            banTime: '禁用時間',
            banTimeHelper: '默認禁用時間為 10 分鐘，禁用時間為 -1 則表示永久禁用',
            findTime: '發現周期',
            banAction: '禁用方式',
            banActionOption: '通過 {0} 來禁用指定的 IP 地址',
            allPorts: ' (所有端口)',
            ignoreIP: 'IP 白名單',
            bannedIP: 'IP 黑名單',
            logPath: '日誌路徑',
            logPathHelper: '預設為 /var/log/secure 或者 /var/log/auth.log',
        },
        ftp: {
            ftp: 'FTP 帳戶',
            notStart: '當前未 FTP 服務，請先開啟！',
            operation: '對 FTP 服務進行 [{0}] 操作，是否繼續？',
            noPasswdMsg: '無法獲取當前 FTP 賬號密碼，請先設置密碼後重試！',
            enableHelper: '啟用選取的 FTP 帳號後，該 FTP 帳號將恢復訪問權限，是否繼續操作？',
            disableHelper: '停用選取的 FTP 帳號後，該 FTP 帳號將失去訪問權限，是否繼續操作？',
            syncHelper: '同步伺服器與資料庫中的 FTP 帳戶資料，是否繼續操作？',
            dirSystem: '該目錄為系統保留目錄，修改可能導致系統崩潰，請修改後重試！',
            dirHelper: '開啟 FTP 需要修改目錄權限，請謹慎選擇',
            dirMsg: '開啟 FTP 將修改整個 {0} 目錄權限，是否繼續？',
        },
        clam: {
            clam: '病毒掃描',
            cron: '定時掃描',
            cronHelper: '專業版支持定時掃描功能',
            specErr: '執行周期格式錯誤，請檢查後重試！',
            disableMsg: '停止定時執行會導致該掃描任務不再自動執行。是否繼續？',
            enableMsg: '啟用定時執行會讓該掃描任務定期自動執行。是否繼續？',
            showFresh: '顯示病毒庫服務',
            hideFresh: '隱藏病毒庫服務',
            clamHelper: 'ClamAV 建議最低配置：3 GiB 以上記憶體、單核 2.0 GHz 以上 CPU，以及至少 5 GiB 可用硬盤空間。',
            notStart: '當前未 ClamAV 服務，請先開啟！',
            removeRecord: '刪除報告文件',
            noRecords: '點擊“執行”按鈕開始掃描，掃描結果將會記錄在這裏。',
            removeResultHelper: '刪除任務執行過程中生成的報告文件，以清理存儲空間。',
            removeInfected: '刪除病毒文件',
            removeInfectedHelper: '刪除任務檢測到的病毒文件，以確保伺服器的安全和正常運行。',
            clamCreate: '創建掃描規則',
            infectedStrategy: '感染文件策略',
            removeHelper: '刪除病毒文件，請謹慎選擇！',
            move: '移動',
            moveHelper: '將病毒文件移動到指定目錄下',
            copyHelper: '將病毒文件複製到指定目錄下',
            none: '不操作',
            noneHelper: '不對病毒文件採取任何操作',
            scanDir: '掃描目錄',
            infectedDir: '隔離目錄',
            scanDate: '掃描時間',
            scanResult: '掃描報告條數',
            tail: '日誌顯示行數',
            infectedFiles: '感染文件數',
            log: '詳情',
            clamConf: '掃描配置',
            clamLog: '掃描日誌',
            freshClam: '病毒庫刷新配置',
            freshClamLog: '病毒庫刷新日誌',
            alertHelper: '專業版支持定時掃描和短信告警功能',
            alertTitle: '病毒掃描「{0}」任務检测到感染文件告警',
        },
    },
    logs: {
        core: '面板服務',
        agent: '節點監控',
        panelLog: '面板日誌',
        operation: '操作日誌',
        login: '訪問日誌',
        loginIP: '登錄 IP',
        loginAddress: '登錄地址',
        loginAgent: '用戶代理',
        loginStatus: '登錄狀態',
        system: '系統日誌',
        deleteLogs: '清空日誌',
        resource: '資源',
        detail: {
            ai: 'AI',
            groups: '分組',
            hosts: '主機',
            apps: '應用',
            websites: '網站',
            containers: '容器',
            files: '文件管理',
            runtimes: '運行環境',
            process: '進程管理',
            toolbox: '工具箱',
            backups: '備份 / 還原',
            tampers: '防篡改',
            xsetting: '介面設定',
            logs: '日誌審計',
            settings: '面板設定',
            cronjobs: '計畫任務',
            waf: 'WAF',
            databases: '資料庫',
            licenses: '許可證',
            nodes: '節點',
            commands: '快速命令',
        },
        websiteLog: '網站日誌',
        runLog: '運行日誌',
        errLog: '錯誤日誌',
        task: '任務日誌',
        taskName: '任務名稱',
        taskRunning: '運行中',
    },
    file: {
        fileDirNum: '共 {0} 個目錄，{1} 個檔案，',
        currentDir: '當前目錄',
        dir: '文件夾',
        upload: '上傳',
        download: '下載',
        uploadFile: '@:file.upload@:file.file',
        uploadDirectory: '@:file.upload@:file.dir',
        fileName: '文件名',
        search: '在當前目錄下查找',
        mode: '權限',
        editPermissions: '編輯@:file.mode',
        owner: '所有者',
        file: '文件',
        remoteFile: '遠程下載',
        share: '分享',
        sync: '數據同步',
        size: '大小',
        updateTime: '修改時間',
        rename: '重命名',
        role: '權限',
        info: '屬性',
        linkFile: '軟連接文件',
        shareList: '分享列表',
        zip: '壓縮',
        group: '用戶組',
        path: '路徑',
        public: '公共',
        setRole: '設置權限',
        link: '是否鏈接',
        rRole: '讀取',
        wRole: '寫入',
        xRole: '可執行',
        compress: '壓縮',
        deCompress: '解壓',
        compressType: '壓縮格式',
        compressDst: '壓縮路徑',
        replace: '覆蓋已存在的文件',
        compressSuccess: '壓縮成功',
        deCompressSuccess: '解壓成功',
        deCompressDst: '解壓路徑',
        linkType: '鏈接類型',
        softLink: '軟鏈接',
        hardLink: '硬鏈接',
        linkPath: '鏈接路徑',
        selectFile: '選擇文件',
        downloadSuccess: '下載成功',
        downloadUrl: '下載地址',
        downloadStart: '下載開始!',
        moveSuccess: '移動成功',
        copySuccess: '復製成功',
        move: '移動',
        calculate: '計算',
        canNotDeCompress: '無法解壓此文件',
        uploadSuccess: '上傳成功!',
        downloadProcess: '下載進度',
        downloading: '正在下載...',
        infoDetail: '文件屬性',
        root: '根目錄',
        list: '文件列表',
        sub: '子目錄',
        theme: '主題',
        language: '語言',
        eol: '行尾符',
        copyDir: '復製路徑',
        paste: '粘貼',
        changeOwner: '修改用戶和用戶組',
        containSub: '同時修改子文件屬性',
        ownerHelper: 'PHP 運行環境默認用戶:用戶組為 1000:1000, 容器內外用戶顯示不一致為正常現象',
        searchHelper: '支持 * 等通配符',
        uploadFailed: '【{0}】 文件上傳失敗',
        fileUploadStart: '正在上傳【{0}】....',
        currentSelect: '當前選中: ',
        unsupportedType: '不支持的文件類型',
        deleteHelper: '確定刪除所選檔案？ 預設刪除之後將進入回收站？',
        fileHelper: '注意：1. 搜尋結果不支援排序功能 2. 資料夾無法依大小排序。',
        forceDeleteHelper: '永久刪除檔案（不進入回收站，直接刪除）',
        recycleBin: '回收站',
        sourcePath: '原路徑',
        deleteTime: '刪除時間',
        confirmReduce: '確定還原以下文件？',
        reduceSuccess: '還原成功',
        reduce: '還原',
        reduceHelper: '如果原路徑存在同名檔案或目錄，將會被覆蓋，是否繼續？',
        clearRecycleBin: '清空回收站',
        clearRecycleBinHelper: '是否清空回收站？',
        favorite: '收藏夾',
        removeFavorite: '是否從收藏夾移出？',
        addFavorite: '添加/移出收藏夾',
        clearList: '清空列表',
        deleteRecycleHelper: '確定永久刪除以下文件？',
        typeErrOrEmpty: '【{0}】 檔案類型錯誤或為空資料夾',
        dropHelper: '將需要上傳的文件拖曳到此處',
        fileRecycleBin: '檔案回收站',
        fileRecycleBinMsg: '已{0}回收站',
        wordWrap: '自動換行',
        deleteHelper2: '確定刪除所選檔案？ 刪除操作不可回滾',
        ignoreCertificate: '忽略不可信證書',
        ignoreCertificateHelper:
            '下載時忽略不可信證書可能導致數據洩露或篡改。請謹慎使用此選項，僅在信任下載源的情況下啟用',
        uploadOverLimit: '文件數量超過 1000！ 請壓縮後上傳',
        clashDitNotSupport: '檔名禁止包含 .1panel_clash',
        clashDeleteAlert: '回收站資料夾不能刪除',
        clashOpenAlert: '回收站目錄請點選【回收站】按鈕開啟',
        right: '前進',
        back: '後退',
        top: '返回上一層',
        up: '上一層',
        openWithVscode: 'VS Code 打開',
        vscodeHelper: '請確保本地已安裝 VS Code 並配置了 SSH Remote 插件',
        saveContentAndClose: '檔案已被修改，是否保存並關閉？',
        saveAndOpenNewFile: '檔案已被修改，是否保存並打開新檔案？',
        noEdit: '檔案未修改，無需此操作！',
        noNameFolder: '未命名資料夾',
        noNameFile: '未命名檔案',
        minimap: '縮略圖',
        fileCanNotRead: '此文件不支持預覽',
        panelInstallDir: '1Panel 安裝目錄不能删除',
        wgetTask: '下載任務',
        existFileTitle: '同名檔案提示',
        existFileHelper: '上傳的檔案存在同名檔案，是否覆蓋？',
        existFileSize: '文件大小（新->舊）',
        existFileDirHelper: '選擇的檔案/資料夾存在同名，請謹慎操作！',
        coverDirHelper: '選取要覆蓋的資料夾，將複製到目標路徑！',
        noSuchFile: '找不到該檔案或目錄，請檢查後重試。',
        setting: '设置',
        showHide: '顯示隱藏檔案',
        noShowHide: '不顯示隱藏檔案',
        cancelUpload: '取消上傳',
        cancelUploadHelper: '是否取消上傳，取消後將清空上傳列表',
    },
    ssh: {
        autoStart: '開機自啟',
        enable: '設置開機自啟',
        disable: '關閉開機自啟',
        sshAlert: '列表數據根據登錄時間排序，但請註意，切換時區或其他操作可能導致登錄日誌的時間出現偏差。',
        sshAlert2: '您可以通过工具箱中的 Fail2ban 屏蔽尝试暴力破解的 IP 地址，从而提高主机的安全性。',
        sshOperate: '對 SSH 服務進行 [{0}] 操作，是否繼續？',
        sshChange: 'SSH 配置修改',
        sshChangeHelper: '此操作將 {0} 修改為 [{1}] ，是否繼續？',
        sshFileChangeHelper: '直接修改配置文件可能會導致服務不可用，請謹慎操作，是否繼續？',
        port: '連接端口',
        portHelper: '指定 SSH 服務監聽的端口號，默認為 22。',
        listenAddress: '監聽地址',
        allV4V6: '0.0.0.0:{0}(IPv4)和 :::{0}(IPv6)',
        listenHelper: '同時取消 IPv4 和 IPv6 設置，將會同時監聽 0.0.0.0:{0}(IPv4) 和 :::{0}(IPv6)',
        addressHelper: '指定 SSH 服務監聽的 IP 地址',
        permitRootLogin: 'root 用戶',
        rootSettingHelper: 'root 用戶 SSH 登錄方式，默認所有 SSH 登錄。',
        rootHelper1: '允許 SSH 登錄',
        rootHelper2: '禁止 SSH 登錄',
        rootHelper3: '僅允許密鑰登錄',
        rootHelper4: '僅允許執行預先定義的命令，不能進行其他操作',
        passwordAuthentication: '密碼認證',
        pwdAuthHelper: '是否啟用密碼認證，默認啟用。',
        pubkeyAuthentication: '密鑰認證',
        privateKey: '私鑰',
        publicKey: '公鑰',
        password: '密碼',
        createMode: '創建方式',
        generate: '自動生成',
        unSyncPass: '密鑰密碼無法同步',
        syncHelper: '同步操作將清理失效密鑰並同步新增的完整密鑰對，是否繼續？',
        input: '手動輸入',
        import: '文件上傳',
        pubkey: '密鑰信息',
        pubKeyHelper: '當前密鑰信息僅對用戶 {0} 生效',
        encryptionMode: '加密方式',
        passwordHelper: '支持大小寫英文、數字,長度6-10',
        reGenerate: '重新生成密鑰',
        keyAuthHelper: '是否啟用密鑰認證，默認啟用。',
        useDNS: '反向解析',
        dnsHelper: '控製 SSH 服務器是否啟用 DNS 解析功能，從而驗證連接方的身份。',
        analysis: '統計信息',
        denyHelper: '將對下列地址進行【屏蔽】操作，設置後該 IP 將禁止訪問服務器，是否繼續？',
        acceptHelper: '將對下列地址進行【放行】操作，設置後該 IP 將恢復正常訪問，是否繼續？',
        noAddrWarning: '當前未選中任何可【{0}】地址，請檢查後重試！',
        loginLogs: 'SSH 登錄日誌',
        loginMode: '登錄方式',
        authenticating: '密鑰',
        publickey: '密鑰',
        belong: '歸屬地',
        local: '內網',
        remote: '外網',
        session: '會話',
        loginTime: '登錄時間',
        loginIP: '登錄IP',
        stopSSHWarn: '是否斷開此SSH連接',
    },
    setting: {
        panel: '面板',
        user: '面板用户',
        userChange: '修改面板用户',
        userChangeHelper: '修改面板用户将退出登录，是否继续？',
        passwd: '面板密码',
        emailHelper: '用於密碼找回',
        title: '面板別名',
        panelPort: '面板端口',
        titleHelper: '支援長度 3 至 30 的英文字母、中文、數字、空格和常見的特殊符號。',
        portHelper: '建議端口範圍8888 - 65535，註意：有安全組的服務器請提前在安全組放行新端口',
        portChange: '端口修改',
        portChangeHelper: '服務端口修改需要重啟服務，是否繼續？',
        theme: '主題顏色',
        menuTabs: '菜單標簽頁',
        componentSize: '組件大小',
        dark: '暗色',
        darkGold: '黑金',
        light: '亮色',
        auto: '跟隨系統',
        language: '系統語言',
        languageHelper: '默認跟隨瀏覽器語言，設置後只對當前瀏覽器生效，更換瀏覽器後需要重新設置',
        sessionTimeout: '超時時間',
        sessionTimeoutError: '最小超時時間為 300 秒',
        sessionTimeoutHelper: '如果用戶超過 {0} 秒未操作面板，面板將自動退出登錄',
        systemIP: '預設存取地址',
        systemIPHelper: '應用跳轉、容器存取等功能將使用此地址進行路由。每個節點可設定不同地址。',
        proxy: '代理伺服器',
        proxyHelper: '設置代理伺服器後，將在以下場景中生效：',
        proxyHelper1: '應用商店的安裝包下載和同步（專業版功能）',
        proxyHelper2: '系統版本升級及獲取更新說明（專業版功能）',
        proxyHelper3: '系統許可證的驗證和同步',
        proxyHelper4: 'Docker 的網絡訪問將通過代理伺服器進行（專業版功能）',
        proxyHelper5: '系統類型腳本庫的統一下載與同步（專業版功能）',
        proxyHelper6: '申請證書（專業版功能）',
        proxyType: '代理類型',
        proxyUrl: '代理地址',
        proxyPort: '代理端口',
        proxyPasswdKeep: '記住密碼',
        proxyDocker: 'Docker 代理',
        proxyDockerHelper: '將代理伺服器配寘同步至 Docker，支持離線服務器拉取鏡像等操作',
        syncToNode: '同步至子節點',
        syncToNodeHelper: '同步設置至其他節點',
        nodes: '節點',
        selectNode: '選擇節點',
        selectNodeError: '請選擇節點',
        apiInterface: 'API 接口',
        apiInterfaceClose: '關閉後將不能使用 API 接口進行訪問，是否繼續？',
        apiInterfaceHelper: '提供面板支持 API 接口訪問',
        apiInterfaceAlert1: '請不要在生產環境開啟，這可能新增服務器安全風險',
        apiInterfaceAlert2: '請不要使用協力廠商應用調用面板 API，以防止潜在的安全威脅。',
        apiInterfaceAlert3: 'API 接口檔案',
        apiInterfaceAlert4: '使用檔案',
        apiKey: '接口密钥',
        apiKeyHelper: '接口密钥用於外部應用訪問 API 接口',
        ipWhiteList: 'IP白名單',
        ipWhiteListEgs: '當存在多個 IP 時，需要換行顯示，例：\n************1 \n172.16.10.0/24',
        ipWhiteListHelper:
            '必需在 IP 白名單清單中的 IP 才能訪問面板 API 接口，0.0.0.0/0（所有 IPv4），::/0（所有 IPv6）',
        apiKeyValidityTime: '介面金鑰有效期',
        apiKeyValidityTimeEgs: '介面金鑰有效期（組織分）',
        apiKeyValidityTimeHelper: '介面時間戳記到請求時的當前時間戳之間有效（組織分），設定為0時，不做時間戳記校驗',
        apiKeyReset: '接口密钥重置',
        apiKeyResetHelper: '重置密钥後，已關聯密钥服務將失效，請重新添加新密鑰至服務。',
        confDockerProxy: '配寘 Docker 代理',
        restartNowHelper: '配寘 Docker 代理需要重啓 Docker 服務。',
        restartNow: '立即重啓',
        restartLater: '稍後手動重啟',
        systemIPWarning: '當前節點尚未配置預設存取地址，請前往面板設定進行設定！',
        systemIPWarning1: '當前服務器地址設置為 {0}，無法快速跳轉！',
        changePassword: '密碼修改',
        oldPassword: '原密碼',
        newPassword: '新密碼',
        retryPassword: '確認密碼',
        noSpace: '輸入信息不能包括空格符號',
        duplicatePassword: '新密碼不能與原始密碼一致，請重新輸入！',
        diskClean: '缓存清理',
        developerMode: '預覽體驗計劃',
        developerModeHelper: '獲取 1Panel 的預覽版本，以分享有關新功能和更新的反饋',

        thirdParty: '第三方賬號',
        scope: '使用範圍',
        public: '公有',
        publicHelper: '公有類型的備份帳號會同步到各個子節點，子節點可以一起使用',
        private: '私有',
        privateHelper: '私有類型的備份帳號只創建在當前節點上，僅供當前節點使用',
        noTypeForCreate: '當前無可創建備份類型',
        LOCAL: '服務器磁盤',
        OSS: '阿裏雲 OSS',
        S3: '亞馬遜 S3 雲存儲',
        mode: '模式',
        MINIO: 'MINIO',
        SFTP: 'SFTP',
        WebDAV: 'WebDAV',
        WebDAVAlist: 'WebDAV 連接 Alist 可參考官方文檔',
        UPYUN: '又拍雲',
        ALIYUN: '阿里雲盤',
        ALIYUNHelper: '當前阿里雲盤非客戶端下載最大限制為 100 MB，超過限制需要通過客戶端下載',
        ALIYUNRecover:
            '當前阿里雲盤非客戶端下載最大限制為 100 MB，超過限制需要通過客戶端下載到本地後，同步快照進行恢復',
        GoogleDrive: '谷歌云盘',
        analysis: '解析',
        analysisHelper: '粘貼整個 token 內容，自動解析所需部分，具體操作可參考官方文檔',
        serviceName: '服務名稱',
        operator: '操作員',
        OneDrive: '微軟 OneDrive',
        isCN: '世紀互聯',
        isNotCN: '國際版',
        client_id: '客戶端 ID',
        client_secret: '客戶端密鑰',
        redirect_uri: '重定向 URL',
        onedrive_helper: '自訂配置可參考官方文件',
        clickToRefresh: '單擊可手動刷新',
        refreshTime: '令牌刷新時間',
        refreshStatus: '令牌刷新狀態',
        codeWarning: '當前授權碼格式錯誤，請重新確認！',
        backupDir: '備份目录',
        code: '授權碼',
        codeHelper:
            '請點擊獲取按鈕，然後登錄 {0} 復製跳轉鏈接中 code 後面的內容，粘貼到該輸入框中，具體操作可參考官方文檔。',
        loadCode: '獲取',
        COS: '騰訊雲 COS',
        ap_beijing_1: '北京一區',
        ap_beijing: '北京',
        ap_nanjing: '南京',
        ap_shanghai: '上海',
        ap_guangzhou: '廣州',
        ap_chengdu: '成都',
        ap_chongqing: '重慶',
        ap_shenzhen_fsi: '深圳金融',
        ap_shanghai_fsi: '上海金融',
        ap_beijing_fsi: '北京金融',
        ap_hongkong: '中國香港',
        ap_singapore: '新加坡',
        ap_mumbai: '孟買',
        ap_jakarta: '雅加達',
        ap_seoul: '首爾',
        ap_bangkok: '曼谷',
        ap_tokyo: '東京',
        na_siliconvalley: '硅谷（美西）',
        na_ashburn: '弗吉尼亞（美東）',
        na_toronto: '多倫多',
        sa_saopaulo: '聖保羅',
        eu_frankfurt: '法蘭克福',
        KODO: '七牛雲 Kodo',
        scType: '存儲類型',
        typeStandard: '標準存儲',
        typeStandard_IA: '低頻存儲',
        typeArchive: '歸檔存儲',
        typeDeep_Archive: '深度歸檔存儲',
        scLighthouse: '預設，輕量物件儲存僅支援該儲存類型',
        scStandard: '標準存儲，適用於實時訪問的大量熱點文件、頻繁的數據交互等業務場景。',
        scStandard_IA: '低頻存儲，適用於較低訪問頻率（例如平均每月訪問頻率1到2次）的業務場景，最少存儲30天。',
        scArchive: '歸檔存儲，適用於極低訪問頻率（例如半年訪問1次）的業務場景。',
        scDeep_Archive: '深度歸檔存儲，適用於極低訪問頻率（例如1年訪問1~2次）的業務場景。',
        archiveHelper: '歸檔存儲的文件無法直接下載，需要先在對應的雲服務商網站進行恢復操作，請謹慎使用！',
        backupAlert: '理論上只要雲廠商兼容 S3 協議，就可以用現有的亞馬遜 S3 雲存儲來備份，具體配置參考 ',
        domain: '加速域名',
        backupAccount: '備份賬號',
        loadBucket: '獲取桶',
        accountName: '賬戶名稱',
        accountKey: '賬戶密鑰',
        address: '地址',
        path: '路徑',
        backupJump: '未在當前備份列表中的備份檔案，請嘗試從檔案目錄中下載後導入備份。',

        snapshot: '快照',
        noAppData: '暫無可選擇系統應用',
        noBackupData: '暫無可選擇備份數據',
        stepBaseData: '基礎數據',
        stepAppData: '系統應用',
        stepPanelData: '系統數據',
        stepBackupData: '備份數據',
        stepOtherData: '其他數據',
        operationLog: '保留操作日誌',
        loginLog: '保留訪問日誌',
        systemLog: '保留系統日誌',
        taskLog: '保留任務日誌',
        monitorData: '保留監控數據',
        dockerConf: '保留 Docker 配置',
        selectAllImage: '備份所有應用鏡像',
        logLabel: '日誌',
        agentLabel: '節點配置',
        appDataLabel: '應用數據',
        appImage: '應用鏡像',
        appBackup: '應用備份',
        backupLabel: '備份目錄',
        confLabel: '配置文件',
        dockerLabel: '容器',
        taskLabel: '計劃任務',
        resourceLabel: '應用資源目錄',
        runtimeLabel: '運行環境',
        appLabel: '應用',
        databaseLabel: '數據庫',
        snapshotLabel: '快照文件',
        websiteLabel: '網站',
        directoryLabel: '目錄',
        appStoreLabel: '應用商店',
        shellLabel: '腳本',
        tmpLabel: '臨時目錄',
        sslLabel: '證書目錄',
        reCreate: '创建快照失败',
        reRollback: '回滾快照失敗',
        deleteHelper: '將刪除該快照的所有備份文件，包括第三方備份賬號中的文件。',
        status: '快照狀態',
        ignoreRule: '排除規則',
        editIgnoreRule: '@:commons.button.edit@:setting.ignoreRule',
        ignoreHelper: '快照時將使用該規則對 1Panel 數據目錄進行壓縮備份，請謹慎修改。',
        ignoreHelper1: '一行一個，例： \n*.log\n/opt/1panel/cache',
        panelInfo: '寫入 1Panel 基礎信息',
        panelBin: '備份 1Panel 系統文件',
        daemonJson: '備份 Docker 配置文件',
        appData: '備份 1Panel 已安裝應用',
        panelData: '備份 1Panel 數據目錄',
        backupData: '備份 1Panel 本地備份目錄',
        compress: '製作快照文件',
        upload: '上傳快照文件',
        recoverDetail: '恢復詳情',
        createSnapshot: '創建快照',
        importSnapshot: '同步快照',
        importHelper: '快照文件目錄：',
        lastRecoverAt: '上次恢復時間',
        lastRollbackAt: '上次回滾時間',
        reDownload: '重新下載備份文件',
        recoverErrArch: '不支援在不同伺服器架構之間進行快照恢復操作!',
        recoverErrSize: '檢測到目前磁碟空間不足，請檢查或清理後重試!',
        recoverHelper: '即將從快照 {0} 開始恢復，恢復前請確認以下資訊：',
        recoverHelper1: '恢復需要重新啟動 Docker 以及 1Panel 服務',
        recoverHelper2: '請確保伺服器磁碟空間充足 ( 快照檔案大小: {0}, 可用空間: {1} )',
        recoverHelper3: '請確保伺服器架構與建立快照伺服器架構資訊保持一致 (目前伺服器架構: {0} )',
        rollback: '回滾',
        rollbackHelper:
            '即將回滾本次恢復，回滾將替換所有本次恢復的檔案，過程中可能需要重新啟動 Docker 以及 1Panel 服務，是否繼續？',

        upgradeRecord: '升級記錄',
        upgrading: '正在升級中，請稍候...',
        upgradeHelper: '升級操作需要重啟 1Panel 服務，是否繼續？',
        noUpgrade: '當前已經是最新版本',
        versionHelper: '1Panel 版本號命名規則為： [大版本].[功能版本].[Bug 修復版本]，例：',
        rollbackLocalHelper: '主節點暫不支援直接回滾，請手動執行「1pctl restore」命令回滾！',
        upgradeCheck: '檢查更新',
        upgradeNotes: '更新內容',
        upgradeNow: '立即更新',
        source: '下載源',
        versionNotSame: '節點版本與主節點不一致，請在節點管理中升級後重試。',
        versionCompare: '檢測到節點 {0} 版本已是當前可升級最新版本，請檢查主節點版本後重試！',

        safe: '安全',
        bindInfo: '監聽地址',
        bindAll: '監聽所有',
        bindInfoHelper: '修改服務監聽地址或協議可能導致服務不可用，是否繼續？',
        ipv6: '監聽 IPv6',
        bindAddress: '監聽地址',
        entrance: '安全入口',
        showEntrance: '啟用概覽頁未開啟提醒',
        entranceHelper: '開啟安全入口後只能通過指定安全入口登錄面板',
        entranceError: '請輸入 5-116 位安全登錄入口，僅支持輸入數字或字母',
        entranceInputHelper: '安全入口設置為空時，則取消安全入口',
        randomGenerate: '隨機生成',
        expirationTime: '密碼過期時間',
        unSetting: '未設置',
        noneSetting: '為面板密碼設置過期時間，過期後需要重新設置密碼',
        expirationHelper: '密碼過期時間為 [0] 天時，則關閉密碼過期功能',
        days: '過期天數',
        expiredHelper: '當前密碼已過期，請重新修改密碼：',
        timeoutHelper: '【 {0} 天後 】面板密碼即將過期，過期後需要重新設置密碼',
        complexity: '密碼復雜度驗證',
        complexityHelper: '開啟後密碼必須滿足長度為 8-30 位，並包含字母、數字、至少兩種特殊字符',
        bindDomain: '域名綁定',
        unBindDomain: '域名解綁',
        panelSSL: '面板 SSL',
        panelSSLHelper: '面板 SSL 自动续期后需要手动重启 1Panel 服务才可生效',
        unBindDomainHelper: '解除域名綁定可能造成系統不安全，是否繼續？',
        bindDomainHelper: '設置域名綁定後，僅能通過設置中域名訪問 1Panel 服務',
        bindDomainHelper1: '綁定域名為空時，則取消域名綁定',
        bindDomainWarning: '設置域名綁定後，將退出當前登錄，且僅能通過設置中域名訪問 1Panel 服務，是否繼續？',
        allowIPs: '授權 IP',
        unAllowIPs: '取消授權',
        unAllowIPsWarning: '授權 IP 為空將允許所有 IP 訪問系統，可能造成系統不安全，是否繼續？',
        allowIPsHelper: '設置授權 IP 後，僅有設置中的 IP 可以訪問 1Panel 服務',
        allowIPsWarning: '設置授權 IP 後，僅有設置中的 IP 可以訪問 1Panel 服務，是否繼續？',
        allowIPsHelper1: '授權 IP 為空時，則取消授權 IP',
        allowIPEgs: '當存在多個授權 IP 時，需要換行顯示，例： \n************1 \n172.16.10.0/24',
        mfa: '兩步驗證',
        mfaClose: '關閉兩步驗證將導致服務安全性降低，是否繼續？',
        secret: '密鑰',
        mfaAlert: '兩步驗證密碼是基於當前時間生成，請確保服務器時間已同步',
        mfaHelper: '開啟後會驗證手機應用驗證碼',
        mfaHelper1: '下載兩步驗證手機應用 如:',
        mfaHelper2: '使用手機應用掃描以下二維碼，獲取 6 位驗證碼',
        mfaHelper3: '輸入手機應用上的 6 位數字',
        mfaCode: '驗證碼',
        mfaInterval: '刷新時間（秒）',
        mfaTitleHelper: '用於區分不同 1Panel 主機，修改後請重新掃描或手動添加密鑰信息！',
        mfaIntervalHelper: '修改刷新時間後，請重新掃描或手動添加密鑰信息！',
        sslChangeHelper: 'https 設置修改需要重啟服務，是否繼續？',
        sslDisable: '禁用',
        sslDisableHelper: '禁用 https 服務，需要重啟面板才能生效，是否繼續？',
        noAuthSetting: '未认证设置',
        noAuthSettingHelper: '當用戶未登錄且未正確輸入安全入口、授權 IP 或綁定域名時，此回應可以隱藏面板特徵。',
        responseSetting: '响应设置',
        help200: '幫助頁面',
        error400: '錯誤請求',
        error401: '未授權',
        error403: '禁止訪問',
        error404: '未找到',
        error408: '請求超時',
        error416: '無效請求',
        error444: '連線已關閉',
        error500: '內部伺服器錯誤',

        https: '為面板設置 https 協議訪問，提升面板訪問安全性',
        certType: '證書類型',
        selfSigned: '自簽名',
        selfSignedHelper: '自簽證書，不被瀏覽器信任，顯示不安全是正常現象',
        select: '選擇已有',
        domainOrIP: '域名或 IP：',
        timeOut: '過期時間：',
        rootCrtDownload: '根證書下載',
        primaryKey: '密鑰',
        certificate: '證書',

        about: '關於',
        project: '項目地址',
        issue: '問題反饋',
        doc: '官方文檔',
        star: '點亮 Star',
        description: 'Linux 服務器運維管理面板',
        forum: '論壇求助',
        doc2: '使用手冊',
        currentVersion: '當前運行版本：',

        license: '許可證',
        bindNode: '綁定節點',
        menuSetting: '菜單設定',
        menuSettingHelper: '當只存在 1 個子選單時，選單列將僅展示該子選單',
        showAll: '全部顯示',
        hideALL: '全部隱藏',
        ifShow: '是否顯示',
        menu: '選單',
        confirmMessage: '即將刷新頁面更新高級功能菜單列表，是否繼續？',
        compressPassword: '壓縮密碼',
        backupRecoverMessage: '請輸入壓縮或解壓縮密碼（留空則不設定）',
    },
    license: {
        community: '社區版',
        oss: '社區版',
        pro: '專業版：',
        trial: '試用',
        add: '添加社區版',
        licenseAlert: '僅當許可證正常綁定到節點時，該許可證才能添加社區版節點，只有正常綁定到許可證的節點支援切換。',
        licenseUnbindHelper: '檢測到該許可證存在社區版節點，請解除綁定後重試！',
        subscription: '訂閱',
        perpetual: '永久授權',
        versionConstraint: '{0} 版本買斷',
        forceUnbind: '強制解除綁定',
        forceUnbindHelper: '強制解除綁定將忽略解除過程中產生的錯誤，最終解除許可證綁定。',
        updateForce: '強制更新（忽略解除綁定過程中的所有錯誤，確保最終操作成功）',
        trialInfo: '版本',
        authorizationId: '訂閱授權 ID',
        authorizedUser: '被授權方',
        lostHelper: '許可證已達到最大重試次數，請手動點擊同步按鈕，以確保專業版功能正常使用，詳情：',
        exceptionalHelper: '許可證同步驗證異常，請手動點擊同步按鈕，以確保專業版功能正常使用，詳情：',
        quickUpdate: '快速更新',
        power: '授權',
        unbindHelper: '解除綁定後將清除該節點所有專業版相關設置，是否繼續？',
        importLicense: '導入許可證',
        importHelper: '請點擊或拖動許可文件到此處',
        levelUpPro: '升級專業版',
        licenseSync: '許可證同步',
        knowMorePro: '了解更多',
        closeAlert: '當前頁面可在面板設置中關閉顯示',
        introduce: '功能介紹',
        waf: '升級專業版可以獲得攔截地圖、日誌、封鎖記錄、地理位置封禁、自定義規則、自定義攔截頁面等功能。',
        tamper: '升級專業版可以保護網站免受未經授權的修改或篡改。',
        tamperHelper: '操作失敗，該文件或文件夾已經開啟防篡改，請檢查後重試！',
        setting: '升級專業版可以自定義面板 Logo、歡迎簡介等信息。',
        monitor: '升級專業版可以查看網站的即時狀態、訪客趨勢、訪客來源、請求日誌等資訊。 ',
        alert: '陞級專業版可通過簡訊接收告警資訊，並查看告警日誌，全面掌控各類關鍵事件，確保系統運行無憂。',
        node: '升級專業版可以使用 1Panel 管理多台 linux 伺服器。',
        fileExchange: '升級專業版可以在多台服務器之間快速傳輸文件。',
        app: '升級專業版可通過手機APP，查看服務資訊、异常監控等。',
        cluster: '升級專業版可以管理 MySQL/Postgres/Reids 主從集群。',
    },
    clean: {
        scan: '開始掃描',
        scanHelper: '輕鬆梳理 1Panel 運行期間積累的垃圾文件',
        clean: '立即清理',
        reScan: '重新掃描',
        cleanHelper: '已勾選文件及目錄清理後無法回滾（系統緩存文件清理需要重啟服務），是否繼續？',
        statusSuggest: '( 建議清理 )',
        statusClean: '( 很幹凈 )',
        statusEmpty: '非常幹凈，無需清理！',
        statusWarning: '( 謹慎操作 )',
        lastCleanTime: '上次清理時間: {0}',
        lastCleanHelper: '清理文件及目錄：{0} 個， 總計清理：{1}',
        cleanSuccessful: '清理成功！',
        currentCleanHelper: '本次清理文件及目錄：{0} 個， 總計清理：{1}',
        suggest: '( 建議清理 )',
        totalScan: '待清理垃圾文件共計： ',
        selectScan: '已選中垃圾文件共計： ',

        system: '系統垃圾',
        systemHelper: '快照、升級等過程中產生的臨時文件以及版本叠代過程中廢棄的文件內容',
        panelOriginal: '系統快照恢復前備份文件',
        backup: '臨時備份目錄',
        upgrade: '系統升級備份文件',
        upgradeHelper: '( 建議保留最新的升級備份用於系統回滾 )',
        cache: '系統緩存文件',
        cacheHelper: '( 謹慎操作，清理需要重啟服務 )',
        snapshot: '系統快照臨時文件',
        rollback: '恢復前備份目錄',

        upload: '臨時上傳文件',
        uploadHelper: '系統上傳備份列表中上傳的臨時文件',
        download: '臨時下載文件',
        downloadHelper: '系統從第三方備份賬號下載的臨時文件',
        directory: '文件夾',

        systemLog: '系統日誌文件',
        systemLogHelper: '系統日誌信息、容器構建或鏡像拉取等日誌信息以及計劃任務中產生的日誌文件',
        dockerLog: '容器操作日誌文件',
        taskLog: '計劃任務執行日誌文件',
        shell: 'Shell 腳本計劃任務',
        containerShell: '容器內執行 Shell 腳本計劃任務',
        curl: 'CURL 計劃任務',

        containerTrash: '容器垃圾',
        volumes: '存儲卷',
        buildCache: '容器建置快取',
    },
    app: {
        app: '應用',
        installName: '安裝名稱',
        installed: '已安裝',
        all: '全部',
        version: '版本',
        detail: '詳情',
        params: '參數',
        author: '作者',
        source: '來源',
        appName: '應用名稱',
        deleteWarn: '刪除操作會把所有數據和備份一並刪除，此操作不可回滾，是否繼續？',
        syncSuccess: '同步成功',
        canUpgrade: '可升級',
        backupName: '文件名稱',
        backupPath: '文件路徑',
        backupdate: '備份時間',
        versionSelect: '請選擇版本',
        operatorHelper: '將對選中應用進行 {0} 操作，是否繼續？',
        checkInstalledWarn: '未檢測到 {0} ,請進入應用商店點擊安裝！',
        gotoInstalled: '去安裝',
        limitHelper: '該應用已安裝，不支持重復安裝',
        deleteHelper: '{0}已經關聯以下資源，請檢查後重試！',
        checkTitle: '提示',
        defaultConfig: '默認配置',
        defaultConfigHelper: '已恢復為默認配置，保存後生效',
        forceDelete: '強製刪除',
        forceDeleteHelper: '強製刪除，會忽略刪除過程中產生的錯誤並最終刪除元數據',
        deleteBackup: '刪除備份',
        deleteBackupHelper: '同時刪除應用備份',
        deleteDB: '刪除數據庫',
        deleteDBHelper: '同時刪除與應用關聯的數據庫',
        noService: '無{0}',
        toInstall: '去安裝',
        param: '參數配置',
        syncAppList: '更新應用列表',
        alreadyRun: '已安裝',
        less1Minute: '小於1分鐘',
        appOfficeWebsite: '官方網站',
        github: '開源社區',
        document: '文檔說明',
        updatePrompt: '當前應用均為最新版本',
        installPrompt: '尚未安裝任何應用',
        updateHelper: '更新參數可能導致應用無法啟動，請提前備份並謹慎操作',
        updateWarn: '更新參數需要重建應用，是否繼續？',
        busPort: '服務端口',
        syncStart: '開始同步！請稍後刷新應用商店',
        advanced: '高級設置',
        cpuCore: '核心數',
        containerName: '容器名稱',
        containerNameHelper: '可以為空，為空自動生成',
        allowPort: '端口外部訪問',
        allowPortHelper: '允許端口外部訪問會放開防火墻端口',
        appInstallWarn: '應用端口默認不允許外部訪問，可以在下方高級設置中選擇放開',
        upgradeStart: '開始升級！請稍後刷新頁面',
        toFolder: '進入安裝目錄',
        editCompose: '編輯 compose 文件',
        editComposeHelper: '編輯 compose 文件可能導致軟件安裝失敗',
        composeNullErr: 'compose 不能為空',
        takeDown: '已廢棄',
        allReadyInstalled: '已安裝',
        installHelper: '配置鏡像加速可以解決鏡像拉取失敗的問題',
        installWarn: '當前未勾選端口外部訪問，將無法通過外網IP:端口訪問，是否繼續？ ',
        showIgnore: '查看忽略應用',
        cancelIgnore: '取消忽略',
        ignoreList: '忽略列表',
        appHelper: '部分應用的安裝使用說明請在應用詳情頁查看',
        backupApp: '升級前備份應用',
        backupAppHelper: '升級失敗會使用備份自動回滾,請在日誌審計-系統日誌中查看失敗原因',
        openrestyDeleteHelper: '強制刪除 OpenResty 會刪除所有的網站，請確認風險後操作',
        downloadLogHelper1: '即將下載 {0} 套用所有日誌，是否繼續？ ',
        downloadLogHelper2: '即將下載 {0} 應用最近 {1} 條日誌，是否繼續？ ',
        syncAllAppHelper: '即將同步所有應用，是否繼續？ ',
        hostModeHelper: '目前應用網路模式為 host 模式，如需放開端口，請在防火牆頁面手動放開',
        showLocal: '顯示本機應用程式',
        reload: '重載',
        upgradeWarn: '升級應用程式會取代 docker-compose.yml 文件，如有更改，可以點擊查看文件對比',
        newVersion: '新版本',
        oldVersion: '目前版本',
        composeDiff: '文件對比',
        showDiff: '看對比',
        useNew: '使用自訂版本',
        useDefault: '使用預設版本',
        useCustom: '自訂 docker-compose.yml',
        useCustomHelper: '使用自訂 docker-compose.yml 文件，可能會導致應用程式升級失敗，如無必要，請勿勾選',
        diffHelper: '左側為舊版本，右側為新版，編輯之後點擊使用自訂版本保存',
        pullImage: '拉取鏡像',
        pullImageHelper: '在應用啟動之前執行 docker pull 來拉取鏡像',
        deleteImage: '刪除鏡像',
        deleteImageHelper: '刪除應用相關鏡像，刪除失敗任務不會終止',
        requireMemory: '最低內存',
        supportedArchitectures: '支持架構',
        link: '鏈接',
        showCurrentArch: '本前伺服器架構應用',
        syncLocalApp: '同步本地應用',
        memoryRequiredHelper: '目前應用記憶體需求 {0}',
        gpuConfig: '開啟 GPU 支援',
        gpuConfigHelper: '請確保機器有 NVIDIA GPU 並且安裝 NVIDIA 驅動 和 NVIDIA docker Container Toolkit',
        webUI: 'Web 訪問地址',
        webUIPlaceholder: '例如：example.com:8080/login',
        defaultWebDomain: '默認訪問地址',
        defaultWebDomainHepler:
            '默認訪問用於應用端口跳轉，例如應用端口為 8080 則跳轉地址為 http(s)://默認訪問地址:8080',
        webUIConfig: '當前節點尚未配置預設存取地址，請在應用參數或者前往面板設定進行設定！',
        toLink: '連結',
        customAppHelper: '在安裝自訂應用商店包之前，請確保沒有任何已安裝的應用。',
        forceUninstall: '強制卸載',
        syncCustomApp: '同步自訂應用',
        ignoreAll: '忽略後續所有版本',
        ignoreVersion: '忽略指定版本',
        specifyIP: '綁定主機 IP',
        specifyIPHelper: '設置端口綁定的主機地址/網卡（如果你不清楚這個的作用，請不要填寫）',
        uninstallDeleteBackup: '卸載應用-刪除備份',
        uninstallDeleteImage: '卸載應用-刪除鏡像',
        upgradeBackup: '應用升級前備份應用',
    },
    website: {
        primaryDomain: '主域名',
        otherDomains: '其他域名',
        static: '靜態網站',
        deployment: '一鍵部署',
        supportUpType: '僅支持 .tar.gz 文件',
        zipFormat: '.tar.gz 壓縮包結構：test.tar.gz 壓縮包內，必需包含 {0} 文件',
        proxy: '反向代理',
        alias: '代號',
        ftpUser: 'FTP 帳號',
        ftpPassword: 'FTP 密碼',
        ftpHelper: '建立站點的同時，為站點建立一個對應 FTP 帳戶，並且 FTP 目錄指向站點所在目錄。',
        remark: '備註',
        groupSetting: '分組管理',
        createGroup: '創建分組',
        appNew: '新裝應用',
        appInstalled: '已裝應用',
        create: '創建網站',
        delete: '刪除網站',
        deleteApp: '刪除應用',
        deleteBackup: '刪除備份',
        domain: '域名',
        domainHelper: '一行一個域名,支持*和IP地址,支持域名:端口',
        addDomain: '新增域名',
        domainConfig: '域名設置',
        defaultDoc: '默認文檔',
        perserver: '並發限製',
        perserverHelper: '限製當前站點最大並發數',
        perip: '單IP限製',
        peripHelper: '限製單個IP訪問最大並發數',
        rate: '流量限製',
        rateHelper: '限製每個請求的流量上(單位:KB)',
        limitHelper: '啟用流量控製',
        other: '其他',
        currentSSL: '當前證書',
        dnsAccount: 'DNS賬號',
        applySSL: '證書申請',
        SSLList: '證書列表',
        createDnsAccount: 'DNS賬戶',
        aliyun: '阿裏雲DNS',
        manual: '手動解析',
        key: '密鑰',
        check: '查看',
        acmeAccountManage: 'Acme 賬戶',
        email: '郵箱',
        acmeAccount: 'Acme 賬戶',
        provider: '驗證方式',
        dnsManual: '手動解析',
        expireDate: '過期時間',
        brand: '組織',
        deploySSL: '部署',
        deploySSLHelper: '確定部署證書？',
        ssl: '證書',
        dnsAccountManage: 'DNS 賬戶',
        renewSSL: '續簽',
        renewHelper: '確定續簽證書？',
        renewSuccess: '續簽證書',
        enableHTTPS: '啟用 HTTPS',
        aliasHelper: '代號是網站目錄的文件夾名稱',
        lastBackupAt: '上次備份時間',
        null: '無',
        nginxConfig: 'Nginx配置',
        websiteConfig: '網站設置',
        basic: '基本',
        source: '配置文件',
        security: '安全',
        nginxPer: '性能調整',
        neverExpire: '永不過期',
        setDefault: '設為默認',
        deleteHelper: '相關應用狀態不正常，請檢查',
        toApp: '去已安裝列表',
        cycle: '周期',
        frequency: '頻率',
        ccHelper: '{0} 秒內累計請求同一URL超過 {1} 次,觸發CC防禦,封鎖此IP',
        mustSave: '修改之後需要保存才能生效',
        fileExt: '文件擴展名',
        fileExtBlock: '文件擴展名黑名單',
        value: '值',
        enable: '開啟',
        proxyAddress: '代理地址',
        proxyHelper: '例: 127.0.0.1:8080',
        forceDelete: '強製刪除',
        forceDeleteHelper: '強製刪除，會忽略刪除過程中產生的錯誤並最終刪除元數據',
        deleteAppHelper: '同時刪除關聯應用、數據庫以及應用備份',
        deleteBackupHelper: '同時刪除網站備份',
        deleteConfirmHelper: '刪除操作無法回滾，請輸入 <span style="color:red"> "{0}" </span> 刪除',
        staticPath: '對應主目錄:',
        limit: '限製方案',
        blog: '論壇/博客',
        imageSite: '圖片站',
        downloadSite: '下載站',
        shopSite: '商城',
        doorSite: '門戶',
        qiteSite: '企業',
        videoSite: '視頻',
        errLog: '錯誤日誌',
        accessLog: '網站日誌',
        stopHelper: '停止站點後將無法正常訪問，用戶訪問會顯示當前站點停止頁面，是否繼續操作？',
        startHelper: '啟用站點後，用戶可以正常訪問網站內容，是否繼續操作？',
        sitePath: '網站目錄',
        siteAlias: '網站代號',
        primaryPath: 'root 目錄',
        folderTitle: '網站主要包含以下資料夾',
        wafFolder: '防火墻規則',
        indexFolder: '網站root目錄',
        logFolder: '網站日誌',
        sslFolder: '網站證書',
        enableOrNot: '是否啟用',
        oldSSL: '選擇已有證書',
        manualSSL: '手動導入證書',
        select: '選擇',
        selectSSL: '選擇證書',
        privateKey: '私鑰(KEY)',
        certificate: '證書(PEM格式)',
        HTTPConfig: 'HTTP 選項',
        HTTPSOnly: '禁止 HTTP',
        HTTPToHTTPS: '訪問HTTP自動跳轉到HTTPS',
        HTTPAlso: 'HTTP可直接訪問',
        sslConfig: 'SSL 選項',
        disableHTTPS: '禁用 HTTPS',
        disableHTTPSHelper: '禁用 HTTPS會刪除證書相關配置，是否繼續？',
        SSLHelper: '註意：請勿將SSL證書用於非法網站 \n 如開啟後無法使用HTTPS訪問，請檢查安全組是否正確放行443端口',
        SSLConfig: '證書設置',
        SSLProConfig: 'SSL 協議設置',
        supportProtocol: '支持的協議版本',
        encryptionAlgorithm: '加密算法',
        notSecurity: '（不安全）',
        encryptHelper:
            "Let's Encrypt 簽發證書有頻率限製，但足以滿足正常需求，過於頻繁操作會導致簽發失敗。具體限製請看 <a target=「_blank」 href='https://letsencrypt.org/zh-cn/docs/rate-limits/'>官方文檔</a> ",
        ipValue: '值',
        ext: '文件擴展名',
        wafInputHelper: '按行輸入數據，一行一個',
        data: '數據',
        ever: '永久',
        nextYear: '一年後',
        noLog: '當前沒有日誌...',
        defaultServer: '默認站點',
        noDefaultServer: '未設置',
        defaultServerHelper:
            '設置默認站點後,所有未綁定的域名和IP都被定向到默認站點\n可有效防止惡意解析\n但同時會導致 WAF 未授權域名攔截失敗',
        websiteDeploymentHelper: '使用從 1Panel 部署的應用創建網站',
        websiteStatictHelper: '在主機上創建網站目錄',
        websiteProxyHelper:
            '代理已有服務,例如本機已安裝使用 8080 端口的 halo 服務，那麼代理地址為 http://127.0.0.1:8080',
        restoreHelper: '確認使用此備份恢復？',
        wafValueHelper: '值',
        runtimeProxyHelper: '使用從 1Panel 創建的運行環境',
        runtime: '運行環境',
        deleteRuntimeHelper: '運行環境應用需要跟網站一並刪除，請謹慎處理',
        proxyType: '監聽網絡類型',
        unix: 'Unix 網絡',
        tcp: 'TCP/IP 網絡',
        phpFPM: 'FPM 配置文件',
        phpConfig: 'PHP 配置文件',
        updateConfig: '配置修改',
        isOn: '開啟',
        isOff: '關閉',
        rewrite: '偽靜態',
        rewriteMode: '方案',
        current: '當前',
        rewriteHelper: '若設置偽靜態後，網站無法正常訪問，請嘗試設置回default',
        runDir: '運行目錄',
        runUserHelper:
            '透過 PHP 容器運行環境部署的網站，需要將 index 和子目錄下的所有檔案、資料夾擁有者和使用者群組設定為 1000，本地 PHP 環境需要參考本機 PHP-FPM 使用者和使用者群組設定',
        userGroup: '運行用戶/組',
        uGroup: '用戶組',
        proxyPath: '前端請求路徑',
        proxyPass: '後端代理地址',
        cache: '緩存',
        cacheTime: '緩存時間',
        enableCache: '開啟緩存',
        proxyHost: '後端域名',
        disabled: '已停止',
        startProxy: '開啟反向代理',
        stopProxy: '關閉反向代理',
        sourceFile: '源文',
        proxyHelper1: '訪問這個目錄時將會把目標URL的內容返回並顯示',
        proxyPassHelper: '代理的站點，必須為可正常訪問的URL',
        proxyHostHelper: '將域名添加到請求頭傳遞到代理服務器',
        modifier: '匹配規則',
        modifierHelper: '例：= 精確匹配，~ 正則匹配，^~ 匹配路徑開頭 等',
        replace: '文本替換',
        addReplace: '添加文本替換',
        replaced: '搜索字符串（不能為空）',
        replaceText: '替換為字符串',
        replacedErr: '搜索字符串不能為空',
        replacedErr2: '搜索字符串不能重復',
        basicAuth: '密碼訪問',
        editBasicAuthHelper: '密碼為非對稱加密，無法回顯，編輯需要重新設置密碼',
        antiLeech: '防盜鏈',
        extends: '擴展名',
        browserCache: '瀏覽器緩存',
        leechLog: '記錄防盜鏈日誌',
        accessDomain: '允許的域名',
        leechReturn: '響應資源',
        noneRef: '允許來源為空',
        disable: '未啟用',
        disableLeechHelper: '是否禁用防盜鏈',
        disableLeech: '禁用防盜鏈',
        ipv6: '監聽 IPV6',
        leechReturnError: '請填寫 HTTP 狀態碼',
        selectAcme: '選擇 Acme 賬號',
        imported: '手動創建',
        importType: '導入方式',
        pasteSSL: '粘貼代碼',
        localSSL: '選擇伺服器文件',
        privateKeyPath: '私鑰文件',
        certificatePath: '證書文件',
        ipWhiteListHelper: 'IP白名單的作用：所有規則對IP白名單無效',
        redirect: '重定向',
        sourceDomain: '源域名/路徑',
        targetURL: '目標URL地址',
        keepPath: '保留URI參數',
        path: '路徑',
        redirectType: '重定向類型',
        redirectWay: '方式',
        keep: '保留',
        notKeep: '不保留',
        redirectRoot: '重定向到首頁',
        redirectHelper: '301永久重定向，302臨時重定向',
        changePHPVersionWarn: '此動作不可回滾，是否繼續',
        changeVersion: '切換版本',
        retainConfig: '是否保留 php-fpm.conf 和 php.ini 文件',
        runDirHelper2: '請確保二級運行目錄位於 index 目錄下',
        openrestyHelper: 'OpenResty 默認 HTTP 端口：{0} HTTPS 端口：{1}，可能影響網站域名訪問和 HTTPS 強制跳轉',
        primaryDomainHelper: '支援網域:port',
        acmeAccountType: '賬號類型',
        keyType: '密鑰演算法',
        tencentCloud: '騰訊雲',
        containWarn: '其他域名中包含主域名，请重新輸入',
        rewriteHelper2: '從應用程式商店安裝的 WordPress 等應用，預設已經配置好偽靜態，重複配置可能會報錯',
        websiteBackupWarn: '僅支援導入本機備份，導入其他機器備份可能會恢復失敗',
        ipWebsiteWarn: 'IP 為網域名稱的網站，需要設定為預設網站才能正常存取',
        hstsHelper: '開啟 HSTS 可以增加網站安全性',
        includeSubDomains: '子域',
        hstsIncludeSubDomainsHelper: '啟用後，HSTS策略將應用於目前域名的所有子域名',
        defaultHtml: '預設頁面',
        website404: '網站 404 錯誤頁',
        domain404: '網站不存在頁面',
        indexHtml: '靜態網站預設頁',
        stopHtml: '網站停用頁',
        indexPHP: 'PHP 網站預設頁',
        sslExpireDate: '憑證過期時間',
        website404Helper: '網站 404 錯誤頁僅支援 PHP 運行環境網站和靜態網站',
        sni: '回源 SNI',
        sniHelper: '反代後端為 https 的時候可能需要設置回源 SNI，具體需要看 CDN 服務商文檔',
        huaweicloud: '華為雲',
        createDb: '建立資料庫',
        enableSSLHelper: '開啟失敗不會影響網站創建',
        batchAdd: '批量添加域名',
        generateDomain: '生成',
        global: '全局',
        subsite: '子網站',
        subsiteHelper: '子網站可以選擇已存在的 PHP 和靜態網站的目錄作為主目錄。',
        parentWbeiste: '父級網站',
        deleteSubsite: '刪除當前網站需要先刪除子網站 {0}',
        loadBalance: '負載均衡',
        server: '節點',
        algorithm: '演算法',
        ipHash: 'IP 雜湊',
        ipHashHelper: '基於客戶端 IP 位址將請求分配到特定伺服器，可以確保特定客戶端總是被路由到同一伺服器。',
        leastConn: '最少連接',
        leastConnHelper: '將請求發送到當前活動連接數最少的伺服器。',
        leastTime: '最少時間',
        leastTimeHelper: '將請求發送到當前活動連接時間最短的伺服器。',
        defaultHelper:
            '預設方法，請求被均勻分配到每個伺服器。如果伺服器有權重配置，則根據指定的權重分配請求，權重越高的伺服器接收更多請求。',
        weight: '權重',
        maxFails: '最大失敗次數',
        maxConns: '最大連接數',
        strategy: '策略',
        strategyDown: '停用',
        strategyBackup: '備用',
        staticChangePHPHelper: '目前為靜態網站，可切換為 PHP 網站',
        proxyCache: '反向代理快取',
        cacheLimit: '快取空間限制',
        shareCahe: '快取計數內存大小',
        cacheExpire: '快取過期時間',
        shareCaheHelper: '每1M內存可以存儲約8000個快取對象',
        cacheLimitHelper: '超過限制會自動刪除舊的快取',
        cacheExpireJHelper: '超出時間快取未命中將會被刪除',
        realIP: '真實 IP',
        ipFrom: 'IP 來源',
        ipFromHelper:
            '通過配置可信 IP 來源，OpenResty 會分析 HTTP Header 中的 IP 資訊，準確識別並記錄訪客的真實 IP 地址，包括在存取日誌中',
        ipFromExample1: '如果前端是 Frp 等工具，可以填寫 Frp 的 IP 地址，類似 127.0.0.1',
        ipFromExample2: '如果前端是 CDN，可以填寫 CDN 的 IP 地址段',
        ipFromExample3: '如果不確定，可以填 0.0.0.0/0（ipv4） ::/0（ipv6） [注意：允許任意來源 IP 不安全]',
        http3Helper:
            'HTTP/3 是 HTTP/2 的升級版本，提供更快的連線速度和更好的性能，但並非所有瀏覽器都支援 HTTP/3，啟用後可能會導致部分瀏覽器無法訪問',

        changeDatabase: '切換資料庫',
        changeDatabaseHelper1: '資料庫關聯用於備份恢復網站。',
        changeDatabaseHelper2: '切換其他資料庫會導致以前的備份無法恢復。',
        saveCustom: '另存为模版',
        rainyun: '雨雲',
        volcengine: 'Volcengine',
        runtimePortHelper: '當前運行環境存在多個端口，請選擇一個代理端口。',
        runtimePortWarn: '當前運行環境沒有端口，無法代理',
        cacheWarn: '請先關閉反代中的緩存開關',
        loadBalanceHelper: '創建負載均衡後，請前往‘反向代理’，添加代理並將後端地址設置為：http://<負載均衡名稱>',
        favorite: '收藏',
        cancelFavorite: '取消收藏',
        useProxy: '使用代理',
        useProxyHelper: '使用面板設置中的代理服務器地址',
        westCN: '西部數碼',
        openBaseDir: '防跨站攻擊',
        openBaseDirHelper: 'open_basedir 用於限制 PHP 文件訪問路徑，有助於防止跨站訪問和提升安全性',
        serverCacheTime: '伺服器緩存時間',
        serverCacheTimeHelper: '請求在伺服器端緩存的時間，到期前相同請求會直接返回緩存結果，不再請求源站。',
        browserCacheTime: '瀏覽器緩存時間',
        browserCacheTimeHelper: '靜態資源在瀏覽器本地緩存的時間，減少重複請求。到期前用戶刷新頁面會直接使用本地緩存。',
        donotLinkeDB: '不關聯數據庫',
        toWebsiteDir: '進入網站目錄',
    },
    php: {
        short_open_tag: '短標簽支持',
        max_execution_time: '最大腳本運行時間',
        max_input_time: '最大輸入時間',
        memory_limit: ' 腳本內存限製',
        post_max_size: 'POST數據最大尺寸',
        file_uploads: '是否允許上傳文件',
        upload_max_filesize: '允許上傳文件的最大尺寸',
        max_file_uploads: '允許同時上傳文件的最大數量',
        default_socket_timeout: 'Socket超時時間',
        error_reporting: '錯誤級別',
        display_errors: '是否輸出詳細錯誤信息',
        cgi_fix_pathinfo: '是否開啟pathinfo',
        date_timezone: '時區',
        disableFunction: '禁用函數',
        disableFunctionHelper: '輸入要禁用的函數，例如exec，多個請用,分割',
        uploadMaxSize: '上傳限製',
        indexHelper: '為保障PHP網站正常運行，請將代碼放置於 index 目錄，並避免重命名',
        extensions: '擴充範本',
        extension: '擴充',
        extensionHelper: '多個擴充功能,分割',
        toExtensionsList: '檢視擴充清單',
        containerConfig: '容器配置',
        containerConfigHelper: '環境變量等信息可以在創建完成之後在配置-容器配置中修改',
        dateTimezoneHelper: '示例：TZ=Asia/Shanghai（請根據需要自行添加）',
    },
    nginx: {
        serverNamesHashBucketSizeHelper: '服務器名字的hash表大小',
        clientHeaderBufferSizeHelper: '客戶端請求的頭buffer大小',
        clientMaxBodySizeHelper: '最大上傳文件',
        keepaliveTimeoutHelper: '連接超時時間',
        gzipMinLengthHelper: '最小壓縮文件',
        gzipCompLevelHelper: '壓縮率',
        gzipHelper: '是否開啟壓縮傳輸',
        connections: '活動連接(Active connections)',
        accepts: '總連接次數(accepts)',
        handled: '總握手次數(handled)',
        requests: '總請求數(requests)',
        reading: '請求數(Reading)',
        writing: '響應數(Writing)',
        waiting: '駐留進程(Waiting)',
        status: '當前狀態',
        configResource: '配置修改',
        saveAndReload: '保存並重載',
        clearProxyCache: '清除反代快取',
        clearProxyCacheWarn: '此操作將刪除緩存目錄下的所有文件，是否繼續？',
        create: '新增模組',
        update: '編輯模組',
        params: '參數',
        packages: '軟體包',
        script: '腳本',
        module: '模組',
        build: '建構',
        buildWarn: '建構 OpenResty 需要預留一定的 CPU 和內存，時間較長，請耐心等待',
        mirrorUrl: '軟體源',
        paramsHelper: '例如：--add-module=/tmp/ngx_brotli',
        packagesHelper: '例如：git,curl 以逗號分割',
        scriptHelper: '編譯之前執行的腳本，通常用於下載模組原始碼，安裝依賴等',
        buildHelper: '添加/修改模組後點擊構建，構建成功後會自動重啟 OpenResty',
        defaultHttps: 'HTTPS 防竄站',
        defaultHttpsHelper1: '開啟後可以解決 HTTPS 竄站問題',
    },
    ssl: {
        create: '申請證書',
        provider: '類型',
        manualCreate: '手動創建',
        acmeAccount: 'Acme 賬號',
        resolveDomain: '解析域名',
        err: '錯誤',
        value: '記錄值',
        dnsResolveHelper: '請到DNS解析服務商處添加以下解析記錄：',
        detail: '詳情',
        msg: '證書信息',
        ssl: '證書',
        key: '私鑰',
        startDate: '生效時間',
        organization: '簽發機構',
        renewConfirm: '是否確定給網域名稱 {0} 申請證書？ ',
        autoRenew: '自動續簽',
        autoRenewHelper: '距離到期時間30天自動續約',
        renewSuccess: '續簽成功',
        renewWebsite: '該證書已經和以下網站關聯，申請會同步應用到這些網站',
        createAcme: '創建賬戶',
        acmeHelper: 'Acme 賬戶用於申請免費證書',
        upload: '上傳證書',
        applyType: '申請方式',
        apply: '申請',
        applyStart: '證書申請開始',
        getDnsResolve: '正在取得 DNS 解析值,請稍後 ...',
        selfSigned: '自簽證書',
        ca: '證書頒發機構',
        commonName: '憑證主體名稱(CN)',
        caName: '機構名稱',
        company: '公司/組織',
        department: '部門',
        city: '城市',
        province: '省份',
        country: '國家代號',
        commonNameHelper: '例如:',
        selfSign: '簽發證書',
        days: '有效期限',
        domainHelper: '一行一個網域名稱,支援*和IP位址',
        pushDir: '推送憑證到本機目錄',
        dir: '目錄',
        pushDirHelper: '會在此目錄下產生兩個文件，憑證檔案：fullchain.pem 密钥檔案：privkey.pem',
        organizationDetail: '機構詳情',
        fromWebsite: '從網站獲取',
        dnsMauanlHelper: '手動解析模式需要在建立完之後點選申請按鈕取得 DNS 解析值',
        httpHelper: '使用 HTTP 模式需安裝 OpenResty，且不支持申請泛域名證書。',
        buypassHelper: 'Buypass 大陸地區無法訪問',
        googleHelper: '如何取得EAB HmacKey 和EAB kid',
        googleCloudHelper: 'Google Cloud API 大陸大部分地區無法存取',
        skipDNSCheck: '跳過 DNS 校驗',
        skipDNSCheckHelper: '如果出現申請超時問題，請勾選此處，其他情況請勿勾選',
        cfHelper: '請勿使用 Global API Key',
        deprecated: '即將廢棄',
        deprecatedHelper: '已經停止維護，可能會在以後的某個版本廢棄，請使用騰訊雲方式解析',
        disableCNAME: '停用 CNAME',
        disableCNAMEHelper: '有 CNAME 配置的域名，如果申請失敗，可以勾選此處',
        nameserver: 'DNS 伺服器',
        nameserverHelper: '使用自訂的 DNS 伺服器來校驗網域名稱',
        edit: '編輯證書',
        execShell: '申請憑證之後執行腳本',
        shell: '腳本內容',
        shellHelper:
            '腳本預設執行目錄為 1Panel 安裝目錄，如果有推送證書，那麼執行目錄為證書推送目錄。預設超時時間 30 分鐘',
        customAcme: '自訂 ACME 服務',
        customAcmeURL: 'ACME 服務 URL',
        baiduCloud: '百度雲',
    },
    firewall: {
        create: '創建規則',
        edit: '編輯規則',
        ccDeny: 'CC 防護',
        ipWhiteList: 'IP 白名單',
        ipBlockList: 'IP 黑名單',
        fileExtBlockList: '文件擴展名黑名單',
        urlWhiteList: 'URL 白名單',
        urlBlockList: 'URL 黑名單',
        argsCheck: 'GET 參數校驗',
        postCheck: 'POST 參數校驗',
        cookieBlockList: 'Cookie 黑名單',

        dockerHelper: 'Linux 防火墻 {0} 無法禁用 Docker 端口映射，應用可以在 [已安裝] 頁面編輯參數來控製端口是否放開',
        quickJump: '快速跳轉',
        used: '已使用',
        unUsed: '未使用',
        firewallHelper: '{0}系統防火墻',
        firewallNotStart: '當前未開啟系統防火墻，請先開啟！',
        restartFirewallHelper: '該操作將對當前防火牆進行重啟操作，是否繼續？',
        stopFirewallHelper: '系統防火墻關閉後，服務器將失去安全防護，是否繼續？',
        startFirewallHelper: '系統防火墻開啟後，可以更好的防護服務器安全，是否繼續？',
        noPing: '禁 ping',
        noPingTitle: '是否禁 ping',
        noPingHelper: '禁 ping 後將無法 ping 通服務器，是否繼續？',
        onPingHelper: '解除禁 ping 後您的服務器可能會被黑客發現，是否繼續？',
        changeStrategy: '修改{0}策略',
        changeStrategyIPHelper1: 'IP 策略修改為【屏蔽】，設置後該 IP 將禁止訪問服務器，是否繼續？',
        changeStrategyIPHelper2: 'IP 策略修改為【放行】，設置後該 IP 將恢復正常訪問，是否繼續？',
        changeStrategyPortHelper1: '端口策略修改為【拒絕】，設置後端口將拒絕外部訪問，是否繼續？',
        changeStrategyPortHelper2: '端口策略修改為【允許】，設置後端口將恢復正常訪問，是否繼續？',
        stop: '禁止',
        portFormatError: '請輸入正確的端口信息！',
        portHelper1: '多個端口，如：8080,8081',
        portHelper2: '範圍端口，如：8080-8089',
        strategy: '策略',
        accept: '允許',
        drop: '拒絕',
        anyWhere: '所有 IP',
        address: '指定 IP',
        addressHelper: '支持輸入 IP 或 IP 段',
        allow: '放行',
        deny: '屏蔽',
        addressFormatError: '請輸入合法的 ip 地址！',
        addressHelper1: '支持輸入 IP 或 IP 段：************ 或 **********/24',
        addressHelper2: '多個 IP 或 IP 段 請用 "," 隔開：************,**********/24',
        allIP: '所有 IP',
        portRule: '端口規則',
        createPortRule: '@:commons.button.create@:firewall.portRule',
        forwardRule: '端口轉發',
        createForwardRule: '@:commons.button.create@:firewall.forwardRule',
        ipRule: 'IP 規則',
        createIpRule: '@:commons.button.create @:firewall.ipRule',
        userAgent: 'User-Agent 過濾',
        sourcePort: '來源端口',
        targetIP: '目標 IP',
        targetPort: '目標端口',
        forwardHelper1: '如果是本機端口轉發，目標 IP 為：127.0.0.1',
        forwardHelper2: '如果目標 IP 不填寫，默認為本機端口轉發',
        forwardHelper3: '當前僅支持 IPv4 的端口轉發',
    },
    runtime: {
        runtime: '運行環境',
        workDir: '工作目錄',
        create: '創建運行環境',
        localHelper: '本地運行環境需要自行安裝',
        versionHelper: 'PHP的版本,例 v8.0',
        buildHelper: '擴展越多，製作映像檔時占用的 CPU 越高，可在建立環境後再安裝擴展。',
        openrestyWarn: 'PHP 需要升級  OpenResty 至 ******** 版本以上才能使用',
        toupgrade: '去升級',
        edit: '編輯運行環境',
        extendHelper: '列表中不存在的擴展，可以手動輸入之後選擇，例:輸入 sockets ，然後在下拉列表中選擇第一個',
        rebuildHelper: '編輯擴展後需要【重建】PHP 應用之後才能生效',
        rebuild: '重建 PHP 應用',
        source: 'PHP 擴展源',
        ustc: '中國科學技術大學',
        netease: '網易',
        aliyun: '阿里雲',
        tsinghua: '清華大學',
        xtomhk: 'XTOM 鏡像站（香港）',
        xtom: 'XTOM 鏡像站（全球）',
        phpsourceHelper: '根據你的網絡環境選擇合適的源',
        appPort: '應用端口',
        externalPort: '外部映射端口',
        packageManager: '包管理器',
        codeDir: '源碼目錄',
        appPortHelper: '應用端口是指容器內部運行的端口',
        externalPortHelper: '外部映射端口是指將容器內部端口映射到外部的端口',
        runScript: '啟動命令',
        runScriptHelper: '啟動命令是指容器啟動後運行的命令',
        open: '開啟',
        operatorHelper: '將對選取的執行環境進行 {0} 操作，是否繼續？ ',
        taobao: '淘寶',
        tencent: '騰訊',
        imageSource: '鏡像源',
        moduleManager: '模塊管理',
        module: '模塊',
        nodeOperatorHelper: '是否{0} {1} 模組？ 操作可能導致運轉環境異常，請確認後操作',
        customScript: '自訂啟動指令',
        customScriptHelper: '請填寫完整的啟動指令，例如：npm run start',
        portError: '不能填寫相同連接埠',
        systemRestartHelper: '狀態說明：中斷-系統重新啟動導致狀態取得失敗',
        javaScriptHelper: '請填寫完整啟動指令，例如：java -jar halo.jar -Xmx1024M -Xms256M',
        javaDirHelper: '目錄中要包含 jar 包，子目錄中包含也可',
        goHelper: '請填寫完整啟動命令，例如：go run main.go 或 ./main',
        goDirHelper: '目錄中要包含 go 文件或者二進制文件，子目錄中包含也可',
        extension: '擴充',
        installExtension: '是否確認安裝擴充功能 {0}',
        loadedExtension: '已載入擴充功能',
        popularExtension: '常用擴充',
        uninstallExtension: '是否確認卸載擴充功能 {0}',
        phpConfigHelper: '修改配置需要重新啟動運行環境，是否繼續',
        operateMode: '運行模式',
        dynamic: '動態',
        static: '靜態',
        ondemand: '按需',
        dynamicHelper: '動態調整進程數，彈性高，適合流量波動較大或低記憶體的網站',
        staticHelper: '固定進程數，適合高併發穩定流量的網站，資源消耗較高',
        ondemandHelper: '進程按需啟動和銷毀，資源利用最優，但初始回應可能較慢',
        max_children: '允許創建的最大進程數',
        start_servers: '啟動時所建立的進程數',
        min_spare_servers: '最小空閒行程數',
        max_spare_servers: '最大空閒行程數',
        envKey: '名稱',
        envValue: '值',
        environment: '環境變數',
        pythonHelper:
            '請填寫完整啟動指令，例如：pip install -r requirements.txt && python  manage.py runserver 0.0.0.0:5000',
        dotnetHelper: '請填寫完整的啟動命令，例如 dotnet MyWebApp.dll',
        dirHelper: '說明：請填寫容器內的目錄路徑',
        concurrency: '並發方案',
        loadStatus: '負載狀態',
    },
    process: {
        pid: '進程ID',
        ppid: '父進程ID',
        numThreads: '線程',
        memory: '內存',
        diskRead: '磁盤讀',
        diskWrite: '磁盤寫',
        netSent: '上行',
        netRecv: '下行',
        numConnections: '連接',
        startTime: '啟動時間',
        running: '運行中',
        sleep: '睡眠',
        stop: '停止',
        idle: '空閑',
        zombie: '僵屍進程',
        wait: '等待',
        lock: '鎖定',
        blocked: '阻塞',
        cmdLine: '啟動命令',
        basic: '基本信息',
        mem: '內存信息',
        openFiles: '文件打開',
        env: '環境變量',
        noenv: '無',
        net: '網絡連接',
        laddr: '本地地址/端口',
        raddr: '远程地址/端口',
        stopProcess: '結束',
        viewDetails: '查看詳情',
        stopProcessWarn: '是否確定結束此進程 (PID:{0})？',
        processName: '進程名稱',
    },
    tool: {
        supervisor: {
            loadStatusErr: '獲取進程狀態失敗，請檢查 supervisor 服務狀態',
            notSupport: '未檢測到 Supervisor 服務，請前往腳本庫頁面手動安裝',
            list: '守護進程',
            config: 'Supervisor 配置',
            primaryConfig: '主配置文件位置',
            notSupportCtl: '未檢測到 supervisorctl，請前往腳本庫頁面手動安裝',
            user: '啟動用戶',
            command: '啟動命令',
            dir: '運行目錄',
            numprocs: '進程數量',
            initWarn:
                '初始化操作需要修改配置文件的 [include] files 參數，修改後的服務配置文件所在目錄: 1panel安裝目錄/1panel/tools/supervisord/supervisor.d/',
            operatorHelper: '將對 {0} 進行 {1} 操作，是否繼續？ ',
            uptime: '運行時長',
            notStartWarn: 'Supervisor 未啟動，請先啟動',
            serviceName: '服務名稱',
            initHelper: '偵測到 Supervisor 服務尚未初始化，請點擊上方狀態列的初始化按鈕進行設定。',
            serviceNameHelper: 'systemctl 管理的 Supervisor 服務名稱，一般為 supervisor 或 supervisord',
            restartHelper: '初始化會重啟服務，導致原有的守護進程全部關閉',
            RUNNING: '運行中',
            STOPPED: '已停止',
            STOPPING: '停止中',
            STARTING: '啟動中',
            FATAL: '啟動失敗',
            BACKOFF: '啟動異常',
            ERROR: '錯誤',
            statusCode: '狀態碼',
            manage: '管理',
            autoRestart: '自動重啟',
            EXITED: '已退出',
            autoRestartHelper: '程式異常退出後是否自動重啟',
            autoStart: '自動啟動',
            autoStartHelper: 'Supervisor 啟動後是否自動啟動服務',
        },
    },
    xpack: {
        expiresTrialAlert:
            '溫馨提醒：您的專業版試用將在 {0} 天後到期，屆時所有專業版功能將無法繼續使用，請及時續費或升級到正式版本。',
        expiresAlert:
            '溫馨提醒：您的專業版許可證將在 {0} 天後到期，屆時所有專業版功能將無法繼續使用，請及時續費以保證正常使用。',
        menu: '高級功能',
        upage: 'AI 建站',
        app: {
            app: 'APP',
            title: '面板別名',
            titleHelper: '面板別名用於 APP 端的顯示（默認面板別名）',
            qrCode: '二維碼',
            apiStatusHelper: '面板 APP 需要開啟 API 接口功能',
            apiInterfaceHelper: '支持面板 API 接口訪問功能（面板 APP 需要開啟該功能）',
            apiInterfaceHelper1:
                '面板 APP 訪問需將訪問者添加至白名單，非固定 IP 建議添加 0.0.0.0/0（所有 IPv4），::/0（所有 IPv6）',
            qrCodeExpired: '刷新時間',
            apiLeakageHelper: '請勿洩露二維碼，確保僅在受信任的環境中使用',
        },
        waf: {
            name: 'WAF',
            blackWhite: '黑白名單',
            globalSetting: '全域設定',
            websiteSetting: '網站設定',
            blockRecords: '封鎖紀錄',
            world: '世界',
            china: '中國',
            intercept: '攔截',
            request: '請求',
            count4xx: '4xx 數量',
            count5xx: '5xx 數量',
            todayStatus: '今日狀態',
            reqMap: '攔截地圖（30日）',
            resource: '來源',
            count: '數量',
            hight: '高',
            low: '低',
            reqCount: '請求數',
            interceptCount: '攔截數',
            requestTrends: '請求趨勢（7天）',
            interceptTrends: '攔截趨勢（7天）',
            whiteList: '白名單',
            blackList: '黑名單',
            ipBlackListHelper: '黑名單中的 IP 無法存取網站',
            ipWhiteListHelper: '白名單中的 IP 不受任何規則限制',
            uaBlackListHelper: '攜帶黑名單中的 User-Agent 的請求將被攔截',
            uaWhiteListHelper: '攜帶白名單中的 User-Agent 的請求不受任何規則限制',
            urlBlackListHelper: '請求黑名單中的 URL 將被攔截',
            urlWhiteListHelper: '請求白名單中的 URL 請求不受任何規則限制',
            ccHelper: '{0} 秒內累積請求任意網站超過 {1} 次，封鎖此 IP {2}',
            blockTime: '封鎖時間',
            attackHelper: '{0} 秒內累計攔截超過 {1} 次，封鎖此 IP {2}',
            notFoundHelper: '{0} 秒內累計請求回傳 404 超過 {1} 次，封鎖此 IP {2}',
            frequencyLimit: '頻率限制',
            regionLimit: '地區限制',
            defaultRule: '預設規則',
            accessFrequencyLimit: '存取頻率限制',
            attackLimit: '攻擊頻率限制',
            notFoundLimit: '404 頻率限制',
            urlLimit: 'URL 頻率限制',
            urlLimitHelper: '為單一 URL 設定存取頻率',
            sqliDefense: 'SQL 注入防禦',
            sqliHelper: '辨識請求中的 SQL 注入並攔截',
            xssHelper: '辨識請求中的 XSS 並攔截',
            xssDefense: 'XSS 防禦',
            uaDefense: '惡意 User-Agent 規則',
            uaHelper: '包含常見的惡意爬蟲規則',
            argsDefense: '惡意參數規則',
            argsHelper: '在禁止請求中攜帶惡意參數',
            cookieDefense: '惡意 Cookie 規則',
            cookieHelper: '禁止請求中攜帶惡意 Cookie',
            headerDefense: '惡意 Header 規則',
            headerHelper: '禁止請求中攜帶惡意 Header',
            httpRule: 'HTTP 請求方法規則',
            httpHelper:
                '設定允許存取的方法類型，如果想限制某些類型瀏覽，請關閉這個類型的按鈕，例如：僅允許 GET 類型瀏覽，那麼需要關閉除了 GET 之外的其他類型按鈕',
            geoRule: '地區存取限制',
            geoHelper: '限制某些地區瀏覽你的網站，例如：允許中國大陸瀏覽，那麼中國大陸以外的請求都會被攔截',
            ipLocation: 'IP 歸屬地',
            action: '動作',
            ruleType: '攻擊類型',
            ipHelper: '請輸入 IP',
            attackLog: '攻擊日誌',
            rule: '規則',
            ipArr: 'IPV4 範圍',
            ipStart: '起始 IP',
            ipEnd: '結束 IP',
            ipv4: 'IPV4',
            ipv6: 'IPV6',
            urlDefense: 'URL 規則',
            urlHelper: '禁止存取的 URL',
            dirFilter: '目錄過濾',
            sqlInject: 'SQL 注入',
            xss: 'XSS',
            phpExec: 'PHP 腳本執行',
            oneWordTrojan: '一句話木馬',
            appFilter: '套用危險目錄過濾',
            webshell: 'Webshell',
            args: '惡意參數',
            protocolFilter: '協議過濾',
            javaFilter: 'Java 危險檔案過濾',
            scannerFilter: '掃描器過濾',
            escapeFilter: '轉義過濾',
            customRule: '自訂規則',
            httpMethod: 'HTTP 方法過濾',
            fileExt: '檔案上傳限制',
            fileExtHelper: '禁止上傳的檔案副檔名',
            deny: '禁止',
            allow: '允許',
            field: '匹配對象',
            pattern: '符合條件',
            ruleContent: '符合內容',
            contain: '包含',
            equal: '等於',
            regex: '正規表示式',
            notEqual: '不等於',
            customRuleHelper: '根據條件匹配執行對應動作',
            actionAllow: '允許',
            blockIP: '封鎖 IP',
            code: '返回狀態碼',
            noRes: '斷開連線 (444)',
            badReq: '參數錯誤 (400)',
            forbidden: '禁止瀏覽 (403)',
            serverErr: '伺服器錯誤 (500)',
            resHtml: '回應頁面',
            allowHelper: '允許瀏覽會跳過後續的 WAF 規則，請謹慎使用',
            captcha: '人機驗證',
            fiveSeconds: '5 秒驗證',
            location: '地區',
            redisConfig: 'Redis 配置',
            redisHelper: '開啟 Redis 可以將暫時拉黑的 IP 持久化',
            wafHelper: '關閉之後所有網站將失去防護',
            attackIP: '攻擊 IP',
            attackParam: '攻擊訊息',
            execRule: '命中規則',
            acl: 'ACL',
            sql: 'SQL 注入',
            cc: '瀏覽頻率限制',
            isBlocking: '封鎖中',
            isFree: '已解封',
            unLock: '解封',
            unLockHelper: '是否解封 IP:{0}?',
            saveDefault: '儲存預設',
            saveToWebsite: '應用在網站',
            saveToWebsiteHelper: '是否將目前設定套用到所有網站？ ',
            websiteHelper: '此處為建立網站的預設設定，修改之後需要應用到網站才能生效',
            websiteHelper2: '此處為建立網站的預設設定，具體配置請在網站處修改',
            ipGroup: 'IP 組',
            ipGroupHelper: '一行一個 IP 或 IP 段，支援 IPv4 和 IPv6， 例如：*********** 或 ***********/24',
            ipBlack: 'IP 黑名單',
            openRestyAlert: 'OpenResty 版本需要高於 {0}',
            initAlert: '首次使用需要初始化，會修改網站設定文件，原有的 WAF 設定會遺失，請一定提前備份 OpenResty',
            initHelper: '初始化操作將清除現有的 WAF 配置，您確定要進行初始化嗎？ ',
            mainSwitch: '總開關',
            websiteAlert: '請先建立網站',
            defaultUrlBlack: 'URL 規則',
            htmlRes: '攔截頁面',
            urlSearchHelper: '請輸入 URL，支援模糊搜尋',
            toCreate: '去建立',
            closeWaf: '關閉 WAF',
            closeWafHelper: '關閉 WAF 會使網站失去防護，是否繼續',
            addblack: '封鎖',
            addwhite: '加白',
            addblackHelper: '是否把 IP:{0} 加到預設黑名單?',
            addwhiteHelper: '是否把 IP:{0} 加到預設白名單?',
            defaultUaBlack: 'User-Agent 規則',
            defaultIpBlack: '惡意 IP 群組',
            cookie: 'Cookie 規則',
            urlBlack: 'URL 黑名單',
            uaBlack: 'User-Agent 黑名單',
            attackCount: '攻擊頻率限制',
            fileExtCheck: '檔案上傳限制',
            geoRestrict: '地區存取限制',
            attacklog: '攔截紀錄',
            unknownWebsite: '未授權網域存取',
            geoRuleEmpty: '地區不能為空',
            unknown: '網站不存在',
            geo: '地區限制',
            revertHtml: '是否還原{0}為預設頁面？',
            five_seconds: '5 秒驗證',
            header: 'Header 規則',
            methodWhite: 'HTTP 規則',
            expiryDate: '有效期限',
            expiryDateHelper: '驗證通過後有效期內不再驗證',
            defaultIpBlackHelper: '從網路收集的一些惡意 IP，阻止其存取',
            notFoundCount: '404 頻率限制',
            matchValue: '匹配值',
            headerName: '支持非特殊字元開頭、英文、數字、-，長度3-30',
            cdnHelper: '使用 CDN 的網站可以打開此處來取得正確來源 IP',
            clearLogWarn: '清空日誌將無法復原，是否繼續？',
            commonRuleHelper: '規則為模糊匹配',
            blockIPHelper: '封鎖 IP 暫時儲存在 OpenResty 中，重啟 OpenResty 會解封，可以透過封鎖功能永久拉黑',
            addWhiteUrlHelper: '是否把 URL {0} 加到白名單?',
            dashHelper: '社群版也可使用全域設定和網站設定中的功能',
            wafStatusHelper: 'WAF 未開啟，請在全域設定中開啟',
            ccMode: '模式',
            global: '全域模式',
            uriMode: 'URL 模式',
            globalHelper: '全域模式：當單位時間內任意URL的請求總數超過閾值時觸發',
            uriModeHelper: 'URL模式：單位時間內對單一URL的請求數量超過閾值時觸發',
            ip: 'IP 黑名單',
            globalSettingHelper: '有【網站】標籤的設定，需要在【網站設定】生效，全域設定僅為新建網站的預設設定',
            globalSettingHelper2: '設定生效需要【全域設定】和【網站設定】的開關同時開啟',
            urlCCHelper: '{0} 秒內累計請求此 URL 超過 {1} 次，封鎖此 IP {2}',
            urlCCHelper2: 'URL 不能帶參數',
            notContain: '不包含',
            urlcc: 'URL 頻率限制',
            method: '請求類型',
            addIpsToBlock: '批量拉黑 IP',
            addUrlsToWhite: '批量加白 URL',
            noBlackIp: 'IP 已拉黑，無需再次拉黑',
            noWhiteUrl: 'URL 已加白，無需再次加白',
            spiderIpHelper:
                '包含百度、Bing、谷歌、360、神马、搜狗、字节、DuckDuckGo、Yandex，關閉之後會攔截所有蜘蛛訪問',
            spiderIp: '蜘蛛 IP 池',
            geoIp: 'IP 地址庫',
            geoIpHelper: '用來確認 IP 的地理位置',
            stat: '攻擊報表',
            statTitle: '報表',
            attackIp: '攻擊 IP',
            attackCountNum: '攻擊次數',
            percent: '佔比',
            addblackUrlHelper: '是否把 URL:{0} 添加到默認黑名單？',
            rce: '遠程代碼執行',
            software: '軟件',
            cveHelper: '包含常見軟件、框架的漏洞',
            vulnCheck: '補充規則',
            ssrf: 'SSRF 漏洞',
            afr: '任意文件讀取',
            ua: '未授權訪問',
            id: '信息洩露',
            aa: '認證繞過',
            dr: '目錄遍歷',
            xxe: 'XXE 漏洞',
            suid: '序列化漏洞',
            dos: '拒絕服務漏洞',
            afd: '任意文件下載',
            sqlInjection: 'SQL 注入',
            afw: '任意文件寫入',
            il: '信息洩露',
            clearAllLog: '清空所有日誌',
            exportLog: '導出日誌',
            appRule: '應用規則',
            appRuleHelper: '常見應用的規則，開啟之後可以減少誤報，一個網站只能使用一個規則',
            logExternal: '排除記錄類型',
            ipWhite: 'IP 白名單',
            urlWhite: 'URL 白名單',
            uaWhite: 'User-Agent 白名單',
            logExternalHelper:
                '排除記錄類型不會被記錄到日誌中，黑白名單、地區訪問限制、自定義規則會產生大量日誌，建議排除',
            ssti: 'SSTI 攻擊',
            crlf: 'CRLF 注入',
            strict: '嚴格模式',
            strictHelper: '使用更嚴格的規則來校驗請求',
            saveLog: '保存日誌',
            remoteURLHelper: '遠程 URL 需要保證每行一個 IP 並且沒有其他字符',
            notFound: 'Not Found (404)',
            serviceUnavailable: '服務不可用 (503)',
            gatewayTimeout: '網關超時 (504)',
            belongToIpGroup: '屬於 IP 組',
            notBelongToIpGroup: '不屬於 IP 組',
            unknownWebsiteKey: '未知域名',
            special: '指定',
        },
        monitor: {
            name: '網站監控',
            pv: '瀏覽量',
            uv: '訪客數',
            flow: '流量',
            ip: '獨立 IP',
            spider: '蜘蛛',
            visitors: '訪客趨勢',
            today: '今天',
            last7days: '最近 7 天',
            last30days: '最近 30 天',
            uvMap: '訪客地圖 (30日)',
            qps: '即時請求數（1分鐘）',
            flowSec: '即時流量（1分鐘）',
            excludeCode: '排除狀態碼',
            excludeUrl: '排除 URL',
            excludeExt: '排除擴展名',
            cdnHelper: '透過 CDN 設定的 Header 來取得真實 IP',
            reqRank: '瀏覽排行',
            refererDomain: '來源網域',
            os: '作業系統',
            browser: '瀏覽器/客戶端',
            device: '裝置',
            showMore: '看更多',
            unknown: '其他',
            pc: '電腦',
            mobile: '移動端',
            wechat: '微信',
            machine: '機器',
            tencent: '騰訊瀏覽器',
            ucweb: 'UC 瀏覽器',
            '2345explorer': '2345 瀏覽器',
            huaweibrowser: '華為瀏覽器',
            log: '請求日誌',
            statusCode: '狀態碼',
            requestTime: '回應時間',
            flowRes: '回應流量',
            method: '請求類型',
            statusCodeHelper: '可在上方輸入狀態碼',
            statusCodeError: '狀態碼型別錯誤',
            methodHelper: '可在上方輸入請求類型',
            all: '所有',
            baidu: '百度',
            google: 'Google',
            bing: '必應',
            bytes: '今日頭條',
            sogou: '搜狗',
            failed: '錯誤',
            ipCount: 'IP 數',
            spiderCount: '蜘蛛請求',
            averageReqTime: '平均回應時間',
            totalFlow: '總流量',
            logSize: '日誌檔案大小',
            realIPType: '真實IP取得方式',
            fromHeader: '從 HTTP Header 取得',
            fromHeaders: '從 Header 清單中取得',
            header: 'HTTP Header',
            cdnConfig: 'CDN 適配',
            xff1: '取得 X-Forwarded-For 的上一级代理程式',
            xff2: '取得 X-Forwarded-For 的上上一级代理程式',
            xff3: '取得 X-Forwarded-For 的上上上一级代理程式',
            xffHelper:
                '例如：X-Forwarded-For: <client>,<proxy1>,<proxy2>,<proxy3> 上一階代理程式會取最後一個 IP <proxy3>',
            headersHelper: '從下列常用的 CDN 攜帶真實 IP 的 HTTP Header 中取得，取第一個能取得到的值',
            monitorCDNHelper: '修改網站監控的 CDN 設定會同步更新 WAF 的 CDN 設定',
            wafCDNHelper: '修改 WAF 的 CDN 設定會同步更新網站監控的 CDN 設定',
            statusErr: '狀態碼格式錯誤',
            shenma: '神馬搜尋',
            duckduckgo: 'DuckDuckGo',
            '360': '360 搜尋',
            exceptUri: '排除 Uri',
            top100Helper: '顯示 Top 100 的資料',
            logSaveDay: '日誌儲存天數',
            cros: 'Chrome OS',
            theworld: '世界之窗瀏覽器',
            edge: 'Edge',
            maxthon: '遨遊瀏覽器',
            monitorStatusHelper: '監控未開啟，請在設定中開啟',
            excludeIp: '排除 IP',
            excludeUa: '排除 User-Agent',
            remotePort: '遠程端口',
            unknown_browser: '未知',
            unknown_os: '未知',
            unknown_device: '未知',
            logSaveSize: '最大日誌保存大小',
            logSaveSizeHelper: '此處為單個網站的日誌保存大小',
            '360se': '360 安全瀏覽器',
            websites: '網站列表',
            trend: '趨勢統計',
            reqCount: '請求數',
            uriHelper: '可以使用 /test/* 或者 /*/index.php 來排除 Uri',
        },
        tamper: {
            tamper: '網站防篡改',
            ignoreTemplate: '排除目錄模板',
            protectTemplate: '保護文件模板',
            templateContent: '模板內容',
            template: '模板',
            tamperHelper1:
                '一鍵部署類型的網站，建議啟用應用目錄防篡改功能；如出現網站無法正常使用或備份、恢復失敗的情況，請先關閉防篡改功能；',
            tamperHelper2: '將限制非排除目錄下受保護文件的讀寫、刪除、權限和所有者修改操作',
            tamperPath: '防護目錄',
            tamperPathEdit: '修改路徑',
            log: '攔截日誌',
            totalProtect: '總防護',
            todayProtect: '今日防護',
            addRule: '添加規則',
            ignore: '排除目錄',
            ignoreHelper: '一行一個，例： \ntmp\n./tmp',
            ignoreTemplateHelper: '添加要忽略的文件夾名，以 , 分隔，例：tmp,cache',
            templateRule: '長度1-512，名稱不能含有{0}等符號',
            ignoreHelper1: '添加要忽略的文件夾名或特定路徑',
            ignoreHelper2: '要忽略特定文件夾，請使用以 ./ 開頭的相對路徑',
            protect: '保護文件',
            protectHelper: '一行一個，例： \npng\n./test.css',
            protectTemplateHelper: '添加要忽略的文件名或後綴名，以 , 分隔，例：conf,.css',
            protectHelper1: '可指定文件名、後綴名或特定文件進行保護',
            protectHelper2: '要保護特定文件，請使用以 ./ 開頭的相對路徑',
            enableHelper: '即將啟用下列網站的防篡改功能，以提升網站安全性，是否繼續？',
            disableHelper: '即將關閉下列網站的防篡改功能，是否繼續？',
        },
        setting: {
            setting: '界面設定',
            title: '面板描述',
            titleHelper: '將會顯示在使用者登入頁面 (例: Linux 伺服器運維管理面板，建議 8-15 位)',
            logo: 'Logo (不帶文字)',
            logoHelper: '將會顯示在選單收縮時管理頁面左上方 (建議圖片大小為: 82px*82px)',
            logoWithText: 'Logo (帶文字)',
            logoWithTextHelper: '將會顯示在選單展開時管理頁面左上方 (建議圖片大小為: 185px*55px)',
            favicon: '網站圖標',
            faviconHelper: '網站圖標 (建議圖片大小為: 16px*16px)',
            reUpload: '選擇文件',
            setDefault: '復原預設',
            setHelper: '即將儲存目前介面設定內容，是否繼續？',
            setDefaultHelper: '即將復原所有界面設定到初始狀態，是否繼續？',
            logoGroup: 'Logo',
            imageGroup: '圖片',
            loginImage: '登入頁圖片',
            loginImageHelper: '將會顯示在登入頁面（建議圖片大小為：500*416px）',
            loginBgType: '登入頁背景類型',
            loginBgImage: '登入頁背景圖片',
            loginBgImageHelper: '將會顯示在登入頁面背景圖片（建議圖片大小為：1920*1080px）',
            loginBgColor: '登入頁背景顏色',
            loginBgColorHelper: '將會顯示在登入頁面背景顏色',
            image: '圖片',
            bgColor: '背景色',
            loginGroup: '登入頁面',
            loginBtnLinkColor: '按鈕顏色',
            loginBtnLinkColorHelper: '將顯示為登入頁面上的按鈕顏色',
        },
        helper: {
            wafTitle1: '攔截地圖',
            wafContent1: '統計並顯示 30 天內的攔截地理位置分佈',
            wafTitle2: '地區瀏覽限制',
            wafContent2: '依地理位置限制網站的存取來源',
            wafTitle3: '自訂攔截頁面',
            wafContent3: '自訂請求被攔截之後的顯示頁面',
            wafTitle4: '自訂規則（ACL）',
            wafContent4: '根據自訂的規則攔截請求',

            tamperTitle1: '檔案完整性監控',
            tamperContent1: '監控網站檔案的完整性，包括核心檔案、腳本檔案和配置檔案等。',
            tamperTitle2: '即時掃描和檢測',
            tamperContent2: '通過即時掃描網站檔案系統，檢測是否存在異常或被篡改的檔案。',
            tamperTitle3: '安全權限設定',
            tamperContent3:
                '通過合理的權限設定和瀏覽控制策略，網站防篡改功能可以限制對網站檔案的瀏覽權限，減少潛在的攻擊面。',
            tamperTitle4: '日誌紀錄與分析',
            tamperContent4: '紀錄檔案瀏覽和操作日誌，以便管理員進行後續的審計和分析，以及發現潛在的安全威脅。',

            settingTitle1: '自訂歡迎語',
            settingContent1: '在 1Panel 登入頁上設定自訂的歡迎語。',
            settingTitle2: '自訂 Logo',
            settingContent2: '允許上傳包含品牌名稱或其他文字的 Logo 圖像。',
            settingTitle3: '自訂網站圖示',
            settingContent3: '允許上傳自訂的圖示，以替代預設的瀏覽器圖示，提升用戶體驗。',

            monitorTitle1: '訪客趨勢',
            monitorContent1: '統計並顯示網站的訪客趨勢',
            monitorTitle2: '訪客地圖',
            monitorContent2: '統計並顯示網站的訪客地理位置分佈',
            monitorTitle3: '瀏覽統計',
            monitorContent3: '統計網站的請求資訊，包括蜘蛛，瀏覽設備，請求狀態等',
            monitorTitle4: '即時監控',
            monitorContent4: '即時監控網站的請求訊息，包括請求數，流量等',

            alertTitle1: '簡訊告警',
            alertContent1:
                '當服務器資源使用异常、網站及證書過期、新版本更新、密碼過期等情况發生時，通過簡訊告警通知用戶，確保及時處理。',
            alertTitle2: '告警日誌',
            alertContent2: '為用戶提供查看告警日誌的功能，方便追跡和分析歷史告警事件。',
            alertTitle3: '告警設定',
            alertContent3:
                '為用戶提供自訂手機號、每日推送次數、每日推送時間的配寘，方便用戶的設定來更加合理的進行推送告警。',

            nodeTitle1: '一鍵添加節點',
            nodeContent1: '快速接入多台服務器節點',
            nodeTitle2: '批量升級',
            nodeContent2: '一次操作同步升級所有節點',
            nodeTitle3: '節點狀態監控',
            nodeContent3: '實時掌握各節點運行狀態',
            nodeTitle4: '快速遠程連接',
            nodeContent4: '一鍵直連節點遠程終端',

            fileExchangeTitle1: '密鑰認證傳輸',
            fileExchangeContent1: '通過 SSH 密鑰進行身份驗證，確保傳輸的安全性。',
            fileExchangeTitle2: '高效文件同步',
            fileExchangeContent2: '僅同步變化內容，大幅提高傳輸速度與穩定性。',
            fileExchangeTitle3: '支持多節點互傳',
            fileExchangeContent3: '可在不同節點間便捷傳送項目文件，靈活管理多台服務器。',

            appTitle1: '靈活管理面板',
            appContent1: '隨時隨地輕鬆管理你的 1Panel 伺服器。',
            appTitle2: '全面服務資訊',
            appContent2: '在移動端進行應用、網站、Docker、資料庫等基礎管理，支持快速創建應用與網站。',
            appTitle3: '實時異常監控',
            appContent3: '移動端實時查看伺服器狀態、WAF 安全監控、網站訪問統計與進程健康狀況。',

            clusterTitle1: '主從部署',
            clusterContent1: '支持在不同節點創建 MySQL/Postgres/Redis 主從實例，自動完成主從關聯與初始化',
            clusterTitle2: '主從管理',
            clusterContent2: '統一頁面集中管理多個主從節點，查看其角色、運行狀態等信息',
            clusterTitle3: '複製狀態',
            clusterContent3: '展示主從複製狀態與延遲信息，輔助排查同步異常問題',
        },
        node: {
            master: '主節點',
            masterBackup: '主節點備份',
            backupNode: '備份節點',
            backupFrequency: '備份頻率（小時）',
            backupCopies: '備份記錄保留份數',
            noBackupNode: '當前備份節點為空，請選擇備份節點保存後重試！',
            masterBackupAlert:
                '當前未配置主節點備份，為保障數據安全，請盡快設置備份節點，便於主節點故障時可人工切換新主節點。',
            node: '節點',
            addr: '地址',
            nodeUnhealthy: '節點狀態異常',
            deletedNode: '已刪除節點 {0} 暫不支援升級操作！',
            nodeUnhealthyHelper: '檢測到該節點狀態異常，請在 [節點管理] 中檢查後重試！',
            nodeUnbind: '節點未綁定許可證',
            nodeUnbindHelper: '檢測到該節點未綁定許可證，請在 [面板設定 - 許可證] 選單中綁定後重試！',
            memTotal: '記憶體總計',
            nodeManagement: '節點管理',
            addNode: '新增節點',
            connInfo: '連接資訊',
            nodeInfo: '節點資訊',
            syncInfo: '數據同步,',
            syncHelper: '當主節點數據發生變化時，實時同步到該子節點,',
            syncBackupAccount: '備份帳號設定',
            syncWithMaster: '升級為專業版後，將預設同步所有資料，可在節點管理中手動調整同步策略。',
            syncProxy: '系統代理設定',
            syncProxyHelper: '同步系統代理設定需要重啟 Docker',
            syncProxyHelper1: '重啟 Docker 可能會影響當前正在運行的容器服務。',
            syncProxyHelper2: '可前往 容器 - 設定 頁面手動重啟。',
            syncProxyHelper3: '同步系統代理設定需要重啟 Docker，重啟可能會影響當前正在運行的容器服務',
            syncProxyHelper4: '同步系統代理設定需要重啟 Docker，可稍後前往 容器 - 設定 頁面手動重啟。',
            syncCustomApp: '同步自訂應用倉庫',
            syncAlertSetting: '系統告警設定',
            syncNodeInfo: '節點基礎數據,',
            nodeSyncHelper: '節點信息同步將同步以下信息：',
            nodeSyncHelper1: '1. 公用的備份帳號信息',
            nodeSyncHelper2: '2. 主節點與子節點的連接信息',

            nodeCheck: '可用性檢查',
            checkSSH: '檢查節點 SSH 連接',
            checkUserPermission: '檢查節點用戶權限',
            isNotRoot: '檢測到該節點不支持免密 sudo，且當前為非 root 用戶',
            checkLicense: '檢查節點許可證狀態',
            checkService: '檢查節點已存在服務信息',
            checkPort: '檢查節點端口可達',
            panelExist: '檢查到該節點正在運行 1Panel V1 服務，請先通過遷移腳本升級至 V2 後再進行添加。',
            coreExist:
                '當前節點已作為主節點啟用，無法直接作為從節點添加。請先將其降級為從節點後再添加，具體可參考文件。',
            agentExist: '檢查到該節點已安裝 1panel-agent，繼續添加將保留現有數據，僅替換 1panel-agent 服務。',
            oldDataExist: '檢查到該節點存在 1Panel V2 歷史數據，將使用以下信息覆蓋當前設置',
            errLicense: '檢查到該節點綁定的許可證不可用，請檢查後重試！',
            errNodePort: '檢查到節點端口 [ {0} ] 無法訪問，請檢查防火牆或安全組是否已放行該端口。',

            reinstallHelper: '重新安裝節點 {0}，是否繼續？',
            unhealthyCheck: '異常檢查',
            fixOperation: '修復方案',
            checkName: '檢查項目',
            checkSSHConn: '檢查 SSH 連接可用性',
            fixSSHConn: '手動編輯節點，確認連接資訊',
            checkConnInfo: '檢查 Agent 連接資訊',
            checkStatus: '檢查節點服務可用性',
            fixStatus: '執行 "systemctl status 1panel-agent.service" 以檢查服務是否已啟動。',
            checkAPI: '檢查節點 API 可用性',
            fixAPI: '檢查節點日誌，確認防火牆端口是否正常放行。',
            forceDelete: '強制刪除',
            operateHelper: '將對以下節點執行 {0} 操作，是否繼續？',
            forceDeleteHelper: '強制刪除將忽略節點刪除錯誤並刪除資料庫元資料',
            uninstall: '刪除節點資料',
            uninstallHelper: '此操作將刪除與節點相關的所有 1Panel 資料，請謹慎選擇！',
            baseDir: '安裝目錄',
            baseDirHelper: '安裝目錄為空時，預設安裝於 /opt 目錄下',
            nodePort: '節點端口',
            offline: '離線模式',
            freeCount: '免費額度 [{0}]',
            offlineHelper: '當節點處於離線環境時使用',
        },
        customApp: {
            name: '自訂應用倉庫',
            appStoreType: '應用商店包來源',
            appStoreUrl: '倉庫地址',
            local: '本機路徑',
            remote: '遠端連結',
            imagePrefix: '映像前綴',
            imagePrefixHelper:
                '作用：自訂映像前綴，修改 compose 檔案中的映像欄位，例如：當映像前綴設定為 1panel/custom 時，MaxKB 的映像欄位將變更為 1panel/custom/maxkb:v1.10.0',
            closeHelper: '是否取消使用自訂應用倉庫',
            appStoreUrlHelper: '僅支援 .tar.gz 格式',
            postNode: '同步至子節點',
            postNodeHelper: '把自訂商店包同步至子節點的安裝目錄下的 tmp/customApp/apps.tar.gz 中',
            nodes: '節點',
            selectNode: '選擇節點',
            selectNodeError: '請選擇節點',
            licenseHelper: '專業版支持自定義應用倉庫功能',
        },
        alert: {
            isAlert: '是否告警',
            alertCount: '告警次數',
            clamHelper: '掃描到感染檔案時觸發告警',
            cronJobHelper: '定時任務執行失敗時將觸發告警',
            licenseHelper: '專業版支持簡訊告警功能',
            alertCountHelper: '每日最大告警次數',
            alert: '簡訊告警',
            logs: '告警日誌',
            list: '告警清單',
            addTask: '建立簡訊告警',
            editTask: '編輯簡訊告警',
            alertMethod: '告警方式',
            alertMsg: '告警內容',
            alertRule: '告警規則',
            titleSearchHelper: '請輸入告警標題，支援模糊搜尋',
            taskType: '告警類型',
            ssl: '網站證書（SSL）到期',
            siteEndTime: '網站到期',
            panelPwdEndTime: '面板密碼到期',
            panelUpdate: '面板新版本提醒',
            cpu: '面板服務器 CPU 佔用過高告警',
            memory: '面板服務器記憶體佔用過高告警',
            load: '面板服務器負載佔用過高告警',
            disk: '面板服務器磁碟佔用過高告警',
            website: '網站',
            certificate: '證書',
            remainingDays: '剩餘天數',
            sendCount: '告警次數',
            sms: '簡訊通知',
            wechat: '微信公眾號',
            dingTalk: '釘釘通知',
            feiShu: '飛書通知',
            mail: '信箱通知',
            weCom: '企業微信',
            sendCountRulesHelper: '到期前發送告警的總數（每日僅發送一次）',
            panelUpdateRulesHelper: '新版本發送告警總數（每日僅發送一次）',
            oneDaySendCountRulesHelper: '每日發送告警的總數',
            siteEndTimeRulesHelper: '永不過期的網站，不觸發告警',
            autoRenewRulesHelper: '證書開啟自動續期，剩餘天數小於31天，不觸發告警',
            panelPwdEndTimeRulesHelper: '面板未設定密碼到期時長，不能使用密碼到期告警',
            sslRulesHelper: '所有ssl證書',
            diskInfo: '磁碟資訊',
            monitoringType: '監測類型',
            autoRenew: '自動續簽',
            useDisk: '佔用磁碟',
            usePercentage: '佔用百分比',
            changeStatus: '狀態修改',
            disableMsg: '停止告警任務會導致該任務不再發送告警消息。 是否繼續？',
            enableMsg: '啟用告警任務會讓該任務發送告警消息。 是否繼續？',
            useExceed: '使用超過',
            useExceedRulesHelper: '使用超過指定值觸發告警',
            cpuUseExceedAvg: 'CPU 平均使用率超過指定值',
            memoryUseExceedAvg: '記憶體平均使用率超過指定值',
            loadUseExceedAvg: '負載平均使用率超過指定值',
            cpuUseExceedAvgHelper: '指定時間內 CPU 平均使用率超過指定值',
            memoryUseExceedAvgHelper: '指定時間內記憶體平均使用率超過指定值',
            loadUseExceedAvgHelper: '指定時間內負載平均使用率超過指定值',
            resourceAlertRulesHelper: '注意：30分鐘內持續告警只發送一次簡訊',
            specifiedTime: '指定時間',
            deleteTitle: '删除告警',
            deleteMsg: '是否確認删除告警任務？',
            allSslTitle: '所有網站證書（SSL）到期告警',
            sslTitle: '網站「{0}」證書（SSL）到期告警',
            allSiteEndTimeTitle: '所有網站到期告警',
            siteEndTimeTitle: '網站「{0}」到期告警',
            panelPwdEndTimeTitle: '面板密碼到期告警',
            panelUpdateTitle: '面板新版本提醒',
            cpuTitle: 'CPU 佔用過高告警',
            memoryTitle: '記憶體佔用過高告警',
            loadTitle: '負載佔用過高告警',
            diskTitle: '掛載目錄「{0}」的磁碟佔用過高告警',
            allDiskTitle: '磁碟佔用過高告警',
            timeRule: '剩餘時間小於{0}天（如未處理，次日會重新發送）',
            panelUpdateRule: '檢測到面板有新版本時發送一次（如未處理，次日會重新發送）',
            avgRule: '{0}分鐘內平均{1}佔用超過{2}%觸發，每天發送{3}次',
            diskRule: '掛載目錄「{0}」的磁碟佔用超過{1}{2}觸發，每天發送{3}次',
            allDiskRule: '磁碟佔用超過{0}{1}觸發，每天發送{2}次',
            cpuName: ' CPU ',
            memoryName: '記憶體',
            loadName: '負載',
            diskName: '磁碟',
            syncAlertInfo: '同步',
            syncAlertInfoMsg: '是否同步告警資訊內容狀態？',
            pushError: '推送失敗',
            pushSuccess: '推送成功',
            syncError: '同步失敗',
            success: '告警成功',
            pushing: '推送中...',
            error: '告警失敗',
            cleanLog: '清空日誌',
            cleanAlertLogs: '清空告警日誌',
            daily: '當日第 {0} 次告警',
            cumulative: '累計第 {0} 次告警',
            clams: '病毒掃描',
            taskName: '任務名稱',
            cronJobType: '任務類型',
            clamPath: '掃描目錄',
            cronjob: '計劃任務',
            app: '備份應用',
            web: '備份網站',
            database: '備份資料庫',
            directory: '備份目錄',
            log: '備份日誌',
            snapshot: '系統快照',
            clamsRulesHelper: '需要開啟告警的病毒掃描任務',
            cronJobRulesHelper: '需要配寘此類型的計劃任務',
            clamsTitle: '病毒掃描「{0}」任務檢測到感染文件告警',
            cronJobAppTitle: '計劃任務-備份應用「{0}」任務失敗告警',
            cronJobWebsiteTitle: '計劃任務-備份網站「{0}」任務失敗告警',
            cronJobDatabaseTitle: '計劃任務-備份資料庫「{0}」任務失敗告警',
            cronJobDirectoryTitle: '計劃任務-備份目錄「{0}」任務失敗告警',
            cronJobLogTitle: '計劃任務-備份日誌「{0}」任務失敗告警',
            cronJobSnapshotTitle: '計劃任務-系統快照「{0}」任務失敗告警',
            cronJobShellTitle: '計劃任務-Shell 腳本「 {0} 」任務失敗告警',
            cronJobCurlTitle: '計劃任務-瀏覽 URL「 {0} 」任務失敗告警',
            cronJobCutWebsiteLogTitle: '計劃任務-切割網站日誌「 {0} 」任務失敗告警',
            cronJobCleanTitle: '計劃任務-快取清理「 {0} 」任務失敗告警',
            cronJobNtpTitle: '計劃任務-同步服务器时间「 {0} 」任務失敗告警',
            clamsRule: '病毒掃描任務檢測到感染文件告警，每天發送{0}次',
            cronJobAppRule: '備份應用任務失敗告警，每天發送{0}次',
            cronJobWebsiteRule: '備份網站任務失敗告警，每天發送{0}次',
            cronJobDatabaseRule: '備份資料庫任務失敗告警，每天發送{0}次',
            cronJobDirectoryRule: '備份目錄任務失敗告警，每天發送{0}次',
            cronJobLogRule: '備份日誌任務失敗告警，每天發送{0}次',
            cronJobSnapshotRule: '系統快照任務失敗告警，每天發送{0}次',
            cronJobShellRule: 'Shell 腳本任務失敗告警，每天發送{0}次',
            cronJobCurlRule: '瀏覽 URL任務失敗告警，每天發送{0}次',
            cronJobCutWebsiteLogRule: '切割網站日誌任務失敗告警，每天發送{0}次',
            cronJobCleanRule: '快取清理任務失敗告警，每天發送{0}次',
            cronJobNtpRule: '同步服務器時間任務失敗告警，每天發送{0}次',
            alertSmsHelper: '簡訊額度：總量{0}條，已使用{1}條',
            goBuy: '去購買',
            phone: '手機號',
            phoneHelper: '请請填寫真實的手機號，以免不能正常接收告警資訊',
            dailyAlertNum: '每日告警次數',
            dailyAlertNumHelper: '每日告警通知的總次數，最多通知 100 次',
            timeRange: '時間範圍',
            sendTimeRange: '可發送時間範圍',
            sendTimeRangeHelper: '可推送{0}時間範圍',
            to: '至',
            startTime: '開始時間',
            endTime: '結束時間',
            defaultPhone: '預設使用與許可證綁定的帳戶手機號',
            noticeAlert: '通知告警',
            resourceAlert: '資源告警',
            agentOfflineAlertHelper: '當節點啟用離線告警時，主節點將每半小時掃描並執行一次告警任務。',
            offline: '離線告警',
            offlineHelper: '設為離線告警時，主節點將每半小時掃描並執行一次告警任務。',
            offlineOff: '開啟離線告警',
            offlineOffHelper: '開啟離線告警後，主節點將每半小時掃描並執行一次告警任務。',
            offlineClose: '關閉離線告警',
            offlineCloseHelper: '關閉離線告警後，需由子節點自行告警，請確保子節點網路通暢，以免告警失敗。',
            alertNotice: '警報通知',
            methodConfig: '發送方式設定',
            commonConfig: '全域設定',
            smsConfig: '簡訊',
            smsConfigHelper: '設定簡訊通知號碼',
            emailConfig: '郵件',
            emailConfigHelper: '設定 SMTP 郵件發送服務',
            deleteConfigTitle: '刪除警報設定',
            deleteConfigMsg: '是否確定刪除此警報設定？',
            test: '測試',
            alertTestOk: '測試通知成功',
            alertTestFailed: '測試通知失敗',
            displayName: '顯示名稱',
            sender: '寄件地址',
            password: '密碼',
            host: 'SMTP 伺服器',
            port: '連接埠',
            encryption: '加密方式',
            recipient: '收件者',
            licenseTime: '授權到期提醒',
            licenseTimeTitle: '授權到期提醒',
            displayNameHelper: '郵件的寄件人顯示名稱',
            senderHelper: '用於發送郵件的電子信箱地址',
            passwordHelper: '郵件服務的授權碼',
            hostHelper: 'SMTP 伺服器地址，例如：smtp.qq.com',
            portHelper: 'SSL 通常為 465，TLS 通常為 587',
            sslHelper: '若 SMTP 連接埠為 465，通常需要啟用 SSL',
            tlsHelper: '若 SMTP 連接埠為 587，通常需要啟用 TLS',
        },
        theme: {
            lingXiaGold: '凌霞金',
            classicBlue: '經典藍',
            freshGreen: '清新綠',
            customColor: '自訂主題色',
            setDefault: '復原預設',
            setDefaultHelper: '即將復原主題配色到初始狀態，是否繼續？',
            setHelper: '即將儲存目前選定的主題配色，是否繼續？',
        },
        exchange: {
            exchange: '文件對傳',
            exchangeConfirm: '是否將 {0} 節點文件/文件夾 {1} 傳輸到 {2} 節點 {3} 目錄？',
        },
        cluster: {
            cluster: '應用高可用',
            name: '集群名稱',
            addCluster: '添加集群',
            installNode: '安裝節點',
            master: '主節點',
            slave: '從節點',
            replicaStatus: '主從狀態',
            unhealthyDeleteError: '安裝節點狀態異常，請在節點列表檢查後重試！',
            replicaStatusError: '狀態獲取異常，請檢查主節點。',
            masterHostError: '主節點 IP 不能為 127.0.0.1',
        },
    },
};
export default {
    ...fit2cloudTwLocale,
    ...message,
};
