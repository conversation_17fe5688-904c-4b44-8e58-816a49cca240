import fit2cloudEnLocale from 'fit2cloud-ui-plus/src/locale/lang/ru';

const message = {
    commons: {
        true: 'да',
        false: 'нет',
        colon: ': ',
        example: 'Например, ',
        fit2cloud: 'FIT2CLOUD',
        lingxia: 'Lingxia',
        button: {
            run: 'Запуск',
            create: 'Создать ',
            add: 'Добавить ',
            save: 'Сохранить ',
            set: 'Изменить конфигурацию',
            sync: 'Синхронизировать ',
            delete: 'Удалить',
            edit: 'Редактировать ',
            enable: 'Включить',
            disable: 'Отключить',
            confirm: 'Подтвердить',
            cancel: 'Отмена',
            reset: 'Сбросить',
            restart: 'Перезапустить',
            conn: 'Подключить',
            disConn: 'Отключить',
            clean: 'Очистить',
            login: 'Войти',
            close: 'Закрыть',
            off: 'Закрыть',
            stop: 'Остановить',
            start: 'Запустить',
            view: 'Просмотр',
            watch: 'Наблюдать',
            handle: 'Запустить',
            expand: 'Развернуть',
            clone: 'Клонировать',
            collapse: 'Свернуть',
            log: 'Логи',
            back: 'Назад',
            backup: 'Бэкап',
            recover: 'Восстановить',
            retry: 'Повторить',
            upload: 'Загрузить',
            download: 'Скачать',
            init: 'Инициализировать',
            verify: 'Проверить',
            saveAndEnable: 'Сохранить и включить',
            import: 'Импорт',
            export: 'Экспорт',
            power: 'Авторизация',
            search: 'Поиск',
            refresh: 'Обновить',
            get: 'Получить',
            upgrade: 'Обновить',
            update: 'обновление',
            ignore: 'Игнорировать обновление',
            install: 'установить',
            copy: 'Копировать',
            random: 'Случайно',
            uninstall: 'Удалить',
            fullscreen: 'Полный экран',
            quitFullscreen: 'Выйти из полного экрана',
            showAll: 'Показать все',
            hideSome: 'Скрыть некоторые',
            agree: 'Согласен',
            notAgree: 'Не согласен',
            preview: 'Предпросмотр',
            open: 'Открыть',
            notSave: 'Не сохранять',
            createNewFolder: 'Создать новую папку',
            createNewFile: 'Создать новый файл',
            helpDoc: 'Справка',
            unbind: 'Отвязать',
            cover: 'Заменить',
            skip: 'Пропустить',
            fix: 'Исправить',
            down: 'Остановить',
            up: 'Запустить',
            sure: 'Подтвердить',
            show: 'Показать',
            hide: 'Скрыть',
        },
        operate: {
            start: 'Запустить',
            stop: 'Остановить',
            restart: 'Перезапустить',
            reload: 'Перезагрузить',
            rebuild: 'Перестроить',
            sync: 'Синхронизировать',
            up: 'Запустить',
            down: 'Остановить',
            delete: 'Удалить',
        },
        search: {
            timeStart: 'Время начала',
            timeEnd: 'Время окончания',
            timeRange: 'До',
            dateStart: 'Дата начала',
            dateEnd: 'Дата окончания',
        },
        table: {
            all: 'Все',
            total: 'Всего {0}',
            name: 'Имя',
            type: 'Тип',
            status: 'Статус',
            records: 'Записи',
            group: 'Группа',
            createdAt: 'Время создания',
            publishedAt: 'Время публикации',
            date: 'Дата',
            updatedAt: 'Время обновления',
            operate: 'Операции',
            message: 'Сообщение',
            description: 'Описание',
            interval: 'Интервал',
            user: 'Владелец',
            title: 'Заголовок',
            port: 'Порт',
            forward: 'Переадресация',
            protocol: 'Протокол',
            tableSetting: 'Настройки таблицы',
            refreshRate: 'Частота обновления',
            selectColumn: 'Выбрать столбец',
            local: 'локальный',
            serialNumber: 'Серийный номер',
            manageGroup: 'Управление группами',
            backToList: 'Вернуться к списку',
            keepEdit: 'Продолжить редактирование',
        },
        loadingText: {
            Upgrading: 'Обновление системы, пожалуйста, подождите...',
            Restarting: 'Перезагрузка системы, пожалуйста, подождите...',
            Recovering: 'Восстановление из снапшота, пожалуйста, подождите...',
            Rollbacking: 'Откат из снапшота, пожалуйста, подождите...',
        },
        msg: {
            noneData: 'Нет данных',
            delete: 'Эта операция удаления не может быть отменена. Хотите продолжить?',
            clean: 'Эта операция очистки не может быть отменена. Хотите продолжить?',
            closeDrawerHelper: 'Система может не сохранить внесённые вами изменения. Продолжить?',
            deleteSuccess: 'Успешно удалено',
            loginSuccess: 'Успешный вход',
            operationSuccess: 'Операция выполнена успешно',
            copySuccess: 'Успешно скопировано',
            notSupportOperation: 'Эта операция не поддерживается',
            requestTimeout: 'Время запроса истекло, попробуйте позже',
            infoTitle: 'Подсказка',
            notRecords: 'Для текущей задачи не создано записей выполнения',
            sureLogOut: 'Вы уверены, что хотите выйти?',
            createSuccess: 'Успешно создано',
            updateSuccess: 'Успешно обновлено',
            uploadSuccess: 'Успешно загружено',
            operateConfirm: 'Если вы уверены в операции, введите её вручную: ',
            inputOrSelect: 'Пожалуйста, выберите или введите',
            copyFailed: 'Не удалось скопировать',
            operatorHelper: 'Операция "{1}" будет выполнена над "{0}" и не может быть отменена. Хотите продолжить?',
            notFound: 'Извините, запрошенная страница не существует.',
            unSupportType: 'Текущий тип файла не поддерживается.',
            unSupportSize: 'Загруженный файл превышает {0}M, пожалуйста, подтвердите!',
            fileExist: 'Файл уже существует в текущей папке. Повторная загрузка не поддерживается.',
            fileNameErr:
                'Вы можете загружать только файлы, имя которых содержит от 1 до 256 символов, включая английские буквы, китайские иероглифы, цифры или точки (.-_)',
            confirmNoNull: 'Убедитесь, что значение {0} не пустое.',
            errPort: 'Неверная информация о порте, пожалуйста, проверьте!',
            remove: 'Удалить',
            backupHelper: 'Текущая операция создаст резервную копию {0}. Хотите продолжить?',
            recoverHelper: 'Восстановление из файла {0}. Эта операция необратима. Хотите продолжить?',
            refreshSuccess: 'Обновление успешно',
            rootInfoErr: 'Это уже корневой каталог',
            resetSuccess: 'Сброс успешен',
            creatingInfo: 'Создание, эта операция не нужна',
        },
        login: {
            username: 'Имя пользователя',
            password: 'Пароль',
            welcome: 'Добро пожаловать, введите имя пользователя и пароль для входа!',
            errorAuthInfo: 'Введенное имя пользователя или пароль неверны, пожалуйста, введите заново!',
            errorMfaInfo: 'Неверная информация аутентификации, попробуйте еще раз!',
            captchaHelper: 'Капча',
            errorCaptcha: 'Ошибка кода капчи!',
            notSafe: 'Доступ запрещен',
            safeEntrance1: 'В текущей среде включен безопасный вход',
            safeEntrance2: 'Введите следующую команду в SSH-терминале для просмотра входа в панель: 1pctl user-info',
            errIP1: 'В текущей среде включен доступ по авторизованному IP-адресу',
            errDomain1: 'В текущей среде включена привязка домена доступа',
            errHelper: 'Для сброса информации о привязке выполните следующую команду в SSH-терминале: ',
            codeInput: 'Пожалуйста, введите 6-значный код подтверждения MFA',
            mfaTitle: 'MFA Сертификация',
            mfaCode: 'MFA код подтверждения',
            title: 'Панель управления Linux сервером',
            licenseHelper: '<Лицензионное соглашение сообщества>',
            errorAgree: 'Нажмите, чтобы согласиться с Лицензией программного обеспечения сообщества',
            logout: 'Выход',
            agreeTitle: 'Соглашение',
            agreeContent:
                'Для лучшей защиты ваших законных прав и интересов, пожалуйста, прочитайте и согласитесь со следующим соглашением &laquo; <a href = "https://www.fit2cloud.com/legal/licenses.html" target = "_blank" > Лицензионное соглашение сообщества </a> &raquo;',
        },
        rule: {
            username: 'Введите имя пользователя',
            password: 'Введите пароль',
            rePassword: 'Подтверждение пароля не совпадает с паролем.',
            requiredInput: 'Это поле обязательно для заполнения.',
            requiredSelect: 'Выберите элемент из списка',
            illegalChar: 'В настоящее время не поддерживается вставка символов & ; $ \' ` ( ) " > < |',
            illegalInput: 'Это поле не должно содержать недопустимых символов.',
            commonName:
                'Это поле должно начинаться с неспециальных символов и должно состоять из английских букв, китайских иероглифов, цифр, ".", "-" и "_" длиной 1-128.',
            userName: 'Поддерживает начало без специальных символов, английский, китайский, цифры и _, длина 3-30',
            simpleName:
                'Это поле не должно начинаться с подчеркивания и должно состоять из английских букв, цифр и "_" длиной 3-30.',
            simplePassword:
                'Это поле не должно начинаться с подчеркивания и должно состоять из английских букв, цифр и "_" длиной 1-30.',
            dbName: 'Это поле не должно начинаться с подчеркивания и должно состоять из английских букв, цифр и "_" длиной 1-64.',
            imageName: 'Поддерживает начало без специальных символов, английский, цифры, :@/.-_, длина 1-256',
            composeName: 'Поддерживаются неспециальные символы в начале, строчные буквы, цифры, - и _, длина 1-256',
            volumeName: 'Это поле должно состоять из английских букв, цифр, ".", "-" и "_" длиной 2-30.',
            supervisorName:
                'Это поле должно начинаться с неспециальных символов и должно состоять из английских букв, цифр, "-" и "_" длиной 1-128.',
            complexityPassword:
                'Это поле должно состоять из английских букв, цифр длиной 8-30 и содержать как минимум два специальных символа.',
            commonPassword: 'Длина этого поля должна быть больше 6.',
            linuxName:
                'Длина этого поля должна быть от 1 до 128. Поле не должно содержать эти специальные символы: "{0}".',
            email: 'Это поле должно быть действительным адресом электронной почты.',
            number: 'Это поле должно быть числом.',
            integer: 'Это поле должно быть положительным целым числом.',
            ip: 'Это поле должно быть действительным IP-адресом.',
            host: 'Это поле должно быть действительным IP-адресом или доменным именем.',
            hostHelper: 'Поддерживается ввод IP-адреса или доменного имени',
            port: 'Это поле должно быть действительным номером порта.',
            selectHelper: 'Пожалуйста, выберите правильный файл {0}',
            domain: 'Это поле должно быть в формате: example.com или example.com:8080.',
            databaseName: 'Это поле должно состоять из английских букв, цифр и "_" длиной 1-30.',
            ipErr: 'Это поле должно быть действительным IP-адресом.',
            numberRange: 'Это поле должно быть числом между {0} и {1}.',
            paramName: 'Это поле должно состоять из английских букв, цифр, ".", "-" и "_" длиной 2-30.',
            paramComplexity:
                'Это поле не должно начинаться и заканчиваться специальными символами и должно состоять из английских букв, цифр, "{0}" длиной 6-128.',
            paramUrlAndPort: 'Это поле должно быть в формате "http(s)://(доменное имя/ip):(порт)".',
            nginxDoc: 'Это поле должно состоять из английских букв, цифр и ".".',
            appName:
                'Это поле не должно начинаться и заканчиваться символами "-" и "_" и должно состоять из английских букв, цифр, "-" и "_" длиной 2-30.',
            containerName: 'Поддерживаются буквы, цифры, -, _ и .; не может начинаться с - _ или .; длина: 2-128',
            mirror: 'Адрес ускорения зеркала должен начинаться с http(s)://, поддерживает английские буквы (как заглавные, так и строчные), цифры, . / и -, и не должен содержать пустых строк.',
            disableFunction: 'Поддерживаются только буквы, подчеркивания и запятые',
            leechExts: 'Поддерживаются только буквы, цифры и запятые',
            paramSimple: 'Поддерживаются строчные буквы и цифры, длина 1-128',
            filePermission: 'Ошибка прав доступа к файлу',
            formatErr: 'Ошибка формата, пожалуйста, проверьте и повторите попытку',
            phpExtension: 'Поддерживаются только запятые, подчеркивания, строчные английские буквы и цифры',
            paramHttp: 'Должно начинаться с http:// или https://',
            phone: 'Неверный формат номера телефона',
            authBasicPassword: 'Поддерживает буквы, цифры и общие специальные символы, длина 1-72',
            length128Err: 'Длина не может превышать 128 символов',
            maxLength: 'Длина не может превышать {0} символов',
            alias: 'Поддерживает английский, цифры, - и _, длина 1-30, и не может начинаться или заканчиваться на -_.',
        },
        res: {
            paramError: 'Запрос не удался, попробуйте позже!',
            forbidden: 'У текущего пользователя нет прав',
            serverError: 'Ошибка сервиса',
            notFound: 'Ресурс не существует',
            commonError: 'Запрос не удался',
        },
        service: {
            serviceNotStarted: 'Сервис {0} не запущен.',
        },
        status: {
            running: 'Работает',
            done: 'Завершено',
            scanFailed: 'Неполный',
            success: 'Успешно',
            waiting: 'Ожидание',
            waiting1: 'Ожидание',
            failed: 'Ошибка',
            stopped: 'Остановлен',
            error: 'Ошибка',
            created: 'Создан',
            restarting: 'Перезапуск',
            uploading: 'Загрузка',
            unhealthy: 'Нездоровый',
            removing: 'Удаление',
            paused: 'Приостановлен',
            exited: 'Вышел',
            dead: 'Мертв',
            installing: 'Установка',
            enabled: 'Включен',
            disabled: 'Отключен',
            normal: 'Нормально',
            building: 'Сборка',
            upgrading: 'Обновление',
            pending: 'Ожидает редактирования',
            rebuilding: 'Пересборка',
            deny: 'Отказано',
            accept: 'Принято',
            used: 'Используется',
            unUsed: 'Не используется',
            starting: 'Запуск',
            recreating: 'Пересоздание',
            creating: 'Создание',
            init: 'Ожидание приложения',
            ready: 'нормально',
            applying: 'Применение',
            uninstalling: 'Удаление',
            lost: 'Потеряно',
            bound: 'Привязано',
            unbind: 'Не привязано',
            exceptional: 'Исключение',
            free: 'Свободно',
            enable: 'Включено',
            disable: 'Отключено',
            deleted: 'Удалено',
            downloading: 'Загрузка',
            packing: 'Упаковка',
            sending: 'Отправка',
            healthy: 'Нормально',
            executing: 'Выполнение',
            installerr: 'Ошибка установки',
            applyerror: 'Ошибка применения',
            systemrestart: 'Прервано',
            starterr: 'Ошибка запуска',
            uperr: 'Ошибка запуска',
        },
        units: {
            second: ' секунда | секунда | секунд',
            minute: 'минута | минута | минут',
            hour: 'час | час | часов',
            day: 'день | день | дней',
            week: 'неделя | неделя | недель',
            month: 'месяц | месяц | месяцев',
            year: 'год | год | лет',
            time: 'раз',
            core: 'ядро | ядро | ядер',
            secondUnit: 'с',
            minuteUnit: 'мин',
            hourUnit: 'ч',
            dayUnit: 'д',
            millisecond: 'Миллисекунда',
        },
    },
    menu: {
        home: 'Обзор',
        apps: 'Приложения',
        website: 'Сайт | Сайты',
        project: 'Проект | Проекты',
        config: 'Конфигурация | Конфигурации',
        ssh: 'Настройки SSH',
        firewall: 'Firewall',
        ssl: 'Сертификат | Сертификаты',
        database: 'База данных | Базы данных',
        aiTools: 'AI',
        mcp: 'MCP',
        container: 'Контейнер | Контейнеры',
        cronjob: 'Cron | Задачи Cron',
        system: 'Система',
        security: 'Безопасность',
        files: 'Файлы',
        monitor: 'Мониторинг',
        terminal: 'Терминалы',
        settings: 'Настройка | Настройки',
        toolbox: 'Инструменты',
        logs: 'Лог | Логи',
        runtime: 'Среда исполнения | Среды исполнения',
        processManage: 'Процесс | Процессы',
        process: 'Процесс | Процессы',
        network: 'Сеть | Сети',
        supervisor: 'Супервизор',
        tamper: 'Защита от несанкционированного доступа',
        app: 'Приложение',
        msgCenter: 'Центр задач',
    },
    home: {
        restart_1panel: 'Перезапустить панель',
        restart_system: 'Перезапустить сервер',
        operationSuccess: 'Операция выполнена успешно, перезагрузка, пожалуйста, обновите браузер вручную позже!',
        entranceHelper:
            'Безопасный вход не включен. Вы можете включить его в "Настройки -> Безопасность" для повышения безопасности системы.',
        appInstalled: 'Приложения',
        systemInfo: 'Системная информация',
        hostname: 'Имя хоста',
        platformVersion: 'Операционная система',
        kernelVersion: 'Ядро',
        kernelArch: 'Архитектура',
        network: 'Сеть',
        io: 'Диск I/O',
        ip: 'Локальный IP',
        proxy: 'Системный прокси',
        baseInfo: 'Базовая информация',
        totalSend: 'Всего отправлено',
        totalRecv: 'Всего получено',
        rwPerSecond: 'Операции ввода/вывода',
        ioDelay: 'Задержка ввода/вывода',
        uptime: 'Работает с',
        runningTime: 'Время работы',
        mem: 'Память',
        swapMem: 'Раздел подкачки',

        runSmoothly: 'Низкая нагрузка',
        runNormal: 'Средняя нагрузка',
        runSlowly: 'Высокая нагрузка',
        runJam: 'Тяжелая нагрузка',

        core: 'Физических ядер',
        logicCore: 'Логических ядер',
        loadAverage: 'Средняя нагрузка за последнюю минуту | Средняя нагрузка за последние {n} минут',
        load: 'Нагрузка',
        mount: 'Точка монтирования',
        fileSystem: 'Файловая система',
        total: 'Всего',
        used: 'Использовано',
        cache: 'Кэш',
        free: 'Свободно',
        shard: 'Шардированный',
        available: 'Доступно',
        percent: 'Утилизация',
        goInstall: 'Установить',

        networkCard: 'Интерфейс',
        disk: 'Диск',
    },
    tabs: {
        more: 'Больше',
        hide: 'Скрыть',
        closeLeft: 'Закрыть слева',
        closeRight: 'Закрыть справа',
        closeCurrent: 'Закрыть текущую',
        closeOther: 'Закрыть другие',
        closeAll: 'Закрыть все',
    },
    header: {
        logout: 'Выход',
    },
    database: {
        manage: 'Управление',
        deleteBackupHelper: 'Удалить резервные копии базы данных одновременно',
        delete: 'Операция удаления не может быть отменена, пожалуйста, введите "',
        deleteHelper: '" для удаления этой базы данных',
        create: 'Создать базу данных',
        noMysql: 'Сервис базы данных (MySQL или MariaDB)',
        noPostgresql: 'Сервис базы данных PostgreSQL',
        goUpgrade: 'Обновить',
        goInstall: 'Установить',
        isDelete: 'Удалено',
        permission: 'Разрешения',
        permissionForIP: 'IP',
        permissionAll: 'Все (%)',
        databaseConnInfo: 'Информация о подключении',
        rootPassword: 'Пароль root',
        serviceName: 'Имя сервиса',
        serviceNameHelper: 'Доступ между контейнерами в одной сети.',
        backupList: 'Резервное копирование',
        loadBackup: 'Импорт',
        remoteAccess: 'Удаленный доступ',
        remoteHelper: 'Несколько IP через запятую, например: *************, *************',
        remoteConnHelper:
            'Удаленное подключение к MySQL как пользователь root может иметь риски безопасности. Поэтому выполняйте эту операцию с осторожностью.',
        changePassword: 'Пароль',
        changePasswordHelper:
            'База данных была связана с приложением. Изменение пароля изменит пароль базы данных приложения одновременно. Изменение вступит в силу после перезапуска приложения.',

        confChange: 'Конфигурация',
        confNotFound:
            'Файл конфигурации не найден. Пожалуйста, обновите приложение до последней версии в магазине приложений и попробуйте снова!',

        portHelper:
            'Этот порт является открытым портом контейнера. Вам нужно сохранить изменение отдельно и перезапустить контейнер!',

        loadFromRemote: 'Синхронизировать',
        userBind: 'Привязать пользователя',
        pgBindHelper:
            'Эта операция используется для создания нового пользователя и привязки его к целевой базе данных. В настоящее время выбор уже существующих пользователей в базе данных не поддерживается.',
        pgSuperUser: 'Суперпользователь',
        loadFromRemoteHelper: 'Это синхронизирует информацию о базе данных на сервере с 1Panel. Хотите продолжить?',
        passwordHelper: 'Невозможно получить, пожалуйста, измените',
        remote: 'Удаленный',
        remoteDB: 'Удаленный сервер | Удаленные серверы',
        createRemoteDB: 'Привязать @.lower:database.remoteDB',
        unBindRemoteDB: 'Отвязать @.lower:database.remoteDB',
        unBindForce: 'Принудительная отвязка',
        unBindForceHelper:
            'Игнорировать все ошибки во время процесса отвязки, чтобы обеспечить успешное завершение операции',
        unBindRemoteHelper:
            'Отвязка удаленной базы данных только удалит связь привязки и не будет напрямую удалять удаленную базу данных',
        editRemoteDB: 'Редактировать удаленный сервер',
        localDB: 'Локальная база данных',
        address: 'Адрес базы данных',
        version: 'Версия базы данных',
        userHelper:
            'Пользователь root или пользователь базы данных с привилегиями root может получить доступ к удаленной базе данных.',
        pgUserHelper: 'Пользователь базы данных с привилегиями суперпользователя.',
        ssl: 'Использовать SSL',
        clientKey: 'Приватный ключ клиента',
        clientCert: 'Сертификат клиента',
        caCert: 'Сертификат CA',
        hasCA: 'Есть сертификат CA',
        skipVerify: 'Игнорировать проверку действительности сертификата',

        formatHelper:
            'Текущая кодировка базы данных - {0}, несоответствие кодировок может привести к ошибке восстановления',
        selectFile: 'Выбрать файл',
        dropHelper: 'Вы можете перетащить загружаемый файл сюда или',
        clickHelper: 'нажмите для загрузки',
        supportUpType: 'Поддерживаются только файлы sql, sql.gz и tar.gz',
        zipFormat: 'Структура архива tar.gz: архив test.tar.gz должен содержать файл test.sql',

        currentStatus: 'Текущее состояние',
        baseParam: 'Базовые параметры',
        performanceParam: 'Параметры производительности',
        runTime: 'Время запуска',
        connections: 'Всего подключений',
        bytesSent: 'Отправлено байт',
        bytesReceived: 'Получено байт',
        queryPerSecond: 'Запросов в секунду',
        txPerSecond: 'Транзакций в секунду',
        connInfo: 'активные/пиковые подключения',
        connInfoHelper: 'Если значение слишком большое, увеличьте "max_connections".',
        threadCacheHit: 'Попадания в кэш потоков',
        threadCacheHitHelper: 'Если значение слишком низкое, увеличьте "thread_cache_size".',
        indexHit: 'Попадания в индекс',
        indexHitHelper: 'Если значение слишком низкое, увеличьте "key_buffer_size".',
        innodbIndexHit: 'Попадания в индекс InnoDB',
        innodbIndexHitHelper: 'Если значение слишком низкое, увеличьте "innodb_buffer_pool_size".',
        cacheHit: 'Попадания в кэш запросов',
        cacheHitHelper: 'Если значение слишком низкое, увеличьте "query_cache_size".',
        tmpTableToDB: 'Временные таблицы на диске',
        tmpTableToDBHelper: 'Если значение слишком большое, попробуйте увеличить "tmp_table_size".',
        openTables: 'Открытые таблицы',
        openTablesHelper: 'Значение конфигурации "table_open_cache" должно быть больше или равно этому значению.',
        selectFullJoin: 'Полные соединения',
        selectFullJoinHelper: 'Если значение не 0, проверьте правильность индексов таблиц данных.',
        selectRangeCheck: 'Количество соединений без индекса',
        selectRangeCheckHelper: 'Если значение не 0, проверьте правильность индексов таблиц данных.',
        sortMergePasses: 'Количество сортировок слиянием',
        sortMergePassesHelper: 'Если значение слишком большое, увеличьте "sort_buffer_size".',
        tableLocksWaited: 'Количество блокировок таблиц',
        tableLocksWaitedHelper:
            'Если значение слишком большое, рассмотрите возможность увеличения производительности базы данных.',

        performanceTuning: 'Настройка производительности',
        optimizationScheme: 'Схема оптимизации',
        keyBufferSizeHelper: 'Размер буфера для индексов',
        queryCacheSizeHelper: 'Кэш запросов. Если эта функция отключена, установите этот параметр в 0.',
        tmpTableSizeHelper: 'Размер кэша временных таблиц',
        innodbBufferPoolSizeHelper: 'Размер буфера InnoDB',
        innodbLogBufferSizeHelper: 'Размер буфера журнала InnoDB',
        sortBufferSizeHelper: '* подключений, размер буфера сортировки на поток',
        readBufferSizeHelper: '* подключений, размер буфера чтения',
        readRndBufferSizeHelper: '* подключений, размер буфера случайного чтения',
        joinBufferSizeHelper: '* подключений, размер кэша таблиц соединений',
        threadStackelper: '* подключений, размер стека на поток',
        binlogCacheSizeHelper: '* подключений, размер кэша бинарного журнала (кратно 4096)',
        threadCacheSizeHelper: 'Размер пула потоков',
        tableOpenCacheHelper: 'Кэш таблиц',
        maxConnectionsHelper: 'Максимум подключений',
        restart: 'Перезапустить',

        slowLog: 'Медленные запросы',
        noData: 'Пока нет медленных запросов.',

        isOn: 'Включено',
        longQueryTime: 'порог (сек)',
        thresholdRangeHelper: 'Пожалуйста, введите корректный порог (1 - 600).',

        timeout: 'Таймаут(сек)',
        timeoutHelper: 'Период таймаута неактивного подключения. 0 означает, что подключение постоянно активно.',
        maxclients: 'Макс. клиентов',
        requirepassHelper:
            'Оставьте пустым, если пароль не установлен. Изменения нужно сохранить отдельно и перезапустить контейнер!',
        databases: 'Количество баз данных',
        maxmemory: 'Максимальное использование памяти',
        maxmemoryHelper: '0 означает без ограничений.',
        tcpPort: 'Текущий порт прослушивания.',
        uptimeInDays: 'Дней в работе.',
        connectedClients: 'Количество подключенных клиентов.',
        usedMemory: 'Текущее использование памяти Redis.',
        usedMemoryRss: 'Размер памяти, запрошенный у операционной системы.',
        usedMemoryPeak: 'Пиковое потребление памяти Redis.',
        memFragmentationRatio: 'Коэффициент фрагментации памяти.',
        totalConnectionsReceived: 'Общее количество подключенных клиентов с момента запуска.',
        totalCommandsProcessed: 'Общее количество выполненных команд с момента запуска.',
        instantaneousOpsPerSec: 'Количество команд, выполняемых сервером в секунду.',
        keyspaceHits: 'Количество успешных поисков ключа в базе данных.',
        keyspaceMisses: 'Количество неудачных попыток найти ключ в базе данных.',
        hit: 'Коэффициент попаданий при поиске ключей.',
        latestForkUsec: 'Количество микросекунд, затраченных на последнюю операцию fork().',
        redisCliHelper: 'Сервис "redis-cli" не обнаружен. Сначала включите сервис.',
        redisQuickCmd: 'Быстрые команды Redis',
        recoverHelper: 'Это перезапишет данные с [{0}]. Хотите продолжить?',
        submitIt: 'Перезаписать данные',

        baseConf: 'Базовая',
        allConf: 'Все',
        restartNow: 'Перезапустить сейчас',
        restartNowHelper1:
            'Необходимо перезапустить систему после вступления изменений конфигурации в силу. Если ваши данные требуют сохранения, сначала выполните операцию сохранения.',
        restartNowHelper: 'Это вступит в силу только после перезапуска системы.',

        persistence: 'Сохранение',
        rdbHelper1: 'секунд(ы), вставить',
        rdbHelper2: 'элементов данных',
        rdbHelper3: 'Выполнение любого из условий запустит сохранение RDB.',
        rdbInfo: 'Убедитесь, что значение в списке правил находится в диапазоне от 1 до 100000',

        containerConn: 'Подключение контейнера',
        connAddress: 'Адрес',
        containerConnHelper:
            'Этот адрес подключения может использоваться приложениями, работающими в среде выполнения веб-сайта (PHP и т.д.) или контейнере.',
        remoteConn: 'Внешнее подключение',
        remoteConnHelper2: 'Используйте этот адрес для неконтейнерных сред или внешних подключений.',
        remoteConnHelper3:
            'Адрес доступа по умолчанию - это IP хоста. Для изменения перейдите к пункту конфигурации "Адрес доступа по умолчанию" на странице настроек панели.',
        localIP: 'Локальный IP',
    },
    aiTools: {
        model: {
            model: 'Модель',
            create: 'Добавить модель',
            create_helper: 'Загрузить "{0}"',
            ollama_doc: 'Вы можете посетить официальный сайт Ollama, чтобы искать и находить больше моделей.',
            container_conn_helper: 'Используйте этот адрес для доступа или подключения между контейнерами',
            ollama_sync:
                'Синхронизация модели Ollama обнаружила, что следующие модели не существуют, хотите удалить их?',
            from_remote: 'Эта модель не была загружена через 1Panel, нет связанных журналов извлечения.',
            no_logs: 'Журналы извлечения для этой модели были удалены и не могут быть просмотрены.',
        },
        proxy: {
            proxy: 'Усиление AI-прокси',
            proxyHelper1: 'Привяжите домен и включите HTTPS для повышения безопасности передачи данных',
            proxyHelper2: 'Ограничьте доступ по IP, чтобы предотвратить утечку данных в публичной сети',
            proxyHelper3: 'Включите потоковую передачу',
            proxyHelper4: 'После создания вы можете просматривать и управлять этим в списке сайтов',
            proxyHelper5:
                'После включения вы можете отключить внешний доступ к порту в Магазине приложений - Установленные - Ollama - Параметры для повышения безопасности.',
            proxyHelper6: 'Чтобы отключить настройку прокси, вы можете удалить её из списка сайтов.',
            whiteListHelper: 'Ограничить доступ только для IP-адресов из белого списка',
        },
        gpu: {
            gpu: 'Мониторинг GPU',
            base: 'Основная информация',
            gpuHelper: 'Команда NVIDIA-SMI или XPU-SMI не обнаружена в текущей системе. Проверьте и попробуйте снова!',
            driverVersion: 'Версия драйвера',
            cudaVersion: 'Версия CUDA',
            process: 'Информация о процессе',
            type: 'Тип',
            typeG: 'Графика',
            typeC: 'Вычисления',
            typeCG: 'Вычисления + Графика',
            processName: 'Имя процесса',
            processMemoryUsage: 'Использование памяти',
            temperatureHelper: 'Высокая температура GPU может вызвать снижение частоты GPU',
            performanceStateHelper: 'От P0 (максимальная производительность) до P12 (минимальная производительность)',
            busID: 'ID шины',
            persistenceMode: 'Режим постоянства',
            enabled: 'Включен',
            disabled: 'Выключен',
            persistenceModeHelper:
                'Режим постоянства позволяет быстрее реагировать на задачи, но увеличивает потребление энергии в режиме ожидания.',
            displayActive: 'Инициализация видеокарты',
            displayActiveT: 'Да',
            displayActiveF: 'Нет',
            ecc: 'Технология проверки и коррекции ошибок (ECC)',
            computeMode: 'Режим вычислений',
            default: 'По умолчанию',
            exclusiveProcess: 'Исключительный процесс',
            exclusiveThread: 'Исключительный поток',
            prohibited: 'Запрещено',
            defaultHelper: 'По умолчанию: процессы могут выполняться одновременно',
            exclusiveProcessHelper:
                'Исключительный процесс: только один контекст CUDA может использовать GPU, но его могут разделять несколько потоков',
            exclusiveThreadHelper: 'Исключительный поток: только один поток в контексте CUDA может использовать GPU',
            prohibitedHelper: 'Запрещено: процессам не разрешено выполняться одновременно',
            migModeHelper:
                'Используется для создания MIG-инстансов для физической изоляции GPU на уровне пользователя.',
            migModeNA: 'Не поддерживается',
        },
        mcp: {
            server: 'Сервер MCP',
            create: 'Добавить сервер',
            edit: 'Редактировать сервер',
            commandHelper: 'Например: npx -y {0}',
            baseUrl: 'Внешний путь доступа',
            baseUrlHelper: 'Например: http://192.168.1.2:8000',
            ssePath: 'Путь SSE',
            ssePathHelper: 'Например: /sse, будьте осторожны, чтобы не дублировать с другими серверами',
            environment: 'Переменные среды',
            envKey: 'Имя переменной',
            envValue: 'Значение переменной',
            externalUrl: 'Внешний адрес подключения',
            operatorHelper: 'Будет выполнена операция {1} на {0}, продолжить?',
            domain: 'Адрес доступа по умолчанию',
            domainHelper: 'Например: *********** или example.com',
            bindDomain: 'Привязать сайт',
            commandPlaceHolder: 'В настоящее время поддерживаются только команды запуска npx и двоичных файлов',
            importMcpJson: 'Импортировать конфигурацию сервера MCP',
            importMcpJsonError: 'Структура mcpServers некорректна',
            bindDomainHelper:
                'После привязки веб-сайта он изменит адрес доступа для всех установленных серверов MCP и закроет внешний доступ к портам',
            outputTransport: 'Тип вывода',
            streamableHttpPath: 'Путь потоковой передачи',
            streamableHttpPathHelper: 'Например: /mcp, обратите внимание, чтобы не перекрывать другие серверы',
        },
    },
    container: {
        create: 'Создать контейнер',
        edit: 'Редактировать контейнер',
        updateHelper1:
            'Обнаружено, что этот контейнер происходит из магазина приложений. Обратите внимание на следующие два пункта:',
        updateHelper2:
            '1. Текущие изменения не будут синхронизированы с установленными приложениями в магазине приложений.',
        updateHelper3:
            '2. Если вы измените приложение на странице установки, текущее отредактированное содержимое станет недействительным.',
        updateHelper4:
            'Редактирование контейнера требует пересборки, и все непостоянные данные будут потеряны. Хотите продолжить?',
        containerList: 'Список контейнеров',
        operatorHelper: 'Действие {0} будет выполнено для следующего контейнера. Хотите продолжить?',
        operatorAppHelper:
            'Операция "{0}" будет выполнена для следующего(-их) контейнера(-ов) и может повлиять на работающие сервисы. Хотите продолжить?',
        start: 'Запустить',
        stop: 'Остановить',
        restart: 'Перезапустить',
        kill: 'Завершить',
        pause: 'Приостановить',
        unpause: 'Возобновить',
        rename: 'Переименовать',
        remove: 'Удалить',
        removeAll: 'Удалить все',
        containerPrune: 'Очистить',
        containerPruneHelper1: 'Это удалит все контейнеры, которые находятся в остановленном состоянии.',
        containerPruneHelper2:
            'Если контейнеры из магазина приложений, вам нужно перейти в "Магазин приложений -> Установленные" и нажать кнопку "Пересобрать" для их переустановки после выполнения очистки.',
        containerPruneHelper3: 'Это действие нельзя отменить. Хотите продолжить?',
        imagePrune: 'Очистить',
        imagePruneSome: 'Очистить непомеченные',
        imagePruneSomeEmpty: 'Нет образов с тегом "none" для очистки.',
        imagePruneSomeHelper: 'Очистить образы с тегом "none", которые не используются никакими контейнерами.',
        imagePruneAll: 'Очистить неиспользуемые',
        imagePruneAllEmpty: 'Нет неиспользуемых образов для очистки.',
        imagePruneAllHelper: 'Очистить образы, которые не используются никакими контейнерами.',
        networkPrune: 'Очистить',
        networkPruneHelper: 'Это удалит все неиспользуемые сети. Хотите продолжить?',
        volumePrune: 'Очистить',
        volumePruneHelper: 'Это удалит все неиспользуемые локальные тома. Хотите продолжить?',
        cleanSuccess: 'Операция успешна, количество очищенных элементов: {0}!',
        cleanSuccessWithSpace:
            'Операция успешна. Количество очищенных дисков: {0}. Освобождено дискового пространства: {1}!',
        unExposedPort: 'Текущий адрес сопоставления портов - 127.0.0.1, что не позволяет внешний доступ.',
        upTime: 'Время работы',
        fetch: 'Получить',
        lines: 'Строки',
        linesHelper: 'Пожалуйста, введите правильное количество логов для получения!',
        lastDay: 'Последний день',
        last4Hour: 'Последние 4 часа',
        lastHour: 'Последний час',
        last10Min: 'Последние 10 минут',
        cleanLog: 'Очистить лог',
        downLogHelper1: 'Это загрузит все логи из контейнера {0}. Хотите продолжить?',
        downLogHelper2: 'Это загрузит последние {0} логов из контейнера {1}. Хотите продолжить?',
        cleanLogHelper: 'Это потребует перезапуска контейнера и не может быть отменено. Хотите продолжить?',
        newName: 'Новое имя',
        source: 'Использование ресурсов',
        cpuUsage: 'Использование CPU',
        cpuTotal: 'Всего CPU',
        core: 'Ядро',
        memUsage: 'Использование памяти',
        memTotal: 'Лимит памяти',
        memCache: 'Кэш памяти',
        ip: 'IP-адрес',
        cpuShare: 'Доли CPU',
        cpuShareHelper:
            'Движок контейнера использует базовое значение 1024 для долей CPU. Вы можете увеличить его, чтобы дать контейнеру больше времени CPU.',
        inputIpv4: 'Пример: ***********',
        inputIpv6: 'Пример: 2001:0db8:85a3:0000:0000:8a2e:0370:7334',

        containerFromAppHelper:
            'Обнаружено, что этот контейнер происходит из магазина приложений. Операции с приложением могут привести к недействительности текущих изменений.',
        containerFromAppHelper1:
            'Нажмите кнопку [Параметры] в списке установленных приложений, чтобы перейти на страницу редактирования и изменить имя контейнера.',
        command: 'Команда',
        console: 'Взаимодействие с контейнером',
        tty: 'Выделить псевдо-TTY (-t)',
        openStdin: 'Держать STDIN открытым, даже если не подключен (-i)',
        custom: 'Пользовательский',
        emptyUser: 'Если пусто, вы войдете как пользователь по умолчанию',
        privileged: 'Привилегированный',
        privilegedHelper:
            'Разрешить контейнеру выполнять определенные привилегированные операции на хосте, что может повысить риски контейнера. Используйте с осторожностью!',
        editComposeHelper:
            'Примечание: Установленные переменные окружения будут по умолчанию записаны в файл 1panel.env.\nЕсли вы хотите использовать эти параметры в контейнере, вам также нужно вручную добавить ссылку env_file в файле compose.',

        upgradeHelper: 'Имя репозитория/Имя образа: Версия образа',
        upgradeWarning2:
            'Операция обновления требует пересборки контейнера, все несохраненные данные будут потеряны. Хотите продолжить?',
        oldImage: 'Текущий образ',
        sameImageContainer: 'Контейнеры с одинаковым образом',
        sameImageHelper: 'Контейнеры, использующие один образ, можно массово обновить после выбора',
        targetImage: 'Целевой образ',
        imageLoadErr: 'Не обнаружено имя образа для контейнера',
        appHelper: 'Контейнер происходит из магазина приложений, и обновление может сделать сервис недоступным.',

        input: 'Ручной ввод',
        forcePull: 'Всегда загружать образ',
        forcePullHelper:
            'Это будет игнорировать существующие образы на сервере и загружать последний образ из реестра.',
        server: 'Хост',
        serverExample: '80, 80-88, ip:80 или ip:80-88',
        containerExample: '80 или 80-88',
        exposePort: 'Открыть порт',
        exposeAll: 'Открыть все',
        cmdHelper: 'Пример: nginx -g "daemon off;"',
        entrypointHelper: 'Пример: docker-entrypoint.sh',
        autoRemove: 'Автоудаление',
        cpuQuota: 'Количество ядер CPU',
        memoryLimit: 'Память',
        limitHelper: 'Если установлено 0, это означает отсутствие ограничений. Максимальное значение {0}',
        mount: 'Монтирование',
        volumeOption: 'Том',
        hostOption: 'Хост',
        serverPath: 'Путь на сервере',
        containerDir: 'Путь в контейнере',
        volumeHelper: 'Убедитесь, что содержимое тома хранения корректно',
        modeRW: 'Чтение-Запись',
        modeR: 'Только чтение',
        mode: 'Режим',
        env: 'Переменные окружения',
        restartPolicy: 'Политика перезапуска',
        always: 'всегда',
        unlessStopped: 'если не остановлен',
        onFailure: 'при сбое (по умолчанию пять раз)',
        no: 'никогда',

        refreshTime: 'Интервал обновления',
        cache: 'Кэш',

        image: 'Образ | Образы',
        imagePull: 'Загрузить',
        imagePush: 'Отправить',
        imageDelete: 'Удалить образ',
        imageTagDeleteHelper: 'Удалить другие теги, связанные с этим ID образа',
        repoName: 'Реестр контейнеров',
        imageName: 'Имя образа',
        pull: 'Загрузить',
        path: 'Путь',
        importImage: 'Импорт',
        build: 'Сборка',
        imageBuild: 'Сборка',
        pathSelect: 'Путь',
        label: 'Метка',
        imageTag: 'Тег образа',
        push: 'Отправить',
        fileName: 'Имя файла',
        export: 'Экспорт',
        exportImage: 'Экспорт образа',
        size: 'Размер',
        tag: 'Теги',
        tagHelper: 'По одному в строке. Например,\nkey1=value1\nkey2=value2',
        imageNameHelper: 'Имя образа и тег, например: nginx:latest',
        cleanBuildCache: 'Очистить кэш сборки',
        delBuildCacheHelper:
            'Это удалит все кэшированные артефакты, созданные во время сборки, и не может быть отменено. Хотите продолжить?',
        urlWarning: 'Префикс URL не должен включать http:// или https://. Пожалуйста, измените.',

        network: 'Сеть | Сети',
        networkHelper:
            'Это может привести к неправильной работе некоторых приложений и сред выполнения. Хотите продолжить?',
        createNetwork: 'Создать',
        networkName: 'Имя',
        driver: 'Драйвер',
        option: 'Опция',
        attachable: 'Подключаемая',
        subnet: 'Подсеть',
        scope: 'Диапазон IP',
        gateway: 'Шлюз',
        auxAddress: 'Исключить IP',

        volume: 'Том | Тома',
        volumeDir: 'Директория тома',
        nfsEnable: 'Включить NFS хранилище',
        nfsAddress: 'Адрес',
        mountpoint: 'Точка монтирования',
        mountpointNFSHelper: 'например, /nfs, /nfs-share',
        options: 'Опции',
        createVolume: 'Создать',

        repo: 'Реестры',
        createRepo: 'Добавить',
        httpRepoHelper: 'Работа с репозиторием HTTP-типа требует перезапуска службы Docker.',
        httpRepo: 'Выбор HTTP протокола требует перезапуска службы Docker для добавления в небезопасные реестры.',
        delInsecure: 'Удаление учетных данных',
        delInsecureHelper: 'Это перезапустит службу Docker для удаления из небезопасных реестров. Хотите продолжить?',
        downloadUrl: 'Сервер',
        imageRepo: 'Репозиторий образов',
        repoHelper: 'Включает ли зеркало репозитория/организации/проекта?',
        auth: 'Требуется аутентификация',
        mirrorHelper:
            'Если есть несколько зеркал, они должны быть разделены новой строкой, например:\nhttp://xxxxxx.m.daocloud.io \nhttps://xxxxxx.mirror.aliyuncs.com',
        registrieHelper:
            'Если существует несколько частных репозиториев, они должны быть разделены новой строкой, например:\n*************:8081 \n*************:8081',

        compose: 'Compose | Composes',
        fromChangeHelper: 'Переключение источника очистит текущее отредактированное содержимое. Хотите продолжить?',
        composePathHelper: 'Путь сохранения файла конфигурации: {0}',
        composeHelper:
            'Композиция, созданная через редактор 1Panel или шаблон, будет сохранена в директории {0}/docker/compose.',
        deleteFile: 'Удалить файл',
        deleteComposeHelper:
            'Удалить все файлы, связанные с compose контейнера, включая файлы конфигурации и постоянные файлы. Пожалуйста, действуйте с осторожностью!',
        deleteCompose: '" Удалить эту композицию.',
        createCompose: 'Создать',
        composeDirectory: 'Директория',
        template: 'Шаблон',
        composeTemplate: 'Шаблон Compose | Шаблоны Compose',
        createComposeTemplate: 'Создать',
        content: 'Содержимое',
        contentEmpty: 'Содержимое Compose не может быть пустым, пожалуйста, введите и попробуйте снова!',
        containerNumber: 'Количество контейнеров',
        containerStatus: 'Статус контейнера',
        exited: 'Завершен',
        running: 'Работает ( {0} / {1} )',
        composeDetailHelper: 'Compose создан вне 1Panel. Операции запуска и остановки не поддерживаются.',
        composeOperatorHelper: 'Операция {1} будет выполнена для {0}. Хотите продолжить?',
        composeDownHelper: 'Это остановит и удалит все контейнеры и сети под compose {0}. Хотите продолжить?',

        setting: 'Настройка | Настройки',
        operatorStatusHelper: 'Это выполнит "{0}" службы Docker. Хотите продолжить?',
        dockerStatus: 'Служба Docker',
        daemonJsonPathHelper: 'Убедитесь, что путь конфигурации совпадает с указанным в docker.service.',
        mirrors: 'Зеркала реестра',
        mirrorsHelper: '',
        mirrorsHelper2: 'Подробности см. в официальной документации. ',
        registries: 'Небезопасные реестры',
        ipv6Helper:
            'При включении IPv6 необходимо добавить сеть контейнера IPv6. Конкретные шаги настройки см. в официальной документации.',
        ipv6CidrHelper: 'Диапазон пула IPv6-адресов для контейнеров',
        ipv6TablesHelper: 'Автоматическая настройка Docker IPv6 для правил Iptables.',
        experimentalHelper:
            'Включение ip6tables требует включения этой конфигурации; в противном случае ip6tables будет игнорироваться',
        cutLog: 'Опция логирования',
        cutLogHelper1: 'Текущая конфигурация будет влиять только на вновь созданные контейнеры.',
        cutLogHelper2: 'Существующие контейнеры необходимо пересоздать для применения конфигурации.',
        cutLogHelper3:
            'Обратите внимание, что пересоздание контейнеров может привести к потере данных. Если ваши контейнеры содержат важные данные, обязательно сделайте резервную копию перед выполнением операции пересборки.',
        maxSize: 'Максимальный размер',
        maxFile: 'Максимум файлов',
        liveHelper:
            'По умолчанию, когда демон Docker завершает работу, он останавливает работающие контейнеры. Вы можете настроить демон так, чтобы контейнеры продолжали работать, если демон становится недоступным. Эта функциональность называется live restore. Опция live restore помогает уменьшить время простоя контейнера из-за сбоев демона, плановых простоев или обновлений.',
        liveWithSwarmHelper: 'Конфигурация демона live-restore несовместима с режимом swarm.',
        iptablesDisable: 'Отключить Iptables',
        iptablesHelper1: 'Автоматическая настройка правил Iptables для Docker.',
        iptablesHelper2:
            'Отключение Iptables приведет к тому, что контейнеры не смогут взаимодействовать с внешними сетями.',
        daemonJsonPath: 'Путь конфигурации',
        serviceUnavailable: 'Служба Docker в настоящее время не запущена.',
        startIn: ' для запуска',
        sockPath: 'Unix domain socket',
        sockPathHelper: 'Канал связи между демоном Docker и клиентом.',
        sockPathHelper1: 'Путь по умолчанию: /var/run/docker-x.sock',
        sockPathMsg:
            'Сохранение настройки Socket Path может привести к недоступности службы Docker. Хотите продолжить?',
        sockPathErr: 'Пожалуйста, выберите или введите правильный путь к файлу Docker sock',
        related: 'Связанные',
        includeAppstore: 'Показывать контейнеры из магазина приложений',
        excludeAppstore: 'Скрыть контейнер магазина приложений',

        cleanDockerDiskZone: 'Очистить дисковое пространство, используемое Docker',
        cleanImagesHelper: '(Очистить все образы, не используемые контейнерами)',
        cleanContainersHelper: '(Очистить все остановленные контейнеры)',
        cleanVolumesHelper: '(Очистить все неиспользуемые локальные тома)',

        makeImage: 'Создать образ',
        newImageName: 'Новое имя образа',
        commitMessage: 'Сообщение коммита',
        author: 'Автор',
        ifPause: 'Приостановить контейнер во время создания',
        ifMakeImageWithContainer: 'Создать новый образ из этого контейнера?',
    },
    cronjob: {
        create: 'Создать задачу cron',
        edit: 'Редактировать задачу cron',
        errImport: 'Аномальное содержимое файла:',
        errImportFormat:
            'Данные или формат запланированной задачи ненормальны. Пожалуйста, проверьте и повторите попытку!',
        importHelper:
            'Повторяющиеся запланированные задачи будут автоматически пропущены при импорте. По умолчанию задачи устанавливаются в статус 【Отключено】, а при аномальной ассоциации данных - в статус 【Ожидает редактирования】.',
        changeStatus: 'Изменить статус',
        disableMsg: 'Это остановит автоматическое выполнение запланированной задачи. Хотите продолжить?',
        enableMsg: 'Это позволит запланированной задаче автоматически выполняться. Хотите продолжить?',
        taskType: 'Тип',
        record: 'Записи',
        viewRecords: 'Записи',
        shell: 'Shell',
        log: 'Логи резервного копирования',
        logHelper: 'Резервное копирование системного лога',
        ogHelper1: '1. Системный лог 1Panel',
        logHelper2: '2. Лог SSH-входов на сервер',
        logHelper3: '3. Все логи сайта',
        containerCheckBox: 'В контейнере (не нужно вводить команду контейнера)',
        containerName: 'Имя контейнера',
        ntp: 'Синхронизация времени',
        ntp_helper: 'Вы можете настроить NTP сервер на странице Быстрой настройки в Инструментах.',
        app: 'Резервное копирование приложения',
        website: 'Резервное копирование сайта',
        rulesHelper: 'Поддерживает несколько правил исключения, разделяются английскими запятыми , напр.: *.log,*.sql',
        lastRecordTime: 'Время последнего выполнения',
        all: 'Все',
        failedRecord: 'Неудачные записи',
        successRecord: 'Успешные записи',
        database: 'Резервное копирование базы данных',
        missBackupAccount: 'Не удалось найти учетную запись резервного копирования',
        syncDate: 'Время синхронизации',
        clean: 'Очистка кэша',
        curl: 'Доступ к URL',
        taskName: 'Имя',
        cronSpec: 'Цикл запуска',
        cronSpecDoc:
            'Пользовательское расписание поддерживает только формат [минута час день месяц неделя], например, 0 0 * * *. Подробности см. в официальной документации.',
        cronSpecHelper: 'Введите правильный период выполнения',
        cleanHelper:
            'Эта операция записывает все записи выполнения задач, файлы резервных копий и файлы логов. Хотите продолжить?',
        directory: 'Директория резервного копирования',
        sourceDir: 'Директория резервного копирования',
        snapshot: 'Снапшот системы',
        allOptionHelper:
            'Текущий план задачи - резервное копирование всех [{0}]. Прямое скачивание сейчас не поддерживается. Вы можете проверить список резервных копий в меню [{0}].',
        exclusionRules: 'Правило исключения',
        exclusionRulesHelper: 'Правила исключения будут применяться ко всем операциям сжатия этой резервной копии.',
        default_download_path: 'Ссылка для скачивания по умолчанию',
        saveLocal: 'Сохранять локальные резервные копии (столько же, сколько копий в облачном хранилище)',
        url: 'URL-адрес',
        targetHelper: 'Учетные записи резервного копирования управляются в настройках панели.',
        withImageHelper: 'Резервное копирование образов из магазина приложений увеличит размер файла снимка.',
        ignoreApp: 'Исключить приложения',
        withImage: 'Резервное копирование всех образов приложений',
        retainCopies: 'Сохранять записи',
        retryTimes: 'Количество повторов',
        timeout: 'Таймаут',
        ignoreErr: 'Игнорировать ошибки',
        ignoreErrHelper:
            'Игнорировать ошибки во время резервного копирования для выполнения всех задач резервного копирования',
        retryTimesHelper: '0 означает отсутствие повторов после сбоя',
        retainCopiesHelper: 'Количество копий для сохранения записей выполнения и логов',
        retainCopiesHelper1: 'Количество копий для сохранения файлов резервных копий',
        retainCopiesUnit: ' копий (Просмотр)',
        cronSpecRule: 'Формат периода выполнения в строке {0} неверен. Пожалуйста, проверьте и попробуйте снова!',
        perMonthHelper: 'Выполнять {0} числа каждого месяца в {1}:{2}',
        perWeekHelper: 'Выполнять каждую неделю в {0} в {1}:{2}',
        perDayHelper: 'Выполнять каждый день в {0}:{1}',
        perHourHelper: 'Выполнять каждый час в {0} минут',
        perNDayHelper: 'Выполнять каждые {0} дней в {1}:{2}',
        perNHourHelper: 'Выполнять каждые {0} часов в {1}',
        perNMinuteHelper: 'Выполнять каждые {0} минут',
        perNSecondHelper: 'Выполнять каждые {0} секунд',
        perMonth: 'Каждый месяц',
        perWeek: 'Каждую неделю',
        perHour: 'Каждый час',
        perNDay: 'Каждые N дней',
        perDay: 'Каждый день',
        perNHour: 'Каждые N часов',
        perNMinute: 'Каждые N минут',
        perNSecond: 'Каждые N секунд',
        day: 'дней',
        dayUnit: 'д',
        monday: 'Понедельник',
        tuesday: 'Вторник',
        wednesday: 'Среда',
        thursday: 'Четверг',
        friday: 'Пятница',
        saturday: 'Суббота',
        sunday: 'Воскресенье',
        shellContent: 'Скрипт',
        errRecord: 'Неправильное логирование',
        errHandle: 'Сбой выполнения задачи Cron',
        noRecord: 'Запустите задачу Cron, и вы увидите записи здесь.',
        cleanData: 'Очистить данные',
        cleanRemoteData: 'Удалить удалённые данные',
        cleanDataHelper: 'Удалить файл резервной копии, созданный во время этой задачи.',
        noLogs: 'Пока нет вывода задачи...',
        errPath: 'Ошибка пути резервной копии [{0}], невозможно скачать!',
        cutWebsiteLog: 'Ротация логов сайта',
        cutWebsiteLogHelper: 'Ротированные файлы логов будут сохранены в директории резервных копий 1Panel.',

        requestExpirationTime: 'Время истечения запроса на загрузку (часы)',
        unitHours: 'Единица: часы',
        alertTitle: 'Плановая задача - {0} «{1}» Оповещение о сбое задачи',
        library: {
            script: 'Скрипт',
            isInteractive: 'Интерактивный',
            interactive: 'Интерактивный скрипт',
            interactiveHelper:
                'Требует ввода пользователя во время выполнения и не может использоваться в запланированных задачах.',
            library: 'Библиотека скриптов',
            create: 'Добавить скрипт',
            edit: 'Редактировать скрипт',
            groupHelper:
                'Установите разные группы на основе характеристик скрипта, что позволяет быстрее выполнять операции фильтрации скриптов.',
            handleHelper: 'Выполнить сценарий {1} на {0}, продолжить?',
            noSuchApp:
                'Служба {0} не обнаружена. Пожалуйста, сначала быстро установите её, используя библиотеку скриптов!',
            syncHelper:
                'Выполнить синхронизацию библиотеки системных скриптов? Это действие затрагивает только системные скрипты. Продолжить?',
        },
    },
    monitor: {
        globalFilter: 'Глобальный фильтр',
        enableMonitor: 'Включить',
        storeDays: 'Дни хранения',
        cleanMonitor: 'Очистить записи мониторинга',

        avgLoad: 'Средняя нагрузка',
        loadDetail: 'Детали нагрузки',
        resourceUsage: 'Использование',
        networkCard: 'Сетевой интерфейс',
        read: 'Чтение',
        write: 'Запись',
        readWriteCount: 'Операции ввода/вывода',
        readWriteTime: 'Задержка ввода/вывода',
        today: 'Сегодня',
        yesterday: 'Вчера',
        lastNDay: 'Последние {0} дней',
        memory: 'Память',
        cache: 'Кэш',
        disk: 'Диск',
        network: 'Сеть',
        up: 'Исходящий',
        down: 'Входящий',
        interval: 'Интервал(минут)',

        gpuUtil: 'Использование GPU',
        temperature: 'Температура',
        performanceState: 'Состояние производительности',
        powerUsage: 'Потребление энергии',
        memoryUsage: 'Использование памяти',
        fanSpeed: 'Скорость вентилятора',
    },
    terminal: {
        local: 'Локальный',
        localHelper: 'Локальное имя используется только для локальной идентификации системы.',
        connLocalErr: '無法自動認證，請填寫本地服務器的登錄信息！',
        testConn: 'Проверить подключение',
        saveAndConn: 'Сохранить и подключиться',
        connTestOk: 'Информация о подключении доступна',
        connTestFailed: 'Подключение недоступно, пожалуйста, проверьте информацию подключения.',
        host: 'Хост | Хосты',
        createConn: 'Новое подключение',
        manageGroup: 'Управление группами',
        noHost: 'Нет хостов',
        groupChange: 'Изменить группу',
        expand: 'Развернуть все',
        fold: 'Свернуть все',
        batchInput: 'Пакетная обработка',
        quickCommand: 'Быстрая команда | Быстрые команды',
        quickCommandHelper: 'Вы можете использовать быстрые команды внизу страницы "Терминалы -> Терминалы".',
        groupDeleteHelper:
            'После удаления группы все подключения в группе будут перемещены в группу по умолчанию. Хотите продолжить?',
        command: 'Команда',
        quickCmd: 'Быстрая команда',
        addHost: 'Добавить',
        localhost: 'Локальный хост',
        ip: 'Адрес',
        authMode: 'Аутентификация',
        passwordMode: 'Пароль',
        rememberPassword: 'Запомнить информацию аутентификации',
        keyMode: 'Приватный ключ',
        key: 'Приватный ключ',
        keyPassword: 'Пароль приватного ключа',
        emptyTerminal: 'В настоящее время нет подключенных терминалов.',
    },
    toolbox: {
        common: {
            toolboxHelper: 'Некоторые проблемы с установкой и использованием описаны в',
        },
        swap: {
            swap: 'Раздел Swap',
            swapHelper1:
                'Размер swap должен быть в 1-2 раза больше физической памяти, регулируется в зависимости от конкретных требований;',
            swapHelper2:
                'Перед созданием файла подкачки убедитесь, что на системном диске достаточно свободного места, так как размер файла подкачки будет занимать соответствующее дисковое пространство;',
            swapHelper3:
                'Swap может помочь снизить нагрузку на память, но это только альтернатива. Чрезмерная зависимость от swap может привести к снижению производительности системы. Рекомендуется в первую очередь увеличить память или оптимизировать использование памяти приложениями;',
            swapHelper4:
                'Рекомендуется регулярно отслеживать использование swap для обеспечения нормальной работы системы.',
            swapDeleteHelper:
                'Эта операция удалит раздел Swap {0}. В целях безопасности системы соответствующий файл не будет удален автоматически. Если требуется удаление, выполните его вручную!',
            saveHelper: 'Пожалуйста, сначала сохраните текущие настройки!',
            saveSwap: 'Сохранение текущей конфигурации изменит размер раздела Swap {0} на {1}. Хотите продолжить?',
            swapMin: 'Минимальный размер раздела - 40 КБ. Пожалуйста, измените и попробуйте снова!',
            swapMax: 'Максимальное значение для размера раздела - {0}. Пожалуйста, измените и попробуйте снова!',
            swapOff: 'Минимальный размер раздела - 40 КБ. Установка значения 0 отключит раздел Swap.',
        },
        device: {
            dnsHelper: 'DNS сервер',
            dnsAlert:
                'Внимание! Изменение конфигурации файла /etc/resolv.conf будет восстановлено до значений по умолчанию после перезагрузки системы.',
            dnsHelper1:
                'При наличии нескольких DNS-записей они должны отображаться с новой строки. Например:\n114.114.114.114\n8.8.8.8',
            hostsHelper: 'Разрешение имен хостов',
            hosts: 'Домен',
            hostAlert: 'Скрыты закомментированные записи, нажмите кнопку "Все настройки" для просмотра или настройки',
            toolbox: 'Быстрые настройки',
            hostname: 'Имя хоста',
            passwd: 'Системный пароль',
            passwdHelper: 'Вводимые символы не могут включать $ и &',
            timeZone: 'Часовой пояс',
            localTime: 'Время сервера',
            timeZoneChangeHelper: 'Изменение системного часового пояса требует перезапуска службы. Продолжить?',
            timeZoneHelper:
                'Если команда "timedatectl" не установлена, вы не сможете изменить часовой пояс. Система использует эту команду для изменения часового пояса.',
            timeZoneCN: 'Пекин',
            timeZoneAM: 'Лос-Анджелес',
            timeZoneNY: 'Нью-Йорк',
            ntpALi: 'Alibaba',
            ntpGoogle: 'Google',
            syncSite: 'NTP сервер',
            hostnameHelper:
                'Изменение имени хоста зависит от команды "hostnamectl". Если команда не установлена, изменение может не удаться.',
            userHelper:
                'Имя пользователя зависит от команды "whoami" для получения. Если команда не установлена, получение может не удаться.',
            passwordHelper:
                'Изменение пароля зависит от команды "chpasswd". Если команда не установлена, изменение может не удаться.',
            hostHelper:
                'В предоставленном содержимом есть пустое значение. Пожалуйста, проверьте и попробуйте снова после изменения!',
            dnsCheck: 'Проверить доступность',
            dnsOK: 'Информация о конфигурации DNS доступна!',
            dnsTestFailed: 'Информация о конфигурации DNS недоступна.',
        },
        fail2ban: {
            sshPort: 'Прослушивание SSH порта',
            sshPortHelper: 'Текущий Fail2ban прослушивает порт SSH-подключения хоста',
            unActive: 'Служба Fail2ban в настоящее время не включена.',
            operation: 'Вы собираетесь выполнить операцию "{0}" над службой Fail2ban. Хотите продолжить?',
            fail2banChange: 'Изменение конфигурации Fail2ban',
            ignoreHelper: 'IP-адреса из белого списка будут игнорироваться при блокировке. Хотите продолжить?',
            bannedHelper: 'IP-адреса из черного списка будут заблокированы сервером. Хотите продолжить?',
            maxRetry: 'Максимальное количество попыток',
            banTime: 'Время блокировки',
            banTimeHelper: 'Время блокировки по умолчанию 10 минут, -1 означает постоянную блокировку',
            banTimeRule: 'Пожалуйста, введите допустимое время блокировки или -1',
            banAllTime: 'Постоянная блокировка',
            findTime: 'Период обнаружения',
            banAction: 'Действие блокировки',
            banActionOption: 'Блокировать указанные IP-адреса используя {0}',
            allPorts: ' (Все порты)',
            ignoreIP: 'Белый список IP',
            bannedIP: 'Черный список IP',
            logPath: 'Путь к логам',
            logPathHelper: 'По умолчанию /var/log/secure или /var/log/auth.log',
        },
        ftp: {
            ftp: 'FTP аккаунт | FTP аккаунты',
            notStart: 'Служба FTP в настоящее время не запущена, пожалуйста, сначала запустите её!',
            operation: 'Это выполнит операцию "{0}" над службой FTP. Хотите продолжить?',
            noPasswdMsg:
                'Невозможно получить текущий пароль FTP аккаунта, пожалуйста, установите пароль и попробуйте снова!',
            enableHelper: 'Включение выбранного FTP аккаунта восстановит его права доступа. Хотите продолжить?',
            disableHelper: 'Отключение выбранного FTP аккаунта отзовет его права доступа. Хотите продолжить?',
            syncHelper: 'Синхронизировать данные FTP аккаунта между сервером и базой данных. Хотите продолжить?',
            dirSystem:
                'Это системный каталог. Его изменение может вызвать сбой системы. Пожалуйста, измените и повторите попытку!',
            dirHelper: 'Для включения FTP требуется изменение прав доступа к каталогу - выбирайте осторожно',
            dirMsg: 'Включение FTP изменит права доступа для всего каталога {0}. Продолжить?',
        },
        clam: {
            clam: 'Антивирусное сканирование',
            cron: 'Запланированное сканирование',
            cronHelper: 'Профессиональная версия поддерживает функцию запланированного сканирования',
            specErr: 'Ошибка формата расписания выполнения, пожалуйста, проверьте и попробуйте снова!',
            disableMsg:
                'Остановка запланированного выполнения предотвратит автоматический запуск этой задачи сканирования. Хотите продолжить?',
            enableMsg:
                'Включение запланированного выполнения позволит этой задаче сканирования запускаться автоматически через регулярные интервалы. Хотите продолжить?',
            showFresh: 'Показать службу обновления сигнатур',
            hideFresh: 'Скрыть службу обновления сигнатур',
            clamHelper:
                'Минимальная рекомендуемая конфигурация для ClamAV: 3 ГБ ОЗУ или больше, одноядерный процессор с частотой 2.0 ГГц или выше, и не менее 5 ГБ свободного места на жестком диске.',
            notStart: 'Служба ClamAV в настоящее время не запущена, пожалуйста, сначала запустите её!',
            removeRecord: 'Удалить файлы отчетов',
            noRecords: 'Нажмите кнопку "Запустить" для начала сканирования, и вы увидите записи здесь.',
            removeResultHelper: 'Удалить файлы отчетов, созданные во время выполнения задачи, чтобы освободить место.',
            removeInfected: 'Удалить зараженные файлы',
            removeInfectedHelper:
                'Удалить зараженные файлы, обнаруженные во время задачи, для обеспечения безопасности и нормальной работы сервера.',
            clamCreate: 'Создать правило сканирования',
            infectedStrategy: 'Стратегия для зараженных файлов',
            removeHelper: 'Удалить зараженные файлы, выбирайте осторожно!',
            move: 'Переместить',
            moveHelper: 'Переместить зараженные файлы в указанную директорию',
            copyHelper: 'Копировать зараженные файлы в указанную директорию',
            none: 'Ничего не делать',
            noneHelper: 'Не предпринимать действий с зараженными файлами',
            scanDir: 'Директория сканирования',
            infectedDir: 'Директория зараженных файлов',
            scanDate: 'Дата сканирования',
            scanResult: 'Последние строки лога сканирования',
            tail: 'Строк',
            infectedFiles: 'Зараженные файлы',
            log: 'Подробности',
            clamConf: 'Демон Clam AV',
            clamLog: '@:toolbox.clam.clamConf логи',
            freshClam: 'FreshClam',
            freshClamLog: '@:toolbox.clam.freshClam логи',
            alertHelper: 'Профессиональная версия поддерживает запланированное сканирование и SMS-оповещения',
            alertTitle: 'Задача антивирусного сканирования 「{0}」 обнаружила зараженный файл',
        },
    },
    logs: {
        core: 'Сервис панели',
        agent: 'Мониторинг узлов',
        panelLog: 'Логи панели',
        operation: 'Логи операций',
        login: 'Логи входа',
        loginIP: 'IP входа',
        loginAddress: 'Адрес входа',
        loginAgent: 'Агент входа',
        loginStatus: 'Статус',
        system: 'Системные логи',
        deleteLogs: 'Очистить логи',
        resource: 'Ресурс',
        detail: {
            ai: 'AI',
            groups: 'Группы',
            hosts: 'Хосты',
            apps: 'Приложения',
            websites: 'Вебсайты',
            containers: 'Контейнеры',
            files: 'Управление файлами',
            runtimes: 'Среды выполнения',
            process: 'Управление процессами',
            toolbox: 'Инструментальный ящик',
            backups: 'Резервное копирование / Восстановление',
            tampers: 'Защита от подделки',
            xsetting: 'Настройки интерфейса',
            logs: 'Аудит журналов',
            settings: 'Настройки панели',
            cronjobs: 'Запланированные задачи',
            waf: 'WAF',
            databases: 'Базы данных',
            licenses: 'лицензии',
            nodes: 'ноды',
            commands: 'Быстрые команды',
        },
        websiteLog: 'Логи веб-сайта',
        runLog: 'Логи выполнения',
        errLog: 'Логи ошибок',
    },
    file: {
        fileDirNum: '{0} каталогов, {1} файлов,',
        currentDir: 'Текущий каталог',
        dir: 'Папка',
        upload: 'Загрузить',
        uploadFile: '@:file.upload @.lower:file.file',
        uploadDirectory: '@:file.upload @.lower:file.dir',
        download: 'Скачать',
        fileName: 'Имя файла',
        search: 'Поиск',
        mode: 'Права доступа',
        editPermissions: '@:file.mode',
        owner: 'Владелец',
        file: 'Файл',
        remoteFile: 'Удалённая загрузка',
        share: 'Поделиться',
        sync: 'Синхронизация данных',
        size: 'Размер',
        updateTime: 'Изменен',
        rename: 'Переименовать',
        role: 'Права доступа',
        info: 'Атрибуты',
        linkFile: 'Символическая ссылка',
        batchoperation: 'Пакетная операция',
        shareList: 'Список общих ресурсов',
        zip: 'Сжатый',
        group: 'Группа',
        path: 'Путь',
        public: 'Другие',
        setRole: 'Установить права',
        link: 'Ссылка на файл',
        rRole: 'Чтение',
        wRole: 'Запись',
        xRole: 'Исполнение',
        name: 'Имя',
        compress: 'Сжать',
        deCompress: 'Распаковать',
        compressType: 'Формат сжатия',
        compressDst: 'Путь сжатия',
        replace: 'Перезаписать существующие файлы',
        compressSuccess: 'Успешно сжато',
        deCompressSuccess: 'Успешно распаковано',
        deCompressDst: 'Путь распаковки',
        linkType: 'Тип ссылки',
        softLink: 'Символическая ссылка',
        hardLink: 'Жесткая ссылка',
        linkPath: 'Путь ссылки',
        selectFile: 'Выбрать файл',
        downloadUrl: 'Удаленный URL',
        downloadStart: 'Загрузка начата',
        moveSuccess: 'Успешно перемещено',
        copySuccess: 'Успешно скопировано',
        move: 'Переместить',
        calculate: 'Вычислить',
        canNotDeCompress: 'Невозможно распаковать этот файл',
        uploadSuccess: 'Успешно загружено',
        downloadProcess: 'Прогресс загрузки',
        downloading: 'Загрузка...',
        infoDetail: 'Свойства файла',
        root: 'Корневая директория',
        list: 'Список файлов',
        sub: 'Подпапки',
        downloadSuccess: 'Успешно скачано',
        theme: 'Тема',
        language: 'Язык',
        eol: 'Конец строки',
        copyDir: 'Копировать',
        paste: 'Вставить',
        changeOwner: 'Изменить пользователя и группу',
        containSub: 'Применить изменение прав рекурсивно',
        ownerHelper:
            'Пользователь по умолчанию для среды PHP: группа пользователей 1000:1000, нормально что пользователи внутри и снаружи контейнера показывают несоответствия',
        searchHelper: 'Поддерживает подстановочные знаки, такие как *',
        uploadFailed: '[{0}] Ошибка загрузки файла',
        fileUploadStart: 'Загрузка [{0}]....',
        currentSelect: 'Текущий выбор: ',
        unsupportedType: 'Неподдерживаемый тип файла',
        deleteHelper: 'Вы уверены, что хотите удалить следующие файлы? По умолчанию они будут помещены в корзину',
        fileHelper: 'Примечание:\n1. Результаты поиска нельзя сортировать.\n2. Папки нельзя сортировать по размеру.',
        forceDeleteHelper: 'Удалить файл навсегда (без помещения в корзину, удалить напрямую)',
        recycleBin: 'Корзина',
        sourcePath: 'Исходный путь',
        deleteTime: 'Время удаления',
        confirmReduce: 'Вы уверены, что хотите восстановить следующие файлы?',
        reduceSuccess: 'Успешно восстановлено',
        reduce: 'Восстановление',
        reduceHelper:
            'Если файл или директория с таким же именем существует в исходном пути, он будет перезаписан. Хотите продолжить?',
        clearRecycleBin: 'Очистить',
        clearRecycleBinHelper: 'Вы хотите очистить корзину?',
        favorite: 'Избранное',
        removeFavorite: 'Удалить из избранного?',
        addFavorite: 'Добавить/Удалить в избранное',
        clearList: 'Очистить список',
        deleteRecycleHelper: 'Вы уверены, что хотите навсегда удалить следующие файлы?',
        typeErrOrEmpty: '[{0}] неверный тип файла или пустая папка',
        dropHelper: 'Перетащите сюда файлы для загрузки',
        fileRecycleBin: 'Включить корзину',
        fileRecycleBinMsg: '{0} корзина',
        wordWrap: 'Автоматический перенос строк',
        deleteHelper2: 'Вы уверены, что хотите удалить выбранный файл? Операцию удаления нельзя отменить',
        ignoreCertificate: 'Разрешить небезопасные подключения к серверу',
        ignoreCertificateHelper:
            'Разрешение небезопасных подключений к серверу может привести к утечке или подмене данных. Используйте эту опцию только если доверяете источнику загрузки.',
        uploadOverLimit: 'Количество файлов превышает 1000! Пожалуйста, сожмите и загрузите',
        clashDitNotSupport: 'Имена файлов не должны содержать .1panel_clash',
        clashDeleteAlert: 'Папку "Корзина" нельзя удалить',
        clashOpenAlert: 'Пожалуйста, нажмите кнопку "Корзина" чтобы открыть директорию корзины',
        right: 'Вперед',
        back: 'Назад',
        top: 'Вернуться',
        up: 'Назад',
        openWithVscode: 'Открыть в VS Code',
        vscodeHelper: 'Пожалуйста, убедитесь что VS Code установлен локально и настроен плагин SSH Remote',
        saveContentAndClose: 'Файл был изменен, хотите сохранить и закрыть его?',
        saveAndOpenNewFile: 'Файл был изменен, хотите сохранить и открыть новый файл?',
        noEdit: 'Файл не был изменен, не нужно этого делать!',
        noNameFolder: 'Безымянная папка',
        noNameFile: 'Безымянный файл',
        minimap: 'Мини-карта кода',
        fileCanNotRead: 'Файл не может быть прочитан',
        panelInstallDir: 'Директорию установки 1Panel нельзя удалить',
        wgetTask: 'Задача загрузки',
        existFileTitle: 'Предупреждение о файле с тем же именем',
        existFileHelper: 'Загруженный файл содержит файл с таким же именем. Заменить его?',
        existFileSize: 'Размер файла (новый -> старый)',
        existFileDirHelper: 'Выбранный файл/папка имеет дублирующееся имя. Пожалуйста, действуйте осторожно!\n',
        coverDirHelper: 'При выборе перезаписываемой папки она будет скопирована в целевой путь!',
        noSuchFile: 'Файл или каталог не найдены. Пожалуйста, проверьте и повторите попытку.',
        setting: 'настройка',
        showHide: 'Показывать скрытые файлы',
        noShowHide: 'Не показывать скрытые файлы',
        cancelUpload: 'Отменить загрузку',
        cancelUploadHelper: 'Отменить загрузку или нет, после отмены список загрузок будет очищен.',
    },
    ssh: {
        autoStart: 'Автозапуск',
        enable: 'Включить автозапуск',
        disable: 'Отключить автозапуск',
        sshAlert:
            'Данные списка отсортированы по дате входа. Изменение часового пояса или выполнение других операций может вызвать отклонения в дате логов входа.',
        sshAlert2:
            'Вы можете использовать "Fail2ban" в "Инструментах" для блокировки IP-адресов, пытающихся выполнить брутфорс-атаки, это повысит безопасность хоста.',
        sshOperate: 'Будет выполнена операция "{0}" над службой SSH. Хотите продолжить?',
        sshChange: 'Настройки SSH',
        sshChangeHelper: 'Это действие изменит "{0}" на "{1}". Хотите продолжить?',
        sshFileChangeHelper:
            'Изменение конфигурационного файла может повлиять на доступность службы. Будьте осторожны при выполнении этой операции. Хотите продолжить?',
        port: 'Порт',
        portHelper: 'Указывает порт, который прослушивает служба SSH.',
        listenAddress: 'Адрес прослушивания',
        allV4V6: '0.0.0.0:{0}(IPv4) и :::{0}(IPv6)',
        listenHelper:
            'Если оставить пустыми настройки IPv4 и IPv6, будет прослушиваться "0.0.0.0:{0}(IPv4)" и ":::{0}(IPv6)".',
        addressHelper: 'Указывает адрес, который прослушивает служба SSH.',
        permitRootLogin: 'Разрешить вход пользователя root',
        rootSettingHelper: 'Метод входа по умолчанию для пользователя root - "Разрешить вход по SSH".',
        rootHelper1: 'Разрешить вход по SSH',
        rootHelper2: 'Отключить вход по SSH',
        rootHelper3: 'Разрешен только вход по ключу',
        rootHelper4: 'Можно выполнять только предопределенные команды. Другие операции недоступны.',
        passwordAuthentication: 'Аутентификация по паролю',
        pwdAuthHelper: 'Включить ли аутентификацию по паролю. Этот параметр включен по умолчанию.',
        pubkeyAuthentication: 'Аутентификация по ключу',
        privateKey: 'Приватный ключ',
        publicKey: 'Публичный ключ',
        password: 'Пароль',
        createMode: 'Способ создания',
        generate: 'Автогенерация',
        unSyncPass: 'Пароль ключа не может быть синхронизирован',
        syncHelper:
            'Операция синхронизации удалит недействительные ключи и синхронизирует новые полные ключевые пары. Продолжить?',
        input: 'Ручной ввод',
        import: 'Загрузка файла',
        pubkey: 'Информация о ключе',
        pubKeyHelper: 'Текущая информация о ключе действительна только для пользователя {0}',
        encryptionMode: 'Режим шифрования',
        passwordHelper: 'Может содержать от 6 до 10 цифр и английских букв в разных регистрах',
        reGenerate: 'Перегенерировать ключ',
        keyAuthHelper: 'Включить ли аутентификацию по ключу.',
        useDNS: 'useDNS',
        dnsHelper: 'Управляет включением функции DNS-разрешения на SSH-сервере для проверки подлинности подключения.',
        analysis: 'Статистическая информация',
        denyHelper:
            'Выполнение операции "запретить" для следующих адресов. После установки IP будет запрещен доступ к серверу. Хотите продолжить?',
        acceptHelper:
            'Выполнение операции "разрешить" для следующих адресов. После установки IP восстановит нормальный доступ. Хотите продолжить?',
        noAddrWarning: 'В настоящее время не выбраны адреса [{0}]. Пожалуйста, проверьте и попробуйте снова!',
        loginLogs: 'Логи входа',
        loginMode: 'Режим',
        authenticating: 'Ключ',
        publickey: 'Ключ',
        belong: 'Принадлежность',
        local: 'Локальный',
        session: 'Сессия | Сессии',
        loginTime: 'Время входа',
        loginIP: 'IP входа',
        stopSSHWarn: 'Отключить это SSH-соединение',
    },
    setting: {
        panel: 'Панель',
        user: 'Пользователь панели',
        userChange: 'Изменить пользователя панели',
        userChangeHelper: 'Изменение пользователя панели приведет к выходу из системы. Продолжить?',
        passwd: 'Пароль панели',
        emailHelper: 'Для восстановления пароля',
        title: 'Псевдоним панели',
        panelPort: 'Порт панели',
        titleHelper:
            'Поддерживаются строки длиной от 3 до 30 символов, включающие буквы, китайские иероглифы, цифры, пробелы и распространённые специальные символы',
        portHelper:
            'Рекомендуемый диапазон портов от 8888 до 65535. Примечание: Если на сервере есть группа безопасности, заранее разрешите новый порт в группе безопасности',
        portChange: 'Изменение порта',
        portChangeHelper: 'Изменить порт службы и перезапустить службу. Хотите продолжить?',
        theme: 'Тема',
        menuTabs: 'Вкладки меню',
        dark: 'Тёмная',
        darkGold: 'Тёмное золото',
        light: 'Светлая',
        auto: 'Как в системе',
        language: 'Язык',
        languageHelper: 'По умолчанию следует языку браузера. Этот параметр действует только в текущем браузере',
        sessionTimeout: 'Время сессии',
        sessionTimeoutError: 'Минимальное время сессии 300 секунд',
        sessionTimeoutHelper: 'Панель автоматически выйдет из системы, если не будет операций более {0} секунд.',
        systemIP: 'Адрес доступа по умолчанию',
        systemIPHelper:
            'Перенаправления приложений, доступ к контейнерам и другие функции будут использовать этот адрес для маршрутизации. Каждый узел можно настроить с разным адресом.',
        proxy: 'Прокси',
        proxyHelper: 'После настройки прокси-сервера он будет действовать в следующих сценариях:',
        proxyHelper1:
            'Загрузка установочного пакета и синхронизация из магазина приложений (только профессиональная версия)',
        proxyHelper2: 'Обновление системы и получение информации об обновлениях (только профессиональная версия)',
        proxyHelper4: 'Сеть Docker будет доступна через прокси-сервер (только профессиональная версия)',
        proxyHelper3: 'Проверка и синхронизация системной лицензии',
        proxyHelper5: 'Единая загрузка и синхронизация системных скрипт-библиотек (функция Профессиональной версии)',
        proxyHelper6: 'Подать заявку на сертификат (функция профессиональной версии)',
        proxyType: 'Тип прокси',
        proxyUrl: 'Адрес прокси',
        proxyPort: 'Порт прокси',
        proxyPasswdKeep: 'Запомнить пароль',
        proxyDocker: 'Прокси Docker',
        proxyDockerHelper:
            'Синхронизировать конфигурацию прокси-сервера с Docker, поддержка офлайн загрузки образов и других операций',
        syncToNode: 'Синхронизация с дочерним узлом',
        syncToNodeHelper: 'Синхронизация настроек с другими узлами',
        nodes: 'Узлы',
        selectNode: 'Выберите узел',
        selectNodeError: 'Пожалуйста, выберите узел',
        apiInterface: 'Включить API',
        apiInterfaceClose: 'После закрытия API-интерфейсы будут недоступны. Хотите продолжить?',
        apiInterfaceHelper: 'Разрешить сторонним приложениям доступ к API.',
        apiInterfaceAlert1:
            'Не включайте в производственной среде, так как это может повысить риски безопасности сервера.',
        apiInterfaceAlert2:
            'Не используйте сторонние приложения для вызова API во избежание потенциальных угроз безопасности.',
        apiInterfaceAlert3: 'API документация',
        apiInterfaceAlert4: 'Руководство по использованию',
        apiKey: 'API ключ',
        apiKeyHelper: 'API ключ используется сторонними приложениями для доступа к API.',
        ipWhiteList: 'Белый список IP',
        ipWhiteListEgs: 'По одному в строке. Например,\n172.161.10.111\n172.161.10.0/24',
        ipWhiteListHelper:
            'IP-адреса из белого списка могут получить доступ к API, 0.0.0.0/0 (все IPv4), ::/0 (все IPv6)',
        apiKeyValidityTime: 'Срок действия ключа интерфейса',
        apiKeyValidityTimeEgs: 'Срок действия ключа интерфейса (в единицах)',
        apiKeyValidityTimeHelper:
            'Интерфейс времени метки между текущей меткой времени на момент запроса действителен (в единицах), установлен как 0, не проводится проверка метки времени',
        apiKeyReset: 'Сброс ключа интерфейса',
        apiKeyResetHelper:
            'связанный ключевой сервис станет недействительным. Пожалуйста, добавьте новый ключ к сервису',
        confDockerProxy: 'Настроить прокси docker',
        restartNowHelper: 'Настройка прокси Docker требует перезапуска службы Docker.',
        restartNow: 'Перезапустить немедленно',
        restartLater: 'Перезагрузить вручную позже',
        systemIPWarning:
            'Текущий узел не имеет настроенного адреса доступа по умолчанию. Пожалуйста, перейдите в настройки панели для его настройки!',
        systemIPWarning1: 'Текущий адрес сервера установлен на {0}, быстрое перенаправление невозможно!',
        defaultNetwork: 'Сетевой интерфейс',
        syncTime: 'Время сервера',
        timeZone: 'Часовой пояс',
        timeZoneChangeHelper: 'Изменение часового пояса требует перезапуска службы. Хотите продолжить?',
        timeZoneHelper:
            'Изменение часового пояса зависит от системной службы timedatectl. Вступит в силу после перезапуска службы 1Panel.',
        timeZoneCN: 'Пекин',
        timeZoneAM: 'Лос-Анджелес',
        timeZoneNY: 'Нью-Йорк',
        ntpALi: 'Alibaba',
        ntpGoogle: 'Google',
        syncSite: 'NTP сервер',
        syncSiteHelper:
            'Эта операция будет использовать {0} как источник для синхронизации системного времени. Хотите продолжить?',
        changePassword: 'Изменить пароль',
        oldPassword: 'Текущий пароль',
        newPassword: 'Новый пароль',
        retryPassword: 'Подтвердите пароль',
        noSpace: 'Вводимая информация не может включать пробелы',
        duplicatePassword: 'Новый пароль не может совпадать с текущим, пожалуйста, введите заново!',
        diskClean: 'Очистка кэша',
        developerMode: 'Режим предварительного просмотра',
        developerModeHelper:
            'Вы сможете опробовать новые функции и исправления до их широкого релиза и оставить ранний отзыв.',
        thirdParty: 'Сторонние аккаунты',
        noTypeForCreate: 'В настоящее время не создано типов резервного копирования',
        LOCAL: 'Диск сервера',
        OSS: 'Ali OSS',
        S3: 'Amazon S3',
        mode: 'Режим',
        MINIO: 'MinIO',
        SFTP: 'SFTP',
        WebDAV: 'WebDAV',
        WebDAVAlist: 'Подключение WebDAV к Alist можно найти в официальной документации',
        OneDrive: 'Microsoft OneDrive',
        isCN: 'Китайский интернет',
        isNotCN: 'Международная версия',
        client_id: 'ID клиента',
        client_secret: 'Секрет клиента',
        redirect_uri: 'URL перенаправления',
        onedrive_helper: 'Пользовательская конфигурация описана в официальной документации',
        refreshTime: 'Время обновления токена',
        refreshStatus: 'Статус обновления токена',
        backupDir: 'Директория резервных копий',
        codeWarning: 'Текущий формат кода авторизации неверен, пожалуйста, проверьте еще раз!',
        code: 'Код авторизации',
        codeHelper:
            'Пожалуйста, нажмите кнопку "Получить", затем войдите в OneDrive и скопируйте содержимое после "code" в перенаправленной ссылке. Вставьте его в это поле ввода. Подробные инструкции смотрите в официальной документации.',
        loadCode: 'Получить',
        COS: 'Tencent COS',
        ap_beijing_1: 'Пекин Зона 1',
        ap_beijing: 'Пекин',
        ap_nanjing: 'Нанкин',
        ap_shanghai: 'Шанхай',
        ap_guangzhou: 'Гуанчжоу',
        ap_chengdu: 'Чэнду',
        ap_chongqing: 'Чунцин',
        ap_shenzhen_fsi: 'Шэньчжэнь Финансовый',
        ap_shanghai_fsi: 'Шанхай Финансовый',
        ap_beijing_fsi: 'Пекин Финансовый',
        ap_hongkong: 'Гонконг, Китай',
        ap_singapore: 'Сингапур',
        ap_mumbai: 'Мумбаи',
        ap_jakarta: 'Джакарта',
        ap_seoul: 'Сеул',
        ap_bangkok: 'Бангкок',
        ap_tokyo: 'Токио',
        na_siliconvalley: 'Силиконовая долина (США Запад)',
        na_ashburn: 'Ашберн (США Восток)',
        na_toronto: 'Торонто',
        sa_saopaulo: 'Сан-Паулу',
        eu_frankfurt: 'Франкфурт',
        KODO: 'Qiniu Kodo',
        scType: 'Тип хранилища',
        typeStandard: 'Стандартный',
        typeStandard_IA: 'Стандартный_IA',
        typeArchive: 'Архивный',
        typeDeep_Archive: 'Глубокий_Архив',
        scLighthouse: 'По умолчанию, Легковесное объектное хранилище поддерживает только этот тип хранилища',
        scStandard:
            'Стандартное хранилище подходит для бизнес-сценариев с большим количеством горячих файлов, требующих мгновенного доступа, частого обмена данными и т.д.',
        scStandard_IA:
            'Низкочастотное хранилище подходит для бизнес-сценариев с относительно низкой частотой доступа и хранит данные не менее 30 дней.',
        scArchive: 'Архивное хранилище подходит для бизнес-сценариев с крайне низкой частотой доступа.',
        scDeep_Archive:
            'Долговечное холодное хранилище подходит для бизнес-сценариев с крайне низкой частотой доступа.',
        archiveHelper:
            'Файлы архивного хранилища нельзя скачать напрямую, сначала их нужно восстановить через веб-сайт соответствующего облачного провайдера. Пожалуйста, используйте с осторожностью!',
        backupAlert:
            'Если облачный провайдер совместим с протоколом S3, вы можете напрямую использовать Amazon S3 для резервного копирования.',
        domain: 'Домен ускорения',
        backupAccount: 'Аккаунт резервного копирования | Аккаунты резервного копирования',
        loadBucket: 'Получить корзину',
        accountName: 'Имя аккаунта',
        accountKey: 'Ключ аккаунта',
        address: 'Адрес',
        path: 'Путь',

        safe: 'Безопасность',
        bindInfo: 'IP-адрес',
        bindAll: 'Прослушивать все',
        bindInfoHelper:
            'Изменение адреса прослушивания службы или протокола может привести к недоступности службы. Хотите продолжить?',
        ipv6: 'Прослушивать IPv6',
        bindAddress: 'IP-адрес прослушивания',
        entrance: 'Точка входа',
        showEntrance: 'Показывать уведомление об отключении на странице "Обзор"',
        entranceHelper:
            'Включение безопасной точки входа позволит входить в панель только через указанную точку входа.',
        entranceError:
            'Пожалуйста, введите безопасную точку входа длиной 5-116 символов, поддерживаются только цифры или буквы.',
        entranceInputHelper: 'Оставьте пустым, чтобы отключить безопасную точку входа.',
        randomGenerate: 'Случайно',
        expirationTime: 'Дата истечения',
        unSetting: 'Не задано',
        noneSetting: 'Установите срок действия пароля панели. После истечения срока необходимо сбросить пароль',
        expirationHelper: 'Если срок действия пароля [0] дней, функция истечения срока действия пароля отключена',
        days: 'Дней до истечения',
        expiredHelper: 'Текущий пароль истек. Пожалуйста, измените пароль снова.',
        timeoutHelper:
            '[ {0} дней ] Срок действия пароля панели скоро истечет. После истечения срока необходимо сбросить пароль',
        complexity: 'Проверка сложности',
        complexityHelper:
            'После включения правило проверки пароля будет: 8-30 символов, включая английские буквы, цифры и как минимум два специальных символа.',
        bindDomain: 'Привязать домен',
        unBindDomain: 'Отвязать домен',
        panelSSL: 'SSL панели',
        unBindDomainHelper:
            'Действие по отвязке доменного имени может привести к небезопасности системы. Хотите продолжить?',
        bindDomainHelper: 'После привязки домена только этот домен сможет получить доступ к службе 1Panel.',
        bindDomainHelper1: 'Оставьте пустым, чтобы отключить привязку доменного имени.',
        bindDomainWarning:
            'После привязки домена вы будете выходить из системы и сможете получить доступ к службе 1Panel только через указанное в настройках доменное имя. Хотите продолжить?',
        allowIPs: 'Авторизованные IP',
        unAllowIPs: 'Неавторизованные IP',
        unAllowIPsWarning:
            'Авторизация пустого IP позволит всем IP получить доступ к системе, что может привести к небезопасности системы. Хотите продолжить?',
        allowIPsHelper:
            'После установки списка авторизованных IP-адресов только IP-адреса из списка смогут получить доступ к службе панели.',
        allowIPsWarning:
            'После установки списка авторизованных IP-адресов только IP-адреса из списка смогут получить доступ к службе панели. Хотите продолжить?',
        allowIPsHelper1: 'Оставьте пустым, чтобы отключить ограничение IP-адресов.',
        allowIPEgs: 'По одному в строке. Например,\n*************\n***********/24',
        mfa: '2FA авторизация',
        mfaClose: 'Отключение MFA снизит безопасность службы. Хотите продолжить?',
        secret: 'Секрет',
        mfaInterval: 'Интервал обновления(с)',
        mfaTitleHelper:
            'Заголовок используется для различения разных хостов 1Panel. Отсканируйте снова или вручную добавьте секретный ключ после изменения заголовка.',
        mfaIntervalHelper: 'Отсканируйте снова или вручную добавьте секретный ключ после изменения времени обновления.',
        mfaAlert:
            'Одноразовый токен - это динамически генерируемое 6-значное число, основанное на текущем времени. Убедитесь, что время сервера синхронизировано.',
        mfaHelper: 'После включения потребуется проверка одноразового токена.',
        mfaHelper1: 'Загрузите приложение-аутентификатор, например,',
        mfaHelper2:
            'Чтобы получить одноразовый токен, отсканируйте следующий QR-код с помощью приложения-аутентификатора или скопируйте секретный ключ в приложение аутентификации.',
        mfaHelper3: 'Введите шесть цифр из приложения',
        mfaCode: 'Одноразовый токен',
        sslChangeHelper: 'Изменить настройку https и перезапустить службу. Хотите продолжить?',
        sslDisable: 'Отключить',
        sslDisableHelper:
            'Если служба https отключена, необходимо перезапустить панель, чтобы изменения вступили в силу. Хотите продолжить?',
        noAuthSetting: 'Unauthorized',
        noAuthSettingHelper:
            'Когда пользователи не входят через указанную точку входа безопасности или не получают доступ к панели с указанного IP или доменного имени, этот ответ может скрыть характеристики панели.',
        responseSetting: 'Настройка ответа',
        help200: 'Страница помощи',
        error400: 'Неверный запрос',
        error401: 'Не авторизован',
        error403: 'Запрещено',
        error404: 'Не найдено',
        error408: 'Тайм-аут запроса',
        error416: 'Диапазон не удовлетворяется',
        error444: 'Соединение закрыто',
        error500: 'Ошибка сервера',

        https: 'Настройка доступа по протоколу HTTPS для панели может повысить безопасность доступа к панели.',
        certType: 'Тип сертификата',
        selfSigned: 'Самоподписанный',
        selfSignedHelper:
            'Браузеры могут не доверять самоподписанным сертификатам и отображать предупреждения безопасности.',
        select: 'Выбрать',
        domainOrIP: 'Домен или IP:',
        timeOut: 'Тайм-аут',
        rootCrtDownload: 'Скачать корневой сертификат',
        primaryKey: 'Закрытый ключ',
        certificate: 'Сертификат',
        backupJump:
            'Файлы резервной копии отсутствуют в текущем списке резервных копий, попробуйте скачать из директории файлов и импортировать для резервного копирования.',

        snapshot: 'Снапшот | Снапшоты',
        noAppData: 'Нет доступных системных приложений для выбора',
        noBackupData: 'Нет доступных данных для резервного копирования',
        stepBaseData: 'Основные данные',
        stepAppData: 'Системные приложения',
        stepPanelData: 'Системные данные',
        stepBackupData: 'Резервные данные',
        stepOtherData: 'Другие данные',
        operationLog: 'Сохранять журнал операций',
        loginLog: 'Сохранять журнал доступа',
        systemLog: 'Сохранять системный журнал',
        taskLog: 'Сохранять журнал задач',
        monitorData: 'Сохранять данные мониторинга',
        dockerConf: 'Сохранять Конфигурация Docker',
        selectAllImage: 'Резервное копирование всех образов приложений',
        logLabel: 'Журнал',
        agentLabel: 'Конфигурация узла',
        appDataLabel: 'Данные приложения',
        appImage: 'Образ приложения',
        appBackup: 'Резервная копия приложения',
        backupLabel: 'Каталог резервных копий',
        confLabel: 'Конфигурационные файлы',
        dockerLabel: 'Контейнеры',
        taskLabel: 'Планировщик задач',
        resourceLabel: 'Каталог ресурсов приложения',
        runtimeLabel: 'Среда выполнения',
        appLabel: 'Приложение',
        databaseLabel: 'База данных',
        snapshotLabel: 'Файлы снимков',
        websiteLabel: 'Веб-сайт',
        directoryLabel: 'Каталог',
        appStoreLabel: 'Магазин приложений',
        shellLabel: 'Скрипт',
        tmpLabel: 'Временный каталог',
        sslLabel: 'Каталог сертификатов',
        reCreate: 'Не удалось создать снимок',
        reRollback: 'Не удалось откатить снимок',
        deleteHelper:
            'Все файлы снапшотов, включая те, что находятся в сторонних аккаунтах резервного копирования, будут удалены. Хотите продолжить?',
        status: 'Статус снапшота',
        ignoreRule: 'Правило игнорирования',
        editIgnoreRule: '@:commons.button.edit @.lower:setting.ignoreRule',
        ignoreHelper:
            'Это правило будет использоваться для сжатия и резервного копирования директории данных 1Panel при создании снапшота. По умолчанию игнорируются файлы сокетов.',
        ignoreHelper1: 'По одному в строке. Например,\n*.log\n/opt/1panel/cache',
        panelInfo: 'Записать базовую информацию 1Panel',
        panelBin: 'Резервное копирование системных файлов 1Panel',
        daemonJson: 'Резервное копирование конфигурационного файла Docker',
        appData: 'Резервное копирование установленных приложений из 1Panel',
        panelData: 'Резервное копирование директории данных 1Panel',
        backupData: 'Резервное копирование локальной директории резервных копий для 1Panel',
        compress: 'Создать файл снапшота',
        upload: 'Загрузить файл снапшота',
        recoverDetail: 'Детали восстановления',
        createSnapshot: 'Создать снапшот',
        importSnapshot: 'Синхронизировать снапшот',
        importHelper: 'Директория снапшот: ',
        lastRecoverAt: 'Время последнего восстановления',
        lastRollbackAt: 'Время последнего отката',
        reDownload: 'Скачать файл резервной копии снова',
        recoverErrArch: 'Восстановление снапшотов между разными архитектурами серверов не поддерживается!',
        recoverErrSize:
            'Обнаружено недостаточно места на диске, пожалуйста, проверьте или очистите и попробуйте снова!',
        recoverHelper:
            'Начало восстановления из снапшота {0}, пожалуйста, подтвердите следующую информацию перед продолжением:',
        recoverHelper1: 'Восстановление требует перезапуска служб Docker и 1Panel',
        recoverHelper2:
            'Пожалуйста, убедитесь, что на сервере достаточно места на диске (Размер файла снапшота: {0}, Доступное место: {1})',
        recoverHelper3:
            'Пожалуйста, убедитесь, что архитектура сервера соответствует архитектуре сервера, где был создан снапшот (Текущая архитектура сервера: {0})',
        rollback: 'Откатить',
        rollbackHelper:
            'Откат этого восстановления заменит все файлы из этого восстановления и может потребовать перезапуска служб Docker и 1Panel. Хотите продолжить?',
        upgradeHelper: 'Обновление требует перезапуска службы 1Panel. Хотите продолжить?',
        rollbackLocalHelper:
            'Основной узел не поддерживает прямой откат. Пожалуйста, вручную выполните команду [1pctl restore] для отката!',
        noUpgrade: 'В настоящее время это последняя версия',
        upgradeNotes: 'Примечания к выпуску',
        upgradeNow: 'Обновить сейчас',
        source: 'Источник загрузки',
        versionNotSame:
            'Версия узла не совпадает с основной. Пожалуйста, обновите в Управлении узлами перед повторной попыткой.',
        versionCompare:
            'Обнаружено, что узел {0} уже имеет последнюю обновляемую версию. Пожалуйста, проверьте версию основного узла и повторите попытку!',

        about: 'О программе',
        project: 'GitHub',
        issue: 'Проблема',
        doc: 'Официальная документация',
        star: 'Звезда',
        description: 'Панель управления Linux сервером',
        forum: 'Обсуждения',
        doc2: 'Документация',
        currentVersion: 'Версия',

        license: 'Лицензия',
        bindNode: 'Привязать Узел',
        menuSetting: 'Настройки меню',
        menuSettingHelper: 'Если существует только 1 подменю, в панели меню будет отображаться только это подменю',
        showAll: 'Показать все',
        hideALL: 'Скрыть все',
        ifShow: 'Показывать',
        menu: 'Меню',
        confirmMessage: 'Страница будет обновлена для обновления списка расширенного меню. Продолжить?',
        compressPassword: 'Пароль сжатия',
        backupRecoverMessage:
            'Пожалуйста, введите пароль для сжатия или распаковки (оставьте пустым, чтобы не устанавливать)',
    },
    license: {
        community: 'OSS',
        oss: 'Open Source Software',
        pro: 'Pro',
        trial: 'Пробная версия',
        add: 'Добавить Community Edition',
        licenseAlert:
            'Узлы Community Edition можно добавлять только при правильной привязке лицензии к узлу. Переключение поддерживается только для узлов, правильно привязанных к лицензии.',
        licenseUnbindHelper:
            'Обнаружены узлы Community Edition для этой лицензии. Отвяжите лицензию и повторите попытку!',
        subscription: 'Подписка',
        perpetual: 'Пожизненная лицензия',
        versionConstraint: '{0} Выкуп версии',
        forceUnbind: 'Принудительное отвязывание',
        forceUnbindHelper:
            'Принудительное отвязывание будет игнорировать любые ошибки, возникающие в процессе отвязывания, и в конечном итоге освободит привязку лицензии.',
        updateForce:
            'Принудительное обновление (игнорировать все ошибки при отвязке для гарантии успешного завершения операции)',
        trialInfo: 'Версия',
        authorizationId: 'ID авторизации подписки',
        authorizedUser: 'Авторизованный пользователь',
        lostHelper:
            'Лицензия достигла максимального количества попыток повторной проверки. Пожалуйста, вручную нажмите кнопку синхронизации, чтобы убедиться, что функции профессиональной версии работают правильно. Детали: ',
        disableHelper:
            'Синхронизация лицензии не удалась. Пожалуйста, вручную нажмите кнопку синхронизации, чтобы убедиться, что функции профессиональной версии работают правильно. Детали: ',
        quickUpdate: 'Быстрое обновление',
        power: 'Авторизовать',
        unbindHelper: 'Все настройки Pro будут очищены после отвязки. Хотите продолжить?',
        importLicense: 'Лицензия',
        importHelper: 'Пожалуйста, нажмите или перетащите файл лицензии сюда',
        technicalAdvice: 'Техническая консультация',
        advice: 'Консультация',
        levelUpPro: 'Обновить до Pro',
        licenseSync: 'Синхронизация лицензии',
        knowMorePro: 'Узнать больше',
        closeAlert: 'Текущую страницу можно закрыть в настройках панели',
        introduce: 'Описание функций',
        waf: 'Обновление до профессиональной версии предоставляет такие функции, как карта перехватов, логи, записи блокировок, блокировка по географическому положению, пользовательские правила, пользовательские страницы перехвата и т.д.',
        tamper: 'Обновление до профессиональной версии может защитить веб-сайты от несанкционированных изменений или подделок.',
        setting:
            'Обновление до профессиональной версии позволяет настраивать логотип панели, приветственное сообщение и другую информацию.',
        monitor:
            'Обновление до профессиональной версии позволяет просматривать статус веб-сайта в реальном времени, тенденции посещений, источники посетителей, логи запросов и другую информацию.',
        alert: 'Обновление до профессиональной версии позволяет получать информацию о тревогах через SMS и просматривать логи тревог, полностью контролировать различные ключевые события и обеспечивать беспроблемную работу системы',
        fileExchange: 'Обновите до Профессиональной версии, чтобы быстро передавать файлы между несколькими серверами.',
        app: 'Обновите до профессиональной версии, чтобы просматривать информацию о сервисах, мониторинг аномалий и т.д. через мобильное приложение.',
        cluster:
            'Обновление до профессиональной версии позволяет управлять кластерами мастер-слейв MySQL/Postgres/Reids.',
    },
    clean: {
        scan: 'Начать сканирование',
        scanHelper: 'Легко очищайте мусорные файлы, созданные во время работы 1Panel',
        clean: 'Очистить сейчас',
        reScan: 'Пересканировать',
        cleanHelper: 'Это очистит выбранные системные мусорные файлы, и действие нельзя отменить. Хотите продолжить?',
        statusSuggest: '(Рекомендуется очистка)',
        statusClean: '(Очень чисто)',
        statusEmpty: 'Очень чисто, очистка не требуется!',
        statusWarning: '(Действуйте с осторожностью)',
        lastCleanTime: 'Последняя очистка: {0}',
        lastCleanHelper: 'Очищено файлов и директорий: {0}, всего очищено: {1}',
        cleanSuccessful: 'Успешно очищено',
        currentCleanHelper: 'Очищено файлов и директорий в этой сессии: {0}, Всего очищено: {1}',
        suggest: '(Рекомендуется)',
        totalScan: 'Всего мусорных файлов для очистки: ',
        selectScan: 'Всего выбранных мусорных файлов: ',

        system: 'Системные мусорные файлы',
        systemHelper:
            'Временные файлы, созданные во время снапшотов, обновлений, и устаревшее содержимое файлов при итерациях версий',
        panelOriginal: 'Файлы резервных копий восстановления системных снапшотов',
        backup: 'Временный резервный каталог',
        upgrade: 'Файлы резервных копий системных обновлений',
        upgradeHelper: '(Рекомендуется сохранять последнюю резервную копию обновления для отката системы)',
        cache: 'Системные файлы кэша',
        cacheHelper: '(Действуйте с осторожностью, очистка требует перезапуска службы)',
        rollback: 'Файлы резервных копий перед восстановлением',

        upload: 'Временные файлы загрузки',
        uploadHelper: 'Временные файлы, загруженные из списка системных резервных копий',
        download: 'Временные файлы скачивания',
        downloadHelper: 'Временные файлы, скачанные из сторонних аккаунтов резервного копирования системой',
        directory: 'Директория',

        systemLog: 'Системные файлы логов',
        systemLogHelper:
            'Системная информация логов, логи сборки контейнеров или загрузки образов, и файлы логов, созданные в запланированных задачах',
        dockerLog: 'Файлы логов операций с контейнерами',
        taskLog: 'Файлы логов выполнения запланированных задач',
        containerShell: 'Запланированные задачи Shell-скриптов внутри контейнера',

        containerTrash: 'Корзина контейнеров',
        volumes: 'Тома',
        buildCache: 'Кэш сборки контейнеров',
    },
    app: {
        app: 'Приложение | Приложения',
        installName: 'Имя',
        installed: 'Установленные',
        all: 'Все',
        version: 'Версия',
        detail: 'Детали',
        params: 'Редактировать',
        author: 'Автор',
        source: 'Источник',
        appName: 'Название приложения',
        deleteWarn:
            'Операция удаления удалит все данные и резервные копии. Эту операцию нельзя отменить. Хотите продолжить?',
        syncSuccess: 'Синхронизация выполнена успешно',
        canUpgrade: 'Обновления',
        backupName: 'Имя файла',
        backupPath: 'Путь к файлу',
        backupdate: 'Время резервного копирования',
        versionSelect: 'Пожалуйста, выберите версию',
        operatorHelper: 'Операция {0} будет выполнена для выбранного приложения. Хотите продолжить?',
        startOperatorHelper: 'Приложение будет запущено. Хотите продолжить?',
        stopOperatorHelper: 'Приложение будет остановлено. Хотите продолжить?',
        restartOperatorHelper: 'Приложение будет перезапущено. Хотите продолжить?',
        reloadOperatorHelper: 'Приложение будет перезагружено. Хотите продолжить?',
        checkInstalledWarn: '"{0}" не обнаружено. Перейдите в "Магазин приложений" для установки.',
        gotoInstalled: 'Перейти к установке',
        limitHelper: 'Приложение уже установлено.',
        deleteHelper: '"{0}" связано со следующими ресурсами. Пожалуйста, проверьте и попробуйте снова!',
        checkTitle: 'Подсказка',
        defaultConfig: 'Конфигурация по умолчанию',
        defaultConfigHelper: 'Восстановлено до конфигурации по умолчанию, вступит в силу после сохранения',
        forceDelete: 'Принудительное удаление',
        forceDeleteHelper:
            'Принудительное удаление будет игнорировать ошибки во время процесса удаления и в итоге удалит метаданные.',
        deleteBackup: 'Удалить резервную копию',
        deleteBackupHelper: 'Также удалить резервную копию приложения',
        deleteDB: 'Удалить базу данных',
        deleteDBHelper: 'Также удалить базу данных',
        noService: 'Нет {0}',
        toInstall: 'Перейти к установке',
        param: 'Параметры',
        alreadyRun: 'Возраст',
        syncAppList: 'Синхронизировать',
        less1Minute: 'Меньше 1 минуты',
        appOfficeWebsite: 'Официальный сайт',
        github: 'Github',
        document: 'Документация',
        updatePrompt: 'Нет доступных обновлений',
        installPrompt: 'Пока нет установленных приложений',
        updateHelper:
            'Редактирование параметров может привести к сбою запуска приложения. Пожалуйста, действуйте с осторожностью.',
        updateWarn: 'Обновление параметров требует пересборки приложения. Хотите продолжить?',
        busPort: 'Порт',
        syncStart: 'Начало синхронизации! Пожалуйста, обновите магазин приложений позже',
        advanced: 'Расширенные настройки',
        cpuCore: 'ядро(а)',
        containerName: 'Имя контейнера',
        containerNameHelper: 'Имя контейнера будет автоматически сгенерировано, если не задано',
        allowPort: 'Внешний доступ',
        allowPortHelper: 'Разрешение внешнего доступа к порту откроет порт в брандмауэре',
        appInstallWarn:
            'Приложение по умолчанию не открывает порт для внешнего доступа. Нажмите "Расширенные настройки" для открытия.',
        upgradeStart: 'Начало обновления! Пожалуйста, обновите страницу позже',
        toFolder: 'Открыть директорию установки',
        editCompose: 'Редактировать файл compose',
        editComposeHelper: 'Редактирование файла compose может привести к сбою установки программного обеспечения',
        composeNullErr: 'compose не может быть пустым',
        takeDown: 'Отключить',
        allReadyInstalled: 'Установлено',
        installHelper: 'Если есть проблемы с загрузкой образа, настройте ускорение образов.',
        installWarn:
            'Внешний доступ не включен, что делает приложение недоступным через внешние сети. Хотите продолжить?',
        showIgnore: 'Просмотреть игнорируемые приложения',
        cancelIgnore: 'Отменить игнорирование',
        ignoreList: 'Игнорируемые приложения',
        appHelper: 'Перейдите на страницу приложения, чтобы узнать инструкции и подробности данного приложения.',
        backupApp: 'Создать резервную копию приложения перед обновлением',
        backupAppHelper:
            'Если обновление не удастся, резервная копия будет автоматически восстановлена. Пожалуйста, проверьте причину сбоя в логе аудита-системном логе. Резервная копия по умолчанию сохранит последние 3 копии',
        openrestyDeleteHelper: 'Принудительное удаление OpenResty удалит все веб-сайты. Хотите продолжить?',
        downloadLogHelper1: 'Будут загружены все логи приложения {0}. Хотите продолжить?',
        downloadLogHelper2: 'Будут загружены последние {1} логов приложения {0}. Хотите продолжить?',
        syncAllAppHelper: 'Все приложения будут синхронизированы. Хотите продолжить?',
        hostModeHelper:
            'Текущий режим сети приложения - режим хоста. Если нужно открыть порт, пожалуйста, откройте его вручную на странице брандмауэра.',
        showLocal: 'Показать локальные приложения',
        reload: 'Перезагрузить',
        upgradeWarn:
            'Обновление приложения заменит файл docker-compose.yml. Если есть изменения, вы можете нажать для просмотра сравнения файлов',
        newVersion: 'Новая версия',
        oldVersion: 'Текущая версия',
        composeDiff: 'Сравнение файлов',
        showDiff: 'Просмотреть сравнение',
        useNew: 'Использовать пользовательскую версию',
        useDefault: 'Использовать версию по умолчанию',
        useCustom: 'Настроить docker-compose.yml',
        useCustomHelper:
            'Использование пользовательского файла docker-compose.yml может привести к сбою обновления приложения. Если это не необходимо, не отмечайте это.',
        diffHelper:
            'Слева старая версия, справа новая версия. После редактирования нажмите для сохранения пользовательской версии',
        pullImage: 'Загрузить образ',
        pullImageHelper: 'Выполнить docker pull для загрузки образа перед запуском приложения',
        deleteImage: 'Удалить изображение',
        deleteImageHelper:
            'Удалите изображение, связанное с приложением. Задача не завершится, если удаление не удастся.',
        requireMemory: 'Требуемая память',
        supportedArchitectures: 'Поддерживаемые архитектуры',
        link: 'Ссылка',
        showCurrentArch: 'Приложения для текущей архитектуры сервера',
        syncLocalApp: 'Синхронизировать локальное приложение',
        memoryRequiredHelper: 'Текущее приложение требует {0} памяти',
        gpuConfig: 'Включить поддержку GPU',
        gpuConfigHelper:
            'Убедитесь, что на машине установлен NVIDIA GPU и драйверы NVIDIA, а также NVIDIA Docker Container Toolkit',
        webUI: 'Веб-адрес доступа',
        webUIPlaceholder: 'Например: example.com:8080/login',
        defaultWebDomain: 'Адрес доступа по умолчанию',
        defaultWebDomainHepler: 'Если порт приложения 8080, то адрес будет http(s)://адрес по умолчанию:8080',
        webUIConfig:
            'Текущий узел не имеет настроенного адреса доступа по умолчанию. Пожалуйста, настройте его в параметрах приложения или перейдите в настройки панели!',
        toLink: 'Перейти',
        customAppHelper:
            'Перед установкой пользовательского пакета из магазина приложений убедитесь, что нет установленных приложений.',
        forceUninstall: 'Принудительное удаление',
        syncCustomApp: 'Синхронизировать пользовательское приложение',
        ignoreAll: 'Игнорировать все последующие версии',
        ignoreVersion: 'Игнорировать указанную версию',
        specifyIP: 'Привязать IP хоста',
        specifyIPHelper:
            'Установите адрес хоста/сетевого интерфейса для привязки порта (если вы не уверены в этом, пожалуйста, не заполняйте)',
        uninstallDeleteBackup: 'Деинсталляция приложения - Удаление резервной копии',
        uninstallDeleteImage: 'Деинсталляция приложения - Удаление образа',
        upgradeBackup: 'Резервное копирование приложения перед обновлением',
    },
    website: {
        primaryDomain: 'Основной домен',
        otherDomains: 'Другие домены',
        static: 'Статический',
        deployment: 'Развертывание',
        supportUpType: 'Поддерживаются только файлы .tar.gz',
        zipFormat: 'Структура архива .tar.gz: архив test.tar.gz должен содержать файл {0}',
        proxy: 'Обратный прокси',
        alias: 'Псевдоним',
        ftpUser: 'FTP аккаунт',
        ftpPassword: 'FTP пароль',
        ftpHelper:
            'После создания веб-сайта будет создан соответствующий FTP-аккаунт, и FTP-директория будет связана с директорией веб-сайта.',
        remark: 'Примечание',
        manageGroup: 'Управление группами',
        groupSetting: 'Управление группами',
        createGroup: 'Создать группу',
        appNew: 'Новое приложение',
        appInstalled: 'Установленное приложение',
        create: 'Создать веб-сайт',
        delete: 'Удалить веб-сайт',
        deleteApp: 'Удалить приложение',
        deleteBackup: 'Удалить резервную копию',
        domain: 'Домен',
        domainHelper: 'Один домен в строке.\nПоддерживает wildcard "*" и IP-адреса.\nПоддерживает добавление порта.',
        addDomain: 'Добавить',
        domainConfig: 'Домены',
        defaultDoc: 'Документ',
        perserver: 'Параллельные подключения',
        perserverHelper: 'Ограничить максимальное количество параллельных подключений для текущего сайта',
        perip: 'Один IP',
        peripHelper: 'Ограничить максимальное количество параллельных подключений с одного IP',
        rate: 'Ограничения трафика',
        rateHelper: 'Ограничить поток каждого запроса (единица: КБ)',
        limitHelper: 'Включить контроль потока',
        other: 'Другое',
        currentSSL: 'Текущий сертификат',
        dnsAccount: 'DNS аккаунт',
        applySSL: 'Заявка на сертификат',
        SSLList: 'Список сертификатов',
        createDnsAccount: 'DNS аккаунт',
        aliyun: 'Aliyun',
        manual: 'Ручная настройка',
        key: 'Ключ',
        check: 'Просмотр',
        acmeAccountManage: 'ACME аккаунты',
        email: 'Email',
        acmeAccount: 'ACME аккаунт',
        provider: 'Метод проверки',
        dnsManual: 'Ручное разрешение',
        expireDate: 'Дата истечения',
        brand: 'Организация',
        deploySSL: 'Развертывание',
        deploySSLHelper: 'Вы уверены, что хотите развернуть сертификат?',
        ssl: 'Сертификат | Сертификаты',
        dnsAccountManage: 'DNS провайдеры',
        renewSSL: 'Обновить',
        renewHelper: 'Вы уверены, что хотите обновить сертификат?',
        renewSuccess: 'Обновить сертификат',
        enableHTTPS: 'Включить',
        aliasHelper: 'Псевдоним - это имя директории веб-сайта',
        lastBackupAt: 'время последнего резервного копирования',
        null: 'нет',
        nginxConfig: 'Конфигурация Nginx',
        websiteConfig: 'Настройки веб-сайта',
        basic: 'Основные',
        source: 'Конфигурация',
        security: 'Безопасность',
        nginxPer: 'Настройка производительности',
        neverExpire: 'Никогда',
        setDefault: 'Установить по умолчанию',
        default: 'По умолчанию',
        deleteHelper: 'Статус связанного приложения аномальный, пожалуйста, проверьте',
        toApp: 'Перейти к списку установленных',
        cycle: 'Цикл',
        frequency: 'Частота',
        ccHelper:
            'При накоплении более {1} запросов к одному URL в течение {0} секунд срабатывает защита CC и блокируется этот IP',
        mustSave: 'Изменения нужно сохранить, чтобы они вступили в силу',
        fileExt: 'расширение файла',
        fileExtBlock: 'черный список расширений файлов',
        value: 'значение',
        enable: 'Включить',
        proxyAddress: 'Адрес прокси',
        proxyHelper: 'Пример: 127.0.0.1:8080',
        forceDelete: 'Принудительное удаление',
        forceDeleteHelper:
            'Принудительное удаление будет игнорировать ошибки во время процесса удаления и в итоге удалит метаданные.',
        deleteAppHelper: 'Удалить связанные приложения и резервные копии приложений одновременно',
        deleteBackupHelper: 'Также удалить резервные копии веб-сайта.',
        deleteConfirmHelper:
            'Операцию удаления нельзя отменить. Введите <span style="color:red"> "{0}" </span> для подтверждения удаления.',
        staticPath: 'Соответствующая основная директория - ',
        limit: 'Схема',
        blog: 'Форум/Блог',
        imageSite: 'Сайт изображений',
        downloadSite: 'Сайт загрузок',
        shopSite: 'Магазин',
        doorSite: 'Портал',
        qiteSite: 'Корпоративный',
        videoSite: 'Видео',
        errLog: 'Лог ошибок',
        accessLog: 'Лог веб-сайта',
        stopHelper:
            'После остановки сайта он не будет доступен для нормального доступа, и пользователи будут видеть страницу остановки при посещении. Хотите продолжить?',
        startHelper:
            'После включения сайта пользователи смогут нормально получить доступ к содержимому сайта, хотите продолжить?',
        sitePath: 'Директория',
        siteAlias: 'Псевдоним сайта',
        primaryPath: 'Корневая директория',
        folderTitle: 'Веб-сайт в основном содержит следующие папки',
        wafFolder: 'Правила межсетевого экрана',
        indexFolder: 'Корневая директория веб-сайта',
        logFolder: 'Лог веб-сайта',
        sslFolder: 'Сертификат веб-сайта',
        enableOrNot: 'Включить',
        oldSSL: 'Существующий сертификат',
        manualSSL: 'Импорт сертификата',
        select: 'Выбрать',
        selectSSL: 'Выбрать сертификат',
        privateKey: 'Ключ (KEY)',
        certificate: 'Сертификат (формат PEM)',
        HTTPConfig: 'Опции HTTP',
        HTTPSOnly: 'Блокировать HTTP запросы',
        HTTPToHTTPS: 'Перенаправлять на HTTPS',
        HTTPAlso: 'Разрешить прямые HTTP запросы',
        sslConfig: 'Опции SSL',
        disableHTTPS: 'Отключить HTTPS',
        disableHTTPSHelper: 'Отключение HTTPS удалит конфигурацию, связанную с сертификатом. Хотите продолжить?',
        SSLHelper:
            'Примечание: Не используйте SSL-сертификаты для нелегальных веб-сайтов.\nЕсли после открытия невозможно использовать доступ по HTTPS, проверьте, правильно ли открыт порт 443 в группе безопасности.',
        SSLConfig: 'Настройки сертификата',
        SSLProConfig: 'Настройки протокола',
        supportProtocol: 'Версия протокола',
        encryptionAlgorithm: 'Алгоритм шифрования',
        notSecurity: '(небезопасно)',
        encryptHelper:
            "Let's Encrypt имеет ограничение частоты выдачи сертификатов, но его достаточно для нормальных потребностей. Слишком частые операции приведут к сбою выдачи. Конкретные ограничения см. в <a target='_blank' href='https://letsencrypt.org/zh-cn/docs/rate-limits/'>официальной документации</a>",
        ipValue: 'Значение',
        ext: 'расширение файла',
        wafInputHelper: 'Вводите данные по строкам, одна строка',
        data: 'данные',
        ever: 'постоянно',
        nextYear: 'Через год',
        noLog: 'Логи не найдены',
        defaultServer: 'Сайт по умолчанию',
        noDefaultServer: 'Не установлен',
        defaultServerHelper:
            'После установки сайта по умолчанию все несвязанные доменные имена и IP-адреса будут перенаправлены на сайт по умолчанию\nЭто может эффективно предотвратить вредоносное разрешение\nОднако это также может привести к сбою блокировки неавторизованных доменных имен WAF',
        restoreHelper: 'Вы уверены, что хотите восстановить из этой резервной копии?',
        websiteDeploymentHelper:
            'Используйте установленное приложение или создайте новое приложение для создания веб-сайта.',
        websiteStatictHelper: 'Создать директорию веб-сайта на хосте.',
        websiteProxyHelper:
            'Использовать обратный прокси для проксирования существующей службы. Например, если служба установлена и работает на порту 8080, адрес прокси будет "http://127.0.0.1:8080".',
        runtimeProxyHelper: 'Использовать среду выполнения веб-сайта для создания веб-сайта.',
        runtime: 'Среда выполнения',
        deleteRuntimeHelper:
            'Приложение среды выполнения необходимо удалять вместе с веб-сайтом, пожалуйста, обращайтесь с этим осторожно',
        proxyType: 'Тип сети',
        unix: 'Unix сеть',
        tcp: 'TCP/IP сеть',
        phpFPM: 'Конфигурация FPM',
        phpConfig: 'Конфигурация PHP',
        updateConfig: 'Обновить конфигурацию',
        isOn: 'Вкл',
        isOff: 'Выкл',
        rewrite: 'Псевдостатика',
        rewriteMode: 'Схема',
        current: 'Текущий',
        rewriteHelper:
            'Если установка псевдостатики делает веб-сайт недоступным, попробуйте вернуться к настройкам по умолчанию.',
        runDir: 'Рабочая директория',
        runUserHelper:
            'Для веб-сайтов, развернутых через среду выполнения контейнера PHP, необходимо установить владельца и группу пользователей всех файлов и папок в index и поддиректориях на 1000. Для локальной среды PHP обратитесь к локальным настройкам пользователя и группы PHP-FPM',
        userGroup: 'Пользователь/Группа',
        uGroup: 'Группа',
        proxyPath: 'Путь прокси',
        proxyPass: 'Целевой URL',
        cache: 'Кэш',
        cacheTime: 'Длительность кэширования',
        enableCache: 'Кэш',
        proxyHost: 'Прокси хост',
        disabled: 'Остановлен',
        startProxy: 'Это запустит обратный прокси. Хотите продолжить?',
        stopProxy: 'Это остановит обратный прокси. Хотите продолжить?',
        sourceFile: 'Источник',
        proxyHelper1: 'При доступе к этой директории будет возвращено и отображено содержимое целевого URL.',
        proxyPassHelper: 'Целевой URL должен быть действительным и доступным.',
        proxyHostHelper: 'Передать доменное имя в заголовке запроса прокси-серверу.',
        replacementHelper: 'Можно добавить до 5 замен, оставьте пустым, если замена не требуется.',
        modifier: 'Правила сопоставления',
        modifierHelper: 'Пример: "=" точное совпадение, "~" регулярное совпадение, "^~" совпадение начала пути и т.д.',
        replace: 'Замены текста',
        addReplace: 'Добавить',
        replaced: 'Искомая строка (не может быть пустой)',
        replaceText: 'Заменить на строку',
        replacedErr: 'Искомая строка не может быть пустой',
        replacedErr2: 'Искомая строка не может повторяться',
        basicAuth: 'Базовая аутентификация',
        editBasicAuthHelper:
            'Пароль асимметрично зашифрован и не может быть показан. При редактировании нужно сбросить пароль',
        antiLeech: 'Анти-лич',
        extends: 'Расширение',
        browserCache: 'Кэш',
        leechLog: 'Записывать лог анти-лича',
        accessDomain: 'Разрешенные домены',
        leechReturn: 'Ответ ресурса',
        noneRef: 'Разрешить пустой referrer',
        disable: 'не включено',
        disableLeechHelper: 'Отключить ли анти-лич',
        disableLeech: 'Отключить анти-лич',
        ipv6: 'Прослушивать IPv6',
        leechReturnError: 'Пожалуйста, заполните HTTP код статуса',
        selectAcme: 'Выберите Acme аккаунт',
        imported: 'Создан вручную',
        importType: 'Тип импорта',
        pasteSSL: 'Вставить код',
        localSSL: 'Выбрать файл сервера',
        privateKeyPath: 'Файл приватного ключа',
        certificatePath: 'Файл сертификата',
        ipWhiteListHelper: 'Роль белого списка IP: все правила недействительны для белого списка IP',
        redirect: 'Перенаправление',
        sourceDomain: 'Исходный домен',
        targetURL: 'Целевой URL адрес',
        keepPath: 'URI параметры',
        path: 'путь',
        redirectType: 'тип перенаправления',
        redirectWay: 'Способ',
        keep: 'сохранить',
        notKeep: 'Не сохранять',
        redirectRoot: 'Перенаправить на главную страницу',
        redirectHelper: '301 постоянное перенаправление, 302 временное перенаправление',
        changePHPVersionWarn:
            'Переключение версии PHP удалит оригинальный контейнер PHP (смонтированный код веб-сайта не будет потерян), продолжить?',
        changeVersion: 'Переключить версию',
        retainConfig: 'Сохранить ли файлы php-fpm.conf и php.ini',
        runDirHelper2: 'Пожалуйста, убедитесь, что вторичная рабочая директория находится в директории index',
        openrestyHelper:
            'OpenResty порт HTTP по умолчанию: {0} порт HTTPS: {1}, что может повлиять на доступ к доменному имени веб-сайта и принудительное перенаправление HTTPS',
        primaryDomainHelper: 'Пример: example.com или example.com:8080',
        acmeAccountType: 'Тип аккаунта',
        keyType: 'Алгоритм ключа',
        tencentCloud: 'Tencent Cloud',
        containWarn: 'Доменное имя содержит основной домен, пожалуйста, введите заново',
        rewriteHelper2:
            'Приложения типа WordPress, установленные из магазина приложений, обычно поставляются с предустановленной конфигурацией псевдостатики. Их перенастройка может привести к ошибкам.',
        websiteBackupWarn:
            'Поддерживается только импорт локальных резервных копий, импорт резервных копий с других машин может привести к сбою восстановления',
        ipWebsiteWarn:
            'Веб-сайты с IP в качестве домена должны быть установлены как сайт по умолчанию для нормального доступа.',
        hstsHelper: 'Включение HSTS может повысить безопасность веб-сайта',
        includeSubDomains: 'Поддомены',
        hstsIncludeSubDomainsHelper:
            'После включения политика HSTS будет применяться ко всем поддоменам текущего домена.',
        defaultHtml: 'Страница по умолчанию',
        website404: 'Страница ошибки 404 веб-сайта',
        domain404: 'Домен веб-сайта не существует',
        indexHtml: 'Индекс для статического веб-сайта',
        stopHtml: 'Остановленный веб-сайт',
        indexPHP: 'Индекс для PHP веб-сайта',
        sslExpireDate: 'Дата истечения сертификата',
        website404Helper:
            'Страница ошибки 404 веб-сайта поддерживается только для веб-сайтов со средой выполнения PHP и статических веб-сайтов',
        sni: 'Origin SNI',
        sniHelper:
            'Когда бэкенд обратного прокси использует HTTPS, может потребоваться установить origin SNI. Подробности см. в документации провайдера CDN.',
        huaweicloud: 'Huawei Cloud',
        rcreateDb: 'Создать Базу Данных',
        enableSSLHelper: 'Неудача при включении SSL не повлияет на создание сайта.',
        batchAdd: 'Пакетное Добавление Доменов',
        generateDomain: 'Сгенерировать',
        global: 'Глобальный',
        subsite: 'Подсайт',
        subsiteHelper:
            'Подсайт может выбрать каталог существующего PHP или статического сайта в качестве корневого каталога.',
        parentWebsite: 'Родительский Сайт',
        deleteSubsite: 'Чтобы удалить текущий сайт, сначала необходимо удалить подсайт {0}.',
        loadBalance: 'Балансировка Нагрузки',
        server: 'Узел',
        algorithm: 'Алгоритм',
        ipHash: 'IP Хэш',
        ipHashHelper:
            'Распределяет запросы на определенный сервер на основе IP-адреса клиента, гарантируя, что конкретный клиент всегда направляется на один и тот же сервер.',
        leastConn: 'Наименьшее Количество Соединений',
        leastConnHelper: 'Отправляет запросы на сервер с наименьшим количеством активных соединений.',
        leastTime: 'Наименьшее Время',
        leastTimeHelper: 'Отправляет запросы на сервер с наименьшим временем активного соединения.',
        defaultHelper:
            'Метод по умолчанию, запросы равномерно распределяются между серверами. Если сервер имеет настройку веса, запросы распределяются в соответствии с указанным весом. Серверы с большим весом получают больше запросов.',
        weight: 'Вес',
        maxFails: 'Максимальное Количество Ошибок',
        maxConns: 'Максимальное Количество Соединений',
        strategy: 'Стратегия',
        strategyDown: 'Отключить',
        strategyBackup: 'Резервный',
        staticChangePHPHelper: 'В настоящее время статический сайт, можно переключить на PHP сайт.',
        proxyCache: 'Кэш Обратного Прокси',
        cacheLimit: 'Ограничение Пространства Кэша',
        shareCache: 'Размер Памяти для Подсчета Кэша',
        cacheExpire: 'Время Истечения Кэша',
        shareCacheHelper: '1M памяти может хранить примерно 8000 объектов кэша.',
        cacheLimitHelper: 'Превышение лимита автоматически удалит старые кэши.',
        cacheExpireHelper: 'Кэши, не попавшие в срок истечения, будут удалены.',
        realIP: 'Реальный IP',
        ipFrom: 'Источник IP',
        ipFromHelper:
            'Настроив доверенные источники IP, OpenResty проанализирует информацию об IP в HTTP-заголовке, чтобы точно идентифицировать и записать реальный IP-адрес посетителя, включая журналы доступа.',
        ipFromExample1:
            'Если фронтенд — это инструмент, такой как Frp, вы можете указать IP-адрес Frp, например, 127.0.0.1.',
        ipFromExample2: 'Если фронтенд — это CDN, вы можете указать диапазон IP-адресов CDN.',
        ipFromExample3:
            'Если вы не уверены, вы можете указать 0.0.0.0/0 (IPv4) или ::/0 (IPv6). [Примечание: Разрешение любого источника IP небезопасно.]',
        http3Helper:
            'HTTP/3 — это обновленная версия HTTP/2, обеспечивающая более высокую скорость соединения и лучшую производительность. Однако не все браузеры поддерживают HTTP/3, и его включение может привести к тому, что некоторые браузеры не смогут получить доступ к сайту.',
        changeDatabase: 'Сменить Базу Данных',
        changeDatabaseHelper1: 'Связь базы данных используется для резервного копирования и восстановления сайта.',
        changeDatabaseHelper2:
            'Переключение на другую базу данных может сделать предыдущие резервные копии невосстановимыми.',
        saveCustom: 'Сохранить как Шаблон',
        rainyun: 'Rainyun',
        volcengine: 'Volcengine',
        runtimePortHelper: 'O ambiente de runtime atual possui várias portas. Por favor, selecione uma porta de proxy.',
        runtimePortWarn: 'В текущей среде выполнения нет портов, невозможно проксировать',
        cacheWarn: 'Пожалуйста, сначала выключите кэш в обратном прокси',
        loadBalanceHelper:
            'После создания балансировки нагрузки, пожалуйста, перейдите в "Обратный прокси", добавьте прокси и установите адрес бэкенда на: http://<название балансировки нагрузки>.',
        favorite: 'Избранное',
        cancelFavorite: 'Отменить избранное',
        useProxy: 'Использовать прокси',
        useProxyHelper: 'Использовать адрес прокси-сервера в настройках панели',
        westCN: 'Западный цифровой',
        openBaseDir: 'Предотвращение межсайтовых атак',
        openBaseDirHelper:
            'open_basedir используется для ограничения пути доступа к файлам PHP, что помогает предотвратить межсайтовый доступ и повысить безопасность',
        serverCacheTime: 'Время кеширования на сервере',
        serverCacheTimeHelper:
            'Время, в течение которого запрос кешируется на сервере. В этот период идентичные запросы будут возвращать кешированный результат напрямую, без запроса к исходному серверу.',
        browserCacheTime: 'Время кеширования в браузере',
        browserCacheTimeHelper:
            'Время, в течение которого статические ресурсы кешируются локально в браузере, уменьшая повторные запросы. Пользователи будут использовать локальный кеш напрямую, если срок его действия не истек при обновлении страницы.',
        donotLinkeDB: 'Не связывать с базой данных',
        toWebsiteDir: 'Перейти в каталог сайта',
    },
    php: {
        short_open_tag: 'Поддержка коротких тегов',
        max_execution_time: 'Максимальное время выполнения скрипта',
        max_input_time: 'Максимальное время ввода',
        memory_limit: 'Лимит памяти скрипта',
        post_max_size: 'Максимальный размер данных POST',
        file_uploads: 'Разрешить загрузку файлов',
        upload_max_filesize: 'Максимальный разрешенный размер загружаемых файлов',
        max_file_uploads: 'Максимальное количество файлов, разрешенных для одновременной загрузки',
        default_socket_timeout: 'Тайм-аут сокета',
        error_reporting: 'Уровень ошибок',
        display_errors: 'Выводить подробную информацию об ошибках',
        cgi_fix_pathinfo: 'Включить pathinfo',
        date_timezone: 'Часовой пояс',
        disableFunction: 'Отключить функции',
        disableFunctionHelper: 'Введите функции для отключения, например exec, разделяйте запятыми',
        uploadMaxSize: 'Ограничение загрузки',
        indexHelper:
            'Для обеспечения нормальной работы PHP-сайта разместите код в директории index и избегайте переименования',
        extensions: 'Шаблоны расширений',
        extension: 'Расширение',
        extensionHelper: 'Используйте несколько расширений, разделяйте запятыми',
        toExtensionsList: 'Просмотр списка расширений',
        containerConfig: 'Конфигурация контейнера',
        containerConfigHelper:
            'Переменные окружения и другие данные можно изменить в разделе Конфигурация - Конфигурация контейнера после создания',
        dateTimezoneHelper: 'Пример: TZ=Asia/Shanghai (Пожалуйста, добавьте по мере необходимости)',
    },
    nginx: {
        serverNamesHashBucketSizeHelper: 'Размер хэш-таблицы для имен серверов',
        clientHeaderBufferSizeHelper: 'Размер буфера заголовка для запросов клиента',
        clientMaxBodySizeHelper: 'Максимальный размер загружаемого файла',
        keepaliveTimeoutHelper: 'Тайм-аут соединения',
        gzipMinLengthHelper: 'Минимальный размер сжатого файла',
        gzipCompLevelHelper: 'Степень сжатия',
        gzipHelper: 'Включить сжатие для передачи',
        connections: 'Активные соединения',
        accepts: 'Принято',
        handled: 'Обработано',
        requests: 'Запросы',
        reading: 'Чтение',
        writing: 'Запись',
        waiting: 'Ожидание',
        status: 'Текущий статус',
        configResource: 'Конфигурация',
        saveAndReload: 'Сохранить и перезагрузить',
        clearProxyCache: 'Очистить кэш обратного прокси',
        clearProxyCacheWarn:
            'Это повлияет на все веб-сайты с настроенным кэшем и перезапустит "OpenResty". Хотите продолжить?',
        create: 'Создать модуль',
        update: 'Редактировать модуль',
        params: 'Параметры',
        packages: 'Пакеты',
        script: 'Скрипт',
        module: 'Модуль',
        build: 'Сборка',
        buildWarn:
            'Сборка OpenResty требует резервирования определенного количества CPU и памяти, процесс может занять много времени, пожалуйста, подождите.',
        mirrorUrl: 'Источник программного обеспечения',
        paramsHelper: 'Например: --add-module=/tmp/ngx_brotli',
        packagesHelper: 'Например: git,curl разделенные запятыми',
        scriptHelper:
            'Скрипт, выполняемый перед компиляцией, обычно для загрузки исходного кода модуля, установки зависимостей и т.д.',
        buildHelper:
            'Нажмите Сборка после добавления/изменения модуля. Успешная сборка автоматически перезапустит OpenResty.',
        defaultHttps: 'HTTPS Анти-вмешательство',
        defaultHttpsHelper1: 'Включение этого параметра может решить проблему вмешательства в HTTPS.',
    },
    ssl: {
        create: 'Запросить',
        provider: 'Тип',
        manualCreate: 'Создан вручную',
        acmeAccount: 'ACME аккаунт',
        resolveDomain: 'Разрешить доменное имя',
        err: 'Ошибка',
        value: 'значение записи',
        dnsResolveHelper: 'Пожалуйста, добавьте следующие записи разрешения у провайдера DNS:',
        detail: 'Подробности',
        msg: 'Информация',
        ssl: 'Сертификат',
        key: 'Закрытый ключ',
        startDate: 'Время начала действия',
        organization: 'организация-издатель',
        renewConfirm: 'Это обновит сертификат для доменного имени {0}. Хотите продолжить?',
        autoRenew: 'Автопродление',
        autoRenewHelper: 'Автоматически продлевать за 30 дней до истечения срока',
        renewSuccess: 'Успешно продлено',
        renewWebsite:
            'Этот сертификат связан со следующими веб-сайтами, и заявка будет применена к этим сайтам одновременно',
        createAcme: 'Создать аккаунт',
        acmeHelper: 'Acme аккаунт используется для запроса бесплатных сертификатов',
        upload: 'Импорт',
        applyType: 'Тип',
        apply: 'Продлить',
        applyStart: 'Начало запроса сертификата',
        getDnsResolve: 'Получение значения DNS-разрешения, пожалуйста, подождите...',
        selfSigned: 'Самоподписанный CA',
        ca: 'Центр сертификации',
        commonName: 'Общее имя',
        caName: 'Имя центра сертификации',
        company: 'Название организации',
        department: 'Название подразделения',
        city: 'Название населенного пункта',
        province: 'Название штата или области',
        country: 'Название страны (2-буквенный код)',
        commonNameHelper: 'Например, ',
        selfSign: 'Выпустить сертификат',
        days: 'срок действия',
        domainHelper: 'Одно доменное имя в строке, поддерживает * и IP-адрес',
        pushDir: 'Отправить сертификат в локальную директорию',
        dir: 'Директория',
        pushDirHelper: 'В этой директории будут созданы файл сертификата "fullchain.pem" и файл ключа "privkey.pem".',
        organizationDetail: 'Детали организации',
        fromWebsite: 'С веб-сайта',
        dnsMauanlHelper:
            'В режиме ручного разрешения необходимо нажать кнопку применить после создания для получения значения DNS-разрешения',
        httpHelper:
            'Использование режима HTTP требует установки OpenResty и не поддерживает запрос сертификатов с подстановочными доменными именами.',
        buypassHelper: 'Buypass недоступен в материковом Китае',
        googleHelper: 'Как получить EAB HmacKey и EAB kid',
        googleCloudHelper: 'Google Cloud API недоступен в большинстве регионов материкового Китая',
        skipDNSCheck: 'Пропустить проверку DNS',
        skipDNSCheckHelper: 'Отметьте здесь только если возникает проблема тайм-аута при запросе сертификата.',
        cfHelper: 'Не использовать Global API Key',
        deprecated: 'будет устарелым',
        deprecatedHelper:
            'Обслуживание остановлено и может быть удалено в будущей версии. Пожалуйста, используйте метод Tencent Cloud для анализа',
        disableCNAME: 'Отключить CNAME',
        disableCNAMEHelper: 'Отметьте здесь, если доменное имя имеет запись CNAME и запрос не удается.',
        nameserver: 'DNS сервер',
        nameserverHelper: 'Использовать пользовательский DNS сервер для проверки доменных имен.',
        edit: 'Редактировать сертификат',
        execShell: 'Выполнить скрипт после запроса сертификата.',
        shell: 'Содержимое скрипта',
        shellHelper:
            'Директория выполнения скрипта по умолчанию - директория установки 1Panel. Если сертификат отправляется в локальную директорию, директорией выполнения будет директория отправки сертификата. Тайм-аут выполнения по умолчанию - 30 минут.',
        customAcme: 'Пользовательская служба ACME',
        customAcmeURL: 'URL службы ACME',
        baiduCloud: 'Baidu Cloud',
    },
    firewall: {
        create: 'Создать правило',
        edit: 'Редактировать правило',
        ccDeny: 'CC защита',
        ipWhiteList: 'Белый список IP',
        ipBlockList: 'Черный список IP',
        fileExtBlockList: 'Черный список расширений файлов',
        urlWhiteList: 'Белый список URL',
        urlBlockList: 'Черный список URL',
        argsCheck: 'Проверка GET параметров',
        postCheck: 'Проверка POST параметров',
        cookieBlockList: 'Черный список Cookie',

        dockerHelper:
            'Межсетевой экран Linux "{0}" не может отключить сопоставление портов Docker. Приложение может редактировать параметры на странице "Магазин приложений -> Установленные" для контроля открытия портов.',
        quickJump: 'Быстрый доступ',
        used: 'Используется',
        unUsed: 'Не используется',
        firewallHelper: '{0} межсетевой экран',
        firewallNotStart: 'Межсетевой экран в настоящее время не включен. Сначала включите его.',
        restartFirewallHelper: 'Эта операция перезапустит текущий межсетевой экран. Хотите продолжить?',
        stopFirewallHelper: 'Это лишит сервер защиты безопасности. Хотите продолжить?',
        startFirewallHelper:
            'После включения межсетевого экрана безопасность сервера будет лучше защищена. Хотите продолжить?',
        noPing: 'Отключить ping',
        noPingTitle: 'Отключить ping',
        noPingHelper: 'Это отключит ping, и сервер не будет отвечать на ICMP-запросы. Хотите продолжить?',
        onPingHelper: 'Это включит ping, и хакеры смогут обнаружить ваш сервер. Хотите продолжить?',
        changeStrategy: 'Изменить стратегию {0}',
        changeStrategyIPHelper1:
            'Изменить стратегию IP-адреса на [deny]. После установки IP-адреса доступ к серверу будет запрещен. Хотите продолжить?',
        changeStrategyIPHelper2:
            'Изменить стратегию IP-адреса на [allow]. После установки IP-адреса нормальный доступ будет восстановлен. Хотите продолжить?',
        changeStrategyPortHelper1:
            'Изменить политику портов на [drop]. После установки политики портов внешний доступ будет запрещен. Хотите продолжить?',
        changeStrategyPortHelper2:
            'Изменить политику портов на [accept]. После установки политики портов нормальный доступ к портам будет восстановлен. Хотите продолжить?',
        stop: 'Остановить',
        portFormatError: 'Это поле должно быть действительным портом.',
        portHelper1: 'Несколько портов, например 8080 и 8081',
        portHelper2: 'Диапазон портов, например 8080-8089',
        changeStrategyHelper:
            'Изменить стратегию {0} [{1}] на [{2}]. После установки {0} будет иметь внешний доступ {2}. Хотите продолжить?',
        portHelper: 'Можно ввести несколько портов, например 80,81, или диапазон портов, например 80-88',
        strategy: 'Стратегия',
        accept: 'Принять',
        drop: 'Отбросить',
        anyWhere: 'Любой',
        address: 'Указанные IP',
        addressHelper: 'Поддерживает IP-адрес или сегмент IP',
        allow: 'Разрешить',
        deny: 'Запретить',
        addressFormatError: 'Это поле должно быть действительным IP-адресом.',
        addressHelper1: 'Поддерживает IP-адрес или диапазон IP. Например, "************" или "***********/24".',
        addressHelper2: 'Для нескольких IP-адресов разделяйте запятой. Например, "************, **********/24".',
        allIP: 'Все IP',
        portRule: 'Правило | Правила',
        createPortRule: '@:commons.button.create @.lower:firewall.portRule',
        forwardRule: 'Правило переадресации портов | Правила переадресации портов',
        createForwardRule: '@:commons.button.create @:firewall.forwardRule',
        ipRule: 'IP правило | IP правила',
        createIpRule: '@:commons.button.create @:firewall.ipRule',
        userAgent: 'Фильтр User-Agent',
        sourcePort: 'Исходный порт',
        targetIP: 'Целевой IP',
        targetPort: 'Целевой порт',
        forwardHelper1:
            'Если вы хотите перенаправить на локальный порт, целевой IP должен быть установлен как "127.0.0.1".',
        forwardHelper2: 'Оставьте целевой IP пустым для перенаправления на локальный порт.',
        forwardHelper3: 'Поддерживается только переадресация портов IPv4.',
    },
    runtime: {
        runtime: 'Среда выполнения',
        workDir: 'Рабочая директория',
        create: 'Создать среду выполнения',
        localHelper: 'Локальная среда выполнения должна быть установлена самостоятельно',
        versionHelper: 'Версия PHP, например v8.0',
        buildHelper:
            'Чем больше расширений, тем выше загрузка процессора при создании образа. Расширения можно установить после создания среды.',
        openrestyWarn: 'PHP требует обновления OpenResty до версии ******** или выше для использования',
        toupgrade: 'Обновить',
        edit: 'Редактировать среду выполнения',
        extendHelper:
            'Неназванные расширения можно вручную ввести и выбрать. Например, введите "sockets" и выберите первый вариант из выпадающего списка, чтобы увидеть список расширений.',
        rebuildHelper: 'После редактирования расширения необходимо пересобрать PHP приложение для применения изменений',
        rebuild: 'Пересобрать PHP приложение',
        source: 'Источник расширений PHP',
        ustc: 'Научно-технический университет Китая',
        netease: 'Netease',
        aliyun: 'Alibaba Cloud',
        default: 'по умолчанию',
        tsinghua: 'Университет Цинхуа',
        xtomhk: 'Зеркало XTOM (Гонконг)',
        xtom: 'Зеркало XTOM (Глобальное)',
        phpsourceHelper: 'Выберите подходящий источник в соответствии с вашей сетевой средой.',
        appPort: 'Порт приложения',
        externalPort: 'Внешний порт',
        packageManager: 'Менеджер пакетов',
        codeDir: 'Директория кода',
        appPortHelper: 'Порт, используемый приложением.',
        externalPortHelper: 'Порт, открытый для внешнего мира.',
        runScript: 'Скрипт запуска',
        runScriptHelper: 'Список команд запуска анализируется из файла package.json в исходной директории.',
        open: 'Открыть',
        operatorHelper: 'Операция {0} будет выполнена для выбранной среды выполнения. Хотите продолжить?',
        taobao: 'Taobao',
        tencent: 'Tencent',
        imageSource: 'Источник образа',
        moduleManager: 'Управление модулями',
        module: 'Модуль',
        nodeOperatorHelper:
            'Выполнить {0} модуля {1}? Операция может вызвать аномалии в среде выполнения, пожалуйста, подтвердите перед продолжением',
        customScript: 'Пользовательская команда запуска',
        customScriptHelper: 'Укажите полную команду запуска. Например, "npm run start".',
        portError: 'Не повторяйте один и тот же порт.',
        systemRestartHelper: 'Описание статуса: Прерывание - получение статуса не удалось из-за перезагрузки системы',
        javaScriptHelper: 'Укажите полную команду запуска. Например, "java -jar halo.jar -Xmx1024M -Xms256M".',
        javaDirHelper: 'Директория должна содержать jar файлы, поддиректории также допустимы',
        goHelper: 'Укажите полную команду запуска. Например, "go run main.go" или "./main".',
        goDirHelper: 'Директория или поддиректория должна содержать файлы Go или бинарные файлы.',
        pythonHelper:
            'Укажите полную команду запуска. Например, "pip install -r requirements.txt && python manage.py runserver 0.0.0.0:5000".',
        dotnetHelper: 'Пожалуйста, укажите полную команду запуска, например, dotnet MyWebApp.dll',
        dirHelper: 'Примечание: Укажите путь к каталогу внутри контейнера',
        concurrency: 'Схема параллелизма',
        loadStatus: 'Состояние нагрузки',
    },
    process: {
        pid: 'ID процесса',
        ppid: 'Родительский PID',
        numThreads: 'Потоки',
        memory: 'Память',
        diskRead: 'Чтение диска',
        diskWrite: 'Запись диска',
        netSent: 'исходящий',
        netRecv: 'входящий',
        numConnections: 'Соединения',
        startTime: 'Время запуска',
        state: 'Состояние',
        running: 'Работает',
        sleep: 'сон',
        stop: 'остановлен',
        idle: 'простой',
        zombie: 'зомби-процесс',
        wait: 'ожидание',
        lock: 'блокировка',
        blocked: 'заблокирован',
        cmdLine: 'Команда запуска',
        basic: 'Основное',
        mem: 'Память',
        openFiles: 'Открытые файлы',
        env: 'Переменные окружения',
        noenv: 'Нет',
        net: 'Сетевые подключения',
        laddr: 'Исходный адрес/порт',
        raddr: 'Целевой адрес/порт',
        stopProcess: 'Завершить',
        viewDetails: 'Подробности',
        stopProcessWarn: 'Вы уверены, что хотите завершить этот процесс (PID:{0})?',
        processName: 'Имя процесса',
    },
    tool: {
        supervisor: {
            loadStatusErr: 'Не удалось получить статус процесса, пожалуйста, проверьте состояние службы supervisor.',
            notSupport:
                'Служба Supervisor не обнаружена. Перейдите на страницу библиотеки скриптов для ручной установки',
            list: 'Демон-процесс | Демон-процессы',
            config: 'Конфигурация Supervisor',
            primaryConfig: 'Расположение основного конфигурационного файла',
            notSupportCtl:
                'supervisorctl не обнаружен. Перейдите на страницу библиотеки скриптов для ручной установки.',
            user: 'Пользователь',
            command: 'Команда',
            dir: 'Директория',
            numprocs: 'Кол-во процессов',
            initWarn:
                'Это изменит значение "files" в секции "[include"] в основном конфигурационном файле. Директория других конфигурационных файлов будет: "{директория установки 1Panel}/1panel/tools/supervisord/supervisor.d/".',
            operatorHelper: 'Операция {1} будет выполнена для {0}, продолжить?',
            uptime: 'Время работы',
            notStartWarn: 'Supervisor не запущен. Сначала запустите его.',
            serviceName: 'Имя службы',
            initHelper:
                'Обнаружен сервис Supervisor, но он не инициализирован. Нажмите кнопку инициализации в верхней панели состояния для настройки.',
            serviceNameHelper: 'Имя службы Supervisor, управляемой systemctl, обычно supervisor или supervisord',
            restartHelper:
                'Это перезапустит службу после инициализации, что приведет к остановке всех существующих демон-процессов.',
            RUNNING: 'Работает',
            STOPPED: 'Остановлен',
            STOPPING: 'Останавливается',
            STARTING: 'Запускается',
            FATAL: 'Не удалось запустить',
            BACKOFF: 'Исключение при запуске',
            ERROR: 'Ошибка',
            statusCode: 'Код статуса',
            manage: 'Управление',
            autoRestart: 'Автоматический перезапуск',
            EXITED: 'Вышел',
            autoRestartHelper: 'Автоматически перезапускать программу после её аварийного завершения',
            autoStart: 'Автозапуск',
            autoStartHelper: 'Автоматически запускать сервис после запуска Supervisor',
        },
    },
    xpack: {
        expiresTrialAlert:
            'Дружеское напоминание: ваша пробная версия Pro истечет через {0} дней, и все функции Pro станут недоступны. Пожалуйста, своевременно продлите или обновите до полной версии.',
        expiresAlert:
            'Дружеское напоминание: ваша лицензия Pro истечет через {0} дней, и все функции Pro станут недоступны. Пожалуйста, продлите лицензию вовремя, чтобы обеспечить дальнейшее использование.',
        menu: 'Рro',
        upage: 'AI Конструктор сайтов',
        app: {
            app: 'APP',
            title: 'Псевдоним панели',
            titleHelper: 'Псевдоним панели используется для отображения в приложении (псевдоним панели по умолчанию)',
            qrCode: 'QR код',
            apiStatusHelper: 'APP панели необходимо включить функцию API интерфейса',
            apiInterfaceHelper:
                'Поддержка доступа к API-интерфейсу панели (эта функция должна быть включена для мобильного приложения панели)',
            apiInterfaceHelper1:
                'Для доступа к приложению панели необходимо добавить посетителя в белый список. Для нестабильных IP рекомендуется добавить 0.0.0.0/0 (все IPv4), ::/0 (все IPv6)',
            qrCodeExpired: 'Время обновления',
            apiLeakageHelper: 'Не раскрывайте QR-код. Убедитесь, что он используется только в доверенных средах.',
        },
        waf: {
            name: 'WAF',
            blackWhite: 'Черный и белый список',
            globalSetting: 'Глобальные настройки',
            websiteSetting: 'Настройки сайта',
            blockRecords: 'Записи блокировки',
            world: 'Мир',
            china: 'Китай',
            intercept: 'Перехват',
            request: 'Запросы',
            count4xx: 'Количество 4xx',
            count5xx: 'Количество 5xx',
            todayStatus: 'Статус на сегодня',
            reqMap: 'Карта атак (последние 30 дней)',
            resource: 'Источник',
            count: 'Количество',
            hight: 'Высокий',
            low: 'Низкий',
            reqCount: 'Запросы',
            interceptCount: 'Количество перехватов',
            requestTrends: 'Тренды запросов (последние 7 дней)',
            interceptTrends: 'Тренды перехватов (последние 7 дней)',
            whiteList: 'Белый список',
            blackList: 'Черный список',
            ipBlackListHelper: 'IP-адреса в черном списке блокируются от доступа к сайту',
            ipWhiteListHelper: 'IP-адреса в белом списке обходят все ограничения',
            uaBlackListHelper: 'Запросы с значениями User-Agent в черном списке будут заблокированы',
            uaWhiteListHelper: 'Запросы с значениями User-Agent в белом списке обходят все ограничения',
            urlBlackListHelper: 'Запросы к URL-адресам в черном списке будут заблокированы',
            urlWhiteListHelper: 'Запросы к URL-адресам в белом списке обходят все ограничения',
            ccHelper:
                'Если сайт получает более {1} запросов с одного IP-адреса в течение {0} секунд, этот IP будет заблокирован на {2}',
            blockTime: 'Время блокировки',
            attackHelper: 'Если кумулятивные перехваты превышают {1} за {0} секунд, IP будет заблокирован на {2}',
            notFoundHelper:
                'Если кумулятивные запросы возвращают ошибку 404 более {1} раз за {0} секунд, IP будет заблокирован на {2}',
            frequencyLimit: 'Ограничение частоты',
            regionLimit: 'Ограничение по региону',
            defaultRule: 'Правила по умолчанию',
            accessFrequencyLimit: 'Ограничение частоты доступа',
            attackLimit: 'Ограничение частоты атак',
            notFoundLimit: 'Ограничение частоты 404',
            urlLimit: 'Ограничение частоты URL',
            urlLimitHelper: 'Установите частоту доступа для одного URL',
            sqliDefense: 'Защита от SQL-инъекций',
            sqliHelper: 'Обнаружение SQL-инъекций в запросах и их блокировка',
            xssHelper: 'Обнаружение XSS в запросах и их блокировка',
            xssDefense: 'Защита от XSS',
            uaDefense: 'Правила для вредоносных User-Agent',
            uaHelper: 'Включает правила для определения общих вредоносных ботов',
            argsDefense: 'Правила для вредоносных параметров',
            argsHelper: 'Блокирует запросы, содержащие вредоносные параметры',
            cookieDefense: 'Правила для вредоносных Cookies',
            cookieHelper: 'Запрещает передачу вредоносных Cookies в запросах',
            headerDefense: 'Правила для вредоносных заголовков',
            headerHelper: 'Запрещает запросы, содержащие вредоносные заголовки',
            httpRule: 'Правила HTTP-запросов',
            httpHelper:
                'Установите типы методов, которые могут получить доступ. Если вы хотите ограничить определенные типы доступа, отключите эти кнопки. Например: если разрешен только доступ GET, то нужно отключить другие методы, кроме GET',
            geoRule: 'Ограничения по региону',
            geoHelper:
                'Ограничьте доступ к вашему сайту с определенных регионов. Например, если доступ разрешен только из Китая, запросы из-за пределов Китая будут заблокированы',
            ipLocation: 'Местоположение IP',
            action: 'Действие',
            ruleType: 'Тип атаки',
            ipHelper: 'Введите IP-адрес',
            attackLog: 'Журнал атак',
            rule: 'Правило',
            ipArr: 'Диапазон IPV4',
            ipStart: 'Начальный IP',
            ipEnd: 'Конечный IP',
            ipv4: 'IPv4',
            ipv6: 'IPv6',
            urlDefense: 'Правила для URL',
            urlHelper: 'Запрещенный URL',
            dirFilter: 'Фильтрация по директориям',
            sqlInject: 'SQL-инъекция',
            xss: 'XSS',
            phpExec: 'Выполнение PHP-скриптов',
            oneWordTrojan: 'Троян одно слово',
            appFilter: 'Фильтрация опасных директорий',
            webshell: 'Webshell',
            args: 'Вредоносные параметры',
            protocolFilter: 'Фильтр протоколов',
            javaFilter: 'Фильтрация опасных файлов Java',
            scannerFilter: 'Фильтрация сканеров',
            escapeFilter: 'Фильтрация экранирования',
            customRule: 'Пользовательские правила',
            httpMethod: 'Фильтрация HTTP-методов',
            fileExt: 'Ограничение на загрузку файлов',
            fileExtHelper: 'Запрещенные расширения файлов для загрузки',
            deny: 'Запрещено',
            allow: 'Разрешить',
            field: 'Объект',
            pattern: 'Условие',
            ruleContent: 'Содержание',
            contain: 'содержит',
            equal: 'равно',
            regex: 'регулярное выражение',
            notEqual: 'Не равно',
            customRuleHelper: 'Применяйте действия на основе указанных условий',
            actionAllow: 'Разрешить',
            blockIP: 'Заблокировать IP',
            code: 'Код статуса ответа',
            noRes: 'Отключить (444)',
            badReq: 'Неверные параметры (400)',
            forbidden: 'Запрещено (403)',
            serverErr: 'Ошибка сервера (500)',
            resHtml: 'Страница ответа',
            allowHelper: 'Разрешение доступа пропустит последующие WAF-правила, используйте с осторожностью',
            captcha: 'Проверка человеком',
            fiveSeconds: '5-секундная проверка',
            location: 'Регион',
            redisConfig: 'Конфигурация Redis',
            redisHelper: 'Включите Redis для сохранения временно заблокированных IP-адресов',
            wafHelper: 'Все сайты потеряют защиту после отключения',
            attackIP: 'IP атакующего',
            attackParam: 'Детали атаки',
            execRule: 'Срабатывающее правило',
            acl: 'ACL',
            sql: 'SQL-инъекция',
            cc: 'Ограничение частоты доступа',
            isBlocking: 'Заблокирован',
            isFree: 'Не заблокирован',
            unLock: 'Разблокировать',
            unLockHelper: 'Вы хотите разблокировать IP: {0}?',
            saveDefault: 'Сохранить по умолчанию',
            saveToWebsite: 'Применить к сайту',
            saveToWebsiteHelper: 'Применить текущие настройки ко всем сайтам?',
            websiteHelper:
                'Это настройки по умолчанию для создания сайта. Изменения должны быть применены к сайту, чтобы вступить в силу',
            websiteHelper2:
                'Это настройки по умолчанию для создания сайта. Пожалуйста, измените конкретную конфигурацию на сайте',
            ipGroup: 'Группа IP',
            ipGroupHelper:
                'По одному IP или диапазону IP на строку, поддерживает IPv4 и IPv6, например: *********** или ***********/24',
            ipBlack: 'Черный список IP',
            openRestyAlert: 'Версия OpenResty должна быть выше {0}',
            initAlert:
                'Необходима инициализация для первого использования, файл конфигурации сайта будет изменен, и оригинальная конфигурация WAF будет потеряна. Пожалуйста, сделайте резервную копию OpenResty заранее',
            initHelper: 'Инициализация удалит текущую конфигурацию WAF. Вы уверены, что хотите инициализировать?',
            mainSwitch: 'Главный переключатель',
            websiteAlert: 'Пожалуйста, создайте сайт сначала',
            defaultUrlBlack: 'Правила для URL',
            htmlRes: 'Страница перехвата',
            urlSearchHelper: 'Введите URL для поддержки нечеткого поиска',
            toCreate: 'Создать',
            closeWaf: 'Закрыть WAF',
            closeWafHelper: 'Закрытие WAF приведет к потере защиты сайта, продолжить?',
            addblack: 'Черный',
            addwhite: 'Добавить белый',
            addblackHelper: 'Добавить IP:{0} в черный список по умолчанию?',
            addwhiteHelper: 'Добавить IP:{0} в белый список по умолчанию?',
            defaultUaBlack: 'Правило для User-Agent',
            defaultIpBlack: 'Вредоносная группа IP',
            cookie: 'Правила для Cookies',
            urlBlack: 'Черный список URL',
            uaBlack: 'Черный список User-Agent',
            attackCount: 'Ограничение частоты атак',
            fileExtCheck: 'Ограничение на загрузку файлов',
            geoRestrict: 'Ограничение по региону',
            attacklog: 'Запись перехвата',
            unknownWebsite: 'Неавторизованный доступ к домену',
            geoRuleEmpty: 'Регион не может быть пустым',
            unknown: 'Сайт не существует',
            geo: 'Ограничение по региону',
            revertHtml: 'Вы хотите восстановить {0} как страницу по умолчанию?',
            five_seconds: '5-секундная проверка',
            header: 'Правила заголовков',
            methodWhite: 'HTTP-правила',
            expiryDate: 'Дата истечения',
            expiryDateHelper: 'После прохождения проверки она больше не будет выполняться в пределах срока действия',
            defaultIpBlackHelper: 'Некоторые вредоносные IP-адреса, собранные с интернета, чтобы предотвратить доступ',
            notFoundCount: 'Ограничение частоты 404',
            matchValue: 'Значение для совпадения',
            headerName: 'Поддерживает английский, цифры, -, длина 3-30',
            cdnHelper: 'Сайты, использующие CDN, могут включить это для получения правильного исходного IP',
            clearLogWarn: 'Очистка журнала невозможна, хотите продолжить?',
            commonRuleHelper: 'Правило поддерживает нечеткое совпадение',
            blockIPHelper:
                'Заблокированные IP-адреса временно хранятся в OpenResty и будут разблокированы при перезапуске OpenResty. Они могут быть заблокированы навсегда через функцию блокировки',
            addWhiteUrlHelper: 'Добавить URL {0} в белый список?',
            dashHelper:
                'В общественной версии также можно использовать функции в глобальных настройках и настройках сайта',
            wafStatusHelper: 'WAF не включен, пожалуйста, включите его в глобальных настройках',
            ccMode: 'Режим',
            global: 'Глобальный режим',
            uriMode: 'Режим URL',
            globalHelper:
                'Глобальный режим: активируется, когда общее количество запросов к любому URL за определенный промежуток времени превышает порог',
            uriModeHelper:
                'Режим URL: активируется, когда количество запросов к одному URL за определенный промежуток времени превышает порог',

            ip: 'Черный список IP',
            globalSettingHelper:
                'Настройки с тегом [Website] должны быть включены в [Настройки сайта], а глобальные настройки являются только настройками по умолчанию для новых сайтов',
            globalSettingHelper2:
                'Настройки должны быть включены как в [Глобальных настройках], так и в [Настройках сайта]',
            urlCCHelper: 'Более {1} запросов к этому URL за {0} секунд, блокируем этот IP {2}',
            urlCCHelper2: 'URL не может содержать параметры',
            notContain: 'Не содержит',
            urlcc: 'Ограничение частоты URL',
            method: 'Тип запроса',
            addIpsToBlock: 'Массовая блокировка IP',
            addUrlsToWhite: 'Массовое добавление URL в белый список',
            noBlackIp: 'IP уже заблокирован, не нужно блокировать снова',
            noWhiteUrl: 'URL уже в белом списке, не нужно добавлять снова',
            spiderIpHelper:
                'Включает Baidu, Bing, Google, 360, Shenma, Sogou, ByteDance, DuckDuckGo, Yandex. Закрытие этого параметра заблокирует доступ всех пауков.',
            spiderIp: 'Пул IP-адресов поисковых роботов',
            geoIp: 'Библиотека IP-адресов',
            geoIpHelper: 'Используется для определения географического положения IP',
            stat: 'Отчет об атаках',
            statTitle: 'Отчет',
            attackIp: 'IP-адрес атаки',
            attackCountNum: 'Количество атак',
            percent: 'Процент',
            addblackUrlHelper: 'Добавить URL: {0} в черный список по умолчанию?',
            rce: 'Удаленное выполнение кода',
            software: 'Программное обеспечение',
            cveHelper: 'Содержит уязвимости распространенных программ и фреймворков',
            vulnCheck: 'Дополнительные правила',
            ssrf: 'Уязвимость SSRF',
            afr: 'Чтение произвольных файлов',
            ua: 'Неавторизованный доступ',
            id: 'Утечка информации',
            aa: 'Обход аутентификации',
            dr: 'Обход каталогов',
            xxe: 'Уязвимость XXE',
            suid: 'Уязвимость сериализации',
            dos: 'Уязвимость отказа в обслуживании',
            afd: 'Загрузка произвольных файлов',
            sqlInjection: 'Внедрение SQL',
            afw: 'Запись произвольных файлов',
            il: 'Утечка информации',
            clearAllLog: 'Очистить все логи',
            exportLog: 'Экспортировать логи',
            appRule: 'Правила приложений',
            appRuleHelper:
                'Распространенные правила приложений, включение может уменьшить ложные срабатывания, один сайт может использовать только одно правило',
            logExternal: 'Исключить типы записей',
            ipWhite: 'Белый список IP',
            urlWhite: 'Белый список URL',
            uaWhite: 'Белый список User-Agent',
            logExternalHelper:
                'Исключенные типы записей не будут регистрироваться в логах, черные/белые списки, региональные ограничения доступа и пользовательские правила генерируют много логов, рекомендуется исключить',
            ssti: 'Атака SSTI',
            crlf: 'Инъекция CRLF',
            strict: 'Строгий режим',
            strictHelper: 'Использует более строгие правила для проверки запросов',
            saveLog: 'Сохранить лог',
            remoteURLHelper: 'Удаленный URL должен содержать один IP на строку и не содержать других символов',
            notFound: 'Not Found (404)',
            serviceUnavailable: 'Сервис недоступен (503)',
            gatewayTimeout: 'Тайм-аут шлюза (504)',
            belongToIpGroup: 'Принадлежит к группе IP',
            notBelongToIpGroup: 'Не принадлежит к группе IP',
            unknownWebsiteKey: 'Неизвестный домен',
            special: 'Специальный',
        },
        monitor: {
            name: 'Мониторинг веб-сайта',
            pv: 'Просмотры страниц',
            uv: 'Уникальные посетители',
            flow: 'Трафик',
            ip: 'IP',
            spider: 'Поисковые роботы',
            visitors: 'Тренды посетителей',
            today: 'Сегодня',
            last7days: 'Последние 7 дней',
            last30days: 'Последние 30 дней',
            uvMap: 'Карта посетителей (30-е число)',
            qps: 'Запросы в реальном времени (в минуту)',
            flowSec: 'Трафик в реальном времени (в минуту)',
            excludeCode: 'Исключить статус-коды',
            excludeUrl: 'Исключить URL',
            excludeExt: 'Исключить расширения',
            cdnHelper: 'Получение реального IP из заголовка, предоставленного CDN',
            reqRank: 'Рейтинг посещений',
            refererDomain: 'Домен-реферер',
            os: 'Операционная система',
            browser: 'Браузер/Клиент',
            device: 'Устройство',
            showMore: 'Подробнее',
            unknown: 'Прочее',
            pc: 'Компьютер',
            mobile: 'Мобильное устройство',
            wechat: 'WeChat',
            machine: 'Машина',
            tencent: 'Браузер Tencent',
            ucweb: 'UC Browser',
            '2345explorer': 'Браузер 2345',
            huaweibrowser: 'Браузер Huawei',
            log: 'Логи запросов',
            statusCode: 'Статус-код',
            requestTime: 'Время отклика',
            flowRes: 'Трафик отклика',
            method: 'Метод запроса',
            statusCodeHelper: 'Введите статус-код выше',
            statusCodeError: 'Неверный тип статус-кода',
            methodHelper: 'Введите метод запроса выше',
            all: 'Все',
            baidu: 'Baidu',
            google: 'Google',
            bing: 'Bing',
            bytes: 'Заголовки сегодня',
            sogou: 'Sogou',
            failed: 'Ошибка',
            ipCount: 'Количество IP',
            spiderCount: 'Запросы от поисковых роботов',
            averageReqTime: 'Среднее время отклика',
            totalFlow: 'Общий трафик',
            logSize: 'Размер файла лога',
            realIPType: 'Метод получения реального IP',
            fromHeader: 'Получение из заголовка HTTP',
            fromHeaders: 'Получение из списка заголовков',
            header: 'HTTP-заголовок',
            cdnConfig: 'Конфигурация CDN',
            xff1: 'Первичный прокси из X-Forwarded-For',
            xff2: 'Вторичный прокси из X-Forwarded-For',
            xff3: 'Третичный прокси из X-Forwarded-For',
            xffHelper:
                'Например: X-Forwarded-For: <client>,<proxy1>,<proxy2>,<proxy3>. Последний IP <proxy3> будет считаться прокси верхнего уровня',
            headersHelper:
                'Получение реального IP из распространённых заголовков CDN HTTP, выбирая первое доступное значение',
            monitorCDNHelper: 'Изменение конфигурации CDN для мониторинга веб-сайта также обновит настройки WAF CDN',
            wafCDNHelper: 'Изменение конфигурации WAF CDN также обновит настройки мониторинга веб-сайта',
            statusErr: 'Неверный формат статус-кода',
            shenma: 'Shenma Search',
            duckduckgo: 'DuckDuckGo',
            '360': '360 Search',
            excludeUri: 'Исключить URI',
            top100Helper: 'Показать топ 100 данных',
            logSaveDay: 'Период хранения логов (дни)',
            cros: 'Chrome OS',
            theworld: 'Браузер TheWorld',
            edge: 'Microsoft Edge',
            maxthon: 'Браузер Maxthon',
            monitorStatusHelper: 'Мониторинг не включён, пожалуйста, включите его в настройках',
            excludeIp: 'Исключить IP-адреса',
            excludeUa: 'Исключить User-Agent',
            remotePort: 'Удаленный порт',
            unknown_browser: 'Неизвестно',
            unknown_os: 'Неизвестно',
            unknown_device: 'Неизвестно',
            logSaveSize: 'Максимальный размер сохранения логов',
            logSaveSizeHelper: 'Это размер сохранения логов для одного сайта',
            '360se': '360 Secure Browser',
            websites: 'Список веб-сайтов',
            trend: 'Статистика тренда',
            reqCount: 'Количество запросов',
            uriHelper: 'Вы можете использовать /test/* или /*/index.php для исключения Uri',
        },
        tamper: {
            tamper: 'Защита от подделки сайта',
            ignoreTemplate: 'Исключить шаблон каталога',
            protectTemplate: 'Защитить шаблон файла',
            templateContent: 'Содержимое шаблона',
            template: 'Шаблон',
            tamperHelper1:
                'Для сайтов с развертыванием в один клик рекомендуется включить функцию защиты от подделки каталога приложений; если сайт не работает должным образом или возникают проблемы с резервным копированием и восстановлением, сначала отключите функцию защиты от подделки;',
            tamperHelper2:
                'Будут ограничены операции чтения, записи, удаления, изменения прав и владельца защищенных файлов вне исключенных каталогов',
            tamperPath: 'Защищаемый каталог',
            tamperPathEdit: 'Изменить путь',
            log: 'Журнал перехвата',
            totalProtect: 'Общая защита',
            todayProtect: 'Защита сегодня',
            addRule: 'Добавить правило',
            ignore: 'Исключить каталог',
            ignoreHelper: 'По одному на строку, например: \ntmp\n./tmp',
            ignoreTemplateHelper:
                'Добавьте имена папок, которые нужно игнорировать, разделяя их запятыми, например: tmp,cache',
            templateRule: 'Длина 1-512, имя не должно содержать символы, такие как {0}',
            ignoreHelper1: 'Добавьте имена папок или конкретные пути, которые нужно игнорировать',
            ignoreHelper2: 'Чтобы игнорировать конкретную папку, используйте относительный путь, начинающийся с ./',
            protect: 'Защитить файл',
            protectHelper: 'По одному на строку, например: \npng\n./test.css',
            protectTemplateHelper:
                'Добавьте имена файлов или расширения, которые нужно игнорировать, разделяя их запятыми, например: conf,.css',
            protectHelper1: 'Можно указать имя файла, расширение или конкретный файл для защиты',
            protectHelper2: 'Чтобы защитить конкретный файл, используйте относительный путь, начинающийся с ./',
            enableHelper:
                'Скоро будет включена функция защиты от подделки для следующих сайтов для повышения безопасности сайта, продолжить?',
            disableHelper: 'Скоро будет отключена функция защиты от подделки для следующих сайтов, продолжить?',
        },
        setting: {
            setting: 'Настройки Панели',
            title: 'Описание Панели',
            titleHelper:
                'Будет отображаться на странице входа пользователя (например, панель управления Linux сервером, рекомендуется от 8 до 15 символов)',
            logo: 'Логотип (Без Текста)',
            logoHelper:
                'Будет отображаться в верхнем левом углу страницы управления при свёрнутом меню (рекомендуемый размер изображения: 82px*82px)',
            logoWithText: 'Логотип (С Текстом)',
            logoWithTextHelper:
                'Будет отображаться в верхнем левом углу страницы управления при развернутом меню (рекомендуемый размер изображения: 185px*55px)',
            favicon: 'Иконка Сайта',
            faviconHelper: 'Иконка сайта (рекомендуемый размер изображения: 16px*16px)',
            reUpload: 'Выбрать Файл',
            setDefault: 'Восстановить По Умолчанию',
            setHelper: 'Текущие настройки будут сохранены. Вы хотите продолжить?',
            setDefaultHelper: 'Все настройки панели будут восстановлены по умолчанию. Вы хотите продолжить?',
            logoGroup: 'Логотип',
            imageGroup: 'Изображение',
            loginImage: 'Изображение страницы входа',
            loginImageHelper: 'Будет отображаться на странице входа (Рекомендуемый размер: 500x416px)',
            loginBgType: 'Тип фона страницы входа',
            loginBgImage: 'Фоновое изображение страницы входа',
            loginBgImageHelper: 'Будет отображаться как фон страницы входа (Рекомендуемый размер: 1920x1080px)',
            loginBgColor: 'Цвет фона страницы входа',
            loginBgColorHelper: 'Будет отображаться как цвет фона страницы входа',
            image: 'Изображение',
            bgColor: 'Цвет фона',
            loginGroup: 'Страница входа',
            loginBtnLinkColor: 'Цвет кнопки/ссылки',
            loginBtnLinkColorHelper: 'Будет отображаться как цвет кнопки/ссылки на странице входа',
        },
        helper: {
            wafTitle1: 'Карта Перехватов',
            wafContent1: 'Отображает географическое распределение перехватов за последние 30 дней',
            wafTitle2: 'Ограничения Регионального Доступа',
            wafContent2: 'Ограничивайте источники доступа к сайту в зависимости от географического расположения',
            wafTitle3: 'Пользовательская Страница Перехвата',
            wafContent3: 'Создайте пользовательскую страницу для отображения после перехвата запроса',
            wafTitle4: 'Пользовательские Правила (ACL)',
            wafContent4: 'Перехватывайте запросы в соответствии с пользовательскими правилами',

            tamperTitle1: 'Мониторинг Целостности Файлов',
            tamperContent1:
                'Мониторинг целостности файлов веб-сайта, включая основные файлы, скрипты и файлы конфигурации.',
            tamperTitle2: 'Реальное Сканирование и Обнаружение',
            tamperContent2:
                'Обнаруживайте аномальные или измененные файлы, выполняя сканирование файловой системы веб-сайта в реальном времени.',
            tamperTitle3: 'Настройки Разрешений Безопасности',
            tamperContent3:
                'Ограничивайте доступ к файлам веб-сайта с помощью правильных настроек разрешений и политик контроля доступа, снижая потенциальную поверхность атаки.',
            tamperTitle4: 'Ведение Логов и Анализ',
            tamperContent4:
                'Записывайте логи доступа и операций с файлами для последующего аудита и анализа администраторами, а также для выявления потенциальных угроз безопасности.',

            settingTitle1: 'Пользовательское Приветственное Сообщение',
            settingContent1: 'Установите пользовательское приветственное сообщение на странице входа в 1Panel.',
            settingTitle2: 'Пользовательский Логотип',
            settingContent2: 'Разрешите загрузку изображений логотипов, содержащих названия брендов или другой текст.',
            settingTitle3: 'Пользовательская Иконка Сайта',
            settingContent3:
                'Разрешите загрузку пользовательских иконок для замены стандартной иконки браузера, улучшая пользовательский опыт.',

            monitorTitle1: 'Тренд Посетителей',
            monitorContent1: 'Статистика и отображение тенденций посещаемости веб-сайта',
            monitorTitle2: 'Карта Посетителей',
            monitorContent2: 'Статистика и отображение географического распределения посетителей веб-сайта',
            monitorTitle3: 'Статистика Доступа',
            monitorContent3:
                'Статистика запросов веб-сайта, включая поисковые боты, устройства доступа, статус запросов и т. д.',
            monitorTitle4: 'Мониторинг в Реальном Времени',
            monitorContent4:
                'Мониторинг запросов веб-сайта в реальном времени, включая количество запросов, трафик и т. д.',

            alertTitle1: 'SMS Уведомления',
            alertContent1:
                'При аномальном использовании ресурсов сервера, истечении срока действия сайта и сертификата, появлении новой версии или истечении срока действия пароля пользователи будут уведомлены через SMS, чтобы обеспечить своевременную обработку.',
            alertTitle2: 'Журнал Уведомлений',
            alertContent2:
                'Предоставляет пользователям возможность просмотра журналов уведомлений, чтобы облегчить отслеживание и анализ исторических событий.',
            alertTitle3: 'Настройки Уведомлений',
            alertContent3:
                'Предоставляет пользователям возможность настройки номеров телефонов, частоты и времени отправки уведомлений в день, что позволяет настраивать более удобные уведомления.',

            nodeTitle1: 'Добавление узла одним кликом',
            nodeContent1: 'Быстро интегрируйте несколько серверных узлов',
            nodeTitle2: 'Пакетное обновление',
            nodeContent2: 'Синхронизируйте и обновите все узлы одной операцией',
            nodeTitle3: 'Мониторинг статуса узла',
            nodeContent3: 'Реальное наблюдение за рабочим статусом каждого узла',
            nodeTitle4: 'Быстрое удаленное подключение',
            nodeContent4: 'Одним кликом подключитесь к удаленным терминалам узлов',

            fileExchangeTitle1: 'Передача с аутентификацией по ключу',
            fileExchangeContent1: 'Аутентифицируйтесь через SSH-ключи, чтобы обеспечить безопасность передачи.',
            fileExchangeTitle2: 'Эффективная синхронизация файлов',
            fileExchangeContent2:
                'Только синхронизируйте измененное содержимое, чтобы значительно повысить скорость и стабильность передачи.',
            fileExchangeTitle3: 'Поддержка обмена между несколькими узлами',
            fileExchangeContent3:
                'Легко передавайте проектные файлы между разными узлами, гибко управляйте несколькими серверами.',

            appTitle1: 'Гибкое управление панелью',
            appContent1: 'Легко управляйте сервером 1Panel в любое время и в любом месте.',
            appTitle2: 'Полная информация о сервисе',
            appContent2:
                'Управляйте приложениями, сайтами, Docker, базами данных и т. д. через мобильное приложение и создавайте приложения и сайты быстро.',
            appTitle3: 'Мониторинг аномалий в реальном времени',
            appContent3:
                'Просматривайте статус сервера, мониторинг безопасности WAF, статистику посещений сайта и состояние процессов в реальном времени через мобильное приложение.',

            clusterTitle1: 'Развертывание мастер-слейв',
            clusterContent1:
                'Поддерживает создание мастер-слейв экземпляров MySQL/Postgres/Redis на разных узлах, автоматически завершая связь мастер-слейв и инициализацию',
            clusterTitle2: 'Управление мастер-слейв',
            clusterContent2:
                'Единая страница для централизованного управления несколькими узлами мастер-слейв, просмотр их ролей, статуса выполнения и т.д.',
            clusterTitle3: 'Состояние репликации',
            clusterContent3:
                'Отображает состояние репликации мастер-слейв и информацию о задержке, помогая в устранении проблем синхронизации',
        },
        node: {
            master: 'Главный узел',
            masterBackup: 'Резервная копия главного узла',
            backupNode: 'Резервный узел',
            backupFrequency: 'Частота резервного копирования (часы)',
            backupCopies: 'Количество сохраняемых резервных копий',
            noBackupNode:
                'Резервный узел в настоящее время пуст. Выберите резервный узел для сохранения и повторите попытку!',
            masterBackupAlert:
                'Резервное копирование главного узла не настроено. Для обеспечения безопасности данных, пожалуйста, настройте резервный узел как можно скорее, чтобы можно было вручную переключиться на новый главный узел в случае сбоя.',
            node: 'Узел',
            addr: 'Адрес',
            nodeUnhealthy: 'Некорректное состояние узла',
            deletedNode: 'Удалённый узел {0} в настоящее время не поддерживает операции обновления!',
            nodeUnhealthyHelper:
                'Обнаружено некорректное состояние узла. Проверьте в [Управление узлами] и повторите попытку!',
            nodeUnbind: 'Узел не привязан к лицензии',
            nodeUnbindHelper:
                'Обнаружено, что узел не привязан к лицензии. Привяжите в меню [Настройки панели - Лицензия] и повторите попытку!',
            memTotal: 'Общая память',
            nodeManagement: 'Управление узлом',
            addNode: 'Добавить узел',
            connInfo: 'Информация о подключении',
            nodeInfo: 'Информация об узле',
            syncInfo: 'Синхронизация данных,',
            syncHelper:
                'При изменении данных главного узла, происходит синхронизация с этим дочерним узлом в реальном времени,',
            syncBackupAccount: 'Настройки резервной учётной записи',
            syncWithMaster:
                'После обновления до Pro все данные будут синхронизироваться по умолчанию. Политики синхронизации можно настроить вручную в управлении узлами.',
            syncProxy: 'Настройки системного прокси',
            syncProxyHelper: 'Синхронизация настроек системного прокси требует перезапуска Docker',
            syncProxyHelper1: 'Перезапуск Docker может повлиять на работающие сервисы контейнеров.',
            syncProxyHelper2: 'Вы можете перезапустить вручную на странице Контейнеры - Конфигурация.',
            syncProxyHelper3:
                'Синхронизация настроек системного прокси требует перезапуска Docker, что может повлиять на работающие сервисы контейнеров',
            syncProxyHelper4:
                'Синхронизация настроек системного прокси требует перезапуска Docker. Вы можете перезапустить вручную позже на странице Контейнеры - Конфигурация.',
            syncCustomApp: 'Синхронизировать пользовательский репозиторий приложений',
            syncAlertSetting: 'Настройки системных предупреждений',
            syncNodeInfo: 'Базовые данные узла,',
            nodeSyncHelper: 'Синхронизация информации о узле будет синхронизировать следующую информацию:',
            nodeSyncHelper1: '1. Информация о публичной резервной учетной записи',
            nodeSyncHelper2: '2. Информация о соединении между основным узлом и подузлами',

            nodeCheck: 'Проверка доступности',
            checkSSH: 'Проверить SSH-подключение узла',
            checkUserPermission: 'Проверка прав пользователя узла',
            isNotRoot:
                'Обнаружено, что sudo без пароля не поддерживается на этом узле и текущий пользователь не является root',
            checkLicense: 'Проверить статус лицензии узла',
            checkService: 'Проверить информацию о существующих службах на узле',
            checkPort: 'Проверить доступность порта узла',
            panelExist:
                'Обнаружено, что на этом узле работает служба 1Panel V1. Перед добавлением обновите до V2 с помощью скрипта миграции.',
            coreExist:
                'Текущий узел уже активирован как мастер-узел и не может быть добавлен напрямую как подчинённый узел. Пожалуйста, сначала понизьте его до подчинённого узла перед добавлением, обратитесь к документации для подробностей.',
            agentExist:
                'Обнаружено, что 1panel-agent уже установлен на этом узле. Продолжение сохранит существующие данные и заменит только службу 1panel-agent.',
            oldDataExist:
                'Обнаружены исторические данные 1Panel V2 на этом узле. Текущие настройки будут перезаписаны следующей информацией:',
            errLicense: 'Лицензия, привязанная к этому узлу, недоступна. Пожалуйста, проверьте и повторите попытку!',
            errNodePort:
                'Обнаружено, что порт узла [ {0} ] недоступен. Проверьте, разрешен ли этот порт в брандмауэре или группе безопасности.',

            reinstallHelper: 'Переустановить узел {0}, вы хотите продолжить?',
            unhealthyCheck: 'Проверка на неисправности',
            fixOperation: 'Решение проблемы',
            checkName: 'Элемент проверки',
            checkSSHConn: 'Проверка доступности SSH-соединения',
            fixSSHConn: 'Вручную отредактируйте узел, чтобы подтвердить информацию о подключении',
            checkConnInfo: 'Проверка информации о подключении агента',
            checkStatus: 'Проверка доступности службы узла',
            fixStatus: 'Запустите "systemctl status 1panel-agent.service", чтобы проверить, запущена ли служба.',
            checkAPI: 'Проверка доступности API узла',
            fixAPI: 'Проверьте журналы узла и убедитесь, что порты брандмауэра правильно открыты.',
            forceDelete: 'Принудительное удаление',
            operateHelper: 'Будет выполнена операция {0} для следующих узлов, вы хотите продолжить?',
            forceDeleteHelper:
                'Принудительное удаление проигнорирует ошибки удаления узла и удалит метаданные базы данных',
            uninstall: 'Удалить данные узла',
            uninstallHelper: 'Это удалит все данные 1Panel, связанные с узлом. Выбирайте осторожно!',
            baseDir: 'Каталог установки',
            baseDirHelper: 'Если каталог установки пуст, по умолчанию он будет установлен в каталоге /opt',
            nodePort: 'Порт узла',
            offline: 'Автономный режим',
            freeCount: 'Бесплатная квота [{0}]',
            offlineHelper: 'Используется, когда узел находится в автономной среде',
        },
        customApp: {
            name: 'Пользовательское хранилище приложений',
            appStoreType: 'Источник пакета App Store',
            appStoreUrl: 'URL хранилища',
            local: 'Локальный путь',
            remote: 'Удаленная ссылка',
            imagePrefix: 'Префикс образа',
            imagePrefixHelper:
                'Функция: Настройка префикса образа и изменение поля образа в файле compose. Например, если префикс образа установлен как 1panel/custom, поле образа для MaxKB изменится на 1panel/custom/maxkb:v1.10.0',
            closeHelper: 'Отменить использование пользовательского хранилища приложений',
            appStoreUrlHelper: 'Поддерживается только формат .tar.gz',
            postNode: 'Синхронизировать с подузлом',
            postNodeHelper:
                'Синхронизируйте пользовательский пакет магазина с tmp/customApp/apps.tar.gz в каталоге установки дочернего узла',
            nodes: 'Выбрать узлы',
            selectNode: 'Выбрать узел',
            selectNodeError: 'Пожалуйста, выберите узел',
            licenseHelper: 'Профессиональная версия поддерживает функцию пользовательского репозитория приложений',
        },
        alert: {
            isAlert: 'Оповещение',
            alertCount: 'Количество оповещений',
            clamHelper: 'Отправлять оповещение при обнаружении зараженных файлов',
            cronJobHelper: 'Отправлять оповещение при сбое выполнения задачи',
            licenseHelper: 'Профессиональная версия поддерживает SMS-оповещения',
            alertCountHelper: 'Максимальная дневная частота оповещений',
            alert: 'SMS Уведомление',
            logs: 'Журнал Уведомлений',
            list: 'Список Уведомлений',
            addTask: 'Создать Уведомление',
            editTask: 'Редактировать Уведомление',
            alertMethod: 'Метод',
            alertMsg: 'Сообщение Уведомления',
            alertRule: 'Правила Уведомлений',
            titleSearchHelper: 'Введите название уведомления для нечеткого поиска',
            taskType: 'Тип',
            ssl: 'Срок действия сертификата (SSL)',
            siteEndTime: 'Истечение срока действия сайта',
            panelPwdEndTime: 'Истечение срока действия пароля панели',
            panelUpdate: 'Доступна новая версия панели',
            cpu: 'Уведомление о загрузке процессора сервера',
            memory: 'Уведомление о памяти сервера',
            load: 'Уведомление о нагрузке сервера',
            disk: 'Уведомление о диске сервера',
            website: 'Веб-сайт',
            certificate: 'SSL Сертификат',
            remainingDays: 'Оставшиеся дни',
            sendCount: 'Количество Отправок',
            sms: 'SMS',
            wechat: 'WeChat',
            dingTalk: 'DingTalk',
            feiShu: 'FeiShu',
            mail: 'Электронная Почта',
            weCom: 'WeCom',
            sendCountRulesHelper: 'Общее количество уведомлений до истечения срока действия (раз в день)',
            panelUpdateRulesHelper: 'Общее количество уведомлений для новой версии панели (раз в день)',
            oneDaySendCountRulesHelper: 'Максимальное количество уведомлений в день',
            siteEndTimeRulesHelper: 'Сайты с неограниченным сроком действия не будут вызывать уведомления',
            autoRenewRulesHelper:
                'Сертификаты с включенным автоматическим продлением и оставшимися днями менее 31 не будут вызывать уведомления',
            panelPwdEndTimeRulesHelper:
                'Уведомления об истечении срока действия пароля панели недоступны, если срок действия не задан',
            sslRulesHelper: 'Все SSL Сертификаты',
            diskInfo: 'Диск',
            monitoringType: 'Тип Мониторинга',
            autoRenew: 'Автопродление',
            useDisk: 'Использование Диска',
            usePercentage: 'Процент Использования',
            changeStatus: 'Изменить Статус',
            disableMsg:
                'Остановка задачи уведомления предотвратит отправку сообщений этой задачей. Вы хотите продолжить?',
            enableMsg: 'Включение задачи уведомления позволит этой задаче отправлять сообщения. Вы хотите продолжить?',
            useExceed: 'Использование Превышает',
            useExceedRulesHelper: 'Отправить уведомление, если использование превышает установленное значение',
            cpuUseExceedAvg: 'Среднее использование процессора превышает заданное значение',
            memoryUseExceedAvg: 'Среднее использование памяти превышает заданное значение',
            loadUseExceedAvg: 'Средняя нагрузка превышает заданное значение',
            cpuUseExceedAvgHelper: 'Среднее использование процессора за указанное время превышает заданное значение',
            memoryUseExceedAvgHelper: 'Среднее использование памяти за указанное время превышает заданное значение',
            loadUseExceedAvgHelper: 'Средняя нагрузка за указанное время превышает заданное значение',
            resourceAlertRulesHelper: 'Примечание: Непрерывные уведомления в течение 30 минут отправят только одно SMS',
            specifiedTime: 'Указанное Время',
            deleteTitle: 'Удалить Уведомление',
            deleteMsg: 'Вы уверены, что хотите удалить задачу уведомления?',

            allSslTitle: 'Уведомления об истечении срока действия всех SSL сертификатов сайтов',
            sslTitle: 'Уведомление об истечении срока действия SSL сертификата для сайта {0}',
            allSiteEndTimeTitle: 'Уведомления об истечении срока действия всех сайтов',
            siteEndTimeTitle: 'Уведомление об истечении срока действия сайта {0}',
            panelPwdEndTimeTitle: 'Уведомление об истечении срока действия пароля панели',
            panelUpdateTitle: 'Уведомление о новой версии панели',
            cpuTitle: 'Уведомление о высокой загрузке процессора',
            memoryTitle: 'Уведомление о высокой загрузке памяти',
            loadTitle: 'Уведомление о высокой нагрузке',
            diskTitle: 'Уведомление о высокой загрузке диска для точки монтирования {0}',
            allDiskTitle: 'Уведомление о высокой загрузке диска',

            timeRule:
                'Оставшееся время менее {0} дней (если не обработано, будет отправлено повторно на следующий день)',
            panelUpdateRule:
                'Отправить уведомление один раз при обнаружении новой версии панели (если не обработано, будет отправлено повторно на следующий день)',
            avgRule: 'Среднее использование {1} превышает {2}% в течение {0} минут, отправляется {3} раз в день',
            diskRule: 'Использование диска для точки монтирования {0} превышает {1}{2}, отправляется {3} раз в день',
            allDiskRule: 'Использование диска превышает {0}{1}, отправляется {2} раз в день',

            cpuName: 'Процессор',
            memoryName: 'Память',
            loadName: 'Нагрузка',
            diskName: 'Диск',

            syncAlertInfo: 'Ручная отправка',
            syncAlertInfoMsg: 'Вы хотите вручную отправить задачу уведомления?',
            pushError: 'Не удалось отправить',
            pushSuccess: 'Отправка успешна',
            syncError: 'Ошибка синхронизации',
            success: 'Уведомление успешно',
            pushing: 'В процессе отправки...',
            error: 'Ошибка оповещения',
            cleanLog: 'Очистить логи',
            cleanAlertLogs: 'Очистить журналы уведомлений',
            daily: 'Ежедневное количество уведомлений: {0}',
            cumulative: 'Общее количество уведомлений: {0}',
            clams: 'Антивирусная проверка',
            taskName: 'Имя задачи',
            cronJobType: 'Тип задачи',
            clamPath: 'Директория проверки',
            cronjob: 'Задача Cron',
            app: 'Резервное копирование приложения',
            web: 'Резервное копирование сайта',
            database: 'Резервное копирование базы данных',
            directory: 'Резервное копирование директории',
            log: 'Резервное копирование логов',
            snapshot: 'Системный снимок',
            clamsRulesHelper: 'Задачи сканирования на вирусы, требующие уведомлений',
            cronJobRulesHelper: 'Этот тип планируемой задачи требует конфигурации',
            clamsTitle: 'Задача сканирования на вирусы 「 {0} 」 обнаружила заражённый файл',
            cronJobAppTitle: 'Задача Cron - ошибка задачи резервного копирования приложения 「 {0} 」',
            cronJobWebsiteTitle: 'Задача Cron - ошибка задачи резервного копирования сайта 「 {0} 」',
            cronJobDatabaseTitle: 'Задача Cron - ошибка задачи резервного копирования базы данных 「 {0} 」',
            cronJobDirectoryTitle: 'Задача Cron - ошибка задачи резервного копирования директории 「 {0} 」',
            cronJobLogTitle: 'Задача Cron - ошибка задачи резервного копирования логов 「 {0} 」',
            cronJobSnapshotTitle: 'Задача Cron - ошибка задачи резервного копирования снимка системы 「 {0} 」',
            cronJobShellTitle: 'Задача Cron - ошибка выполнения скрипта Shell 「 {0} 」',
            cronJobCurlTitle: 'Задача Cron - ошибка доступа к URL 「 {0} 」',
            cronJobCutWebsiteLogTitle: 'Задача Cron - ошибка задачи нарезки логов сайта 「 {0} 」',
            cronJobCleanTitle: 'Задача Cron - ошибка задачи очистки кэша 「 {0} 」',
            cronJobNtpTitle: 'Задача Cron - ошибка задачи синхронизации времени сервера 「 {0} 」',
            clamsRule: 'Уведомление об обнаружении заражённого файла, отправляется {0} раз в день',
            cronJobAppRule:
                'Уведомление об ошибке задачи резервного копирования приложения, отправляется {0} раз в день',
            cronJobWebsiteRule:
                'Уведомление об ошибке задачи резервного копирования сайта, отправляется {0} раз в день',
            cronJobDatabaseRule:
                'Уведомление об ошибке задачи резервного копирования базы данных, отправляется {0} раз в день',
            cronJobDirectoryRule:
                'Уведомление об ошибке задачи резервного копирования директории, отправляется {0} раз в день',
            cronJobLogRule: 'Уведомление об ошибке задачи резервного копирования логов, отправляется {0} раз в день',
            cronJobSnapshotRule:
                'Уведомление об ошибке задачи резервного копирования снимка системы, отправляется {0} раз в день',
            cronJobShellRule: 'Уведомление об ошибке выполнения скрипта Shell, отправляется {0} раз в день',
            cronJobCurlRule: 'Уведомление об ошибке задачи доступа к URL, отправляется {0} раз в день',
            cronJobCutWebsiteLogRule: 'Уведомление об ошибке задачи нарезки логов сайта, отправляется {0} раз в день',
            cronJobCleanRule: 'Уведомление об ошибке задачи очистки кэша, отправляется {0} раз в день',
            cronJobNtpRule: 'Уведомление об ошибке задачи синхронизации времени сервера, отправляется {0} раз в день',
            alertSmsHelper: 'Лимит SMS: всего {0} сообщений, уже использовано {1}',
            goBuy: 'Купить больше',
            phone: 'Телефон',
            phoneHelper: 'Укажите реальный номер телефона для получения уведомлений',
            dailyAlertNum: 'Дневной Лимит Уведомлений',
            dailyAlertNumHelper: 'Максимальное количество уведомлений в день (до 100)',
            timeRange: 'Диапазон Времени',
            sendTimeRange: 'Временной интервал отправки',
            sendTimeRangeHelper: 'Можно отправлять в диапазоне {0}',
            to: 'до',
            startTime: 'Время Начала',
            endTime: 'Время Завершения',
            defaultPhone: 'По умолчанию используется номер телефона, привязанный к лицензии',
            noticeAlert: 'Уведомление',
            resourceAlert: 'Уведомление о Ресурсах',
            agentOfflineAlertHelper:
                'При включении офлайн-оповещений для узла главный узел будет каждые 30 минут выполнять проверку и запускать задачи оповещения.',
            offline: 'Оповещение об отключении',
            offlineHelper:
                'Если выбрано офлайн-оповещение, главный узел будет каждые 30 минут выполнять проверку и запускать задачи оповещения.',
            offlineOff: 'Включить офлайн-оповещение',
            offlineOffHelper:
                'Включение офлайн-оповещений заставит главный узел каждые 30 минут выполнять задачи оповещения.',
            offlineClose: 'Отключить офлайн-оповещение',
            offlineCloseHelper:
                'Отключив офлайн-оповещения, вы передаёте ответственность за оповещения на подчинённые узлы. Убедитесь, что сеть работает стабильно, чтобы избежать сбоев.',
            alertNotice: 'Уведомление об оповещении',
            methodConfig: 'Настройка способа уведомления',
            commonConfig: 'Глобальная настройка',
            smsConfig: 'SMS',
            smsConfigHelper: 'Настройка номеров для SMS-уведомлений',
            emailConfig: 'электронной почты',
            emailConfigHelper: 'Настройка службы отправки SMTP-писем',
            deleteConfigTitle: 'Удалить конфигурацию оповещения',
            deleteConfigMsg: 'Вы уверены, что хотите удалить конфигурацию оповещения?',
            test: 'Тест',
            alertTestOk: 'Тестовое уведомление успешно',
            alertTestFailed: 'Не удалось отправить тестовое уведомление',
            displayName: 'Отображаемое имя',
            sender: 'Адрес отправителя',
            password: 'Пароль',
            host: 'SMTP сервер',
            port: 'Порт',
            encryption: 'Метод шифрования',
            recipient: 'Получатель',
            licenseTime: 'Напоминание об истечении лицензии',
            licenseTimeTitle: 'Напоминание об истечении лицензии',
            displayNameHelper: 'Отображаемое имя отправителя письма',
            senderHelper: 'Адрес электронной почты для отправки сообщений',
            passwordHelper: 'Код авторизации почтового сервиса',
            hostHelper: 'Адрес SMTP-сервера, например: smtp.qq.com',
            portHelper: 'SSL обычно использует 465, TLS — 587',
            sslHelper: 'Если порт SMTP — 465, обычно требуется SSL',
            tlsHelper: 'Если порт SMTP — 587, обычно требуется TLS',
        },
        theme: {
            lingXiaGold: 'Лин Ся Золотой',
            classicBlue: 'Классический Синий',
            freshGreen: 'Свежий Зелёный',
            customColor: 'Пользовательский Цвет',
            setDefault: 'По умолчанию',
            setDefaultHelper: 'Цветовая схема темы будет восстановлена до исходного состояния. Вы хотите продолжить?',
            setHelper: 'Выбранная в данный момент цветовая схема темы будет сохранена. Вы хотите продолжить?',
        },
        exchange: {
            exchange: 'Обмен файлами',
            exchangeConfirm: 'Хотите перенести файл/папку {1} с узла {0} в каталог {3} узла {2}?',
        },
        cluster: {
            cluster: 'Высокая доступность приложений',
            name: 'Имя кластера',
            addCluster: 'Добавить кластер',
            installNode: 'Установить узел',
            master: 'Главный узел',
            slave: 'Подчиненный узел',
            replicaStatus: 'Состояние мастер-слейв',
            unhealthyDeleteError:
                'Состояние узла установки аномально, пожалуйста, проверьте список узлов и повторите попытку!',
            replicaStatusError: 'Получение статуса аномально, пожалуйста, проверьте главный узел.',
            masterHostError: 'IP главного узла не может быть 127.0.0.1',
        },
    },
};

export default {
    ...fit2cloudEnLocale,
    ...message,
};
