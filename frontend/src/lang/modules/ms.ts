import fit2cloudEnLocale from 'fit2cloud-ui-plus/src/locale/lang/ms';

const message = {
    commons: {
        true: 'true',
        false: 'false',
        colon: ': ',
        example: 'Sebagai contoh, ',
        fit2cloud: 'FIT2CLOUD',
        lingxia: 'Lingxia',
        button: {
            run: 'J<PERSON><PERSON>',
            create: '<PERSON>ip<PERSON>',
            add: 'Tambah',
            save: 'Simpan',
            set: 'Sunting tetapan',
            sync: '<PERSON>lar<PERSON>',
            delete: 'Padam',
            edit: 'Sunting',
            enable: 'Aktif',
            disable: 'Nyahaktif',
            confirm: 'Sah',
            cancel: 'Bat<PERSON>',
            reset: 'Tetapkan semula',
            restart: '<PERSON>lakan semula',
            conn: 'Sambung',
            disConn: 'Putus sambungan',
            clean: 'Bersih',
            login: 'Log masuk',
            close: 'Tutup',
            off: 'Tutup',
            stop: 'Henti',
            start: '<PERSON><PERSON>',
            view: '<PERSON><PERSON>',
            watch: '<PERSON><PERSON><PERSON>',
            handle: '<PERSON><PERSON>',
            clone: '<PERSON><PERSON>',
            expand: 'Kembang',
            collapse: 'Runtuh',
            log: 'Log',
            back: 'Kemba<PERSON>',
            backup: '<PERSON><PERSON>',
            recover: 'Pulih',
            retry: 'Cuba semula',
            upload: 'Muat naik',
            download: 'Muat turun',
            init: 'Mulakan',
            verify: 'Sahkan',
            saveAndEnable: 'Simpan dan aktifkan',
            import: 'Import',
            export: 'Eksport',
            power: 'Pemberian Kuasa',
            search: 'Cari',
            refresh: 'Segarkan',
            get: 'Dapatkan',
            upgrade: 'Tingkatkan',
            update: 'kemas kini',
            ignore: 'Abaikan peningkatan',
            install: 'pasang',
            copy: 'Salin',
            random: 'Rawak',
            uninstall: 'Nyahpasang',
            fullscreen: 'Masuk ke skrin penuh',
            quitFullscreen: 'Keluar dari skrin penuh',
            showAll: 'Tunjukkan Semua',
            hideSome: 'Sembunyikan Sebahagian',
            agree: 'Setuju',
            notAgree: 'Tidak Setuju',
            preview: 'Pratonton',
            open: 'Buka',
            notSave: 'Tidak Disimpan',
            createNewFolder: 'Cipta folder baru',
            createNewFile: 'Cipta fail baru',
            helpDoc: 'Dokumen Bantuan',
            unbind: 'Nyahkaitkan',
            cover: 'Tindih',
            skip: 'Langkau',
            fix: 'Betulkan',
            down: 'Hentikan',
            up: 'Mulakan',
            sure: 'Sahkan',
            show: 'Tunjukkan',
            hide: 'Sembunyikan',
        },
        operate: {
            start: 'Mula',
            stop: 'Hentikan',
            restart: 'Mulai Semula',
            reload: 'Muat Semula',
            rebuild: 'Bangun Semula',
            sync: 'Segerakkan',
            up: 'Naik',
            down: 'Turun',
            delete: 'Padam',
        },
        search: {
            timeStart: 'Masa mula',
            timeEnd: 'Masa tamat',
            timeRange: 'Hingga',
            dateStart: 'Tarikh mula',
            dateEnd: 'Tarikh tamat',
        },
        table: {
            all: 'Semua',
            total: 'Jumlah {0}',
            name: 'Nama',
            type: 'Jenis',
            status: 'Status',
            records: 'Rekod',
            group: 'Kumpulan',
            createdAt: 'Waktu dicipta',
            publishedAt: 'Waktu diterbitkan',
            date: 'Tarikh',
            updatedAt: 'Waktu dikemas kini',
            operate: 'Operasi',
            message: 'Mesej',
            description: 'Penerangan',
            interval: 'Selang masa',
            user: 'Pemilik',
            title: 'Tajuk',
            port: 'Port',
            forward: 'Hadapan',
            protocol: 'Protokol',
            tableSetting: 'Tetapan jadual',
            refreshRate: 'Kadar penyegaran',
            selectColumn: 'Pilih lajur',
            local: 'Tempatan',
            serialNumber: 'Nombor siri',
            manageGroup: 'Urus Kumpulan',
            backToList: 'Kembali ke Senarai',
            keepEdit: 'Teruskan Mengedit',
        },
        loadingText: {
            Upgrading: 'Peningkatan sistem, sila tunggu...',
            Restarting: 'Memulakan semula sistem, sila tunggu...',
            Recovering: 'Memulihkan daripada snapshot, sila tunggu...',
            Rollbacking: 'Mengembalikan daripada snapshot, sila tunggu...',
        },
        msg: {
            noneData: 'Tiada data tersedia',
            delete: 'Operasi ini tidak boleh diundur. Adakah anda mahu meneruskan?',
            clean: 'Operasi ini tidak boleh diundur. Adakah anda mahu meneruskan?',
            closeDrawerHelper: 'Sistem mungkin tidak menyimpan perubahan yang anda buat. Adakah anda ingin teruskan?',
            deleteSuccess: 'Berjaya dipadam',
            loginSuccess: 'Berjaya log masuk',
            operationSuccess: 'Berjaya dilakukan',
            copySuccess: 'Berjaya disalin',
            notSupportOperation: 'Operasi ini tidak disokong',
            requestTimeout: 'Permintaan telah tamat masa, sila cuba lagi nanti',
            infoTitle: 'Petunjuk',
            notRecords: 'Tiada rekod pelaksanaan dijana untuk tugas semasa',
            sureLogOut: 'Adakah anda pasti mahu log keluar?',
            createSuccess: 'Berjaya dicipta',
            updateSuccess: 'Berjaya dikemas kini',
            uploadSuccess: 'Berjaya dimuat naik',
            operateConfirm: 'Jika anda pasti dengan operasi ini, sila masukkan secara manual: ',
            inputOrSelect: 'Sila pilih atau masukkan',
            copyFailed: 'Gagal disalin',
            operatorHelper:
                'Operasi "{1}" akan dilakukan pada "{0}" dan tidak boleh diundur. Adakah anda mahu meneruskan?',
            notFound: 'Maaf, halaman yang anda minta tidak wujud.',
            unSupportType: 'Jenis fail semasa tidak disokong.',
            unSupportSize: 'Fail yang dimuat naik melebihi {0}M, sila sahkan!',
            fileExist: 'Fail sudah wujud dalam folder semasa. Memuat naik semula tidak disokong.',
            fileNameErr:
                'Anda hanya boleh memuat naik fail dengan nama yang mengandungi 1 hingga 256 aksara, termasuk Bahasa Inggeris, Cina, nombor, atau titik (.-_)',
            confirmNoNull: 'Pastikan nilai {0} tidak kosong.',
            errPort: 'Maklumat port tidak betul, sila sahkan!',
            remove: 'Buang',
            backupHelper: 'Operasi semasa akan membuat sandaran {0}. Adakah anda mahu meneruskan?',
            recoverHelper:
                'Memulihkan daripada fail {0}. Operasi ini tidak boleh diundur. Adakah anda mahu meneruskan?',
            refreshSuccess: 'Berjaya disegarkan',
            rootInfoErr: 'Ia sudah menjadi direktori akar',
            resetSuccess: 'Berjaya ditetapkan semula',
            creatingInfo: 'Sedang mencipta, operasi ini tidak diperlukan',
        },
        login: {
            username: 'Nama Pengguna',
            password: 'Kata Laluan',
            welcome: 'Selamat datang kembali, sila masukkan nama pengguna dan kata laluan anda untuk log masuk!',
            errorAuthInfo: 'Nama pengguna atau kata laluan yang anda masukkan tidak betul, sila cuba lagi!',
            errorMfaInfo: 'Maklumat pengesahan tidak betul, sila cuba lagi!',
            captchaHelper: 'Captcha',
            errorCaptcha: 'Ralat kod captcha!',
            notSafe: 'Akses Ditolak',
            safeEntrance1: 'Log masuk selamat telah diaktifkan dalam persekitaran semasa',
            safeEntrance2: 'Masukkan arahan berikut pada terminal SSH untuk melihat pintu masuk panel: 1pctl user-info',
            errIP1: 'Akses alamat IP yang dibenarkan diaktifkan dalam persekitaran semasa',
            errDomain1: 'Pengikatan nama domain akses diaktifkan dalam persekitaran semasa',
            errHelper: 'Untuk menetapkan semula maklumat pengikatan, jalankan arahan berikut pada terminal SSH: ',
            codeInput: 'Sila masukkan kod pengesahan 6 digit dari pengesah MFA',
            mfaTitle: 'Pengesahan MFA',
            mfaCode: 'Kod pengesahan MFA',
            title: 'Panel Pengurusan Pelayan Linux',
            licenseHelper: '<Perjanjian Lesen Komuniti>',
            errorAgree: 'Klik untuk bersetuju dengan Lesen Perisian Komuniti',
            logout: 'Log keluar',
            agreeTitle: 'Agreement',
            agreeContent:
                'Untuk melindungi hak dan kepentingan sah anda dengan lebih baik, sila baca dan setuju dengan perjanjian berikut &laquo; <a href = "https://www.fit2cloud.com/legal/licenses.html" target = "_blank" > Perjanjian Lesen Komuniti </a> &raquo;',
        },
        rule: {
            username: 'Masukkan nama pengguna',
            password: 'Masukkan kata laluan',
            rePassword: 'Pengesahan kata laluan tidak sepadan dengan kata laluan.',
            requiredInput: 'Ruangan ini wajib diisi.',
            requiredSelect: 'Pilih satu item dalam senarai',
            illegalChar: 'Suntikan aksara & ; $ \' ` ( ) " > < | tidak disokong buat masa ini',
            illegalInput: 'Ruangan ini tidak boleh mengandungi aksara tidak sah.',
            commonName:
                'Ruangan ini mesti bermula dengan aksara bukan khas dan mesti terdiri daripada aksara rumi, Cina, nombor, ".", "-", dan "_" dengan panjang 1-128 aksara.',
            userName:
                'Menyokong bermula dengan bukan aksara khas, Bahasa Inggeris, Bahasa Cina, nombor, dan _, panjang 3-30',
            simpleName: `Ruangan ini tidak boleh bermula dengan aksara garis bawah ("_") dan mesti terdiri daripada aksara rumi, nombor, dan "_" dengan panjang 3-30 aksara.`,
            simplePassword: `Ruangan ini tidak boleh bermula dengan aksara garis bawah ("_") dan mesti terdiri daripada aksara rumi, nombor, dan "_" dengan panjang 1-30 aksara.`,
            dbName: `Ruangan ini tidak boleh bermula dengan aksara garis bawah ("_") dan mesti terdiri daripada aksara rumi, nombor, dan "_" dengan panjang 1-64 aksara.`,
            imageName: 'Menyokong bermula dengan bukan aksara khas, Bahasa Inggeris, nombor, :@/.-_, panjang 1-256',
            composeName:
                'Menyokong aksara bukan khas pada permulaan, huruf kecil, nombor, "-", dan "_", dengan panjang 1-256 aksara.',
            volumeName:
                'Ruangan ini mesti terdiri daripada aksara Bahasa Inggeris, nombor, ".", "-", dan "_" dengan panjang 2-30 aksara.',
            supervisorName:
                'Ruangan ini mesti bermula dengan aksara bukan khas dan mesti terdiri daripada aksara rumi, nombor, "-", dan "_" dengan panjang 1-128 aksara.',
            complexityPassword:
                'Ruangan ini mesti terdiri daripada aksara rumi, nombor dengan panjang 8-30 aksara dan mengandungi sekurang-kurangnya dua aksara khas.',
            commonPassword: 'Panjang ruangan ini mesti melebihi 6 aksara.',
            linuxName: `Panjang ruangan ini mesti antara 1 hingga 128 aksara. Ruangan ini tidak boleh mengandungi aksara khas berikut: "{0}".`,
            email: 'Ruangan ini mesti mengandungi alamat emel yang sah.',
            number: 'Ruangan ini mesti mengandungi nombor.',
            integer: 'Ruangan ini mesti mengandungi integer positif.',
            ip: 'Ruangan ini mesti mengandungi alamat IP yang sah.',
            host: 'Ruangan ini mesti mengandungi alamat IP atau nama domain yang sah.',
            hostHelper: 'Menyokong input alamat IP atau nama domain',
            port: 'Ruangan ini mesti mengandungi nombor port yang sah.',
            selectHelper: 'Sila pilih fail {0} yang betul',
            domain: 'Ruangan ini mesti dalam format: example.com atau example.com:8080.',
            databaseName:
                'Ruangan ini mesti terdiri daripada aksara Bahasa Inggeris, nombor, dan "_" dengan panjang 1-30 aksara.',
            ipErr: 'Ruangan ini mesti mengandungi alamat IP yang sah.',
            numberRange: 'Ruangan ini mesti mengandungi nombor antara {0} dan {1}.',
            paramName:
                'Ruangan ini mesti terdiri daripada aksara rumi, nombor, ".", "-", dan "_" dengan panjang 2-30 aksara.',
            paramComplexity:
                'Ruangan ini tidak boleh bermula atau berakhir dengan aksara khas dan mesti terdiri daripada aksara rumi, nombor, "{0}" dengan panjang 6-128 aksara.',
            paramUrlAndPort: 'Ruangan ini mesti dalam format "http(s)://(nama domain/IP):(port)".',
            nginxDoc: 'Ruangan ini mesti terdiri daripada aksara rumi, nombor, dan ".".',
            appName:
                'Ruangan ini tidak boleh bermula atau berakhir dengan "-" dan "_" dan mesti terdiri daripada aksara rumi, nombor, "-", dan "_" dengan panjang 2-30 aksara.',
            containerName:
                'Menyokong huruf, nombor, -, _ dan .; tidak boleh bermula dengan -, _ atau .; panjang: 2-128 aksara.',
            mirror: 'Alamat pecutan cermin mesti bermula dengan http(s)://, menyokong huruf (huruf besar dan kecil), nombor, ., / dan -, serta tidak boleh mengandungi baris kosong.',
            disableFunction: 'Hanya menyokong huruf, garis bawah, dan ,',
            leechExts: 'Hanya menyokong huruf, nombor, dan ,',
            paramSimple: 'Menyokong huruf kecil dan nombor, panjang 1-128 aksara.',
            filePermission: 'Ralat Kebenaran Fail',
            formatErr: 'Ralat format, sila semak dan cuba lagi',
            phpExtension: 'Hanya menyokong huruf kecil, _, dan nombor',
            paramHttp: 'Mesti bermula dengan http:// atau https://',
            phone: 'Format nombor telefon tidak betul.',
            authBasicPassword: 'Menyokong huruf, nombor, dan aksara khas biasa, panjang 1-72',
            length128Err: 'Panjang tidak boleh melebihi 128 aksara',
            maxLength: 'Panjang tidak boleh melebihi {0} aksara',
            alias: 'Menyokong Bahasa Inggeris, nombor, - dan _, panjang 1-30, dan tidak boleh bermula atau berakhir dengan -_.',
        },
        res: {
            paramError: 'Permintaan gagal, sila cuba lagi nanti!',
            forbidden: 'Pengguna semasa tidak mempunyai kebenaran',
            serverError: 'Kecacatan perkhidmatan',
            notFound: 'Sumber tidak wujud',
            commonError: 'Permintaan gagal',
        },
        service: {
            serviceNotStarted: `Perkhidmatan {0} belum dimulakan.`,
        },
        status: {
            running: 'Sedang Berjalan',
            done: 'Selesai',
            scanFailed: 'Tidak Lengkap',
            success: 'Berjaya',
            waiting: 'Menunggu',
            waiting1: 'Menunggu',
            failed: 'Gagal',
            stopped: 'Dihentikan',
            error: 'Ralat',
            created: 'Dicipta',
            restarting: 'Memulakan Semula',
            uploading: 'Sedang Memuat Naik',
            unhealthy: 'Tidak Sihat',
            removing: 'Sedang Membuang',
            paused: 'Dijeda',
            exited: 'Keluar',
            dead: 'Mati',
            installing: 'Sedang Memasang',
            enabled: 'Diaktifkan',
            disabled: 'Dilumpuhkan',
            normal: 'Normal',
            building: 'Sedang Membina',
            upgrading: 'Sedang Meningkatkan',
            pending: 'Menunggu Edit',
            rebuilding: 'Sedang Membina Semula',
            deny: 'Ditolak',
            accept: 'Diterima',
            used: 'Digunakan',
            unUsed: 'Tidak Digunakan',
            starting: 'Sedang Memulakan',
            recreating: 'Sedang Mencipta Semula',
            creating: 'Sedang Mencipta',
            init: 'Menunggu aplikasi',
            ready: 'Normal',
            applying: 'Sedang Memohon',
            uninstalling: 'Menyahpasang',
            lost: 'Hilang',
            bound: 'Terikat',
            unbind: 'Tidak terikat',
            exceptional: 'Luar biasa',
            free: 'Bebas',
            enable: 'Aktif',
            disable: 'Dilumpuhkan',
            deleted: 'Dihapus',
            downloading: 'Memuat turun',
            packing: 'Membungkus',
            sending: 'Menghantar',
            healthy: 'Sihat',
            executing: 'Melaksanakan',
            installerr: 'Pemasangan gagal',
            applyerror: 'Permohonan gagal',
            systemrestart: 'Dihentikan',
            starterr: 'Permulaan gagal',
            uperr: 'Permulaan gagal',
        },
        units: {
            second: 'saat | saat | saat',
            minute: 'minit | minit | minit',
            hour: 'jam | jam | jam',
            day: 'hari | hari | hari',
            week: 'minggu | minggu | minggu',
            month: 'bulan | bulan | bulan',
            year: 'tahun | tahun | tahun',
            time: 'masa',
            core: 'teras | teras | teras',
            secondUnit: 's',
            minuteUnit: 'min',
            hourUnit: 'h',
            dayUnit: 'd',
            millisecond: 'Milisaat',
        },
    },
    menu: {
        home: 'Overview',
        apps: 'App Store',
        website: 'Website | Websites',
        project: 'Project | Projects',
        config: 'Configuration | Configurations',
        ssh: 'SSH Settings',
        firewall: 'Firewall',
        ssl: 'Certificate | Certificates',
        database: 'Database | Databases',
        aiTools: 'AI',
        mcp: 'MCP',
        container: 'Container | Containers',
        cronjob: 'Cron Job | Cron Jobs',
        system: 'System',
        security: 'Security',
        files: 'Files',
        monitor: 'Monitoring',
        terminal: 'Terminal',
        settings: 'Setting | Settings',
        toolbox: 'Toolbox',
        logs: 'Log | Logs',
        runtime: 'Runtime | Runtimes',
        processManage: 'Process | Processes',
        process: 'Process | Processes',
        network: 'Network | Networks',
        supervisor: 'Supervisor',
        tamper: 'Bukti Pengubahsuaian',
        app: 'Aplikasi',
        msgCenter: 'Pusat Tugas',
    },
    home: {
        restart_1panel: 'Mulakan semula panel',
        restart_system: 'Mulakan semula pelayan',
        operationSuccess: 'Operasi berjaya, sedang memulakan semula, sila segarkan pelayar secara manual nanti!',
        entranceHelper:
            'Pintu masuk keselamatan tidak diaktifkan. Anda boleh mengaktifkannya di "Tetapan -> Keselamatan" untuk meningkatkan keselamatan sistem.',
        appInstalled: 'Aplikasi yang dipasang',
        systemInfo: 'Maklumat sistem',
        hostname: 'Nama hos',
        platformVersion: 'Sistem pengendalian',
        kernelVersion: 'Kernel',
        kernelArch: 'Seni bina',
        network: 'Rangkaian',
        io: 'Disk I/O',
        ip: 'Local IP',
        proxy: 'System proxy',
        baseInfo: 'Base info',
        totalSend: 'Jumlah dihantar',
        totalRecv: 'Jumlah diterima',
        rwPerSecond: 'I/O operations',
        ioDelay: 'I/O latency',
        uptime: 'Up since',
        runningTime: 'Uptime',
        mem: 'System',
        swapMem: 'Swap partition',

        runSmoothly: 'Beban rendah',
        runNormal: 'Beban sederhana',
        runSlowly: 'Beban tinggi',
        runJam: 'Beban berat',

        core: 'Teras Fizikal',
        logicCore: 'Teras Logik',
        loadAverage: 'Purata beban dalam 1 minit terakhir | Purata beban dalam {n} minit terakhir',
        load: 'Beban',
        mount: 'Titik Pemasangan',
        fileSystem: 'Sistem Fail',
        total: 'Jumlah',
        used: 'Digunakan',
        cache: 'Cache',
        free: 'Bebas',
        shard: 'Shard',
        available: 'Tersedia',
        percent: 'Penggunaan',
        goInstall: 'Pergi pasang',

        networkCard: 'Kad rangkaian',
        disk: 'Disk',
    },
    tabs: {
        more: 'Lagi',
        hide: 'Sembunyi',
        closeLeft: 'Tutup kiri',
        closeRight: 'Tutup kanan',
        closeCurrent: 'Tutup semasa',
        closeOther: 'Tutup lain',
        closeAll: 'Tutup Semua',
    },
    header: {
        logout: 'Log keluar',
    },
    database: {
        manage: 'Pengurusan',
        deleteBackupHelper: 'Padam sandaran pangkalan data secara serentak',
        delete: 'Operasi padam tidak boleh diundurkan, sila masukkan "',
        deleteHelper: '" untuk memadam pangkalan data ini',
        create: 'Cipta pangkalan data',
        noMysql: 'Perkhidmatan pangkalan data (MySQL atau MariaDB)',
        noPostgresql: 'Perkhidmatan pangkalan data PostgreSQL',
        goUpgrade: 'Pergi tingkatkan',
        goInstall: 'Pergi pasang',
        isDelete: 'Dihapuskan',
        permission: 'Kebenaran',
        permissionForIP: 'IP',
        permissionAll: 'Kesemuanya(%)',
        localhostHelper:
            'Mengkonfigurasi kebenaran pangkalan data sebagai "localhost" untuk penyebaran kontena akan menghalang akses luar ke kontena. Sila pilih dengan teliti!',
        databaseConnInfo: 'Maklumat sambungan',
        rootPassword: 'Kata laluan root',
        serviceName: 'Nama Perkhidmatan',
        serviceNameHelper: 'Akses antara kontena dalam rangkaian yang sama.',
        backupList: 'Sandaran',
        loadBackup: 'Import',
        remoteAccess: 'Akses jauh',
        remoteHelper: 'Berbilang IP dipisahkan dengan koma, contoh: *************, *************',
        remoteConnHelper:
            'Sambungan jauh ke MySQL sebagai pengguna root mungkin mempunyai risiko keselamatan. Oleh itu, lakukan operasi ini dengan berhati-hati.',
        changePassword: 'Kata laluan',
        changeConnHelper: 'Operasi ini akan mengubah pangkalan data semasa {0}. Adakah anda ingin meneruskan?',
        changePasswordHelper:
            'Pangkalan data telah dikaitkan dengan aplikasi. Menukar kata laluan akan menukar kata laluan pangkalan data aplikasi pada masa yang sama. Perubahan ini akan berkuat kuasa selepas aplikasi dimulakan semula.',

        confChange: 'Konfigurasi',
        confNotFound:
            'Fail konfigurasi tidak dapat dijumpai. Sila tingkatkan aplikasi ke versi terkini di gedung aplikasi dan cuba lagi!',

        portHelper:
            'Port ini adalah port yang didedahkan oleh kontena. Anda perlu menyimpan pengubahsuaian secara berasingan dan memulakan semula kontena!',

        loadFromRemote: 'Selaras',
        userBind: 'Kaitkan pengguna',
        pgBindHelper:
            'Operasi ini digunakan untuk mencipta pengguna baharu dan mengaitkannya dengan pangkalan data sasaran. Pada masa ini, memilih pengguna yang sudah wujud dalam pangkalan data tidak disokong.',
        pgSuperUser: 'Pengguna Super',
        loadFromRemoteHelper:
            'Ini akan menyelaraskan maklumat pangkalan data di pelayan ke 1Panel. Adakah anda mahu meneruskan?',
        passwordHelper: 'Tidak dapat diambil, sila ubah',
        remote: 'Jauh',
        remoteDB: 'Pelayan jauh | Pelayan-pelayan jauh',
        createRemoteDB: 'Kaitkan @.lower:database.remoteDB',
        unBindRemoteDB: 'Nyahkaitkan @.lower:database.remoteDB',
        unBindForce: 'Paksa nyahkait',
        unBindForceHelper: 'Abaikan semua ralat semasa proses nyahkait untuk memastikan operasi akhir berjaya',
        unBindRemoteHelper:
            'Nyahkaitkan pangkalan data jauh hanya akan menghapuskan hubungan pengikatan dan tidak akan terus memadamkan pangkalan data jauh.',
        editRemoteDB: 'Edit pelayan jauh',
        localDB: 'Pangkalan data tempatan',
        address: 'Alamat pangkalan data',
        version: 'Versi pangkalan data',
        userHelper:
            'Pengguna root atau pengguna pangkalan data dengan keistimewaan root boleh mengakses pangkalan data jauh.',
        pgUserHelper: 'Pengguna pangkalan data dengan keistimewaan superuser.',
        ssl: 'Gunakan SSL',
        clientKey: 'Kunci peribadi klien',
        clientCert: 'Sijil klien',
        caCert: 'Sijil CA',
        hasCA: 'Mempunyai sijil CA',
        skipVerify: 'Abaikan pemeriksaan kesahihan sijil',

        formatHelper:
            'Set aksara pangkalan data semasa adalah {0}, ketidakkonsistenan set aksara mungkin menyebabkan kegagalan pemulihan.',
        selectFile: 'Pilih fail',
        dropHelper: 'Anda boleh seret dan lepaskan fail yang ingin dimuat naik di sini atau',
        clickHelper: 'klik untuk memuat naik',
        supportUpType: 'Hanya fail sql, sql.gz, dan tar.gz yang disokong',
        zipFormat: 'Struktur pakej mampatan tar.gz: Pakej mampatan test.tar.gz mesti mengandungi test.sql',

        currentStatus: 'Keadaan semasa',
        baseParam: 'Parameter asas',
        performanceParam: 'Parameter prestasi',
        runTime: 'Waktu mula',
        connections: 'Jumlah sambungan',
        bytesSent: 'Byte dihantar',
        bytesReceived: 'Byte diterima',
        queryPerSecond: 'Pertanyaan per saat',
        txPerSecond: 'Tx per saat',
        connInfo: 'sambungan aktif/puncak',
        connInfoHelper: 'Jika nilai terlalu besar, tingkatkan "max_connections".',
        threadCacheHit: 'Cache benang berjaya',
        threadCacheHitHelper: 'Jika terlalu rendah, tingkatkan "thread_cache_size".',
        indexHit: 'Indeks berjaya',
        indexHitHelper: 'Jika terlalu rendah, tingkatkan "key_buffer_size".',
        innodbIndexHit: 'Kadar berjaya indeks Innodb',
        innodbIndexHitHelper: 'Jika terlalu rendah, tingkatkan "innodb_buffer_pool_size".',
        cacheHit: 'Berjaya pertanyaan cache',
        cacheHitHelper: 'Jika terlalu rendah, tingkatkan "query_cache_size".',
        tmpTableToDB: 'Jadual sementara ke cakera',
        tmpTableToDBHelper: 'Jika terlalu besar, cuba tingkatkan "tmp_table_size".',
        openTables: 'Jadual dibuka',
        openTablesHelper: 'Nilai konfigurasi "table_open_cache" mesti lebih besar atau sama dengan nilai ini.',
        selectFullJoin: 'Pilih penyertaan penuh',
        selectFullJoinHelper: 'Jika nilai bukan 0, periksa sama ada indeks jadual data adalah betul.',
        selectRangeCheck: 'Bilangan penyertaan tanpa indeks',
        selectRangeCheckHelper: 'Jika nilai bukan 0, periksa sama ada indeks jadual data adalah betul.',
        sortMergePasses: 'Bilangan penggabungan terisih',
        sortMergePassesHelper: 'Jika nilai terlalu besar, tingkatkan "sort_buffer_size".',
        tableLocksWaited: 'Bilangan kunci jadual',
        tableLocksWaitedHelper:
            'Jika nilai terlalu besar, pertimbangkan untuk meningkatkan prestasi pangkalan data anda.',

        performanceTuning: 'Penalaan prestasi',
        optimizationScheme: 'Skema pengoptimuman',
        keyBufferSizeHelper: 'Saiz penimbal untuk indeks',
        queryCacheSizeHelper: 'Cache pertanyaan. Jika fungsi ini dilumpuhkan, tetapkan parameter ini kepada 0.',
        tmpTableSizeHelper: 'Saiz cache jadual sementara',
        innodbBufferPoolSizeHelper: 'Saiz penimbal Innodb',
        innodbLogBufferSizeHelper: 'Saiz penimbal log Innodb',
        sortBufferSizeHelper: '* sambungan, saiz penimbal per urutan benang',
        readBufferSizeHelper: '* sambungan, saiz penimbal bacaan',
        readRndBufferSizeHelper: '* sambungan, saiz penimbal bacaan rawak',
        joinBufferSizeHelper: '* sambungan, saiz cache jadual perkaitan',
        threadStackelper: '* sambungan, saiz tumpukan per benang',
        binlogCacheSizeHelper: '* sambungan, saiz cache log binari (gandaan 4096)',
        threadCacheSizeHelper: 'Saiz kolam benang',
        tableOpenCacheHelper: 'Cache jadual',
        maxConnectionsHelper: 'Sambungan maksimum',
        restart: 'Mulakan semula',

        slowLog: 'Log lambat',
        noData: 'Tiada log lambat lagi.',

        isOn: 'Hidup',
        longQueryTime: 'Ambang (saat)',
        thresholdRangeHelper: 'Sila masukkan ambang yang betul (1 - 600).',

        timeout: 'Tamat masa(saat)',
        timeoutHelper: 'Tempoh tamat masa sambungan tidak aktif. 0 menunjukkan sambungan sentiasa aktif.',
        maxclients: 'Klien maksimum',
        requirepassHelper:
            'Biarkan ruangan ini kosong untuk menunjukkan bahawa tiada kata laluan telah ditetapkan. Perubahan perlu disimpan secara berasingan dan kontena perlu dimulakan semula!',
        databases: 'Bilangan pangkalan data',
        maxmemory: 'Penggunaan memori maksimum',
        maxmemoryHelper: '0 menunjukkan tiada had.',
        tcpPort: 'Port mendengar semasa.',
        uptimeInDays: 'Hari beroperasi.',
        connectedClients: 'Bilangan klien yang disambungkan.',
        usedMemory: 'Penggunaan memori semasa Redis.',
        usedMemoryRss: 'Saiz memori yang diminta daripada sistem pengendalian.',
        usedMemoryPeak: 'Penggunaan memori puncak Redis.',
        memFragmentationRatio: 'Nisbah pemecahan memori.',
        totalConnectionsReceived: 'Jumlah bilangan klien yang disambungkan sejak dijalankan.',
        totalCommandsProcessed: 'Jumlah bilangan arahan yang dilaksanakan sejak dijalankan.',
        instantaneousOpsPerSec: 'Bilangan arahan yang dilaksanakan oleh pelayan setiap saat.',
        keyspaceHits: 'Bilangan kali kunci pangkalan data berjaya dijumpai.',
        keyspaceMisses: 'Bilangan percubaan gagal untuk mencari kunci pangkalan data.',
        hit: 'Nisbah pencarian kunci pangkalan data yang berjaya.',
        latestForkUsec: 'Bilangan mikrodetik yang dihabiskan pada operasi fork() terakhir.',
        redisCliHelper: '"redis-cli" perkhidmatan tidak dikesan. Aktifkan perkhidmatan terlebih dahulu.',
        redisQuickCmd: 'Arahan pantas Redis',
        recoverHelper: 'Ini akan menimpa data dengan [{0}]. Adakah anda mahu meneruskan?',
        submitIt: 'Tindih data',

        baseConf: 'Asas',
        allConf: 'Semua',
        restartNow: 'Mula semula sekarang',
        restartNowHelper1:
            'Anda perlu memulakan semula sistem selepas perubahan konfigurasi berkuat kuasa. Jika data anda perlu dipelihara, lakukan operasi simpan terlebih dahulu.',
        restartNowHelper: 'Perubahan ini hanya akan berkuat kuasa selepas sistem dimulakan semula.',

        persistence: 'Ketekalan',
        rdbHelper1: 'saat, masukkan',
        rdbHelper2: 'data',
        rdbHelper3: 'Memenuhi mana-mana syarat akan mencetuskan ketekalan RDB.',
        rdbInfo: 'Pastikan nilai dalam senarai peraturan berada dalam julat 1 hingga 100000',

        containerConn: 'Sambungan kontena',
        connAddress: 'Alamat',
        containerConnHelper:
            'Alamat sambungan ini boleh digunakan oleh aplikasi yang berjalan pada runtime laman web (PHP, dll.) atau kontena.',
        remoteConn: 'Sambungan luaran',
        remoteConnHelper2: 'Gunakan alamat ini untuk persekitaran bukan kontena atau sambungan luar.',
        remoteConnHelper3:
            'Alamat akses lalai ialah IP hos. Untuk mengubahnya, pergi ke item konfigurasi "Alamat Akses Lalai" pada halaman tetapan panel.',
        localIP: 'IP Tempatan',
    },
    aiTools: {
        model: {
            model: 'Model',
            create: 'Tambah Model',
            create_helper: 'Tarik "{0}"',
            ollama_doc: 'Anda boleh melawat laman web rasmi Ollama untuk mencari dan menemui lebih banyak model.',
            container_conn_helper: 'Gunakan alamat ini untuk akses atau sambungan antara kontena',
            ollama_sync:
                'Sincronizando o modelo Ollama, encontrou que os seguintes modelos não existem, deseja excluí-los?',
            from_remote: 'Este modelo não foi baixado via 1Panel, sem logs de pull relacionados.',
            no_logs: 'Os logs de pull deste modelo foram excluídos e não podem ser visualizados.',
        },
        proxy: {
            proxy: 'Peningkatan Proksi AI',
            proxyHelper1: 'Ikatkan domain dan aktifkan HTTPS untuk meningkatkan keselamatan penghantaran',
            proxyHelper2: 'Hadkan akses IP untuk mengelakkan pendedahan di internet awam',
            proxyHelper3: 'Aktifkan penstriman',
            proxyHelper4: 'Setelah selesai, anda boleh melihat dan mengurusnya dalam senarai laman web',
            proxyHelper5:
                'Selepas diaktifkan, anda boleh melumpuhkan akses luaran ke port dalam App Store - Dipasang - Ollama - Parameter untuk meningkatkan keselamatan.',
            proxyHelper6: 'Untuk melumpuhkan konfigurasi proksi, anda boleh memadamnya dari senarai laman web.',
            whiteListHelper: 'Hadkan akses kepada hanya IP dalam senarai putih',
        },
        gpu: {
            gpu: 'Monitor GPU',
            base: 'Maklumat Asas',
            gpuHelper: 'Perintah NVIDIA-SMI atau XPU-SMI tidak dikesan pada sistem semasa. Sila periksa dan cuba lagi!',
            driverVersion: 'Versi Pemacu',
            cudaVersion: 'Versi CUDA',
            process: 'Maklumat Proses',
            type: 'Jenis',
            typeG: 'Grafik',
            typeC: 'Pengiraan',
            typeCG: 'Pengiraan + Grafik',
            processName: 'Nama Proses',
            processMemoryUsage: 'Penggunaan Memori',
            temperatureHelper: 'Suhu GPU yang tinggi boleh menyebabkan pelambatan frekuensi GPU',
            performanceStateHelper: 'Dari P0 (prestasi maksimum) hingga P12 (prestasi minimum)',
            busID: 'ID Bas',
            persistenceMode: 'Mod Ketekalan',
            enabled: 'Diaktifkan',
            disabled: 'Dilumpuhkan',
            persistenceModeHelper:
                'Mod ketekalan membolehkan respons tugas lebih cepat tetapi meningkatkan penggunaan kuasa sedia.',
            displayActive: 'Kad Grafik Dimulakan',
            displayActiveT: 'Ya',
            displayActiveF: 'Tidak',
            ecc: 'Teknologi Pemeriksaan dan Pembetulan Ralat',
            computeMode: 'Mod Pengiraan',
            default: 'Asal',
            exclusiveProcess: 'Proses Eksklusif',
            exclusiveThread: 'Thread Eksklusif',
            prohibited: 'Dilarang',
            defaultHelper: 'Asal: Proses boleh dilaksanakan secara serentak',
            exclusiveProcessHelper:
                'Proses Eksklusif: Hanya satu konteks CUDA boleh menggunakan GPU, tetapi boleh dikongsi oleh berbilang thread',
            exclusiveThreadHelper: 'Thread Eksklusif: Hanya satu thread dalam konteks CUDA boleh menggunakan GPU',
            prohibitedHelper: 'Dilarang: Proses tidak dibenarkan dilaksanakan serentak',
            migModeHelper: 'Digunakan untuk membuat contoh MIG bagi pengasingan fizikal GPU pada tahap pengguna.',
            migModeNA: 'Tidak Disokong',
        },
        mcp: {
            server: 'Pelayan MCP',
            create: 'Tambah Pelayan',
            edit: 'Edit Pelayan',
            commandHelper: 'Contoh: npx -y {0}',
            baseUrl: 'Laluan Akses Luar',
            baseUrlHelper: 'Contoh: http://192.168.1.2:8000',
            ssePath: 'Laluan SSE',
            ssePathHelper: 'Contoh: /sse, berhati-hati jangan bertindan dengan pelayan lain',
            environment: 'Pemboleh Ubah Persekitaran',
            envKey: 'Nama Pemboleh Ubah',
            envValue: 'Nilai Pemboleh Ubah',
            externalUrl: 'Alamat Sambungan Luar',
            operatorHelper: 'Akan melakukan operasi {1} pada {0}, teruskan?',
            domain: 'Alamat Akses Lalai',
            domainHelper: 'Contoh: *********** atau example.com',
            bindDomain: 'Sematkan Laman Web',
            commandPlaceHolder: 'Kini hanya menyokong perintah pelancaran npx dan binari',
            importMcpJson: 'Import Konfigurasi Pelayan MCP',
            importMcpJsonError: 'Struktur mcpServers tidak betul',
            bindDomainHelper:
                'Setelah mengikat laman web, ia akan mengubah alamat akses semua Pelayan MCP yang dipasang dan menutup akses luaran ke pelabuhan',
            outputTransport: 'Jenis Output',
            streamableHttpPath: 'Laluan Streaming',
            streamableHttpPathHelper: 'Contoh: /mcp, elakkan daripada bertindan dengan pelayan lain',
        },
    },
    container: {
        create: 'Cipta kontena',
        edit: 'Sunting kontena',
        updateHelper1: 'Dikesan bahawa kontena ini berasal dari gedung aplikasi. Sila perhatikan dua perkara berikut:',
        updateHelper2: '1. Pengubahsuaian semasa tidak akan diselaraskan ke aplikasi yang dipasang di gedung aplikasi.',
        updateHelper3:
            '2. Jika anda mengubah aplikasi di halaman yang dipasang, kandungan yang sedang diedit akan menjadi tidak sah.',
        updateHelper4:
            'Mengedit kontena memerlukan pembinaan semula, dan sebarang data yang tidak berterusan akan hilang. Adakah anda mahu meneruskan?',
        containerList: 'Senarai kontena',
        operatorHelper: '{0} akan dilakukan pada kontena berikut. Adakah anda mahu meneruskan?',
        operatorAppHelper:
            'Operasi "{0}" akan dilakukan pada kontena berikut dan mungkin mempengaruhi perkhidmatan yang sedang berjalan. Adakah anda mahu meneruskan?',
        start: 'Mulakan',
        stop: 'Hentikan',
        restart: 'Mulakan semula',
        kill: 'Hentikan paksa',
        pause: 'Jeda',
        unpause: 'Sambung semula',
        rename: 'Tukar nama',
        remove: 'Buang',
        removeAll: 'Buang semua',
        containerPrune: 'Prune',
        containerPruneHelper1: 'Ini akan memadam semua kontena yang berada dalam keadaan dihentikan.',
        containerPruneHelper2:
            'Jika kontena berasal dari gedung aplikasi, anda perlu ke "Gedung Aplikasi -> Dipasang" dan klik butang "Bangun Semula" untuk memasangnya semula selepas pembersihan.',
        containerPruneHelper3: 'Operasi ini tidak boleh diundur. Adakah anda mahu meneruskan?',
        imagePrune: 'Prune',
        imagePruneSome: 'Bersihkan yang tidak berlabel',
        imagePruneSomeEmpty: 'Tiada imej dengan tag "none" yang boleh dibersihkan.',
        imagePruneSomeHelper: 'Bersihkan imej dengan tag "none" yang tidak digunakan oleh mana-mana kontena.',
        imagePruneAll: 'Bersihkan yang tidak digunakan',
        imagePruneAllEmpty: 'Tiada imej yang tidak digunakan boleh dibersihkan.',
        imagePruneAllHelper: 'Bersihkan imej yang tidak digunakan oleh mana-mana kontena.',
        networkPrune: 'Prune',
        networkPruneHelper: 'Ini akan membuang semua rangkaian yang tidak digunakan. Adakah anda mahu meneruskan?',
        volumePrune: 'Prune',
        volumePruneHelper: 'Ini akan membuang semua volum tempatan yang tidak digunakan. Adakah anda mahu meneruskan?',
        cleanSuccess: 'Operasi berjaya, bilangan yang dibersihkan kali ini: {0}!',
        cleanSuccessWithSpace:
            'Operasi berjaya. Bilangan cakera yang dibersihkan kali ini ialah {0}. Ruang cakera yang dibebaskan ialah {1}!',
        unExposedPort: 'Alamat pemetaan port semasa ialah 127.0.0.1, yang tidak dapat mengaktifkan akses luaran.',
        upTime: 'Waktu hidup',
        fetch: 'Dapatkan',
        lines: 'Baris',
        linesHelper: 'Sila masukkan bilangan log yang betul untuk diambil!',
        lastDay: 'Hari terakhir',
        last4Hour: '4 jam terakhir',
        lastHour: 'Jam terakhir',
        last10Min: '10 minit terakhir',
        cleanLog: 'Bersihkan log',
        downLogHelper1: 'Ini akan memuat turun semua log dari kontena {0}. Adakah anda mahu meneruskan?',
        downLogHelper2: 'Ini akan memuat turun log terkini {0} dari kontena {0}. Adakah anda mahu meneruskan?',
        cleanLogHelper: 'Ini memerlukan memulakan semula kontena dan tidak boleh diundur. Adakah anda mahu meneruskan?',
        newName: 'Nama baru',
        source: 'Penggunaan sumber',
        cpuUsage: 'Penggunaan CPU',
        cpuTotal: 'Jumlah CPU',
        core: 'Teras',
        memUsage: 'Penggunaan memori',
        memTotal: 'Had memori',
        memCache: 'Cache memori',
        ip: 'Alamat IP',
        cpuShare: 'Bahagian CPU',
        cpuShareHelper:
            'Enjin kontena menggunakan nilai asas 1024 untuk bahagian CPU. Anda boleh meningkatkannya untuk memberikan lebih masa CPU kepada kontena.',
        inputIpv4: 'Contoh: ***********',
        inputIpv6: 'Contoh: 2001:0db8:85a3:0000:0000:8a2e:0370:7334',

        containerFromAppHelper:
            'Dikesan bahawa kontena ini berasal dari gedung aplikasi. Operasi aplikasi boleh menyebabkan suntingan semasa menjadi tidak sah.',
        containerFromAppHelper1:
            'Klik butang [Param] dalam senarai aplikasi yang dipasang untuk memasuki halaman penyuntingan dan mengubah nama kontena.',
        command: 'Arahan',
        console: 'Interaksi kontena',
        tty: 'Peruntukkan TTY palsu (-t)',
        openStdin: 'Pastikan STDIN terbuka walaupun tidak disambungkan (-i)',
        custom: 'Kustom',
        emptyUser: 'Apabila kosong, anda akan log masuk sebagai lalai',
        privileged: 'Privileged',
        privilegedHelper:
            'Benarkan kontena menjalankan operasi teristimewa tertentu pada hos, yang boleh meningkatkan risiko kontena. Gunakan dengan berhati-hati!',
        editComposeHelper:
            'Nota: Pembolehubah persekitaran yang ditetapkan akan ditulis ke fail 1panel.env secara lalai. Jika anda mahu menggunakan parameter ini dalam kontena, anda juga perlu menambah rujukan env_file secara manual dalam fail compose.',

        upgradeHelper: 'Nama Repository/Nama Imej: Versi Imej',
        upgradeWarning2:
            'Operasi peningkatan memerlukan pembinaan semula kontena, sebarang data yang tidak disimpan akan hilang. Adakah anda mahu meneruskan?',
        oldImage: 'Imej semasa',
        sameImageContainer: 'Kontena imej sama',
        sameImageHelper: 'Kontena yang menggunakan imej sama boleh dinaik taraf secara berkumpulan setelah dipilih',
        targetImage: 'Imej sasaran',
        imageLoadErr: 'Tiada nama imej dikesan untuk kontena',
        appHelper:
            'Kontena berasal dari gedung aplikasi, dan peningkatan boleh menyebabkan perkhidmatan tidak tersedia.',
        input: 'Input manual',
        forcePull: 'Tarik imej sentiasa ',
        forcePullHelper: 'Ini akan mengabaikan imej sedia ada di pelayan dan menarik imej terkini dari pendaftaran.',
        server: 'Hos',
        serverExample: '80, 80-88, ip:80 atau ip:80-88',
        containerExample: '80 atau 80-88',
        exposePort: 'Dedahkan port',
        exposeAll: 'Dedahkan semua',
        cmdHelper: 'Contoh: nginx -g "daemon off;"',
        entrypointHelper: 'Contoh: docker-entrypoint.sh',
        autoRemove: 'Buang automatik',
        cpuQuota: 'Bilangan teras CPU',
        memoryLimit: 'Memori',
        limitHelper: 'Jika ditetapkan kepada 0, ia bermakna tiada had. Nilai maksimum ialah {0}',
        mount: 'Mount',
        volumeOption: 'Volume',
        hostOption: 'Hos',
        serverPath: 'Laluan pelayan',
        containerDir: 'Laluan kontena',
        volumeHelper: 'Pastikan kandungan volum storan adalah betul',
        modeRW: 'RW',
        modeR: 'R',
        mode: 'Mod',
        env: 'Persekitaran',
        restartPolicy: 'Polisi Mulakan Semula',
        always: 'sentiasa',
        unlessStopped: 'melainkan dihentikan',
        onFailure: 'gagal (lima kali secara lalai)',
        no: 'tidak pernah',

        refreshTime: 'Selang penyegaran',
        cache: 'Cache',

        image: 'Imej | Imej-imej',
        imagePull: 'Tarik',
        imagePush: 'Tekan',
        imageDelete: 'Padam imej',
        imageTagDeleteHelper: 'Buang tag lain yang berkaitan dengan ID imej ini',
        repoName: 'Pendaftaran kontena',
        imageName: 'Nama imej',
        pull: 'Tarik',
        path: 'Laluan',
        importImage: 'Import',
        build: 'Bina',
        imageBuild: 'Bina',
        pathSelect: 'Laluan',
        label: 'Label',
        imageTag: 'Tag imej',
        push: 'Tekan',
        fileName: 'Nama fail',
        export: 'Eksport',
        exportImage: 'Eksport imej',
        size: 'Saiz',
        tag: 'Tag',
        tagHelper: 'Satu setiap baris. Sebagai contoh,\nkey1=value1\nkey2=value2',
        imageNameHelper: 'Nama dan Tag imej, sebagai contoh: nginx:latest',
        cleanBuildCache: 'Bersihkan cache bina',
        delBuildCacheHelper:
            'Ini akan memadam semua artefak cache yang dijana semasa binaan dan tidak boleh diundur. Adakah anda mahu meneruskan?',
        urlWarning: 'Awalan URL tidak perlu termasuk http:// atau https://. Sila ubah.',

        network: 'Rangkaian | Rangkaian-rangkaian',
        networkHelper:
            'Ini boleh menyebabkan beberapa aplikasi dan persekitaran runtime tidak berfungsi dengan betul. Adakah anda mahu meneruskan?',
        createNetwork: 'Cipta',
        networkName: 'Nama',
        driver: 'Pemacu',
        option: 'Pilihan',
        attachable: 'Boleh dilampirkan',
        subnet: 'Subnet',
        scope: 'Skop IP',
        gateway: 'Gerbang',
        auxAddress: 'Kecualikan IP',

        volume: 'Volum | Volum-volum',
        volumeDir: 'Direktori volum',
        nfsEnable: 'Aktifkan storan NFS',
        nfsAddress: 'Alamat',
        mountpoint: 'Titik pemasangan',
        mountpointNFSHelper: 'contoh: /nfs, /nfs-share',
        options: 'Pilihan',
        createVolume: 'Cipta',

        repo: 'Pendaftaran',
        createRepo: 'Tambah',
        httpRepoHelper: 'Mengoperasikan repositori jenis HTTP memerlukan mulakan semula perkhidmatan Docker.',
        httpRepo:
            'Memilih protokol HTTP memerlukan memulakan semula perkhidmatan Docker untuk menambahkannya ke pendaftaran tidak selamat.',
        delInsecure: 'Padamkan pendaftaran tidak selamat',
        delInsecureHelper:
            'Ini akan memulakan semula perkhidmatan Docker untuk mengeluarkannya dari pendaftaran tidak selamat. Adakah anda mahu meneruskan?',
        downloadUrl: 'Pelayan',
        imageRepo: 'Repo imej',
        repoHelper: 'Adakah ia termasuk repositori cermin/organisasi/projek?',
        auth: 'Memerlukan pengesahan',
        mirrorHelper:
            'Jika terdapat banyak cermin, baris baru mesti dipaparkan, contohnya:\nhttp://xxxxxx.m.daocloud.io \nhttps://xxxxxx.mirror.aliyuncs.com',
        registrieHelper:
            'Jika terdapat banyak repositori persendirian, baris baru mesti dipaparkan, contohnya:\n*************:8081 \n*************:8081',

        compose: 'Compose | Compose-compose',
        fromChangeHelper: 'Menukar sumber akan membersihkan kandungan yang sedang diedit. Adakah anda mahu meneruskan?',
        composePathHelper: 'Laluan simpan fail konfigurasi: {0}',
        composeHelper:
            'Komposisi yang dicipta melalui editor atau templat 1Panel akan disimpan dalam direktori {0}/docker/compose.',
        deleteFile: 'Padam fail',
        deleteComposeHelper:
            'Padam semua fail berkaitan komposisi kontena, termasuk fail konfigurasi dan fail berterusan. Sila berhati-hati!',
        deleteCompose: 'Padam komposisi ini.',
        createCompose: 'Cipta',
        composeDirectory: 'Direktori',
        template: 'Templat',
        composeTemplate: 'Templat Compose | Templat Compose',
        createComposeTemplate: 'Cipta',
        content: 'Kandungan',
        contentEmpty: 'Kandungan Compose tidak boleh kosong, sila masukkan dan cuba lagi!',
        containerNumber: 'Bilangan kontena',
        containerStatus: 'Status kontena',
        exited: 'Keluar',
        running: 'Berjalan ( {0} / {1} )',
        composeDetailHelper: 'Komposisi dibuat di luar 1Panel. Operasi mula dan berhenti tidak disokong.',
        composeOperatorHelper: 'Operasi {1} akan dilakukan pada {0}. Adakah anda mahu meneruskan?',
        composeDownHelper:
            'Ini akan menghentikan dan menghapuskan semua kontena dan rangkaian di bawah komposisi {0}. Adakah anda mahu meneruskan?',

        setting: 'Tetapan | Tetapan',
        operatorStatusHelper: 'Ini akan "{0}" perkhidmatan Docker. Adakah anda mahu meneruskan?',
        dockerStatus: 'Perkhidmatan Docker',
        daemonJsonPathHelper: 'Pastikan laluan konfigurasi sama seperti yang dinyatakan dalam docker.service.',
        mirrors: 'Cermin pendaftaran',
        mirrorsHelper: '',
        mirrorsHelper2: 'Untuk maklumat lanjut, lihat dokumen rasmi.',
        registries: 'Pendaftaran tidak selamat',
        ipv6Helper:
            'Apabila IPv6 diaktifkan, anda perlu menambah rangkaian kontena IPv6. Rujuk dokumen rasmi untuk langkah konfigurasi tertentu.',
        ipv6CidrHelper: 'Julat kolam alamat IPv6 untuk kontena',
        ipv6TablesHelper: 'Konfigurasi automatik Docker IPv6 untuk peraturan iptables.',
        experimentalHelper:
            'Mengaktifkan ip6tables memerlukan konfigurasi ini dihidupkan; jika tidak, ip6tables akan diabaikan.',
        cutLog: 'Pilihan log',
        cutLogHelper1: 'Konfigurasi semasa hanya akan mempengaruhi kontena yang baru dicipta.',
        cutLogHelper2: 'Kontena sedia ada perlu dicipta semula agar konfigurasi berkuat kuasa.',
        cutLogHelper3:
            'Harap maklum bahawa mencipta semula kontena boleh menyebabkan kehilangan data. Jika kontena anda mengandungi data penting, pastikan membuat sandaran sebelum melakukan operasi pembinaan semula.',
        maxSize: 'Saiz maksimum',
        maxFile: 'Fail maksimum',
        liveHelper:
            'Secara lalai, apabila daemon Docker ditamatkan, ia mematikan kontena yang sedang berjalan. Anda boleh mengkonfigurasi daemon supaya kontena kekal berjalan jika daemon menjadi tidak tersedia. Fungsi ini dipanggil pemulihan langsung. Pilihan pemulihan langsung membantu mengurangkan waktu henti kontena akibat kerosakan daemon, pemadaman terancang, atau peningkatan.',
        liveWithSwarmHelper: 'Konfigurasi daemon live-restore tidak serasi dengan mod swarm.',
        iptablesDisable: 'Tutup iptables',
        iptablesHelper1: 'Konfigurasi automatik peraturan iptables untuk Docker.',
        iptablesHelper2:
            'Melumpuhkan iptables akan menyebabkan kontena tidak dapat berkomunikasi dengan rangkaian luaran.',
        daemonJsonPath: 'Laluan Konfigurasi',
        serviceUnavailable: 'Perkhidmatan Docker tidak dimulakan pada masa ini.',
        startIn: ' untuk dimulakan',
        sockPath: 'Socket domain Unix',
        sockPathHelper: 'Saluran komunikasi antara daemon Docker dan klien.',
        sockPathHelper1: 'Laluan lalai: /var/run/docker-x.sock',
        sockPathMsg:
            'Menyimpan tetapan Laluan Socket boleh menyebabkan perkhidmatan Docker tidak tersedia. Adakah anda mahu meneruskan?',
        sockPathErr: 'Sila pilih atau masukkan laluan fail sock Docker yang betul',
        related: 'Berkaitan',
        includeAppstore: 'Tunjukkan kontena dari gedung aplikasi',
        excludeAppstore: 'Sembunyikan Kontena Kedai Aplikasi',

        cleanDockerDiskZone: 'Bersihkan ruang cakera yang digunakan oleh Docker',
        cleanImagesHelper: '( Bersihkan semua imej yang tidak digunakan oleh mana-mana kontena )',
        cleanContainersHelper: '( Bersihkan semua kontena yang dihentikan )',
        cleanVolumesHelper: '( Bersihkan semua volum tempatan yang tidak digunakan )',

        makeImage: 'Cipta imej',
        newImageName: 'Nama imej baru',
        commitMessage: 'Mesej komit',
        author: 'Pengarang',
        ifPause: 'Jeda Kontena Semasa Penciptaan',
        ifMakeImageWithContainer: 'Cipta Imej Baru daripada Kontena Ini?',
    },
    cronjob: {
        create: 'Cipta tugas cron',
        edit: 'Edit tugas cron',
        errImport: 'Kandungan fail tidak normal:',
        errImportFormat: 'Data atau format tugas terjadual yang diimport tidak normal. Sila semak dan cuba lagi!',
        importHelper:
            'Tugas terjadual dengan nama sama akan dilangkau secara automatik semasa import. Tugas akan ditetapkan ke status 【Lumpuh】 secara lalai, dan ditetapkan ke status 【Menunggu Edit】 apabila perkaitan data tidak normal.',
        changeStatus: 'Tukar status',
        disableMsg:
            'Ini akan menghentikan tugas berjadual daripada dilaksanakan secara automatik. Adakah anda mahu meneruskan?',
        enableMsg: 'Ini akan membenarkan tugas berjadual dilaksanakan secara automatik. Adakah anda mahu meneruskan?',
        taskType: 'Jenis',
        record: 'Rekod',
        viewRecords: 'Rekod',
        shell: 'Shell',
        log: 'Log sandaran',
        logHelper: 'Log sistem sandaran',
        ogHelper1: '1. Log Sistem 1Panel ',
        logHelper2: '2. Log log masuk SSH pelayan ',
        logHelper3: '3. Semua log laman web ',
        containerCheckBox: 'Dalam kontena (tidak perlu masukkan arahan kontena)',
        containerName: 'Nama kontena',
        ntp: 'Penyelarasan masa',
        ntp_helper: 'Anda boleh mengkonfigurasi pelayan NTP di halaman Tetapan Pantas Alat.',
        app: 'Aplikasi sandaran',
        website: 'Laman web sandaran',
        rulesHelper: 'Menyokong pelbagai peraturan pengecualian, dipisahkan dengan koma Inggeris , contoh: *.log,*.sql',
        lastRecordTime: 'Waktu pelaksanaan terakhir',
        all: 'Semua',
        failedRecord: 'Rekod kegagalan',
        successRecord: 'Rekod berjaya',
        database: 'Pangkalan data sandaran',
        missBackupAccount: 'Akaun sandaran tidak dijumpai',
        syncDate: 'Waktu penyelarasan',
        clean: 'Bersihkan cache',
        curl: 'Akses URL',
        taskName: 'Nama',
        cronSpec: 'Kitaran pencetus',
        cronSpecDoc:
            'Kitaran pelaksanaan tersuai hanya menyokong format [minit jam hari bulan minggu], contohnya 0 0 * * *. Untuk maklumat lanjut, sila rujuk dokumen rasmi.',
        cronSpecHelper: 'Masukkan tempoh pelaksanaan yang betul',
        cleanHelper:
            'Operasi ini merekodkan semua rekod pelaksanaan tugas, fail sandaran, dan fail log. Adakah anda mahu meneruskan?',
        directory: 'Direktori sandaran',
        sourceDir: 'Direktori sumber',
        snapshot: 'Snapshot sistem',
        allOptionHelper:
            'Pelan tugas semasa adalah untuk menyandarkan semua [{0}]. Muat turun terus tidak disokong buat masa ini. Anda boleh menyemak senarai sandaran dalam menu [{0}].',
        exclusionRules: 'Peraturan pengecualian',
        exclusionRulesHelper: 'Peraturan pengecualian akan terpakai pada semua operasi mampatan bagi sandaran ini.',
        default_download_path: 'Pautan muat turun lalai',
        saveLocal: 'Simpan sandaran tempatan (sama seperti bilangan salinan storan awan)',
        url: 'Alamat URL',
        targetHelper: 'Akaun sandaran diselenggara dalam tetapan panel.',
        withImageHelper: 'Sandarkan imej kedai aplikasi, tetapi ini akan meningkatkan saiz fail snapshot.',
        ignoreApp: 'Kecualikan aplikasi',
        withImage: 'Sandarkan semua imej aplikasi',
        retainCopies: 'Simpan salinan',
        retryTimes: 'Bilangan Cubaan Semula',
        timeout: 'Masa Tamat',
        ignoreErr: 'Abaikan ralat',
        ignoreErrHelper: 'Abaikan ralat semasa sandaran untuk memastikan semua tugas sandaran dilaksanakan',
        retryTimesHelper: '0 bermaksud tiada cubaan semula selepas gagal',
        retainCopiesHelper: 'Bilangan salinan untuk menyimpan rekod pelaksanaan dan log',
        retainCopiesHelper1: 'Bilangan salinan untuk menyimpan fail sandaran',
        retainCopiesUnit: ' salinan (Lihat)',
        cronSpecRule: 'Format tempoh pelaksanaan dalam baris {0} adalah salah. Sila semak dan cuba lagi!',
        perMonthHelper: 'Laksanakan pada hari ke-{0} setiap bulan pada {1}:{2}',
        perWeekHelper: 'Laksanakan setiap minggu pada hari {0} pada {1}:{2}',
        perDayHelper: 'Laksanakan setiap hari pada {0}:{1}',
        perHourHelper: 'Laksanakan setiap jam pada minit ke-{0}',
        perNDayHelper: 'Laksanakan setiap {0} hari pada {1}:{2}',
        perNHourHelper: 'Laksanakan setiap {0} jam pada {1}',
        perNMinuteHelper: 'Laksanakan setiap {0} minit',
        perNSecondHelper: 'Laksanakan setiap {0} saat',
        perMonth: 'Setiap bulan',
        perWeek: 'Setiap minggu',
        perHour: 'Setiap jam',
        perNDay: 'Setiap N hari',
        perDay: 'Setiap hari',
        perNHour: 'Setiap N jam',
        perNMinute: 'Setiap N minit',
        perNSecond: 'Setiap N saat',
        day: 'hari',
        dayUnit: 'h',
        monday: 'Isnin',
        tuesday: 'Selasa',
        wednesday: 'Rabu',
        thursday: 'Khamis',
        friday: 'Jumaat',
        saturday: 'Sabtu',
        sunday: 'Ahad',
        shellContent: 'Skrip',
        errRecord: 'Log salah',
        errHandle: 'Kegagalan pelaksanaan tugas cron',
        noRecord: 'Picu Tugas Cron, dan anda akan melihat rekod di sini.',
        cleanData: 'Bersihkan data',
        cleanRemoteData: 'Padam data jarak jauh',
        cleanDataHelper: 'Padam fail sandaran yang dijana semasa tugas ini.',
        noLogs: 'Tiada keluaran tugas lagi...',
        errPath: 'Laluan sandaran [{0}] salah, tidak boleh dimuat turun!',
        cutWebsiteLog: 'Putaran log laman web',
        cutWebsiteLogHelper: 'Fail log yang diputar akan disandarkan ke direktori sandaran 1Panel.',

        requestExpirationTime: 'Waktu luput permintaan muat naik (Jam)',
        unitHours: 'Unit: Jam',
        alertTitle: 'Tugas Terancang - {0} 「{1}」 Amaran Kegagalan Tugas',
        library: {
            script: 'Skrip',
            isInteractive: 'Interaktif',
            interactive: 'Skrip interaktif',
            interactiveHelper:
                'Memerlukan input pengguna semasa pelaksanaan dan tidak boleh digunakan dalam tugas terjadual.',
            library: 'Perpustakaan Skrip',
            create: 'Tambah Skrip',
            edit: 'Sunting Skrip',
            groupHelper:
                'Tetapkan kumpulan yang berbeza berdasarkan ciri skrip, yang membolehkan operasi penapisan skrip dilakukan dengan lebih pantas.',
            handleHelper: 'Akan melaksanakan skrip {1} pada {0}, teruskan?',
            noSuchApp:
                'Perkhidmatan {0} tidak dikesan. Sila pasang dengan cepat menggunakan pustaka skrip terlebih dahulu!',
            syncHelper: 'Akan menyelaraskan pustaka skrip sistem. Operasi ini hanya melibatkan skrip sistem. Teruskan?',
        },
    },
    monitor: {
        globalFilter: 'Penapis Global',
        enableMonitor: 'Aktifkan',
        storeDays: 'Hari luput',
        cleanMonitor: 'Bersihkan rekod pemantauan',

        avgLoad: 'Purata beban',
        loadDetail: 'Butiran beban',
        resourceUsage: 'Penggunaan sumber',
        networkCard: 'Antara muka rangkaian',
        read: 'Baca',
        write: 'Tulis',
        readWriteCount: 'Operasi I/O',
        readWriteTime: 'Kelewatan I/O',
        today: 'Hari ini',
        yesterday: 'Semalam',
        lastNDay: '{0} hari terakhir',
        memory: 'Memori',
        cache: 'Cache',
        disk: 'Cakera',
        network: 'Rangkaian',
        up: 'Naik',
        down: 'Turun',
        interval: 'Selang (minit)',

        gpuUtil: 'Penggunaan GPU',
        temperature: 'Suhu',
        performanceState: 'Keadaan prestasi',
        powerUsage: 'Penggunaan kuasa',
        memoryUsage: 'Penggunaan memori',
        fanSpeed: 'Kelajuan kipas',
    },
    terminal: {
        local: 'Tempatan',
        localHelper: 'Nama tempatan hanya digunakan untuk pengenalan sistem tempatan.',
        connLocalErr: 'Tidak dapat mengesahkan secara automatik, sila isi maklumat log masuk pelayan tempatan.',
        testConn: 'Uji sambungan',
        saveAndConn: 'Simpan dan sambung',
        connTestOk: 'Maklumat sambungan tersedia',
        connTestFailed: 'Sambungan tidak tersedia, sila semak maklumat sambungan.',
        host: 'Hos | Hos-hos',
        createConn: 'Sambungan baru',
        manageGroup: 'Urus kumpulan',
        noHost: 'Tiada hos',
        groupChange: 'Tukar kumpulan',
        expand: 'Kembangkan semua',
        fold: 'Kontrak semua',
        batchInput: 'Pemprosesan kelompok',
        quickCommand: 'Arahan pantas | Arahan pantas',
        quickCommandHelper: 'Anda boleh menggunakan arahan pantas di bahagian bawah "Terminal -> Terminal".',
        groupDeleteHelper:
            'Selepas kumpulan dikeluarkan, semua sambungan dalam kumpulan akan dipindahkan ke kumpulan lalai. Adakah anda mahu meneruskan?',
        command: 'Arahan',
        quickCmd: 'Arahan pantas',
        addHost: 'Tambah',
        localhost: 'Localhost',
        ip: 'Alamat',
        authMode: 'Pengesahan',
        passwordMode: 'Kata laluan',
        rememberPassword: 'Ingat maklumat pengesahan',
        keyMode: 'Kunci Peribadi',
        key: 'Kunci peribadi',
        keyPassword: 'Kata laluan kunci peribadi',
        emptyTerminal: 'Tiada terminal yang sedang disambungkan.',
    },
    toolbox: {
        common: {
            toolboxHelper: 'Untuk beberapa isu pemasangan dan penggunaan, sila rujuk kepada',
        },
        swap: {
            swap: 'Partition Swap',
            swapHelper1:
                'Saiz partition swap harus 1 hingga 2 kali ganda memori fizikal, boleh disesuaikan berdasarkan keperluan tertentu;',
            swapHelper2:
                'Sebelum mencipta fail swap, pastikan cakera sistem mempunyai ruang yang mencukupi, kerana saiz fail swap akan menggunakan ruang cakera yang bersamaan;',
            swapHelper3:
                'Swap boleh membantu mengurangkan tekanan memori, tetapi ia hanya alternatif. Kebergantungan berlebihan pada swap boleh menyebabkan penurunan prestasi sistem. Disarankan untuk mengutamakan peningkatan memori atau pengoptimuman penggunaan memori aplikasi;',
            swapHelper4:
                'Disarankan untuk memantau penggunaan swap secara berkala bagi memastikan operasi sistem berjalan dengan normal.',
            swapDeleteHelper:
                'Operasi ini akan membuang partition Swap {0}. Atas sebab keselamatan sistem, fail yang sepadan tidak akan dipadamkan secara automatik. Jika pemadaman diperlukan, sila lakukan secara manual!',
            saveHelper: 'Sila simpan tetapan semasa terlebih dahulu!',
            saveSwap:
                'Menyimpan konfigurasi semasa akan menyesuaikan saiz partition Swap {0} kepada {1}. Adakah anda mahu meneruskan?',
            swapMin: 'Saiz minimum partition adalah 40 KB. Sila ubah dan cuba lagi!',
            swapMax: 'Nilai maksimum untuk saiz partition adalah {0}. Sila ubah dan cuba lagi!',
            swapOff: 'Saiz minimum partition adalah 40 KB. Menetapkannya kepada 0 akan mematikan partition Swap.',
        },
        device: {
            dnsHelper: 'Pelayan DNS',
            dnsAlert:
                'Perhatian! Mengubah konfigurasi fail /etc/resolv.conf akan mengembalikan fail ke nilai lalai selepas sistem dimulakan semula.',
            dnsHelper1:
                'Apabila terdapat pelbagai entri DNS, ia perlu dipaparkan pada baris baru. Contoh:\n114.114.114.114\n8.8.8.8',
            hostsHelper: 'Resolusi nama hos',
            hosts: 'Domain',
            hostAlert: 'Rekod komen tersembunyi, sila klik butang Semua Konfigurasi untuk melihat atau menetapkan',
            toolbox: 'Tetapan pantas',
            hostname: 'Nama hos',
            passwd: 'Kata laluan sistem',
            passwdHelper: 'Aksara input tidak boleh termasuk $ dan &',
            timeZone: 'Zon waktu',
            localTime: 'Waktu pelayan',
            timeZoneChangeHelper: 'Mengubah zon waktu sistem memerlukan memulakan semula perkhidmatan. Teruskan?',
            timeZoneHelper:
                'Jika anda tidak memasang arahan "timedatectl", anda mungkin tidak dapat mengubah zon waktu kerana sistem menggunakan arahan itu untuk mengubah zon waktu.',
            timeZoneCN: 'Beijing',
            timeZoneAM: 'Los Angeles',
            timeZoneNY: 'New York',
            ntpALi: 'Alibaba',
            ntpGoogle: 'Google',
            syncSite: 'Pelayan NTP',
            hostnameHelper:
                'Pengubahan nama hos bergantung pada arahan "hostnamectl". Jika arahan itu tidak dipasang, pengubahan mungkin gagal.',
            userHelper:
                'Nama pengguna bergantung pada arahan "whoami" untuk pengambilan. Jika arahan itu tidak dipasang, pengambilan mungkin gagal.',
            passwordHelper:
                'Pengubahan kata laluan bergantung pada arahan "chpasswd". Jika arahan itu tidak dipasang, pengubahan mungkin gagal.',
            hostHelper:
                'Terdapat nilai kosong dalam kandungan yang disediakan. Sila semak dan cuba lagi selepas pengubahsuaian!',
            dnsCheck: 'Uji Ketersediaan',
            dnsOK: 'Maklumat konfigurasi DNS tersedia!',
            dnsTestFailed: 'Maklumat konfigurasi DNS tidak tersedia.',
        },
        fail2ban: {
            sshPort: 'Dengar pada port SSH',
            sshPortHelper: 'Fail2ban semasa mendengar pada port sambungan SSH hos',
            unActive: 'Perkhidmatan Fail2ban tidak diaktifkan pada masa ini.',
            operation: 'Anda akan melaksanakan operasi "{0}" pada perkhidmatan Fail2ban. Adakah anda mahu meneruskan?',
            fail2banChange: 'Pengubahan Konfigurasi Fail2ban',
            ignoreHelper:
                'Senarai IP dalam senarai dibenarkan akan diabaikan untuk disekat. Adakah anda mahu meneruskan?',
            bannedHelper: 'Senarai IP dalam senarai sekatan akan disekat oleh pelayan. Adakah anda mahu meneruskan?',
            maxRetry: 'Percubaan maksimum',
            banTime: 'Waktu sekatan',
            banTimeHelper: 'Waktu sekatan lalai adalah 10 minit, -1 menunjukkan sekatan kekal',
            banTimeRule: 'Sila masukkan waktu sekatan yang sah atau -1',
            banAllTime: 'Sekatan kekal',
            findTime: 'Tempoh penemuan',
            banAction: 'Tindakan sekatan',
            banActionOption: 'Sekat alamat IP tertentu menggunakan {0}',
            allPorts: ' (Semua Port)',
            ignoreIP: 'Senarai IP yang dibenarkan',
            bannedIP: 'Senarai IP yang disekat',
            logPath: 'Laluan log',
            logPathHelper: 'Lalai adalah /var/log/secure atau /var/log/auth.log',
        },
        ftp: {
            ftp: 'Akaun FTP | Akaun FTP',
            notStart: 'Perkhidmatan FTP tidak berjalan pada masa ini, sila mulakan dahulu!',
            operation: 'Ini akan melaksanakan operasi "{0}" pada perkhidmatan FTP. Adakah anda mahu meneruskan?',
            noPasswdMsg:
                'Tidak dapat mendapatkan kata laluan akaun FTP semasa, sila tetapkan kata laluan dan cuba lagi!',
            enableHelper:
                'Mengaktifkan akaun FTP yang dipilih akan memulihkan kebenaran aksesnya. Adakah anda mahu meneruskan?',
            disableHelper:
                'Melumpuhkan akaun FTP yang dipilih akan membatalkan kebenaran aksesnya. Adakah anda mahu meneruskan?',
            syncHelper: 'Selaraskan data akaun FTP antara pelayan dan pangkalan data. Adakah anda mahu meneruskan?',
            dirSystem:
                'Direktori ini dikhaskan untuk sistem. Pengubahsuaian mungkin menyebabkan sistem rosak, sila ubah dan cuba lagi!',
            dirHelper: 'Membolehkan FTP memerlukan perubahan kebenaran direktori - sila pilih dengan berhati-hati',
            dirMsg: 'Membolehkan FTP akan mengubah kebenaran untuk seluruh direktori {0}. Teruskan?',
        },
        clam: {
            clam: 'Imbasan virus',
            cron: 'Imbasan berjadual',
            cronHelper: 'Versi profesional menyokong ciri imbasan berjadual',
            specErr: 'Ralat format jadual pelaksanaan, sila semak dan cuba lagi!',
            disableMsg:
                'Menghentikan pelaksanaan berjadual akan menghalang tugas imbasan ini daripada berjalan secara automatik. Adakah anda mahu meneruskan?',
            enableMsg:
                'Mengaktifkan pelaksanaan berjadual akan membolehkan tugas imbasan ini berjalan secara automatik pada selang waktu yang tetap. Adakah anda mahu meneruskan?',
            showFresh: 'Tunjukkan perkhidmatan pengemas kini tanda tangan',
            hideFresh: 'Sembunyikan perkhidmatan pengemas kini tanda tangan',
            clamHelper:
                'Konfigurasi minimum yang disyorkan untuk ClamAV ialah: RAM 3 GiB atau lebih, CPU teras tunggal dengan 2.0 GHz atau lebih tinggi, dan sekurang-kurangnya 5 GiB ruang cakera keras yang tersedia.',
            notStart: 'Perkhidmatan ClamAV tidak berjalan pada masa ini, sila mulakan dahulu!',
            removeRecord: 'Padam fail laporan',
            noRecords: 'Klik butang "Picu" untuk memulakan imbasan dan anda akan melihat rekod di sini.',
            removeResultHelper:
                'Padam fail laporan yang dijana semasa pelaksanaan tugas untuk membebaskan ruang storan.',
            removeInfected: 'Padam fail virus',
            removeInfectedHelper:
                'Padam fail virus yang dikesan semasa tugas untuk memastikan keselamatan pelayan dan operasi normal.',
            clamCreate: 'Cipta peraturan imbasan',
            infectedStrategy: 'Strategi fail dijangkiti',
            removeHelper: 'Padam fail virus, pilih dengan berhati-hati!',
            move: 'Pindah',
            moveHelper: 'Pindahkan fail virus ke direktori yang ditentukan',
            copyHelper: 'Salin fail virus ke direktori yang ditentukan',
            none: 'Jangan buat apa-apa',
            noneHelper: 'Tidak mengambil tindakan ke atas fail virus',
            scanDir: 'Imbas direktori',
            infectedDir: 'Direktori fail dijangkiti',
            scanDate: 'Tarikh Imbasan',
            scanResult: 'Hujung log imbasan',
            tail: 'Baris',
            infectedFiles: 'Fail dijangkiti',
            log: 'Butiran',
            clamConf: 'Daemon Clam AV',
            clamLog: '@:toolbox.clam.clamConf log',
            freshClam: 'FreshClam',
            freshClamLog: '@:toolbox.clam.freshClam log',
            alertHelper: 'Versi profesional menyokong imbasan berjadual dan amaran SMS',
            alertTitle: 'Tugas imbasan virus 「{0}」 mengesan amaran fail dijangkiti',
        },
    },
    logs: {
        core: 'Perkhidmatan Panel',
        agent: 'Pemantauan Nod',
        panelLog: 'Log Panel',
        operation: 'Log Operasi',
        login: 'Log Masuk',
        loginIP: 'IP Log Masuk',
        loginAddress: 'Alamat Log Masuk',
        loginAgent: 'Ejen Log Masuk',
        loginStatus: 'Status',
        system: 'Log Sistem',
        deleteLogs: 'Bersihkan Log',
        resource: 'Sumber',
        detail: {
            ai: 'AI',
            groups: 'Kumpulan',
            hosts: 'Hos',
            apps: 'Aplikasi',
            websites: 'Laman Web',
            containers: 'Kontena',
            files: 'Pengurusan Fail',
            runtimes: 'Persekitaran Jalankan',
            process: 'Pengurusan Proses',
            toolbox: 'Kotak Alat',
            backups: 'Sandaran / Pulihkan',
            tampers: 'Perlindungan daripada Pinda',
            xsetting: 'Tetapan Antara Muka',
            logs: 'Audit Log',
            settings: 'Tetapan Panel',
            cronjobs: 'Tugas Terjadual',
            waf: 'WAF',
            databases: 'Pangkalan',
            licenses: 'lesen',
            nodes: 'nod',
            commands: 'Perintah Pantas',
        },
        websiteLog: 'Log Laman Web',
        runLog: 'Log Jalankan',
        errLog: 'Log Ralat',
    },
    file: {
        fileDirNum: '{0} direktori, {1} fail,',
        currentDir: 'Direktori Semasa',
        dir: 'Folder',
        upload: 'Muat naik',
        uploadFile: 'Muat naik fail',
        uploadDirectory: 'Muat naik direktori',
        download: 'Muat turun',
        fileName: 'Nama fail',
        search: 'Cari',
        mode: 'Kebenaran',
        editPermissions: 'Edit kebenaran',
        owner: 'Pemilik',
        file: 'Fail',
        remoteFile: 'Muat turun dari jarak jauh',
        share: 'Kongsi',
        sync: 'Penyelarasan Data',
        size: 'Saiz',
        updateTime: 'Diubah suai',
        rename: 'Tukar nama',
        role: 'Kebenaran',
        info: 'Atribut',
        linkFile: 'Pautan lembut',
        batchoperation: 'Operasi kelompok',
        shareList: 'Senarai kongsi',
        zip: 'Dimampatkan',
        group: 'Kumpulan',
        path: 'Laluan',
        public: 'Lain-lain',
        setRole: 'Tetapkan kebenaran',
        link: 'Pautan fail',
        rRole: 'Baca',
        wRole: 'Tulis',
        xRole: 'Boleh laksana',
        name: 'Nama',
        compress: 'Mampatkan',
        deCompress: 'Nyahmampatkan',
        compressType: 'Format mampatan',
        compressDst: 'Laluan mampatan',
        replace: 'Timpa fail sedia ada',
        compressSuccess: 'Berjaya dimampatkan',
        deCompressSuccess: 'Berjaya dinyahmampatkan',
        deCompressDst: 'Laluan nyahmampatan',
        linkType: 'Jenis pautan',
        softLink: 'Pautan lembut',
        hardLink: 'Pautan keras',
        linkPath: 'Laluan pautan',
        selectFile: 'Pilih fail',
        downloadUrl: 'URL Jarak Jauh',
        downloadStart: 'Muat turun bermula',
        moveSuccess: 'Berjaya dipindahkan',
        copySuccess: 'Berjaya disalin',
        move: 'Pindah',
        calculate: 'Kira',
        canNotDeCompress: 'Tidak dapat nyahmampatkan fail ini',
        uploadSuccess: 'Berjaya dimuat naik',
        downloadProcess: 'Kemajuan muat turun',
        downloading: 'Sedang muat turun...',
        infoDetail: 'Sifat fail',
        root: 'Direktori akar',
        list: 'Senarai fail',
        sub: 'Subfolder',
        downloadSuccess: 'Berjaya dimuat turun',
        theme: 'Tema',
        language: 'Bahasa',
        eol: 'Akhir baris',
        copyDir: 'Salin',
        paste: 'Tampal',
        changeOwner: 'Ubah suai pengguna dan kumpulan pengguna',
        containSub: 'Guna perubahan kebenaran secara rekursif',
        ownerHelper:
            'Pengguna lalai persekitaran operasi PHP: kumpulan pengguna adalah 1000:1000, adalah normal jika pengguna di dalam dan di luar kontena menunjukkan ketidakkonsistenan',
        searchHelper: 'Sokong wildcard seperti *',
        uploadFailed: '[{0}] Gagal memuat naik fail',
        fileUploadStart: 'Memuat naik [{0}]....',
        currentSelect: 'Pilihan semasa: ',
        unsupportedType: 'Jenis fail tidak disokong',
        deleteHelper:
            'Adakah anda pasti mahu memadam fail berikut? Secara lalai, ia akan masuk ke tong kitar semula selepas dipadamkan',
        fileHelper: 'Nota:\n1. Keputusan carian tidak boleh diisih.\n2. Folder tidak boleh diisih mengikut saiz.',
        forceDeleteHelper: 'Padamkan fail secara kekal (tanpa masuk ke tong kitar semula, padam terus)',
        recycleBin: 'Tong kitar semula',
        sourcePath: 'Laluan asal',
        deleteTime: 'Masa padam',
        confirmReduce: 'Adakah anda pasti mahu memulihkan fail berikut?',
        reduceSuccess: 'Berjaya dipulihkan',
        reduce: 'Pulihkan',
        reduceHelper:
            'Jika fail atau direktori dengan nama yang sama wujud di laluan asal, ia akan ditimpa. Adakah anda mahu meneruskan?',
        clearRecycleBin: 'Bersihkan',
        clearRecycleBinHelper: 'Adakah anda mahu membersihkan tong kitar semula?',
        favorite: 'Kegemaran',
        removeFavorite: 'Buang daripada kegemaran?',
        addFavorite: 'Tambah/Buang ke Kegemaran',
        clearList: 'Bersihkan senarai',
        deleteRecycleHelper: 'Adakah anda pasti mahu memadam fail berikut secara kekal?',
        typeErrOrEmpty: '[{0}] jenis fail salah atau folder kosong',
        dropHelper: 'Seret fail yang anda mahu muat naik ke sini',
        fileRecycleBin: 'Aktifkan tong kitar semula',
        fileRecycleBinMsg: '{0} tong kitar semula',
        wordWrap: 'Bungkus secara automatik',
        deleteHelper2: 'Adakah anda pasti mahu memadam fail yang dipilih? Operasi pemadaman tidak boleh dipulihkan',
        ignoreCertificate: 'Benarkan sambungan pelayan tidak selamat',
        ignoreCertificateHelper:
            'Membenarkan sambungan pelayan tidak selamat boleh menyebabkan kebocoran atau pengubahan data. Gunakan pilihan ini hanya jika mempercayai sumber muat turun.',
        uploadOverLimit: 'Bilangan fail melebihi 1000! Sila mampatkan dan muat naik',
        clashDitNotSupport: 'Nama fail dilarang mengandungi .1panel_clash',
        clashDeleteAlert: 'Folder "Tong Kitar Semula" tidak boleh dipadamkan',
        clashOpenAlert: 'Sila klik butang "Tong Kitar Semula" untuk membuka direktori tong kitar semula',
        right: 'Ke hadapan',
        back: 'Ke belakang',
        top: 'Pergi ke atas',
        up: 'Kembali',
        openWithVscode: 'Buka dengan VS Code',
        vscodeHelper: 'Pastikan VS Code dipasang secara tempatan dan pemalam SSH Remote dikonfigurasikan',
        saveContentAndClose: 'Fail telah diubah suai, adakah anda mahu menyimpan dan menutupnya?',
        saveAndOpenNewFile: 'Fail telah diubah suai, adakah anda mahu menyimpan dan membuka fail baru?',
        noEdit: 'Fail tidak diubah suai, tiada keperluan untuk tindakan ini!',
        noNameFolder: 'Folder tanpa nama',
        noNameFile: 'Fail tanpa nama',
        minimap: 'Peta mini kod',
        fileCanNotRead: 'Fail tidak dapat dibaca',
        panelInstallDir: 'Direktori pemasangan 1Panel tidak boleh dipadamkan',
        wgetTask: 'Tugas Muat Turun',
        existFileTitle: 'Amaran fail dengan nama yang sama',
        existFileHelper: 'Fail yang dimuat naik mengandungi fail dengan nama yang sama. Adakah anda mahu menimpanya?',
        existFileSize: 'Saiz fail (baru -> lama)',
        existFileDirHelper: 'Fail/folder yang dipilih mempunyai nama yang sama. Sila berhati-hati!\n',
        coverDirHelper: 'Folder yang dipilih untuk ditimpa akan disalin ke laluan destinasi!',
        noSuchFile: 'Fail atau direktori tidak ditemui. Sila periksa dan cuba lagi.',
        setting: 'tetapan',
        showHide: 'Tunjukkan fail tersembunyi',
        noShowHide: 'Jangan tunjukkan fail tersembunyi',
        cancelUpload: 'Batalkan Muat Naik',
        cancelUploadHelper:
            'Adakah hendak membatalkan muat naik, selepas pembatalan senarai muat naik akan dikosongkan.',
    },
    ssh: {
        autoStart: 'Mula automatik',
        enable: 'Aktifkan Mula Automatik',
        disable: 'Nyahaktif Mula Automatik',
        sshAlert:
            'Data senarai disusun berdasarkan tarikh log masuk. Menukar zon waktu atau melakukan operasi lain boleh menyebabkan penyimpangan dalam tarikh log log masuk.',
        sshAlert2:
            'Anda boleh menggunakan "Fail2ban" di "Kotak Alat" untuk menyekat alamat IP yang cuba menyerang secara kasar, dan ini akan meningkatkan keselamatan hos.',
        sshOperate: 'Operasi "{0}" pada perkhidmatan SSH akan dilaksanakan. Adakah anda mahu meneruskan?',
        sshChange: 'Tetapan SSH',
        sshChangeHelper: 'Tindakan ini mengubah "{0}" kepada "{1}". Adakah anda mahu meneruskan?',
        sshFileChangeHelper:
            'Mengubah fail konfigurasi boleh menyebabkan ketidaktersediaan perkhidmatan. Lakukan operasi ini dengan berhati-hati. Adakah anda mahu meneruskan?',
        port: 'Port',
        portHelper: 'Tentukan port yang didengar oleh perkhidmatan SSH.',
        listenAddress: 'Alamat dengar',
        allV4V6: '0.0.0.0:{0}(IPv4) dan :::{0}(IPv6)',
        listenHelper:
            'Membiarkan tetapan IPv4 dan IPv6 kosong akan mendengar pada "0.0.0.0:{0}(IPv4)" dan ":::{0}(IPv6)".',
        addressHelper: 'Tentukan alamat yang didengar oleh perkhidmatan SSH.',
        permitRootLogin: 'Benarkan log masuk pengguna root',
        rootSettingHelper: 'Kaedah log masuk lalai untuk pengguna root ialah "Benarkan log masuk SSH".',
        rootHelper1: 'Benarkan log masuk SSH',
        rootHelper2: 'Lumpuhkan log masuk SSH',
        rootHelper3: 'Hanya log masuk dengan kunci dibenarkan',
        rootHelper4: 'Hanya arahan yang telah ditetapkan boleh dilaksanakan. Operasi lain tidak dibenarkan.',
        passwordAuthentication: 'Pengesahan kata laluan',
        pwdAuthHelper: 'Sama ada untuk mengaktifkan pengesahan kata laluan. Parameter ini diaktifkan secara lalai.',
        pubkeyAuthentication: 'Pengesahan kunci',
        privateKey: 'Kunci Persendirian',
        publicKey: 'Kunci Awam',
        password: 'Kata Laluan',
        createMode: 'Kaedah Penciptaan',
        generate: 'Jana Automatik',
        unSyncPass: 'Kata laluan kunci tidak dapat diselaraskan',
        syncHelper:
            'Operasi segerak akan membersihkan kunci tidak sah dan menyegerakkan pasangan kunci baru yang lengkap. Teruskan?',
        input: 'Input Manual',
        import: 'Muat Naik Fail',
        pubkey: 'Maklumat kunci',
        pubKeyHelper: 'Maklumat kunci semasa hanya berkuat kuasa untuk pengguna {0}',
        encryptionMode: 'Mod penyulitan',
        passwordHelper: 'Boleh mengandungi 6 hingga 10 angka dan huruf dalam kedua-dua huruf besar dan kecil',
        reGenerate: 'Jana semula kunci',
        keyAuthHelper: 'Sama ada untuk mengaktifkan pengesahan kunci.',
        useDNS: 'Gunakan DNS',
        dnsHelper:
            'Kawal sama ada fungsi resolusi DNS diaktifkan pada pelayan SSH untuk mengesahkan identiti sambungan.',
        analysis: 'Maklumat statistik',
        denyHelper:
            "Melaksanakan operasi 'tolak' pada alamat berikut. Selepas menetapkan, IP akan dilarang mengakses pelayan. Adakah anda mahu meneruskan?",
        acceptHelper:
            "Melaksanakan operasi 'terima' pada alamat berikut. Selepas menetapkan, IP akan mendapatkan semula akses normal. Adakah anda mahu meneruskan?",
        noAddrWarning: 'Tiada alamat [{0}] yang dipilih pada masa ini. Sila periksa dan cuba lagi!',
        loginLogs: 'Log Masuk',
        loginMode: 'Mod',
        authenticating: 'Kunci',
        publickey: 'Kunci',
        belong: 'Milikan',
        local: 'Tempatan',
        session: 'Sesi | Sesi-sesi',
        loginTime: 'Waktu log masuk',
        loginIP: 'IP log masuk',
        stopSSHWarn: 'Adakah anda mahu memutuskan sambungan SSH ini',
    },
    setting: {
        panel: 'Panel',
        user: 'Pengguna panel',
        userChange: 'Tukar pengguna panel',
        userChangeHelper: 'Menukar pengguna panel akan menyebabkan anda log keluar. Teruskan?',
        passwd: 'Kata laluan panel',
        emailHelper: 'Untuk pemulihan kata laluan',
        title: 'Alias panel',
        panelPort: 'Port panel',
        titleHelper:
            'Menyokong panjang 3 hingga 30 aksara dengan huruf Inggeris, huruf Cina, nombor, ruang kosong dan aksara khas yang biasa',
        portHelper:
            'Julat port yang disarankan ialah 8888 hingga 65535. Nota: Jika pelayan mempunyai kumpulan keselamatan, benarkan port baru dari kumpulan keselamatan terlebih dahulu',
        portChange: 'Tukar port',
        portChangeHelper: 'Ubah port perkhidmatan dan mulakan semula perkhidmatan. Adakah anda mahu meneruskan?',
        theme: 'Tema',
        menuTabs: 'Tab menu',
        dark: 'Gelap',
        darkGold: 'Emas Gelap',
        light: 'Terang',
        auto: 'Ikut Sistem',
        language: 'Bahasa',
        languageHelper:
            'Secara lalai, ia mengikuti bahasa penyemak imbas. Parameter ini hanya berkuat kuasa pada penyemak imbas semasa',
        sessionTimeout: 'Tempoh tamat sesi',
        sessionTimeoutError: 'Tempoh tamat sesi minimum ialah 300 saat',
        sessionTimeoutHelper: 'Panel akan log keluar secara automatik jika tiada operasi lebih daripada {0} saat.',
        systemIP: 'Alamat akses lalai',
        systemIPHelper:
            'Penghantaran semula aplikasi, akses kontena dan fungsi lain akan menggunakan alamat ini untuk penghalaan. Setiap nod boleh ditetapkan dengan alamat yang berbeza.',
        proxy: 'Proksi pelayan',
        proxyHelper: 'Ia akan berkuat kuasa dalam senario berikut selepas anda menyediakan pelayan proksi:',
        proxyHelper1: 'Muat turun pakej pemasangan dan penyelarasan dari kedai aplikasi (Edisi Profesional sahaja)',
        proxyHelper2: 'Kemas kini sistem dan pengambilan maklumat kemas kini (Edisi Profesional sahaja)',
        proxyHelper4: 'Rangkaian Docker akan diakses melalui pelayan proksi (Edisi Profesional sahaja)',
        proxyHelper3: 'Pengesahan dan penyelarasan lesen sistem',
        proxyHelper5: 'Muat turun dan penyegerakan bersepadu untuk pustaka skrip jenis sistem (Ciri Edisi Profesional)',
        proxyHelper6: 'Mohon sijil (Fungsi versi Pro)',
        proxyType: 'Jenis proksi',
        proxyUrl: 'Alamat proksi',
        proxyPort: 'Port proksi',
        proxyPasswdKeep: 'Ingat Kata Laluan',
        proxyDocker: 'Proksi Docker',
        proxyDockerHelper:
            'Selaraskan konfigurasi pelayan proksi ke Docker, menyokong tarikan imej pelayan luar talian dan operasi lain',
        syncToNode: 'Penyegerakan ke nod anak',
        syncToNodeHelper: 'Penyegerakan tetapan ke nod lain',
        nodes: 'Nod',
        selectNode: 'Pilih nod',
        selectNodeError: 'Sila pilih nod',
        apiInterface: 'Aktifkan API',
        apiInterfaceClose: 'Setelah ditutup, antara muka API tidak boleh diakses. Adakah anda mahu meneruskan?',
        apiInterfaceHelper: 'Benarkan aplikasi pihak ketiga mengakses API.',
        apiInterfaceAlert1:
            'Jangan aktifkan dalam persekitaran pengeluaran kerana ia mungkin meningkatkan risiko keselamatan pelayan.',
        apiInterfaceAlert2:
            'Jangan gunakan aplikasi pihak ketiga untuk memanggil API bagi mengelakkan potensi ancaman keselamatan.',
        apiInterfaceAlert3: 'Dokumen API',
        apiInterfaceAlert4: 'Dokumen Penggunaan',
        apiKey: 'Kunci API',
        apiKeyHelper: 'Kunci API digunakan untuk aplikasi pihak ketiga mengakses API.',
        ipWhiteList: 'Senarai putih IP',
        ipWhiteListEgs: 'Satu per baris. Contoh,\n172.161.10.111\n172.161.10.0/24',
        ipWhiteListHelper: 'IP dalam senarai putih boleh mengakses API, 0.0.0.0/0 (semua IPv4), ::/0 (semua IPv6)',
        apiKeyValidityTime: 'Tempoh sah kunci antara muka',
        apiKeyValidityTimeEgs: 'Tempoh sah kunci antara muka (dalam minit)',
        apiKeyValidityTimeHelper:
            'Cap waktu antara muka sah jika perbezaannya dengan cap waktu semasa (dalam minit) berada dalam julat yang dibenarkan. Nilai 0 melumpuhkan pengesahan.',
        apiKeyReset: 'Tetapkan semula kunci antara muka',
        apiKeyResetHelper:
            'Perkhidmatan kunci yang berkaitan akan menjadi tidak sah. Sila tambah kunci baru pada perkhidmatan',
        confDockerProxy: 'Konfigurasi proksi docker',
        restartNowHelper: 'Mengkonfigurasi proksi Docker memerlukan memulakan semula perkhidmatan Docker.',
        restartNow: 'Mulakan semula sekarang',
        restartLater: 'Mulakan semula secara manual nanti',
        systemIPWarning:
            'Nod semasa belum mempunyai alamat akses lalai yang dikonfigurasi. Sila pergi ke tetapan panel untuk mengkonfigurasinya!',
        systemIPWarning1: 'Alamat pelayan semasa ditetapkan kepada {0}, dan pengalihan cepat tidak mungkin!',
        defaultNetwork: 'Kad rangkaian',
        syncTime: 'Waktu Pelayan',
        timeZone: 'Zon Waktu',
        timeZoneChangeHelper:
            'Menukar zon waktu memerlukan memulakan semula perkhidmatan. Adakah anda mahu meneruskan?',
        timeZoneHelper:
            'Pengubahan zon waktu bergantung pada perkhidmatan sistem timedatectl. Berkuat kuasa selepas mulakan semula perkhidmatan 1Panel.',
        timeZoneCN: 'Beijing',
        timeZoneAM: 'Los Angeles',
        timeZoneNY: 'New York',
        ntpALi: 'Alibaba',
        ntpGoogle: 'Google',
        syncSite: 'Ntp Server',
        syncSiteHelper:
            'Operasi ini akan menggunakan {0} sebagai sumber untuk penyegerakan masa sistem. Adakah anda mahu meneruskan?',
        changePassword: 'Tukar Kata Laluan',
        oldPassword: 'Kata laluan asal',
        newPassword: 'Kata laluan baru',
        retryPassword: 'Sahkan kata laluan',
        noSpace: 'Maklumat input tidak boleh mengandungi aksara ruang',
        duplicatePassword: 'Kata laluan baru tidak boleh sama dengan kata laluan asal, sila masukkan semula!',
        diskClean: 'Pembersihan Cache',
        developerMode: 'Program Pratonton',
        developerModeHelper:
            'Anda akan dapat mengalami ciri dan pembaikan baru sebelum ia dilancarkan secara meluas dan memberikan maklum balas awal.',
        thirdParty: 'Akaun pihak ketiga',
        noTypeForCreate: 'Tiada jenis sandaran yang sedang dibuat',
        LOCAL: 'Cakera pelayan',
        OSS: 'Ali OSS',
        S3: 'Amazon S3',
        mode: 'Mode',
        MINIO: 'MinIO',
        SFTP: 'SFTP',
        WebDAV: 'WebDAV',
        WebDAVAlist: 'WebDAV sambungkan Alist boleh merujuk kepada dokumentasi rasmi',
        OneDrive: 'Microsoft OneDrive',
        isCN: 'Century Internet',
        isNotCN: 'Versi Antarabangsa',
        client_id: 'ID Klien',
        client_secret: 'Rahsia Klien',
        redirect_uri: 'URL Penghalaan Semula',
        onedrive_helper: 'Konfigurasi tersuai boleh dirujuk dalam dokumentasi rasmi',
        refreshTime: 'Waktu Penyegaran Token',
        refreshStatus: 'Status Penyegaran Token',
        backupDir: 'Direktori Sandaran',
        codeWarning: 'Format kod kebenaran semasa tidak betul, sila sahkan semula!',
        code: 'Kod Auth',
        codeHelper:
            'Sila klik butang "Peroleh", kemudian log masuk ke OneDrive dan salin kandungan selepas "code" dalam pautan yang telah diarahkan semula. Tampalkan kandungan tersebut ke dalam kotak input ini. Untuk arahan spesifik, sila rujuk dokumentasi rasmi.',
        loadCode: 'Peroleh',
        COS: 'Tencent COS',
        ap_beijing_1: 'Beijing Zone 1',
        ap_beijing: 'Beijing',
        ap_nanjing: 'Nanjing',
        ap_shanghai: 'Shanghai',
        ap_guangzhou: 'Guangzhou',
        ap_chengdu: 'Chengdu',
        ap_chongqing: 'Chongqing',
        ap_shenzhen_fsi: 'Shenzhen Financial',
        ap_shanghai_fsi: 'Shanghai Financial',
        ap_beijing_fsi: 'Beijing Financial',
        ap_hongkong: 'Hong Kong, China',
        ap_singapore: 'Singapore',
        ap_mumbai: 'Mumbai',
        ap_jakarta: 'Jakarta',
        ap_seoul: 'Seoul',
        ap_bangkok: 'Bangkok',
        ap_tokyo: 'Tokyo',
        na_siliconvalley: 'Silicon Valley (US West)',
        na_ashburn: 'Ashburn (US East)',
        na_toronto: 'Toronto',
        sa_saopaulo: 'Sao Paulo',
        eu_frankfurt: 'Frankfurt',
        KODO: 'Qiniu Kodo',
        scType: ' Jenis storan',
        typeStandard: 'Standard',
        typeStandard_IA: 'Standard_IA',
        typeArchive: 'Archive',
        typeDeep_Archive: 'Deep_Archive',
        scLighthouse: 'Lalai, Penyimpanan objek ringan hanya menyokong jenis penyimpanan ini',
        scStandard:
            'Storan Standard sesuai untuk senario perniagaan dengan sejumlah besar fail panas yang memerlukan akses masa nyata, interaksi data yang kerap, dan sebagainya.',
        scStandard_IA:
            'Storan kekerapan rendah sesuai untuk senario perniagaan dengan kekerapan akses yang agak rendah dan menyimpan data sekurang-kurangnya 30 hari.',
        scArchive: 'Storan arkib sesuai untuk senario perniagaan dengan kekerapan akses yang sangat rendah.',
        scDeep_Archive:
            'Storan sejuk tahan lama sesuai untuk senario perniagaan dengan kekerapan akses yang sangat rendah.',
        archiveHelper:
            'Fail storan arkib tidak boleh dimuat turun secara langsung dan mesti dipulihkan terlebih dahulu melalui laman web penyedia perkhidmatan awan yang berkaitan. Sila gunakan dengan berhati-hati!',
        backupAlert:
            'Jika penyedia awan serasi dengan protokol S3, anda boleh menggunakan Amazon S3 secara langsung untuk sandaran.',
        domain: 'Domain pemecutan',
        backupAccount: 'Akaun sandaran | Akaun sandaran',
        loadBucket: 'Dapatkan baldi',
        accountName: 'Nama akaun',
        accountKey: 'Kunci akaun',
        address: 'Alamat',
        path: 'Laluan',

        safe: 'Keselamatan',
        bindInfo: 'Maklumat ikatan',
        bindAll: 'Dengar Semua',
        bindInfoHelper:
            'Mengubah alamat atau protokol perkhidmatan pendengaran boleh menyebabkan ketidaktersediaan perkhidmatan. Adakah anda mahu meneruskan?',
        ipv6: 'Dengar IPv6',
        bindAddress: 'Alamat pendengaran',
        entrance: 'Pintu masuk',
        showEntrance: 'Tunjukkan amaran dilumpuhkan di halaman "Tinjauan"',
        entranceHelper:
            'Mengaktifkan pintu masuk keselamatan hanya akan membolehkan log masuk ke panel melalui pintu masuk keselamatan yang ditentukan.',
        entranceError:
            'Sila masukkan titik masuk log masuk yang selamat sepanjang 5-116 aksara, hanya nombor atau huruf yang disokong.',
        entranceInputHelper: 'Biarkan kosong untuk melumpuhkan pintu masuk keselamatan.',
        randomGenerate: 'Rawak',
        expirationTime: 'Tarikh Tamat Tempoh',
        unSetting: 'Tidak ditetapkan',
        noneSetting:
            'Tetapkan masa tamat tempoh untuk kata laluan panel. Selepas tamat tempoh, anda perlu menetapkan semula kata laluan',
        expirationHelper:
            'Jika masa tamat tempoh kata laluan ialah [0] hari, fungsi tamat tempoh kata laluan dilumpuhkan',
        days: 'Hari Tamat Tempoh',
        expiredHelper: 'Kata laluan semasa telah tamat tempoh. Sila tukar kata laluan lagi.',
        timeoutHelper:
            '[ {0} hari ] Kata laluan panel akan tamat tempoh. Selepas tamat tempoh, anda perlu menetapkan semula kata laluan',
        complexity: 'Pengesahan kerumitan',
        complexityHelper:
            'Selepas anda mengaktifkannya, peraturan pengesahan kata laluan akan menjadi: 8-30 aksara, termasuk bahasa Inggeris, nombor, dan sekurang-kurangnya dua aksara khas.',
        bindDomain: 'Ikatan domain',
        unBindDomain: 'Buka ikatan domain',
        panelSSL: 'Panel SSL',
        unBindDomainHelper:
            'Tindakan membuka ikatan nama domain boleh menyebabkan ketidakamanan sistem. Adakah anda mahu meneruskan?',
        bindDomainHelper: 'Selepas anda mengikat domain, hanya domain itu yang boleh mengakses perkhidmatan 1Panel.',
        bindDomainHelper1: 'Biarkan kosong untuk melumpuhkan ikatan nama domain.',
        bindDomainWarning:
            'Selepas ikatan domain, anda akan log keluar dan hanya boleh mengakses perkhidmatan 1Panel melalui nama domain yang ditentukan dalam tetapan. Adakah anda mahu meneruskan?',
        allowIPs: 'IP Dibenarkan',
        unAllowIPs: 'IP Tidak Dibenarkan',
        unAllowIPsWarning:
            'Membenarkan IP kosong akan membolehkan semua IP mengakses sistem, yang boleh menyebabkan ketidakamanan sistem. Adakah anda mahu meneruskan?',
        allowIPsHelper:
            'Selepas anda menetapkan senarai alamat IP yang dibenarkan, hanya alamat IP dalam senarai yang boleh mengakses perkhidmatan panel.',
        allowIPsWarning:
            'Selepas anda menetapkan senarai alamat IP yang dibenarkan, hanya alamat IP dalam senarai yang boleh mengakses perkhidmatan panel. Adakah anda mahu meneruskan?',
        allowIPsHelper1: 'Biarkan kosong untuk melumpuhkan sekatan alamat IP.',
        allowIPEgs: 'Satu per baris. Contoh,\n*************\n***********/24',
        mfa: 'Pengesahan dua faktor (2FA)',
        mfaClose: 'Melumpuhkan MFA akan mengurangkan keselamatan perkhidmatan. Adakah anda mahu meneruskan?',
        secret: 'Rahsia',
        mfaInterval: 'Selang penyegaran (saat)',
        mfaTitleHelper:
            'Judul digunakan untuk membezakan hos 1Panel yang berbeza. Imbas lagi atau tambahkan kunci rahsia secara manual selepas anda mengubah judul.',
        mfaIntervalHelper:
            'Imbas lagi atau tambahkan kunci rahsia secara manual selepas anda mengubah masa penyegaran.',
        mfaAlert:
            'Token satu kali ialah nombor 6 digit yang dijana secara dinamik berdasarkan masa semasa. Pastikan masa pelayan disegerakkan.',
        mfaHelper: 'Selepas anda mengaktifkannya, token satu kali perlu disahkan.',
        mfaHelper1: 'Muat turun aplikasi pengesahan, contohnya,',
        mfaHelper2:
            'Untuk mendapatkan token satu kali, imbas kod QR berikut menggunakan aplikasi pengesahan anda atau salin kunci rahsia ke dalam aplikasi pengesahan anda.',
        mfaHelper3: 'Masukkan enam digit dari aplikasi',
        mfaCode: 'Token satu kali',
        sslChangeHelper: 'Ubah tetapan https dan mulakan semula perkhidmatan. Adakah anda mahu meneruskan?',
        sslDisable: 'Lumpuhkan',
        sslDisableHelper:
            'Jika perkhidmatan https dilumpuhkan, anda perlu memulakan semula panel untuk ia berkuat kuasa. Adakah anda mahu meneruskan?',
        noAuthSetting: 'Tetapan tidak dibenarkan',
        noAuthSettingHelper:
            'Apabila pengguna tidak log masuk dengan pintu masuk keselamatan yang ditentukan, atau tidak mengakses panel dari IP atau nama domain yang ditentukan, respons ini boleh menyembunyikan ciri panel.',
        responseSetting: 'Tetapan respons',
        help200: 'Halaman Bantuan',
        error400: 'Permintaan Buruk',
        error401: 'Tidak Dibenarkan',
        error403: 'Dilarang',
        error404: 'Tidak Dijumpai',
        error408: 'Permintaan Tamat Masa',
        error416: 'Julat Tidak Memuaskan',
        error444: 'Sambungan ditutup',
        error500: 'Ralat Pelayan',

        https: 'Menetapkan protokol akses HTTPS untuk panel boleh meningkatkan keselamatan akses panel.',
        certType: 'Jenis sijil',
        selfSigned: 'Diterbitkan sendiri',
        selfSignedHelper:
            'Pelayar mungkin tidak mempercayai sijil diterbitkan sendiri dan mungkin memaparkan amaran keselamatan.',
        select: 'Pilih',
        domainOrIP: 'Domain atau IP:',
        timeOut: 'Tamat Masa',
        rootCrtDownload: 'Muat turun sijil akar',
        primaryKey: 'Kunci utama',
        certificate: 'Sijil',
        backupJump:
            'Fail sandaran tidak berada dalam senarai sandaran semasa, sila cuba muat turun dari direktori fail dan import untuk sandaran.',

        snapshot: 'Snapshot | Snapshots',
        noAppData: 'Tiada aplikasi sistem yang boleh dipilih',
        noBackupData: 'Tiada data sandaran yang boleh dipilih',
        stepBaseData: 'Data Asas',
        stepAppData: 'Aplikasi Sistem',
        stepPanelData: 'Data Sistem',
        stepBackupData: 'Data Sandaran',
        stepOtherData: 'Data Lain',
        operationLog: 'Simpan log operasi',
        loginLog: 'Simpan log akses',
        systemLog: 'Simpan log sistem',
        taskLog: 'Simpan log tugas',
        monitorData: 'Simpan data pemantauan',
        dockerConf: 'Simpan Konfigurasi Docker',
        selectAllImage: 'Simpan semua imej aplikasi',
        logLabel: 'Log',
        agentLabel: 'Konfigurasi Nod',
        appDataLabel: 'Data Aplikasi',
        appImage: 'Imej Aplikasi',
        appBackup: 'Sandaran Aplikasi',
        backupLabel: 'Direktori Sandaran',
        confLabel: 'Fail Konfigurasi',
        dockerLabel: 'Konteks',
        taskLabel: 'Tugas Berjadual',
        resourceLabel: 'Direktori Sumber Aplikasi',
        runtimeLabel: 'Persekitaran Runtime',
        appLabel: 'Aplikasi',
        databaseLabel: 'Pangkalan Data',
        snapshotLabel: 'Fail Snapshot',
        websiteLabel: 'Laman Web',
        directoryLabel: 'Direktori',
        appStoreLabel: 'Kedai Aplikasi',
        shellLabel: 'Skrip',
        tmpLabel: 'Direktori Sementara',
        sslLabel: 'Direktori Sijil',
        reCreate: 'Gagal membuat snapshot',
        reRollback: 'Gagal membalikkan snapshot',
        deleteHelper:
            'Semua fail snapshot termasuk yang ada dalam akaun sandaran pihak ketiga akan dipadamkan. Adakah anda mahu meneruskan?',
        status: 'Status snapshot',
        ignoreRule: 'Aturan abaikan',
        editIgnoreRule: 'Sunting aturan abaikan',
        ignoreHelper:
            'Aturan ini akan digunakan untuk memampatkan dan menyandarkan direktori data 1Panel semasa membuat snapshot. Secara lalai, fail soket diabaikan.',
        ignoreHelper1: 'Satu per baris. Contoh,\n*.log\n/opt/1panel/cache',
        panelInfo: 'Tulis maklumat asas 1Panel',
        panelBin: 'Sandarkan fail sistem 1Panel',
        daemonJson: 'Sandarkan fail konfigurasi Docker',
        appData: 'Sandarkan aplikasi yang dipasang dari 1Panel',
        panelData: 'Sandarkan direktori data 1Panel',
        backupData: 'Sandarkan direktori sandaran tempatan untuk 1Panel',
        compress: 'Cipta fail snapshot',
        upload: 'Muat naik fail snapshot',
        recoverDetail: 'Butiran pemulihan',
        createSnapshot: 'Cipta snapshot',
        importSnapshot: 'Segerakkan snapshot',
        recover: 'Pulihkan',
        lastRecoverAt: 'Waktu pemulihan terakhir',
        lastRollbackAt: 'Waktu pemulangan terakhir',
        reDownload: 'Muat turun semula fail sandaran',
        recoverErrArch: 'Pemulihan snapshot antara seni bina pelayan yang berbeza tidak disokong!',
        recoverErrSize: 'Kekurangan ruang cakera dikesan, sila periksa atau bersihkan dan cuba lagi!',
        recoverHelper: 'Memulakan pemulihan dari snapshot {0}, sila sahkan maklumat berikut sebelum meneruskan:',
        recoverHelper1: 'Pemulihan memerlukan mulakan semula perkhidmatan Docker dan 1Panel',
        recoverHelper2:
            'Pastikan terdapat ruang cakera yang mencukupi pada pelayan (Saiz fail snapshot: {0}, Ruang tersedia: {1})',
        recoverHelper3:
            'Pastikan seni bina pelayan sepadan dengan seni bina pelayan di mana snapshot dicipta (Seni bina pelayan semasa: {0})',
        rollback: 'Pulangkan semula',
        rollbackHelper:
            'Pulangkan semula pemulihan ini akan menggantikan semua fail dari pemulihan ini, dan mungkin memerlukan mulakan semula perkhidmatan Docker dan 1Panel. Adakah anda mahu meneruskan?',

        upgradeHelper: 'Kemas kini memerlukan mulakan semula perkhidmatan 1Panel. Adakah anda mahu meneruskan?',
        rollbackLocalHelper:
            'Nod utama tidak menyokong rollback secara langsung. Sila laksanakan arahan [1pctl restore] secara manual untuk rollback!',
        noUpgrade: 'Ia adalah versi terbaru pada masa ini',
        upgradeNotes: 'Nota pelepasan',
        upgradeNow: 'Kemas kini sekarang',
        source: 'Sumber muat turun',
        versionNotSame:
            'Versi nod tidak sepadan dengan nod utama. Sila naik taraf di Pengurusan Nod sebelum mencuba semula.',
        versionCompare:
            'Nod {0} telah berada pada versi terkini yang boleh dinaik taraf. Sila periksa versi nod utama dan cuba lagi!',

        about: 'Mengenai',
        project: 'GitHub',
        issue: 'Isu',
        doc: 'Dokumen rasmi',
        star: 'Bintang',
        description: 'Panel Pelayan Linux',
        forum: 'Perbincangan',
        doc2: 'Dokumen',
        currentVersion: 'Versi',

        license: 'Lesen',
        bindNode: 'Ikatan Nod',
        menuSetting: 'Tetapan Menu',
        menuSettingHelper: 'Apabila hanya terdapat 1 submenu, bar menu hanya akan memaparkan submenu tersebut',
        showAll: 'Papar Semua',
        hideALL: 'Sembunyikan Semua',
        ifShow: 'Sama ada untuk Dipaparkan',
        menu: 'Menu',
        confirmMessage: 'Halaman akan disegarkan untuk mengemas kini senarai menu lanjutan. Teruskan?',
        compressPassword: 'Kata laluan mampatan',
        backupRecoverMessage:
            'Sila masukkan kata laluan mampatan atau nyahmampatan (biarkan kosong jika tidak menetapkan)',
    },
    license: {
        community: 'OSS',
        oss: 'Perisian Sumber Terbuka',
        pro: 'Pro',
        trial: 'Percubaan',
        add: 'Tambah Edisi Komuniti',
        licenseAlert:
            'Nod Edisi Komuniti hanya boleh ditambah apabila lesen terikat dengan betul pada nod. Hanya nod yang terikat dengan betul pada lesen menyokong penukaran.',
        licenseUnbindHelper: 'Nod Edisi Komuniti dikesan untuk lesen ini. Sila lepaskan ikatan dan cuba lagi!',
        subscription: 'Langganan',
        perpetual: 'Lesen Perpetual',
        versionConstraint: '{0} Pembelian versi',
        forceUnbind: 'Paksakan Nyahikat',
        forceUnbindHelper:
            'Memaksa nyahikat akan mengabaikan sebarang ralat yang berlaku semasa proses nyahikat dan akhirnya melepaskan ikatan lesen.',
        updateForce: 'Kemas kini paksa (abaikan semua ralat semasa nyahikatan untuk memastikan operasi akhir berjaya)',
        trialInfo: 'Versi',
        authorizationId: 'ID Kebenaran Langganan',
        authorizedUser: 'Pengguna yang Dibenarkan',
        lostHelper:
            'Lesen telah mencapai jumlah percubaan semula maksimum. Sila klik butang penyegerakan secara manual untuk memastikan ciri versi profesional berfungsi dengan baik. butiran: ',
        disableHelper:
            'Pengesahan penyegerakan lesen gagal. Sila klik butang penyegerakan secara manual untuk memastikan ciri versi profesional berfungsi dengan baik. butiran: ',
        quickUpdate: 'Kemas Kini Pantas',
        power: 'Kebenaran',
        unbindHelper: 'Semua Tetapan berkaitan Pro akan dibersihkan selepas nyahikat. Adakah anda mahu meneruskan?',
        importLicense: 'Lesen',
        importHelper: 'Sila klik atau seret fail lesen ke sini',
        technicalAdvice: 'Konsultasi Teknikal',
        advice: 'Konsultasi',
        levelUpPro: 'Tingkatkan ke Pro',
        licenseSync: 'Penyegerakan Lesen',
        knowMorePro: 'Ketahui Lebih Lanjut',
        closeAlert: 'Halaman semasa boleh ditutup dalam tetapan panel',
        introduce: 'Pengenalan Ciri',
        waf: 'Menaik taraf ke versi profesional boleh menyediakan ciri seperti peta pencegahan, log, rekod blok, sekatan lokasi geografi, peraturan tersuai, halaman pencegahan tersuai, dan sebagainya.',
        tamper: 'Menaik taraf ke versi profesional boleh melindungi laman web daripada pengubahsuaian atau manipulasi tanpa kebenaran.',
        setting:
            'Menaik taraf ke versi profesional membolehkan penyesuaian logo panel, mesej selamat datang, dan maklumat lain.',
        monitor:
            'Tingkatkan ke versi profesional untuk melihat status masa nyata laman web, tren pelawat, sumber pelawat, log permintaan dan maklumat lain.',
        alert: 'Tingkatkan ke versi profesional untuk menerima maklumat amaran melalui SMS dan melihat log amaran, mengawal sepenuhnya pelbagai acara utama, dan memastikan operasi sistem bebas kerisauan.',
        fileExchange: 'Naik taraf ke Edisi Professional untuk menghantar fail dengan cepat antara pelbagai pelayan.',
        app: 'Tingkatkan ke versi profesional untuk melihat maklumat perkhidmatan, pemantauan abnormal, dll melalui aplikasi mudah alih.',
        cluster:
            'Versi Profesional menaik taraf membolehkan anda menguruskan kelompok induk-hamba MySQL/Postgres/Reids.',
    },
    clean: {
        scan: 'Mulakan imbasan',
        scanHelper: 'Bersihkan fail sampah dengan mudah yang dihasilkan semasa operasi 1Panel',
        clean: 'Bersihkan sekarang',
        reScan: 'Imbas semula',
        cleanHelper:
            'Ini akan membersihkan fail sampah sistem yang dipilih dan tidak boleh dipulihkan. Adakah anda mahu meneruskan?',
        statusSuggest: '(Disyorkan untuk Pembersihan)',
        statusClean: '(Sangat bersih)',
        statusEmpty: 'Sangat bersih, tiada pembersihan diperlukan!',
        statusWarning: '(Berhati-hati)',
        lastCleanTime: 'Dibersihkan Terakhir: {0}',
        lastCleanHelper: 'Fail dan direktori yang dibersihkan: {0}, jumlah yang dibersihkan: {1}',
        cleanSuccessful: 'Berjaya dibersihkan',
        currentCleanHelper: 'Fail dan direktori dibersihkan dalam sesi ini: {0}, Jumlah yang dibersihkan: {1}',
        suggest: '(Disyorkan)',
        totalScan: 'Jumlah fail sampah untuk dibersihkan: ',
        selectScan: 'Jumlah fail sampah yang dipilih: ',

        system: 'Fail Sampah Sistem',
        systemHelper:
            'Fail sementara yang dihasilkan semasa snapshot, peningkatan, dan kandungan fail usang semasa iterasi versi',
        panelOriginal: 'Fail sandaran pemulihan snapshot sistem',
        backup: 'Direktori sandaran sementara',
        upgrade: 'Fail sandaran peningkatan sistem',
        upgradeHelper: '(Disarankan untuk mengekalkan sandaran peningkatan terbaru untuk pemulihan sistem)',
        cache: 'Fail cache sistem',
        cacheHelper: '(Berhati-hati, pembersihan memerlukan permulaan semula perkhidmatan)',
        rollback: 'Fail sandaran sebelum pemulihan',

        upload: 'Fail Muat Naik Sementara',
        uploadHelper: 'Fail sementara dimuat naik dari senarai sandaran sistem',
        download: 'Fail Muat Turun Sementara',
        downloadHelper: 'Fail sementara dimuat turun dari akaun sandaran pihak ketiga oleh sistem',
        directory: 'Direktori',

        systemLog: 'Fail Log Sistem',
        systemLogHelper:
            'Maklumat log sistem, maklumat log pembinaan kontena atau muat turun imej, dan fail log yang dihasilkan dalam tugas berjadual',
        dockerLog: 'Fail log operasi kontena',
        taskLog: 'Fail log pelaksanaan tugas berjadual',
        containerShell: 'Tugas berjadual skrip Shell dalam kontena',

        containerTrash: 'Tong Sampah Kontena',
        volumes: 'Isipadu',
        buildCache: 'Cache Pembinaan Kontena',
    },
    app: {
        app: 'Aplikasi | Aplikasi',
        installName: 'Nama',
        installed: 'Telah Dipasang',
        all: 'Semua',
        version: 'Versi',
        detail: 'Butiran',
        params: 'Edit',
        author: 'Pengarang',
        source: 'Sumber',
        appName: 'Nama Aplikasi',
        deleteWarn:
            'Operasi memadam akan memadam semua data dan sandaran bersama. Operasi ini tidak boleh dipulihkan. Adakah anda mahu meneruskan?',
        syncSuccess: 'Disegerakkan dengan berjaya',
        canUpgrade: 'Kemaskini',
        backupName: 'Nama Fail',
        backupPath: 'Laluan Fail',
        backupdate: 'Masa Sandaran',
        versionSelect: 'Sila pilih versi',
        operatorHelper: 'Operasi {0} akan dilaksanakan pada aplikasi yang dipilih. Adakah anda mahu meneruskan?',
        startOperatorHelper: 'Aplikasi akan dimulakan. Adakah anda mahu meneruskan?',
        stopOperatorHelper: 'Aplikasi akan dihentikan. Adakah anda mahu meneruskan?',
        restartOperatorHelper: 'Aplikasi akan dimulakan semula. Adakah anda mahu meneruskan?',
        reloadOperatorHelper: 'Aplikasi akan dimuat semula. Adakah anda mahu meneruskan?',
        checkInstalledWarn: '"{0}" tidak dikesan. Pergi ke "Kedai Aplikasi" untuk memasang.',
        gotoInstalled: 'Pergi ke pasang',
        limitHelper: 'Aplikasi ini telah dipasang.',
        deleteHelper: '"{0}" telah dikaitkan dengan sumber berikut. Sila semak dan cuba lagi!',
        checkTitle: 'Petunjuk',
        defaultConfig: 'Konfigurasi lalai',
        defaultConfigHelper: 'Telah dipulihkan ke konfigurasi lalai, akan berkuat kuasa selepas menyimpan',
        forceDelete: 'Padam Paksa',
        forceDeleteHelper: 'Padam paksa akan mengabaikan ralat semasa proses pemadaman dan akhirnya memadam metadata.',
        deleteBackup: 'Padam sandaran',
        deleteBackupHelper: 'Juga padam sandaran aplikasi',
        deleteDB: 'Padam pangkalan data',
        deleteDBHelper: 'Juga padam pangkalan data',
        noService: 'Tiada {0}',
        toInstall: 'Pergi ke pasang',
        param: 'Parameter',
        alreadyRun: 'Umur',
        syncAppList: 'Segerak',
        less1Minute: 'Kurang daripada 1 minit',
        appOfficeWebsite: 'Laman web rasmi',
        github: 'Github',
        document: 'Dokumen',
        updatePrompt: 'Tiada kemaskini tersedia',
        installPrompt: 'Belum ada aplikasi yang dipasang',
        updateHelper: 'Mengedit parameter boleh menyebabkan aplikasi gagal dimulakan. Sila berhati-hati.',
        updateWarn: 'Kemaskini parameter memerlukan aplikasi dibina semula. Adakah anda mahu meneruskan?',
        busPort: 'Port',
        syncStart: 'Mulakan penyegerakan! Sila segar semula kedai aplikasi kemudian',
        advanced: 'Tetapan lanjutan',
        cpuCore: 'teras',
        containerName: 'Nama kontena',
        containerNameHelper: 'Nama kontena akan dijana secara automatik jika tidak ditetapkan',
        allowPort: 'Akses luaran',
        allowPortHelper: 'Membenarkan akses port luaran akan membuka port firewall',
        appInstallWarn:
            'Aplikasi tidak membuka port akses luaran secara lalai. Klik "Tetapan lanjutan" untuk membukanya.',
        upgradeStart: 'Mulakan peningkatan! Sila segar semula halaman kemudian',
        toFolder: 'Buka direktori pemasangan',
        editCompose: 'Edit fail compose',
        editComposeHelper: 'Mengedit fail compose boleh menyebabkan pemasangan perisian gagal',
        composeNullErr: 'Compose tidak boleh kosong',
        takeDown: 'Henti Operasi',
        allReadyInstalled: 'Telah Dipasang',
        installHelper: 'Jika terdapat isu tarikan imej, konfigurasikan pecutan imej.',
        installWarn:
            'Akses luaran tidak diaktifkan, yang menghalang aplikasi daripada diakses melalui rangkaian luaran. Adakah anda mahu meneruskan?',
        showIgnore: 'Lihat aplikasi yang diabaikan',
        cancelIgnore: 'Batal abaikan',
        ignoreList: 'Aplikasi yang diabaikan',
        appHelper: 'Pergi ke halaman butiran aplikasi untuk mengetahui arahan pemasangan bagi aplikasi tertentu.',
        backupApp: 'Sandarkan aplikasi sebelum kemaskini',
        backupAppHelper: 'Jika kemaskini gagal, sandaran akan dipulihkan secara automatik.',
        openrestyDeleteHelper: 'Padam paksa OpenResty akan memadam semua laman web. Adakah anda mahu meneruskan?',
        downloadLogHelper1: 'Semua log aplikasi {0} akan dimuat turun. Adakah anda mahu meneruskan?',
        downloadLogHelper2: 'Log terkini {1} aplikasi {0} akan dimuat turun. Adakah anda mahu meneruskan?',
        syncAllAppHelper: 'Semua aplikasi akan disegerakkan. Adakah anda mahu meneruskan?',
        hostModeHelper: 'Mod rangkaian aplikasi semasa ialah mod hos.',
        showLocal: 'Papar aplikasi tempatan',
        reload: 'Muat Semula',
        upgradeWarn: 'Meningkatkan aplikasi akan menggantikan fail docker-compose.yml.',
        newVersion: 'Versi baru',
        oldVersion: 'Versi semasa',
        composeDiff: 'Perbandingan fail',
        showDiff: 'Lihat perbandingan',
        useNew: 'Guna versi tersuai',
        useDefault: 'Guna versi lalai',
        useCustom: 'Sesuaikan docker-compose.yml',
        useCustomHelper: 'Menggunakan fail docker-compose.yml tersuai boleh menyebabkan kemaskini gagal.',
        diffHelper: 'Bahagian kiri ialah versi lama, kanan ialah versi baru.',
        pullImage: 'Tarik Imej',
        pullImageHelper: 'Laksanakan docker pull untuk menarik imej sebelum aplikasi dimulakan.',
        deleteImage: 'Padam Imej',
        deleteImageHelper: 'Padam imej yang berkaitan dengan aplikasi. Tugas tidak akan tamat jika pemadaman gagal.',
        requireMemory: 'Keperluan Memori',
        supportedArchitectures: 'Seni Bina yang Disokong',
        link: 'Pautan',
        showCurrentArch: 'Aplikasi seni bina pelayan semasa',
        syncLocalApp: 'Segerakkan Aplikasi Tempatan',
        memoryRequiredHelper: 'Aplikasi semasa memerlukan {0} memori',
        gpuConfig: 'Aktifkan Sokongan GPU',
        gpuConfigHelper:
            'Pastikan mesin mempunyai NVIDIA GPU dan memasang pemacu NVIDIA serta NVIDIA Docker Container Toolkit',
        webUI: 'Alamat Akses Web',
        webUIPlaceholder: 'Contoh: example.com:8080/login',
        defaultWebDomain: 'Alamat Akses Lalai',
        defaultWebDomainHepler:
            'Jika port aplikasi adalah 8080, alamat loncatan akan menjadi http(s)://alamat akses lalai:8080',
        webUIConfig:
            'Nod semasa belum mempunyai alamat akses lalai yang dikonfigurasi. Sila tetapkan dalam parameter aplikasi atau pergi ke tetapan panel untuk mengkonfigurasinya!',
        toLink: 'Loncat',
        customAppHelper:
            'Sebelum memasang pakej kedai aplikasi tersuai, sila pastikan tidak ada aplikasi yang dipasang.',
        forceUninstall: 'Paksa Nyahpasang',
        syncCustomApp: 'Segerakan Aplikasi Tersuai',
        ignoreAll: 'Abaikan semua versi berikutnya',
        ignoreVersion: 'Abaikan versi yang ditentukan',
        specifyIP: 'Bind IP Hos',
        specifyIPHelper:
            'Tetapkan alamat hos/antara muka rangkaian untuk mengikat port (jika anda tidak pasti mengenai ini, jangan isi)',
        uninstallDeleteBackup: 'Cop Terhapus Semasa Nyahpasang Aplikasi',
        uninstallDeleteImage: 'Imej Terhapus Semasa Nyahpasang Aplikasi',
        upgradeBackup: 'Sandaran Aplikasi Sebelum Naik Taraf',
    },
    website: {
        primaryDomain: 'Domain Utama',
        otherDomains: 'Domain Lain',
        static: 'Statik',
        deployment: 'Penerapan',
        supportUpType: 'Hanya fail .tar.gz disokong',
        zipFormat: 'Struktur fail .tar.gz: fail test.tar.gz mesti mengandungi fail {0}',
        proxy: 'Proksi Terbalik',
        alias: 'Alias',
        ftpUser: 'Akaun FTP',
        ftpPassword: 'Kata Laluan FTP',
        ftpHelper:
            'Selepas membuat laman web, akaun FTP akan dibuat dan direktori FTP akan memautkan ke direktori laman web.',
        remark: 'Catatan',
        manageGroup: 'Urus kumpulan',
        groupSetting: 'Pengurusan Kumpulan',
        createGroup: 'Cipta kumpulan',
        appNew: 'Aplikasi Baru',
        appInstalled: 'Aplikasi yang Dipasang',
        create: 'Cipta laman web',
        delete: 'Padam Laman Web',
        deleteApp: 'Padam Aplikasi',
        deleteBackup: 'Padam Sandaran',
        domain: 'Domain',
        domainHelper: 'Satu domain per baris.\nSokong wildcard "*" dan alamat IP.\nSokong penambahan port.',
        addDomain: 'Tambah',
        domainConfig: 'Domain',
        defaultDoc: 'Dokumen',
        perserver: 'Serentak',
        perserverHelper: 'Hadkan serentak maksimum untuk laman web semasa',
        perip: 'IP Tunggal',
        peripHelper: 'Hadkan jumlah maksimum akses serentak untuk satu IP',
        rate: 'Had trafik',
        rateHelper: 'Had aliran setiap permintaan (unit: KB)',
        limitHelper: 'Aktifkan kawalan aliran',
        other: 'Lain-lain',
        currentSSL: 'Sijil Semasa',
        dnsAccount: 'Akaun DNS',
        applySSL: 'Permohonan Sijil',
        SSLList: 'Senarai Sijil',
        createDnsAccount: 'Akaun DNS',
        aliyun: 'Aliyun',
        manual: 'Penyelesaian Manual',
        key: 'Kunci',
        check: 'Lihat',
        acmeAccountManage: 'Pengurusan Akaun ACME',
        email: 'E-mel',
        acmeAccount: 'Akaun ACME',
        provider: 'Kaedah Pengesahan',
        dnsManual: 'Penyelesaian Manual',
        expireDate: 'Tarikh Luput',
        brand: 'Organisasi',
        deploySSL: 'Penerapan',
        deploySSLHelper: 'Adakah anda pasti mahu menerapkan sijil?',
        ssl: 'Sijil | Sijil-Sijil',
        dnsAccountManage: 'Penyedia DNS',
        renewSSL: 'Perbaharui',
        renewHelper: 'Adakah anda pasti mahu memperbaharui sijil?',
        renewSuccess: 'Sijil diperbaharui',
        enableHTTPS: 'Aktifkan',
        aliasHelper: 'Alias ialah nama direktori laman web',
        lastBackupAt: 'masa sandaran terakhir',
        null: 'tiada',
        nginxConfig: 'Konfigurasi Nginx',
        websiteConfig: 'Tetapan Laman Web',
        basic: 'Asas',
        source: 'Konfigurasi',
        security: 'Keselamatan',
        nginxPer: 'Penalaan Prestasi Nginx',
        neverExpire: 'Tidak Pernah Luput',
        setDefault: 'Tetapkan sebagai lalai',
        default: 'Lalai',
        deleteHelper: 'Status aplikasi berkaitan tidak normal, sila semak',
        toApp: 'Pergi ke senarai dipasang',
        cycle: 'Kitaran',
        frequency: 'Kekerapan',
        ccHelper:
            'Permintaan akumulatif untuk URL yang sama lebih daripada {1} kali dalam {0} saat, mencetuskan pertahanan CC, menyekat IP ini',
        mustSave: 'Pengubahsuaian perlu disimpan untuk berkuat kuasa',
        fileExt: 'Sambungan fail',
        fileExtBlock: 'Senarai blok sambungan fail',
        value: 'Nilai',
        enable: 'Aktifkan',
        proxyAddress: 'Alamat Proksi',
        proxyHelper: 'Contoh: 127.0.0.1:8080',
        forceDelete: 'Padam Paksa',
        forceDeleteHelper: 'Padam paksa akan mengabaikan ralat semasa proses pemadaman dan akhirnya memadam metadata.',
        deleteAppHelper: 'Padam aplikasi yang berkaitan dan sandaran aplikasi pada masa yang sama',
        deleteBackupHelper: 'Juga padamkan sandaran laman web.',
        deleteConfirmHelper: `Operasi pemadaman tidak boleh dibatalkan. Masukkan <span style="color:red"> "{0}" </span> untuk mengesahkan pemadaman.`,
        staticPath: 'Direktori utama yang sepadan ialah ',
        limit: 'Skim',
        blog: 'Forum/Blog',
        imageSite: 'Laman Gambar',
        downloadSite: 'Laman Muat Turun',
        shopSite: 'Pusat Membeli-belah',
        doorSite: 'Portal',
        qiteSite: 'Syarikat',
        videoSite: 'Video',
        errLog: 'Log Ralat',
        accessLog: 'Log Laman Web',
        stopHelper:
            'Selepas menghentikan laman web, ia tidak akan dapat diakses dengan normal, dan pengguna akan melihat halaman berhenti laman web semasa apabila mengunjunginya. Adakah anda mahu meneruskan?',
        startHelper:
            'Selepas mengaktifkan laman web, pengguna boleh mengakses kandungan laman web seperti biasa. Adakah anda mahu meneruskan?',
        sitePath: 'Direktori',
        siteAlias: 'Alias Laman',
        primaryPath: 'Direktori utama',
        folderTitle: 'Laman web ini terutamanya mengandungi folder berikut',
        wafFolder: 'Peraturan firewall',
        indexFolder: 'Direktori akar laman web',
        logFolder: 'Log laman web',
        sslFolder: 'Sijil laman web',
        enableOrNot: 'Aktifkan',
        oldSSL: 'Sijil sedia ada',
        manualSSL: 'Import sijil',
        select: 'Pilih',
        selectSSL: 'Pilih Sijil',
        privateKey: 'Kunci (KEY)',
        certificate: 'Sijil (format PEM)',
        HTTPConfig: 'Pilihan HTTP',
        HTTPSOnly: 'Blok permintaan HTTP',
        HTTPToHTTPS: 'Alihkan ke HTTPS',
        HTTPAlso: 'Benarkan permintaan HTTP langsung',
        sslConfig: 'Pilihan SSL',
        disableHTTPS: 'Lumpuhkan HTTPS',
        disableHTTPSHelper: 'Melumpuhkan HTTPS akan memadam konfigurasi berkaitan sijil. Adakah anda mahu meneruskan?',
        SSLHelper:
            'Nota: Jangan gunakan sijil SSL untuk laman web tidak sah.\nJika akses HTTPS tidak dapat digunakan selepas diaktifkan, semak sama ada kumpulan keselamatan telah melepaskan port 443 dengan betul.',
        SSLConfig: 'Tetapan sijil',
        SSLProConfig: 'Tetapan protokol',
        supportProtocol: 'Versi protokol',
        encryptionAlgorithm: 'Algoritma penyulitan',
        notSecurity: '(tidak selamat)',
        encryptHelper:
            "Let's Encrypt mempunyai had kekerapan untuk mengeluarkan sijil, tetapi mencukupi untuk memenuhi keperluan biasa. Operasi terlalu kerap akan menyebabkan kegagalan pengeluaran. Untuk sekatan tertentu, sila lihat <a target='_blank' href='https://letsencrypt.org/docs/rate-limits/'>dokumen rasmi</a>",
        ipValue: 'Nilai',
        ext: 'sambungan fail',
        wafInputHelper: 'Masukkan data secara berbaris, satu baris',
        data: 'data',
        ever: 'kekal',
        nextYear: 'Satu tahun kemudian',
        noLog: 'Tiada log ditemui',
        defaultServer: 'Tapak lalai',
        noDefaultServer: 'Tidak ditetapkan',
        defaultServerHelper:
            'Setelah menetapkan laman lalai, semua nama domain dan IP yang tidak terikat akan diarahkan ke laman lalai\nIni dapat mencegah penyelesaian yang jahat secara berkesan\nNamun, ini juga boleh menyebabkan kegagalan penghalang nama domain tanpa kebenaran WAF',
        restoreHelper: 'Adakah anda pasti mahu memulihkan menggunakan sandaran ini?',
        websiteDeploymentHelper: 'Gunakan aplikasi yang dipasang atau buat aplikasi baharu untuk mencipta laman web.',
        websiteStatictHelper: 'Cipta direktori laman web pada hos.',
        websiteProxyHelper:
            'Gunakan proksi terbalik untuk memproksi perkhidmatan sedia ada. Contohnya, jika perkhidmatan dipasang dan berjalan pada port 8080, alamat proksi akan menjadi "http://127.0.0.1:8080".',
        runtimeProxyHelper: 'Gunakan runtime laman web untuk mencipta laman web.',
        runtime: 'Runtime',
        deleteRuntimeHelper: 'Aplikasi Runtime perlu dipadamkan bersama laman web, sila berhati-hati',
        proxyType: 'Jenis Rangkaian',
        unix: 'Rangkaian Unix',
        tcp: 'Rangkaian TCP/IP',
        phpFPM: 'Konfigurasi FPM',
        phpConfig: 'Konfigurasi PHP',
        updateConfig: 'Kemas kini Konfigurasi',
        isOn: 'Hidup',
        isOff: 'Mati',
        rewrite: 'Pseudo-statik',
        rewriteMode: 'Skema',
        current: 'Semasa',
        rewriteHelper:
            'Jika menetapkan pseudo-statik menyebabkan laman web tidak dapat diakses, cuba kembali ke tetapan lalai.',
        runDir: 'Direktori Jalankan',
        runUserHelper:
            'Untuk laman web yang dikerahkan melalui persekitaran runtime kontena PHP, anda perlu menetapkan pemilik dan kumpulan pengguna bagi semua fail dan folder di bawah indeks dan subdirektori ke 1000. Untuk persekitaran PHP tempatan, rujuk tetapan pengguna PHP-FPM tempatan',
        userGroup: 'Pengguna/Kumpulan',
        uGroup: 'Kumpulan',
        proxyPath: 'Laluan Proksi',
        proxyPass: 'URL Sasaran',
        cache: 'Cache',
        cacheTime: 'Tempoh Cache',
        enableCache: 'Cache',
        proxyHost: 'Hos Proksi',
        disabled: 'Dihentikan',
        startProxy: 'Ini akan memulakan proksi terbalik. Adakah anda ingin meneruskan?',
        stopProxy: 'Ini akan menghentikan proksi terbalik. Adakah anda ingin meneruskan?',
        sourceFile: 'Sumber',
        proxyHelper1: 'Semasa mengakses direktori ini, kandungan URL sasaran akan dikembalikan dan dipaparkan.',
        proxyPassHelper: 'URL sasaran mesti sah dan boleh diakses.',
        proxyHostHelper: 'Hantar nama domain dalam header permintaan ke pelayan proksi.',
        replacementHelper: 'Hingga 5 penggantian boleh ditambah, sila kosongkan jika tiada penggantian diperlukan.',
        modifier: 'Peraturan padanan',
        modifierHelper:
            'Contoh: "=" adalah padanan tepat, "~" adalah padanan biasa, "^~" memadankan permulaan laluan, dan sebagainya.',
        replace: 'Penggantian Teks',
        addReplace: 'Tambah',
        replaced: 'String Carian (tidak boleh kosong)',
        replaceText: 'Ganti dengan string',
        replacedErr: 'String Carian tidak boleh kosong',
        replacedErr2: 'String Carian tidak boleh berulang',
        basicAuth: 'Pengesahan Asas',
        editBasicAuthHelper:
            'Kata laluan disulitkan secara tidak simetri dan tidak dapat dipaparkan. Penyuntingan perlu menetapkan semula kata laluan',
        antiLeech: 'Anti-leech',
        extends: 'Pelanjutan',
        browserCache: 'Cache',
        leechLog: 'Rekod log anti-leech',
        accessDomain: 'Domain yang dibenarkan',
        leechReturn: 'Sumber tindak balas',
        noneRef: 'Benarkan referrer kosong',
        disable: 'tidak diaktifkan',
        disableLeechHelper: 'Adakah anda ingin mematikan anti-leech',
        disableLeech: 'Matikan anti-leech',
        ipv6: 'Dengar IPv6',
        leechReturnError: 'Sila isikan kod status HTTP',
        selectAcme: 'Pilih akaun Acme',
        imported: 'Dibuat secara manual',
        importType: 'Jenis import',
        pasteSSL: 'Tampal kod',
        localSSL: 'Pilih fail pelayan',
        privateKeyPath: 'Fail kunci peribadi',
        certificatePath: 'Fail sijil',
        ipWhiteListHelper: 'Peranan senarai putih IP: semua peraturan tidak sah untuk senarai putih IP',
        redirect: 'Alihkan',
        sourceDomain: 'Domain sumber',
        targetURL: 'Alamat URL Sasaran',
        keepPath: 'Parameter URI',
        path: 'laluan',
        redirectType: 'Jenis pengalihan',
        redirectWay: 'Cara',
        keep: 'menyimpan',
        notKeep: 'Jangan simpan',
        redirectRoot: 'Alihkan ke halaman utama',
        redirectHelper: 'Pengalihan kekal 301, pengalihan sementara 302',
        changePHPVersionWarn:
            'Menukar versi PHP akan memadamkan kontena PHP asal (kod laman web yang telah dimuatkan tidak akan hilang), teruskan? ',
        changeVersion: 'Tukar versi',
        retainConfig: 'Adakah untuk menyimpan fail php-fpm.conf dan php.ini',
        runDirHelper2: 'Sila pastikan direktori berjalan sekunder berada di bawah direktori indeks',
        openrestyHelper:
            'Port HTTP lalai OpenResty: {0} Port HTTPS: {1}, yang mungkin mempengaruhi akses nama domain laman web dan pengalihan HTTPS paksa',
        primaryDomainHelper: 'Contoh: example.com atau example.com:8080',
        acmeAccountType: 'Jenis akaun',
        keyType: 'Algoritma Kunci',
        tencentCloud: 'Tencent Cloud',
        containWarn: 'Nama domain mengandungi domain utama, sila masukkan semula',
        rewriteHelper2:
            'Aplikasi seperti WordPress yang dipasang dari kedai aplikasi biasanya dilengkapi dengan konfigurasi pseudo-statik praset. Mengkonfigurasi semula mereka boleh menyebabkan ralat.',
        websiteBackupWarn:
            'Hanya menyokong pengimportan sandaran tempatan, pengimportan sandaran dari mesin lain boleh menyebabkan kegagalan pemulihan',
        ipWebsiteWarn:
            'Laman web dengan IP sebagai nama domain perlu disetkan sebagai laman web lalai untuk diakses secara normal.',
        hstsHelper: 'Mengaktifkan HSTS boleh meningkatkan keselamatan laman web',
        includeSubDomains: 'SubDomains',
        hstsIncludeSubDomainsHelper:
            'Apabila diaktifkan, dasar HSTS akan digunakan pada semua subdomain bagi domain semasa.',
        defaultHtml: 'Halaman lalai',
        website404: 'Halaman ralat 404 laman web',
        domain404: 'Domain laman web tidak wujud',
        indexHtml: 'Indeks untuk laman web statik',
        stopHtml: 'Laman web dihentikan',
        indexPHP: 'Indeks untuk laman web PHP',
        sslExpireDate: 'Tarikh Tamat Tempoh Sijil',
        website404Helper:
            'Halaman ralat 404 laman web hanya menyokong laman web persekitaran runtime PHP dan laman web statik',
        sni: 'Sumber SNI',
        sniHelper:
            'Apabila backend proksi terbalik adalah HTTPS, anda mungkin perlu menetapkan sumber SNI. Sila rujuk dokumentasi penyedia perkhidmatan CDN untuk butiran.',
        huaweicloud: 'Huawei Cloud',
        createDb: 'Cipta Pangkalan Data',
        enableSSLHelper: 'Kegagalan mengaktifkan SSL tidak akan menjejaskan penciptaan laman web.',
        batchAdd: 'Tambah Domain Secara Batch',
        generateDomain: 'Hasilkan',
        global: 'Global',
        subsite: 'Sublaman',
        subsiteHelper:
            'Sublaman boleh memilih direktori laman web PHP atau statik yang sedia ada sebagai direktori akar.',
        parentWebsite: 'Laman Web Induk',
        deleteSubsite: 'Untuk memadam laman web semasa, anda mesti memadam sublaman {0} terlebih dahulu.',
        loadBalance: 'Pengimbangan Beban',
        server: 'Nod',
        algorithm: 'Algoritma',
        ipHash: 'IP Hash',
        ipHashHelper:
            'Mengagihkan permintaan ke pelayan tertentu berdasarkan alamat IP klien, memastikan klien tertentu sentiasa diarahkan ke pelayan yang sama.',
        leastConn: 'Sambungan Terkecil',
        leastConnHelper: 'Menghantar permintaan ke pelayan dengan sambungan aktif paling sedikit.',
        leastTime: 'Masa Terkecil',
        leastTimeHelper: 'Menghantar permintaan ke pelayan dengan masa sambungan aktif terpendek.',
        defaultHelper:
            'Kaedah lalai, permintaan diagihkan secara merata ke setiap pelayan. Jika pelayan mempunyai konfigurasi berat, permintaan diagihkan mengikut berat yang ditentukan. Pelayan dengan berat lebih tinggi menerima lebih banyak permintaan.',
        weight: 'Berat',
        maxFails: 'Kegagalan Maksimum',
        maxConns: 'Sambungan Maksimum',
        strategy: 'Strategi',
        strategyDown: 'Lumpuh',
        strategyBackup: 'Sandaran',
        staticChangePHPHelper: 'Kini laman web statik, boleh ditukar ke laman web PHP.',
        proxyCache: 'Cache Proksi Terbalik',
        cacheLimit: 'Had Ruang Cache',
        shareCache: 'Saiz Memori Kiraan Cache',
        cacheExpire: 'Masa Tamat Cache',
        shareCacheHelper: '1M memori boleh menyimpan kira-kira 8000 objek cache.',
        cacheLimitHelper: 'Melebihi had akan menghapus cache lama secara automatik.',
        cacheExpireHelper: 'Cache yang tidak dipenuhi dalam masa tamat akan dihapuskan.',
        realIP: 'IP Sebenar',
        ipFrom: 'Sumber IP',
        ipFromHelper:
            'Dengan mengkonfigurasi sumber IP yang dipercayai, OpenResty akan menganalisis maklumat IP dalam HTTP Header untuk mengenal pasti dan merekodkan alamat IP sebenar pelawat, termasuk dalam log akses.',
        ipFromExample1: 'Jika frontend adalah alat seperti Frp, anda boleh mengisi alamat IP Frp, seperti 127.0.0.1.',
        ipFromExample2: 'Jika frontend adalah CDN, anda boleh mengisi julat IP CDN.',
        ipFromExample3:
            'Jika tidak pasti, anda boleh mengisi 0.0.0.0/0 (IPv4) atau ::/0 (IPv6). [Nota: Membenarkan sebarang sumber IP tidak selamat.]',
        http3Helper:
            'HTTP/3 adalah versi naik taraf HTTP/2, menyediakan kelajuan sambungan yang lebih pantas dan prestasi yang lebih baik. Walau bagaimanapun, tidak semua penyemak imbas menyokong HTTP/3, dan mengaktifkannya mungkin menyebabkan beberapa penyemak imbas tidak dapat mengakses laman web.',
        changeDatabase: 'Tukar Pangkalan Data',
        changeDatabaseHelper1: 'Perkaitan pangkalan data digunakan untuk sandaran dan pemulihan laman web.',
        changeDatabaseHelper2:
            'Menukar ke pangkalan data lain mungkin menyebabkan sandaran sebelumnya tidak dapat dipulihkan.',
        saveCustom: 'Simpan sebagai Templat',
        rainyun: 'Rainyun',
        volcengine: 'Volcengine',
        runtimePortHelper: 'Persekitaran runtime semasa mempunyai beberapa port. Sila pilih port proksi.',
        runtimePortWarn: 'Persekitaran runtime semasa tidak mempunyai port, tidak dapat proksi',
        cacheWarn: 'Sila matikan suis cache dalam pembalikan proksi terlebih dahulu',
        loadBalanceHelper:
            'Setelah mencipta pengimbang beban, sila pergi ke "Reverse Proxy", tambahkan proksi dan tetapkan alamat backend ke: http://<nama pengimbang beban>.',
        favorite: 'Kegemaran',
        cancelFavorite: 'Batalkan Kegemaran',
        useProxy: 'Gunakan Proksi',
        useProxyHelper: 'Gunakan alamat pelayan proksi dalam tetapan panel',
        westCN: 'West Digital',
        openBaseDir: 'Pencegahan Serangan Lintas Situs',
        openBaseDirHelper:
            'open_basedir digunakan untuk membatasi jalur akses file PHP, yang membantu mencegah akses lintas situs dan meningkatkan keamanan',
        serverCacheTime: 'Masa Cache Pelayan',
        serverCacheTimeHelper:
            'Masa permintaan di-cache di pelayan. Semasa tempoh ini, permintaan yang sama akan mengembalikan hasil cache terus tanpa meminta pelayan asal.',
        browserCacheTime: 'Masa Cache Pelayar',
        browserCacheTimeHelper:
            'Masa sumber statik di-cache secara tempatan di pelayar, mengurangkan permintaan berulang. Pengguna akan menggunakan cache tempatan secara langsung sebelum tamat tempoh semasa menyegarkan halaman.',
        donotLinkeDB: 'Jangan Sambungkan Pangkalan Data',
        toWebsiteDir: 'Masuk ke Direktori Laman Web',
    },
    php: {
        short_open_tag: 'Sokongan tag pendek',
        max_execution_time: 'Masa maksimum pelaksanaan skrip',
        max_input_time: 'Masa input maksimum',
        memory_limit: 'Had memori skrip',
        post_max_size: 'Saiz maksimum data POST',
        file_uploads: 'Sama ada membenarkan muat naik fail',
        upload_max_filesize: 'Saiz maksimum fail yang dibenarkan untuk dimuat naik',
        max_file_uploads: 'Bilangan maksimum fail yang dibenarkan untuk dimuat naik pada masa yang sama',
        default_socket_timeout: 'Masa tamat soket',
        error_reporting: 'Tahap kesilapan',
        display_errors: 'Sama ada untuk output maklumat ralat terperinci',
        cgi_fix_pathinfo: 'Sama ada untuk membuka pathinfo',
        date_timezone: 'Zon waktu',
        disableFunction: 'Lumpuhkan fungsi',
        disableFunctionHelper:
            'Masukkan fungsi yang ingin dilumpuhkan, seperti exec, gunakan pemisah untuk banyak fungsi',
        uploadMaxSize: 'Had muat naik',
        indexHelper:
            'Untuk memastikan operasi laman web PHP berjalan lancar, sila letakkan kod dalam direktori indeks dan elakkan menamakan semula',
        extensions: 'Templat sambungan',
        extension: 'Sambungan',
        extensionHelper: 'Gunakan pemisah untuk banyak sambungan',
        toExtensionsList: 'Lihat senarai sambungan',
        containerConfig: 'Konfigurasi Bekas',
        containerConfigHelper:
            'Pembolehubah persekitaran dan maklumat lain boleh diubah suai dalam Konfigurasi - Konfigurasi Bekas selepas penciptaan',
        dateTimezoneHelper: 'Contoh: TZ=Asia/Shanghai (Sila tambahkan jika perlu)',
    },
    nginx: {
        serverNamesHashBucketSizeHelper: 'Saiz jadual hash nama pelayan',
        clientHeaderBufferSizeHelper: 'Saiz buffer header yang diminta oleh klien',
        clientMaxBodySizeHelper: 'Fail muat naik maksimum',
        keepaliveTimeoutHelper: 'Masa tamat sambungan',
        gzipMinLengthHelper: 'Saiz minimum fail untuk pemampatan',
        gzipCompLevelHelper: 'Kadar mampatan',
        gzipHelper: 'Aktifkan pemampatan untuk penghantaran',
        connections: 'Sambungan aktif',
        accepts: 'Diterima',
        handled: 'Diuruskan',
        requests: 'Permintaan',
        reading: 'Membaca',
        writing: 'Menulis',
        waiting: 'Menunggu',
        status: 'Status Semasa',
        configResource: 'Konfigurasi',
        saveAndReload: 'Simpan dan muat semula',
        clearProxyCache: 'Bersihkan cache proksi terbalik',
        clearProxyCacheWarn:
            'Semua laman web yang dikonfigurasi dengan cache akan terjejas dan "OpenResty" akan dimulakan semula. Adakah anda mahu meneruskan?',
        create: 'Tambah Modul',
        update: 'Edit Modul',
        params: 'Parameter',
        packages: 'Pakej',
        script: 'Script',
        module: 'Modul',
        build: 'Bina',
        buildWarn:
            'Membina OpenResty memerlukan menyediakan sejumlah CPU dan memori, dan prosesnya mengambil masa yang lama, sila bersabar.',
        mirrorUrl: 'Sumber Perisian',
        paramsHelper: 'Contoh: --add-module=/tmp/ngx_brotli',
        packagesHelper: 'Contoh: git,curl dipisahkan oleh koma',
        scriptHelper:
            'Skrip yang dilaksanakan sebelum penyusunan, biasanya untuk memuat turun sumber kod modul, memasang kebergantungan, dll.',
        buildHelper:
            'Klik Bina selepas menambah/mengubah suai modul. Pembinaan yang berjaya akan memulakan semula OpenResty secara automatik.',
        defaultHttps: 'HTTPS Anti-tampering',
        defaultHttpsHelper1: 'Mengaktifkan ini dapat menyelesaikan masalah tampering HTTPS.',
    },
    ssl: {
        create: 'Permintaan',
        provider: 'Jenis',
        manualCreate: 'Dicipta secara manual',
        acmeAccount: 'Akaun ACME',
        resolveDomain: 'Selesaikan nama domain',
        err: 'Ralat',
        value: 'Nilai rekod',
        dnsResolveHelper: 'Sila pergi ke pembekal perkhidmatan resolusi DNS untuk menambah rekod resolusi berikut:',
        detail: 'Perincian',
        msg: 'Maklumat',
        ssl: 'Sijil',
        key: 'Kunci peribadi',
        startDate: 'Waktu berkuatkuasa',
        organization: 'Organisasi penerbit',
        renewConfirm: 'Ini akan memperbaharui sijil baru untuk nama domain {0}. Adakah anda mahu meneruskan?',
        autoRenew: 'Pembaharuan Automatik',
        autoRenewHelper: 'Perbaharui secara automatik 30 hari sebelum tamat tempoh',
        renewSuccess: 'Pembaharuan berjaya',
        renewWebsite:
            'Sijil ini telah dikaitkan dengan laman web berikut, dan aplikasi akan digunakan pada laman web ini secara serentak',
        createAcme: 'Buat Akaun',
        acmeHelper: 'Akaun Acme digunakan untuk memohon sijil percuma',
        upload: 'Import',
        applyType: 'Jenis',
        apply: 'Perbaharui',
        applyStart: 'Permohonan sijil bermula',
        getDnsResolve: 'Mendapatkan nilai resolusi DNS, sila tunggu...',
        selfSigned: 'CA Ditandatangani Sendiri',
        ca: 'Pihak berkuasa sijil',
        commonName: 'Nama biasa',
        caName: 'Nama pihak berkuasa sijil',
        company: 'Nama organisasi',
        department: 'Nama unit organisasi',
        city: 'Nama bandar',
        province: 'Nama negeri atau wilayah',
        country: 'Kod negara (2 huruf)',
        commonNameHelper: 'Sebagai contoh, ',
        selfSign: 'Keluarkan sijil',
        days: 'Tempoh sah',
        domainHelper: 'Satu nama domain setiap baris, menyokong * dan alamat IP',
        pushDir: 'Tolakkan sijil ke direktori tempatan',
        dir: 'Direktori',
        pushDirHelper: 'Fail sijil "fullchain.pem" dan fail kunci "privkey.pem" akan dihasilkan dalam direktori ini.',
        organizationDetail: 'Butiran organisasi',
        fromWebsite: 'Daripada laman web',
        dnsMauanlHelper:
            'Dalam mod resolusi manual, anda perlu klik butang mohon selepas penciptaan untuk mendapatkan nilai resolusi DNS',
        httpHelper:
            'Menggunakan mod HTTP memerlukan pemasangan OpenResty dan tidak menyokong permohonan sijil domain wildcard.',
        buypassHelper: `Buypass tidak boleh diakses di tanah besar China`,
        googleHelper: 'Cara mendapatkan EAB HmacKey dan EAB kid',
        googleCloudHelper: `Google Cloud API tidak boleh diakses di kebanyakan kawasan tanah besar China`,
        skipDNSCheck: 'Langkau semakan DNS',
        skipDNSCheckHelper: 'Semak di sini hanya jika anda menghadapi isu tamat masa semasa permintaan pengesahan.',
        cfHelper: 'Jangan gunakan Global API Key',
        deprecated: 'akan dihentikan',
        deprecatedHelper:
            'Penyelenggaraan telah dihentikan dan mungkin akan dibuang dalam versi masa hadapan. Sila gunakan kaedah Tencent Cloud untuk analisis',
        disableCNAME: 'Lumpuhkan CNAME',
        disableCNAMEHelper: 'Semak di sini jika nama domain mempunyai rekod CNAME dan permintaan gagal.',
        nameserver: 'Pelayan DNS',
        nameserverHelper: 'Gunakan pelayan DNS tersuai untuk mengesahkan nama domain.',
        edit: 'Edit sijil',
        execShell: 'Jalankan skrip selepas permintaan pengesahan.',
        shell: 'Kandungan skrip',
        shellHelper:
            'Direktori pelaksanaan lalai skrip adalah direktori pemasangan 1Panel. Jika sijil ditolak ke direktori tempatan, direktori pelaksanaan akan menjadi direktori tolak sijil. Tamat masa pelaksanaan lalai ialah 30 minit.',
        customAcme: 'Perkhidmatan ACME Tersuai',
        customAcmeURL: 'URL Perkhidmatan ACME',
        baiduCloud: 'Baidu Cloud',
    },
    firewall: {
        create: 'Buat peraturan',
        edit: 'Edit peraturan',
        ccDeny: 'Perlindungan CC',
        ipWhiteList: 'Senarai putih IP',
        ipBlockList: 'Senarai blok IP',
        fileExtBlockList: 'Senarai blok sambungan fail',
        urlWhiteList: 'Senarai putih URL',
        urlBlockList: 'Senarai blok URL',
        argsCheck: 'Pengesahan parameter GET',
        postCheck: 'Pengesahan parameter POST',
        cookieBlockList: 'Senarai blok Cookie',

        dockerHelper: `Firewall Linux "{0}" tidak boleh melumpuhkan pemetaan port Docker. Aplikasi boleh mengedit parameter pada halaman "App Store -> Installed" untuk mengawal sama ada port dilepaskan.`,
        quickJump: 'Akses pantas',
        used: 'Digunakan',
        unUsed: 'Tidak Digunakan',
        firewallHelper: '{0} firewall sistem',
        firewallNotStart: `Firewall sistem belum diaktifkan. Aktifkannya dahulu.`,
        restartFirewallHelper: 'Operasi ini akan memulakan semula firewall semasa. Adakah anda mahu meneruskan?',
        stopFirewallHelper:
            'Ini akan menyebabkan pelayan kehilangan perlindungan keselamatan. Adakah anda mahu meneruskan?',
        startFirewallHelper:
            'Selepas firewall diaktifkan, keselamatan pelayan boleh dilindungi dengan lebih baik. Adakah anda mahu meneruskan?',
        noPing: 'Lumpuhkan ping',
        noPingTitle: 'Lumpuhkan ping',
        noPingHelper: `Ini akan melumpuhkan ping, dan pelayan tidak akan memberikan tindak balas ICMP. Adakah anda mahu meneruskan?`,
        onPingHelper:
            'Ini akan mengaktifkan ping, dan penggodam mungkin menemui pelayan anda. Adakah anda mahu meneruskan?',
        changeStrategy: 'Tukar strategi {0}',
        changeStrategyIPHelper1:
            'Tukar strategi alamat IP kepada [deny]. Selepas alamat IP ditetapkan, akses kepada pelayan dilarang. Adakah anda mahu meneruskan?',
        changeStrategyIPHelper2:
            'Tukar strategi alamat IP kepada [allow]. Selepas alamat IP ditetapkan, akses normal dipulihkan. Adakah anda mahu meneruskan?',
        changeStrategyPortHelper1:
            'Tukar dasar port kepada [drop]. Selepas dasar port ditetapkan, akses luaran ditolak. Adakah anda mahu meneruskan?',
        changeStrategyPortHelper2:
            'Tukar dasar port kepada [accept]. Selepas dasar port ditetapkan, akses port biasa akan dipulihkan. Adakah anda mahu meneruskan?',
        stop: 'Hentikan',
        portFormatError: 'Medan ini mesti port yang sah.',
        portHelper1: 'Pelbagai port, contohnya 8080 dan 8081',
        portHelper2: 'Port rentang, contohnya 8080-8089',
        changeStrategyHelper:
            'Tukar strategi {0} [{1}] kepada [{2}]. Selepas tetapan, {0} akan mengakses {2} secara luaran. Adakah anda mahu meneruskan?',
        portHelper: 'Pelbagai port boleh dimasukkan, contohnya 80,81, atau rentang port, contohnya 80-88',
        strategy: 'Strategi',
        accept: 'Terima',
        drop: 'Lumpuhkan',
        anyWhere: 'Mana-mana',
        address: 'Alamat IP tertentu',
        addressHelper: 'Sokong alamat IP atau segmen IP',
        allow: 'Benarkan',
        deny: 'Tolak',
        addressFormatError: 'Medan ini mesti alamat IP yang sah.',
        addressHelper1: 'Sokong alamat IP atau julat IP. Sebagai contoh, "************" atau "***********/24".',
        addressHelper2: 'Untuk pelbagai alamat IP, pisahkan dengan koma. Contohnya, "************, **********/24".',
        allIP: 'Semua IP',
        portRule: 'Peraturan | Peraturan',
        createPortRule: '@:commons.button.create @.lower:firewall.portRule',
        forwardRule: 'Peraturan Pemajuan Port | Peraturan Pemajuan Port',
        createForwardRule: '@:commons.button.create @:firewall.forwardRule',
        ipRule: 'Peraturan IP | Peraturan IP',
        createIpRule: '@:commons.button.create @:firewall.ipRule',
        userAgent: 'Penapis User-Agent',
        sourcePort: 'Port sumber',
        targetIP: 'IP sasaran',
        targetPort: 'Port sasaran',
        forwardHelper1: 'Jika anda ingin memajukan ke port tempatan, IP sasaran harus ditetapkan kepada "127.0.0.1".',
        forwardHelper2: 'Biarkan IP sasaran kosong untuk memajukan ke port tempatan.',
        forwardHelper3: 'Hanya menyokong pemajuan port IPv4.',
    },
    runtime: {
        runtime: 'Runtime',
        workDir: 'Direktori kerja',
        create: 'Cipta runtime',
        localHelper: 'Persekitaran operasi tempatan perlu dipasang sendiri',
        versionHelper: 'Versi PHP, contohnya v8.0',
        buildHelper:
            'Semakin banyak sambungan, semakin tinggi penggunaan CPU semasa membuat imej. Sambungan boleh dipasang selepas persekitaran dibuat.',
        openrestyWarn: 'PHP perlu dinaik taraf kepada OpenResty versi ******** atau lebih tinggi untuk digunakan',
        toupgrade: 'Naik Taraf',
        edit: 'Edit runtime',
        extendHelper:
            'Sambungan yang tidak disenaraikan boleh dimasukkan dan dipilih secara manual. Sebagai contoh, masukkan "sockets" dan pilih pilihan pertama dari senarai juntai bawah untuk melihat senarai sambungan.',
        rebuildHelper: 'Selepas mengedit sambungan, anda perlu membina semula aplikasi PHP untuk ia berkesan',
        rebuild: 'Bina Semula Aplikasi PHP',
        source: 'Sumber sambungan PHP',
        ustc: 'Universiti Sains dan Teknologi China',
        netease: 'Netease',
        aliyun: 'Alibaba Cloud',
        default: 'Default',
        tsinghua: 'Universiti Tsinghua',
        xtomhk: 'Stesen Cermin XTOM (Hong Kong)',
        xtom: 'Stesen Cermin XTOM (Global)',
        phpsourceHelper: 'Pilih sumber yang sesuai mengikut persekitaran rangkaian anda.',
        appPort: 'Port aplikasi',
        externalPort: 'Port luaran',
        packageManager: 'Pengurus pakej',
        codeDir: 'Direktori kod',
        appPortHelper: 'Port yang digunakan oleh aplikasi.',
        externalPortHelper: 'Port yang terdedah kepada dunia luar.',
        runScript: 'Skrip run',
        runScriptHelper: 'Senarai arahan permulaan diuraikan dari fail package.json dalam direktori sumber.',
        open: 'Buka',
        operatorHelper:
            'Operasi {0} akan dilakukan pada persekitaran operasi yang dipilih. Adakah anda mahu meneruskan?',
        taobao: 'Taobao',
        tencent: 'Tencent',
        imageSource: 'Sumber imej',
        moduleManager: 'Pengurusan Modul',
        module: 'Modul',
        nodeOperatorHelper:
            'Adakah {0} {1} modul? Operasi ini mungkin menyebabkan ketidaknormalan dalam persekitaran operasi. Sila pastikan sebelum meneruskan',
        customScript: 'Arahan permulaan tersuai',
        customScriptHelper: 'Sediakan arahan permulaan penuh. Contohnya, "npm run start".',
        portError: 'Jangan ulangi port yang sama.',
        systemRestartHelper: 'Huraian status: Gangguan - status gagal diperoleh kerana sistem dimulakan semula',
        javaScriptHelper: 'Sediakan arahan permulaan penuh. Contohnya, "java -jar halo.jar -Xmx1024M -Xms256M".',
        javaDirHelper: 'Direktori mesti mengandungi fail jar, subdirektori juga diterima',
        goHelper: 'Sediakan arahan permulaan penuh. Contohnya, "go run main.go" atau "./main".',
        goDirHelper: 'Direktori atau subdirektori mesti mengandungi fail Go atau binari.',
        pythonHelper:
            'Sediakan arahan permulaan penuh. Contohnya, "pip install -r requirements.txt && python manage.py runserver 0.0.0.0:5000".',
        dotnetHelper: 'Sila isi arahan pelancaran lengkap, contohnya dotnet MyWebApp.dll',
        dirHelper: 'Nota: Sila isi laluan direktori di dalam bekas',
        concurrency: 'Skim Serentak',
        loadStatus: 'Status Beban',
    },
    process: {
        pid: 'Process ID',
        ppid: 'Parent PID',
        numThreads: 'Threads',
        memory: 'Memory',
        diskRead: 'Disk read',
        diskWrite: 'Disk write',
        netSent: 'uplink',
        netRecv: 'downstream',
        numConnections: 'Connections',
        startTime: 'Start time',
        state: 'State',
        running: 'Running',
        sleep: 'sleep',
        stop: 'stop',
        idle: 'idle',
        zombie: 'zombie process',
        wait: 'waiting',
        lock: 'lock',
        blocked: 'blocked',
        cmdLine: 'Start command',
        basic: 'Basic',
        mem: 'Memory',
        openFiles: 'Open files',
        env: 'Environments',
        noenv: 'None',
        net: 'Network connections',
        laddr: 'Source address/port',
        raddr: 'Destination address/port',
        stopProcess: 'End',
        viewDetails: 'Details',
        stopProcessWarn: 'Are you sure you want to end this process (PID:{0})?',
        processName: 'Process name',
    },
    tool: {
        supervisor: {
            loadStatusErr: 'Gagal mendapatkan status proses, sila semak status perkhidmatan supervisor.',
            notSupport:
                'Perkhidmatan Supervisor tidak dikesan. Sila pergi ke halaman pustaka skrip untuk pemasangan manual',
            list: 'Proses Daemon | Proses Daemon',
            config: 'Konfigurasi Supervisor',
            primaryConfig: 'Lokasi fail konfigurasi utama',
            notSupportCtl: 'supervisorctl tidak dikesan. Sila pergi ke halaman pustaka skrip untuk pemasangan manual.',
            user: 'Pengguna',
            command: 'Perintah',
            dir: 'Direktori',
            numprocs: 'Bil. proses',
            initWarn:
                'Ini akan mengubah nilai "files" dalam bahagian "[include]" dalam fail konfigurasi utama. Direktori fail konfigurasi lain akan menjadi: "{direktori pemasangan 1Panel}/1panel/tools/supervisord/supervisor.d/".',
            operatorHelper: 'Operasi {1} akan dilakukan pada {0}, teruskan?',
            uptime: 'Masa berjalan',
            notStartWarn: 'Supervisor belum dimulakan. Mulakan dahulu.',
            serviceName: 'Nama perkhidmatan',
            initHelper:
                'Perkhidmatan Supervisor dikesan tetapi belum dimulakan. Sila klik butang permulaan di bar status atas untuk konfigurasi.',
            serviceNameHelper:
                'Nama perkhidmatan Supervisor yang diuruskan oleh systemctl, biasanya supervisor atau supervisord',
            restartHelper:
                'Ini akan memulakan semula perkhidmatan selepas inisialisasi, menyebabkan semua proses daemon sedia ada berhenti.',
            RUNNING: 'Berjalan',
            STOPPED: 'Berhenti',
            STOPPING: 'Sedang Berhenti',
            STARTING: 'Sedang Bermula',
            FATAL: 'Gagal bermula',
            BACKOFF: 'Pengecualian permulaan',
            ERROR: 'Ralat',
            statusCode: 'Kod status',
            manage: 'Pengurusan',
            autoRestart: 'Auto Restart',
            EXITED: 'Telah keluar',
            autoRestartHelper:
                'Sama ada untuk memulakan semula program secara automatik selepas ia tamat secara luar jangka',
            autoStart: 'Mula Automatik',
            autoStartHelper: 'Sama ada untuk memulakan perkhidmatan secara automatik selepas Supervisor mula',
        },
    },
    xpack: {
        expiresTrialAlert:
            'Peringatan mesra: Percubaan Pro anda akan tamat dalam {0} hari, dan semua ciri Pro tidak lagi dapat diakses. Sila perbaharui atau naik taraf ke versi penuh tepat pada masanya.',
        expiresAlert:
            'Peringatan mesra: Lesen Pro anda akan tamat dalam {0} hari, dan semua ciri Pro tidak lagi dapat diakses. Sila perbaharui segera untuk memastikan penggunaan berterusan.',
        menu: 'Pro',
        upage: 'Pembina Laman Web AI',
        app: {
            app: 'APP',
            title: 'Nama Panel',
            titleHelper: 'Alias panel digunakan untuk paparan di APP (alias panel lalai)',
            qrCode: 'Kod QR',
            apiStatusHelper: 'APP Panel perlu mengaktifkan fungsi API',
            apiInterfaceHelper:
                'Menyokong akses antara muka API panel (fungsi ini perlu diaktifkan untuk aplikasi panel)',
            apiInterfaceHelper1:
                'Akses aplikasi panel memerlukan penambahan pelawat ke dalam senarai putih, untuk IP yang tidak tetap, disyorkan untuk menambah 0.0.0.0/0(semua IPv4), ::/0 (semua IPv6)',
            qrCodeExpired: 'Masa penyegaran',
            apiLeakageHelper: 'Jangan dedahkan kod QR. Pastikan ia hanya digunakan di persekitaran yang dipercayai.',
        },
        waf: {
            name: 'WAF',
            blackWhite: 'Senarai Hitam dan Putih',
            globalSetting: 'Tetapan Global',
            websiteSetting: 'Tetapan Laman Web',
            blockRecords: 'Rekod Diblok',
            world: 'Dunia',
            china: 'China',
            intercept: 'Sekatan',
            request: 'Permintaan',
            count4xx: 'Kuantiti 4xx',
            count5xx: 'Kuantiti 5xx',
            todayStatus: 'Status Hari Ini',
            reqMap: 'Peta Serangan (30 Hari Lepas)',
            resource: 'Sumber',
            count: 'Kuantiti',
            hight: 'Tinggi',
            low: 'Rendah',
            reqCount: 'Permintaan',
            interceptCount: 'Jumlah Sekatan',
            requestTrends: 'Tren Permintaan (7 Hari Lepas)',
            interceptTrends: 'Tren Sekatan (7 Hari Lepas)',
            whiteList: 'Senarai Putih',
            blackList: 'Senarai Hitam',
            ipBlackListHelper: 'Alamat IP dalam senarai hitam disekat daripada mengakses laman web',
            ipWhiteListHelper: 'Alamat IP dalam senarai putih akan melepasi semua sekatan',
            uaBlackListHelper: 'Permintaan dengan nilai User-Agent dalam senarai hitam akan disekat',
            uaWhiteListHelper: 'Permintaan dengan nilai User-Agent dalam senarai putih akan melepasi semua sekatan',
            urlBlackListHelper: 'Permintaan ke URL dalam senarai hitam akan disekat',
            urlWhiteListHelper: 'Permintaan ke URL dalam senarai putih akan melepasi semua sekatan',
            ccHelper:
                'Jika laman web menerima lebih daripada {1} permintaan daripada IP yang sama dalam {0} saat, IP akan disekat selama {2}',
            blockTime: 'Tempoh Sekatan',
            attackHelper: 'Jika sekatan terkumpul melebihi {1} dalam {0} saat, IP akan disekat selama {2}',
            notFoundHelper:
                'Jika permintaan yang mengembalikan ralat 404 lebih daripada {1} kali dalam {0} saat, IP akan disekat selama {2}',
            frequencyLimit: 'Had Kekerapan',
            regionLimit: 'Had Wilayah',
            defaultRule: 'Peraturan Lalai',
            accessFrequencyLimit: 'Had Kekerapan Akses',
            attackLimit: 'Had Kekerapan Serangan',
            notFoundLimit: 'Had Kekerapan 404',
            urlLimit: 'Had Kekerapan URL',
            urlLimitHelper: 'Tetapkan kekerapan akses untuk URL tunggal',
            sqliDefense: 'Perlindungan Suntikan SQL',
            sqliHelper: 'Mengesan suntikan SQL dalam permintaan dan menyekatnya',
            xssHelper: 'Mengesan XSS dalam permintaan dan menyekatnya',
            xssDefense: 'Perlindungan XSS',
            uaDefense: 'Peraturan User-Agent Berbahaya',
            uaHelper: 'Termasuk peraturan untuk mengenal pasti bot berbahaya biasa',
            argsDefense: 'Peraturan Parameter Berbahaya',
            argsHelper: 'Menyekat permintaan yang mengandungi parameter berbahaya',
            cookieDefense: 'Peraturan Cookie Berbahaya',
            cookieHelper: 'Melarang cookie berbahaya daripada dibawa dalam permintaan',
            headerDefense: 'Peraturan Header Berbahaya',
            headerHelper: 'Melarang permintaan daripada mengandungi header berbahaya',
            httpRule: 'Peraturan Kaedah Permintaan HTTP',
            httpHelper:
                'Tetapkan jenis kaedah yang dibenarkan untuk diakses. Jika anda ingin mengehadkan jenis akses tertentu, sila matikan jenis butang ini. Contoh: hanya akses jenis GET dibenarkan, maka anda perlu matikan butang lain selain GET',
            geoRule: 'Sekatan Akses Wilayah',
            geoHelper:
                'Sekat akses ke laman web anda dari wilayah tertentu, sebagai contoh: jika akses dibenarkan dari China Tanah Besar, maka permintaan dari luar China Tanah Besar akan disekat',
            ipLocation: 'Lokasi IP',
            action: 'Tindakan',
            ruleType: 'Jenis Serangan',
            ipHelper: 'Masukkan alamat IP',
            attackLog: 'Log Serangan',
            rule: 'Peraturan',
            ipArr: 'Julat IPV4',
            ipStart: 'IP Mula',
            ipEnd: 'IP Akhir',
            ipv4: 'IPv4',
            ipv6: 'IPv6',
            urlDefense: 'Peraturan URL',
            urlHelper: 'URL Dilarang',
            dirFilter: 'Penapis Direktori',
            sqlInject: 'Suntikan SQL',
            xss: 'XSS',
            phpExec: 'Pelaksanaan Skrip PHP',
            oneWordTrojan: 'Trojan Satu Perkataan',
            appFilter: 'Penapisan Direktori Berbahaya',
            webshell: 'Webshell',
            args: 'Parameter Berbahaya',
            protocolFilter: 'Penapis Protokol',
            javaFilter: 'Penapisan Fail Berbahaya Java',
            scannerFilter: 'Penapis Pencari',
            escapeFilter: 'Penapis Pengekalan',
            customRule: 'Peraturan Kustom',
            httpMethod: 'Penapis Kaedah HTTP',
            fileExt: 'Had Muat Naik Fail',
            fileExtHelper: 'Lanjaran fail yang dilarang untuk dimuat naik',
            deny: 'Dilarang',
            allow: 'Benarkan',
            field: 'Objek',
            pattern: 'Keadaan',
            ruleContent: 'Kandungan',
            contain: 'termasuk',
            equal: 'sama dengan',
            regex: 'peraturan biasa',
            notEqual: 'Tidak sama dengan',
            customRuleHelper: 'Ambil tindakan berdasarkan keadaan yang ditetapkan',
            actionAllow: 'Benarkan',
            blockIP: 'Sekat IP',
            code: 'Kod Status Kembali',
            noRes: 'Putuskan sambungan (444)',
            badReq: 'Parameter Tidak Sah (400)',
            forbidden: 'Akses Dilarang (403)',
            serverErr: 'Ralat Server (500)',
            resHtml: 'Halaman Respons',
            allowHelper: 'Membenarkan akses akan melangkau peraturan WAF seterusnya, sila gunakan dengan berhati-hati',
            captcha: 'verifikasi manusia-mesin',
            fiveSeconds: 'Verifikasi 5-Saat',
            location: 'Wilayah',
            redisConfig: 'Konfigurasi Redis',
            redisHelper: 'Aktifkan Redis untuk menyimpan IP yang disekat sementara',
            wafHelper: 'Semua laman web akan kehilangan perlindungan selepas menutup',
            attackIP: 'IP Serangan',
            attackParam: 'Butiran Serangan',
            execRule: 'Peraturan Dilanggar',
            acl: 'ACL',
            sql: 'Suntikan SQL',
            cc: 'Had Kekerapan Akses',
            isBlocking: 'Disekat',
            isFree: 'Tidak Disekat',
            unLock: 'Buka Kunci',
            unLockHelper: 'Adakah anda mahu membuka kunci IP: {0}?',
            saveDefault: 'Simpan Lalai',
            saveToWebsite: 'Terapkan ke Laman Web',
            saveToWebsiteHelper: 'Terapkan tetapan semasa ke semua laman web?',
            websiteHelper:
                'Ini adalah tetapan lalai untuk mencipta laman web. Pengubahsuaian perlu diterapkan ke laman web untuk berkuat kuasa',
            websiteHelper2:
                'Ini adalah tetapan lalai untuk mencipta laman web. Sila ubah suai konfigurasi tertentu di laman web',
            ipGroup: 'Kumpulan IP',
            ipGroupHelper:
                'Satu IP atau segmen IP setiap baris, menyokong IPv4 dan IPv6, sebagai contoh: *********** atau ***********/24',
            ipBlack: 'Senarai Hitam IP',
            openRestyAlert: 'Versi OpenResty perlu lebih tinggi daripada {0}',
            initAlert:
                'Penyediaan diperlukan untuk penggunaan kali pertama, fail konfigurasi laman web akan diubah, dan konfigurasi WAF asal akan hilang. Sila buat sandaran OpenResty terlebih dahulu',
            initHelper:
                'Operasi penyediaan akan membersihkan konfigurasi WAF yang sedia ada. Adakah anda pasti mahu menyediakan semula?',
            mainSwitch: 'Suis Utama',
            websiteAlert: 'Sila cipta laman web terlebih dahulu',
            defaultUrlBlack: 'Peraturan URL',
            htmlRes: 'Halaman Sekat',
            urlSearchHelper: 'Sila masukkan URL untuk sokongan carian samar',
            toCreate: 'Cipta',
            closeWaf: 'Tutup WAF',
            closeWafHelper: 'Menutup WAF akan menyebabkan laman web kehilangan perlindungan, adakah anda mahu teruskan',
            addblack: 'Hitam',
            addwhite: 'Tambah putih',
            addblackHelper: 'Tambah IP:{0} ke senarai hitam lalai?',
            addwhiteHelper: 'Tambah IP:{0} ke senarai putih lalai?',
            defaultUaBlack: 'Peraturan User-Agent',
            defaultIpBlack: 'Kumpulan IP Berbahaya',
            cookie: 'Peraturan Cookie',
            urlBlack: 'Senarai Hitam URL',
            uaBlack: 'Senarai Hitam User-Agent',
            attackCount: 'Had Kekerapan Serangan',
            fileExtCheck: 'Had Muat Naik Fail',
            geoRestrict: 'Sekatan Akses Wilayah',
            attacklog: 'Rekod Sekat',
            unknownWebsite: 'Akses domain yang tidak sah',
            geoRuleEmpty: 'Wilayah tidak boleh kosong',
            unknown: 'Laman Web Tidak Wujud',
            geo: 'Sekatan Wilayah',
            revertHtml: 'Adakah anda mahu memulihkan {0} sebagai halaman lalai?',
            five_seconds: 'Verifikasi 5-Saat',
            header: 'Peraturan Header',
            methodWhite: 'Peraturan HTTP',
            expiryDate: 'Tarikh Tamat',
            expiryDateHelper: 'Selepas lulus verifikasi, ia tidak akan disahkan lagi dalam tempoh sah',
            defaultIpBlackHelper: 'Beberapa IP berbahaya yang dikumpul dari Internet untuk mencegah akses',
            notFoundCount: 'Had Kekerapan 404',
            matchValue: 'Nilai Padanan',
            headerName: 'Menyokong Bahasa Inggeris, nombor, -, panjang 3-30',
            cdnHelper: 'Laman web yang menggunakan CDN boleh dibuka di sini untuk mendapatkan IP sumber yang betul',
            clearLogWarn: 'Pembersihan log tidak akan mungkin, adakah anda mahu meneruskan?',
            commonRuleHelper: 'Peraturan adalah padanan samar',
            blockIPHelper:
                'IP yang disekat disimpan sementara dalam OpenResty dan akan dibuka kunci apabila anda memulakan semula OpenResty. Mereka boleh disekat secara kekal melalui fungsi sekatan',
            addWhiteUrlHelper: 'Tambah URL {0} ke senarai putih?',
            dashHelper: 'Versi komuniti juga boleh menggunakan fungsi dalam tetapan global dan tetapan laman web',
            wafStatusHelper: 'WAF tidak diaktifkan, sila aktifkan dalam tetapan global',
            ccMode: 'Mod',
            global: 'Mod Global',
            uriMode: 'Mod URL',
            globalHelper:
                'Mod Global: Digerakkan apabila jumlah permintaan ke mana-mana URL dalam unit masa melebihi had',
            uriModeHelper: 'Mod URL: Digerakkan apabila jumlah permintaan ke satu URL dalam unit masa melebihi had',

            ip: 'Senarai Hitam IP',
            globalSettingHelper:
                'Tetapan dengan tag [Laman Web] perlu diaktifkan dalam [Tetapan Laman Web], dan tetapan global hanya tetapan lalai untuk laman web yang baru dicipta',
            globalSettingHelper2:
                'Tetapan perlu diaktifkan dalam kedua-dua [Tetapan Global] dan [Tetapan Laman Web] pada masa yang sama',
            urlCCHelper: '{1} kepingatan permintaan URL ini dalam {0} saat, menyekat IP ini {2}',
            urlCCHelper2: 'URL tidak boleh mengandung parameter',
            notContain: 'Tidak mengandung',
            urlcc: 'Had frekuensi URL',
            method: 'Jenis permintaan',
            addIpsToBlock: 'Sekat IP secara pukal',
            addUrlsToWhite: 'Tambah URL ke senarai putih secara pukal',
            noBlackIp: 'IP telah disekat, tidak perlu disekat semula',
            noWhiteUrl: 'URL telah dimasukkan ke senarai putih, tidak perlu ditambah semula',
            spiderIpHelper:
                'Termasuk Baidu, Bing, Google, 360, Shenma, Sogou, ByteDance, DuckDuckGo, Yandex. Menutup ini akan menyekat semua akses labah-labah.',
            spiderIp: 'Kolam IP labah-labah',
            geoIp: 'Pustaka Alamat IP',
            geoIpHelper: 'Digunakan untuk mengesahkan lokasi geografi IP',
            stat: 'Laporan Serangan',
            statTitle: 'Laporan',
            attackIp: 'IP Serangan',
            attackCountNum: 'Bilangan Serangan',
            percent: 'Peratusan',
            addblackUrlHelper: 'Adakah anda mahu menambah URL: {0} ke senarai hitam lalai?',
            rce: 'Pelaksanaan Kod Jarak Jauh',
            software: 'Perisian',
            cveHelper: 'Mengandungi kelemahan biasa perisian dan rangka kerja',
            vulnCheck: 'Peraturan Tambahan',
            ssrf: 'Kelemahan SSRF',
            afr: 'Pembacaan Fail Arbitrari',
            ua: 'Akses Tanpa Kebenaran',
            id: 'Pendedahan Maklumat',
            aa: 'Mengelakkan Pengesahan',
            dr: 'Penembusan Direktori',
            xxe: 'Kelemahan XXE',
            suid: 'Kelemahan Serialisasi',
            dos: 'Kelemahan Penafian Perkhidmatan',
            afd: 'Muat Turun Fail Arbitrari',
            sqlInjection: 'Tambahan SQL',
            afw: 'Penulisan Fail Arbitrari',
            il: 'Pendedahan Maklumat',
            clearAllLog: 'Kosongkan Semua Log',
            exportLog: 'Eksport Log',
            appRule: 'Peraturan Aplikasi',
            appRuleHelper:
                'Peraturan aplikasi biasa, membolehkan boleh mengurangkan positif palsu, satu laman web hanya boleh menggunakan satu peraturan',
            logExternal: 'Kecualikan Jenis Rekod',
            ipWhite: 'Senarai Putih IP',
            urlWhite: 'Senarai Putih URL',
            uaWhite: 'Senarai Putih User-Agent',
            logExternalHelper:
                'Jenis rekod yang dikecualikan tidak akan direkodkan dalam log, senarai hitam/putih, sekatan akses wilayah, dan peraturan tersuai akan menghasilkan banyak log, disarankan untuk mengeluarkan',
            ssti: 'Serangan SSTI',
            crlf: 'Penyuntikan CRLF',
            strict: 'Mod Strict',
            strictHelper: 'Gunakan peraturan yang lebih ketat untuk mengesahkan permintaan',
            saveLog: 'Simpan Log',
            remoteURLHelper: 'URL jauh perlu memastikan satu IP setiap baris dan tiada aksara lain',
            notFound: 'Not Found (404)',
            serviceUnavailable: 'Perkhidmatan Tidak Tersedia (503)',
            gatewayTimeout: 'Timeout Gateway (504)',
            belongToIpGroup: 'Tergolong dalam Kumpulan IP',
            notBelongToIpGroup: 'Tidak tergolong dalam Kumpulan IP',
            unknownWebsiteKey: 'Domain Tidak Diketahui',
            special: 'Peraturan Khas',
        },
        monitor: {
            name: 'Pemantauan Laman Web',
            pv: 'Paparan Halaman',
            uv: 'Pelawat Unik',
            flow: 'Aliran Trafik',
            ip: 'IP',
            spider: 'Spider',
            visitors: 'Tren Pelawat',
            today: 'Hari Ini',
            last7days: '7 Hari Terakhir',
            last30days: '30 Hari Terakhir',
            uvMap: 'Peta Pelawat (30th)',
            qps: 'Permintaan Masa Sebenar (setiap minit)',
            flowSec: 'Trafik Masa Sebenar (setiap minit)',
            excludeCode: 'Kecualikan Kod Status',
            excludeUrl: 'Kecualikan URL',
            excludeExt: 'Kecualikan Sambungan',
            cdnHelper: 'Dapatkan IP sebenar dari Header yang disediakan oleh CDN',
            reqRank: 'Peringkat Lawatan',
            refererDomain: 'Domain Rujukan',
            os: 'Sistem',
            browser: 'Pelayar/Klien',
            device: 'Peranti',
            showMore: 'Lebih',
            unknown: 'Lain-lain',
            pc: 'Komputer',
            mobile: 'Peranti Mudah Alih',
            wechat: 'WeChat',
            machine: 'Mesin',
            tencent: 'Pelayar Tencent',
            ucweb: 'Pelayar UC',
            '2345explorer': 'Pelayar 2345',
            huaweibrowser: 'Pelayar Huawei',
            log: 'Log Permintaan',
            statusCode: 'Kod Status',
            requestTime: 'Masa Respons',
            flowRes: 'Trafik Respons',
            method: 'Kaedah Permintaan',
            statusCodeHelper: 'Masukkan kod status di atas',
            statusCodeError: 'Jenis kod status tidak sah',
            methodHelper: 'Masukkan kaedah permintaan di atas',
            all: 'Semua',
            baidu: 'Baidu',
            google: 'Google',
            bing: 'Bing',
            bytes: 'Tajuk Hari Ini',
            sogou: 'Sogou',
            failed: 'Ralat',
            ipCount: 'Kiraan IP',
            spiderCount: 'Permintaan Spider',
            averageReqTime: 'Masa Respons Purata',
            totalFlow: 'Jumlah Trafik',
            logSize: 'Saiz Fail Log',
            realIPType: 'Kaedah pemerolehan IP sebenar',
            fromHeader: 'Dapatkan dari Header HTTP',
            fromHeaders: 'Dapatkan dari senarai Header',
            header: 'Header HTTP',
            cdnConfig: 'Konfigurasi CDN',
            xff1: 'Proksi Tahap Pertama dari X-Forwarded-For',
            xff2: 'Proksi Tahap Kedua dari X-Forwarded-For',
            xff3: 'Proksi Tahap Ketiga dari X-Forwarded-For',
            xffHelper:
                'Contoh: X-Forwarded-For: <client>,<proxy1>,<proxy2>,<proxy3> Proksi tahap atas akan mengambil IP terakhir <proxy3>',
            headersHelper:
                'Dapatkan IP sebenar dari header CDN HTTP yang biasa digunakan, memilih nilai pertama yang tersedia',
            monitorCDNHelper:
                'Mengubah konfigurasi CDN untuk pemantauan laman web juga akan mengemas kini tetapan WAF CDN',
            wafCDNHelper: 'Mengubah konfigurasi WAF CDN juga akan mengemas kini tetapan CDN pemantauan laman web',
            statusErr: 'Format kod status tidak sah',
            shenma: 'Shenma Search',
            duckduckgo: 'DuckDuckGo',
            '360': 'Pencarian 360',
            excludeUri: 'Kecualikan URI',
            top100Helper: 'Tunjukkan data 100 teratas',
            logSaveDay: 'Tempoh Penahanan Log (hari)',
            cros: 'Chrome OS',
            theworld: 'Pelayar TheWorld',
            edge: 'Microsoft Edge',
            maxthon: 'Pelayar Maxthon',
            monitorStatusHelper: 'Pemantauan tidak diaktifkan, sila aktifkan dalam tetapan',
            excludeIp: 'Kecualikan Alamat IP',
            excludeUa: 'Kecualikan User-Agent',
            remotePort: 'Port Jauh',
            unknown_browser: 'Tidak Diketahui',
            unknown_os: 'Tidak Diketahui',
            unknown_device: 'Tidak Diketahui',
            logSaveSize: 'Saiz Simpanan Log Maksimum',
            logSaveSizeHelper: 'Ini adalah saiz simpanan log untuk satu laman web',
            '360se': '360 Pelayar Keselamatan',
            websites: 'Senarai Laman Web',
            trend: 'Statistik Trend',
            reqCount: 'Jumlah Permintaan',
            uriHelper: 'Anda boleh menggunakan /test/* atau /*/index.php untuk mengecualikan Uri',
        },
        tamper: {
            tamper: 'Perlindungan daripada peng篡改 laman web',
            ignoreTemplate: 'Pengecualian templat direktori',
            protectTemplate: 'Lindungi templat fail',
            templateContent: 'Kandungan templat',
            template: 'Templat',
            tamperHelper1:
                'Untuk laman web dengan penyebaran satu klik, disyorkan untuk mengaktifkan fungsi perlindungan daripada peng篡改 direktori aplikasi; jika laman web tidak berfungsi dengan baik atau terdapat masalah dengan sandaran dan pemulihan, sila matikan terlebih dahulu fungsi perlindungan daripada peng篡改;',
            tamperHelper2:
                'Akan ada had pada operasi bacaan, penulisan, penghapusan, pengubahsuaian hak dan pemilik bagi fail yang dilindungi di luar direktori yang dikecualikan',
            tamperPath: 'Direktori yang dilindungi',
            tamperPathEdit: 'Ubah laluan',
            log: 'Log penangkapan',
            totalProtect: 'Perlindungan keseluruhan',
            todayProtect: 'Perlindungan hari ini',
            addRule: 'Tambah peraturan',
            ignore: 'Pengecualian direktori',
            ignoreHelper: 'Satu baris satu, contohnya: \ntmp\n./tmp',
            ignoreTemplateHelper:
                'Tambah nama folder yang ingin diabaikan, dipisahkan dengan koma, contohnya: tmp,cache',
            templateRule: 'Panjang 1-512, nama tidak boleh mengandungi simbol seperti {0}',
            ignoreHelper1: 'Tambah nama folder atau laluan tertentu yang ingin diabaikan',
            ignoreHelper2: 'Untuk mengabaikan folder tertentu, gunakan laluan relatif yang bermula dengan ./',
            protect: 'Lindungi fail',
            protectHelper: 'Satu baris satu, contohnya: \npng\n./test.css',
            protectTemplateHelper:
                'Tambah nama fail atau sambungan yang ingin diabaikan, dipisahkan dengan koma, contohnya: conf,.css',
            protectHelper1: 'Boleh menentukan nama fail, sambungan atau fail tertentu untuk dilindungi',
            protectHelper2: 'Untuk melindungi fail tertentu, gunakan laluan relatif yang bermula dengan ./',
            enableHelper:
                'Fungsi perlindungan daripada peng篡改 akan diaktifkan untuk laman web berikut untuk meningkatkan keselamatan laman web, adakah anda ingin meneruskan?',
            disableHelper:
                'Fungsi perlindungan daripada peng篡改 akan dimatikan untuk laman web berikut, adakah anda ingin meneruskan?',
        },
        setting: {
            setting: 'Tetapan Panel',
            title: 'Deskripsi Panel',
            titleHelper:
                'Akan dipaparkan pada halaman log masuk pengguna (contoh: Panel pengurusan operasi dan penyelenggaraan pelayan Linux, disarankan 8-15 aksara)',
            logo: 'Logo (Tanpa Teks)',
            logoHelper:
                'Akan dipaparkan di sudut kiri atas halaman pengurusan apabila menu dikurangkan (saiz imej yang disarankan: 82px*82px)',
            logoWithText: 'Logo (Dengan Teks)',
            logoWithTextHelper:
                'Akan dipaparkan di sudut kiri atas halaman pengurusan apabila menu diperluaskan (saiz imej yang disarankan: 185px*55px)',
            favicon: 'Ikon Laman Web',
            faviconHelper: 'Ikon laman web (saiz imej yang disarankan: 16px*16px)',
            reUpload: 'Pilih Fail',
            setDefault: 'Pulihkan Tetapan Asal',
            setHelper: 'Tetapan semasa akan disimpan. Adakah anda ingin meneruskan?',
            setDefaultHelper: 'Semua tetapan panel akan dikembalikan ke asal. Adakah anda ingin meneruskan?',
            logoGroup: 'Logo',
            imageGroup: 'Imej',
            loginImage: 'Imej Halaman Log Masuk',
            loginImageHelper: 'Akan dipaparkan di halaman log masuk (Saiz disyorkan: 500*416px)',
            loginBgType: 'Jenis Latar Halaman Log Masuk',
            loginBgImage: 'Imej Latar Halaman Log Masuk',
            loginBgImageHelper:
                'Akan dipaparkan sebagai latar belakang halaman log masuk (Saiz disyorkan: 1920*1080px)',
            loginBgColor: 'Warna Latar Halaman Log Masuk',
            loginBgColorHelper: 'Akan dipaparkan sebagai warna latar belakang halaman log masuk',
            image: 'Imej',
            bgColor: 'Warna Latar',
            loginGroup: 'Halaman Log Masuk',
            loginBtnLinkColor: 'Warna Butang/Pautan',
            loginBtnLinkColorHelper: 'Akan dipaparkan sebagai warna butang/pautan di halaman log masuk',
        },
        helper: {
            wafTitle1: 'Peta Pencegahan',
            wafContent1: 'Memaparkan taburan geografi pencegahan dalam tempoh 30 hari yang lalu',
            wafTitle2: 'Sekatan Akses Wilayah',
            wafContent2: 'Menyekat sumber akses laman web mengikut lokasi geografi',
            wafTitle3: 'Halaman Pencegahan Tersuai',
            wafContent3: 'Cipta halaman tersuai untuk dipaparkan selepas permintaan disekat',
            wafTitle4: 'Peraturan Tersuai (ACL)',
            wafContent4: 'Sekat permintaan mengikut peraturan tersuai',

            tamperTitle1: 'Pemantauan Integriti Fail',
            tamperContent1: 'Pantau integriti fail laman web, termasuk fail teras, skrip, dan fail konfigurasi.',
            tamperTitle2: 'Pengimbasan dan Pengesanan Masa Nyata',
            tamperContent2:
                'Kesan fail yang tidak normal atau diubah suai dengan mengimbas sistem fail laman web secara masa nyata.',
            tamperTitle3: 'Tetapan Kebenaran Keselamatan',
            tamperContent3:
                'Hadkan akses ke fail laman web melalui tetapan kebenaran yang sesuai dan dasar kawalan akses, mengurangkan permukaan serangan yang berpotensi.',
            tamperTitle4: 'Log dan Analisis',
            tamperContent4:
                'Rekod log akses dan operasi fail untuk audit dan analisis selanjutnya oleh pentadbir serta mengenal pasti potensi ancaman keselamatan.',

            settingTitle1: 'Mesej Selamat Datang Tersuai',
            settingContent1: 'Tetapkan mesej selamat datang tersuai pada halaman log masuk 1Panel.',
            settingTitle2: 'Logo Tersuai',
            settingContent2: 'Benarkan memuat naik gambar logo yang mengandungi nama jenama atau teks lain.',
            settingTitle3: 'Ikon Laman Web Tersuai',
            settingContent3:
                'Benarkan memuat naik ikon tersuai untuk menggantikan ikon pelayar lalai, meningkatkan pengalaman pengguna.',

            monitorTitle1: 'Trend Pelawat',
            monitorContent1: 'Statistik dan memaparkan trend pelawat laman web',
            monitorTitle2: 'Peta Pelawat',
            monitorContent2: 'Statistik dan memaparkan taburan geografi pelawat laman web',
            monitorTitle3: 'Statistik Akses',
            monitorContent3:
                'Statistik maklumat permintaan laman web, termasuk labah-labah, peranti akses, status permintaan, dan sebagainya.',
            monitorTitle4: 'Pemantauan Masa Nyata',
            monitorContent4:
                'Pemantauan masa nyata maklumat permintaan laman web, termasuk bilangan permintaan, trafik, dan sebagainya.',

            alertTitle1: 'Amaran SMS',
            alertContent1:
                'Apabila berlaku penggunaan sumber pelayan yang tidak normal, tamat tempoh laman web dan sijil, kemas kini versi baru, tamat tempoh kata laluan, dan sebagainya, pengguna akan diberitahu melalui amaran SMS untuk memastikan pemprosesan tepat pada masanya.',
            alertTitle2: 'Log Amaran',
            alertContent2:
                'Memberikan fungsi kepada pengguna untuk melihat log amaran bagi memudahkan penjejakan dan analisis peristiwa amaran sejarah.',
            alertTitle3: 'Tetapan Amaran',
            alertContent3:
                'Memberikan pengguna konfigurasi nombor telefon tersuai, kekerapan push harian, dan masa push harian, memudahkan pengguna untuk menetapkan amaran push yang lebih munasabah.',

            nodeTitle1: 'Satu Klik Tambah Node',
            nodeContent1: 'Mengintegrasikan pelbagai nod pelayan dengan cepat',
            nodeTitle2: 'Kelompok Naik Taraf',
            nodeContent2: 'Segerakan dan naik taraf semua nod dengan satu operasi',
            nodeTitle3: 'Pemantauan Status Node',
            nodeContent3: 'Memantau status operasi setiap nod secara real-time',
            nodeTitle4: 'Sambungan Jauh Pantas',
            nodeContent4: 'Sambung terus ke terminal jauh nod dengan satu klik',

            fileExchangeTitle1: 'Pengangkutan Pengesahan Kunci',
            fileExchangeContent1: 'Mengesahkan melalui kunci SSH untuk memastikan keselamatan pengangkutan.',
            fileExchangeTitle2: 'Segerakan Fail Cekap',
            fileExchangeContent2:
                'Hanya menyegerakkan kandungan yang berubah untuk meningkatkan kelajuan dan kestabilan pengangkutan secara signifikan.',
            fileExchangeTitle3: 'Sokongan Pertukaran Inter-Node',
            fileExchangeContent3:
                'Mudah memindahkan fail projek antara nod yang berbeza, mengurus pelbagai pelayan dengan fleksibiliti.',

            appTitle1: 'Pengurusan Panel yang Fleksibel',
            appContent1: 'Uruskan pelayan 1Panel anda dengan mudah pada bila-bila masa dan di mana sahaja.',
            appTitle2: 'Maklumat Perkhidmatan Komprehensif',
            appContent2:
                'Uruskan aplikasi, laman web, Docker, pangkalan data, dll melalui aplikasi mudah alih dan sokong pembinaan pantas aplikasi dan laman web.',
            appTitle3: 'Pemantauan Anomali Masa Nyata',
            appContent3:
                'Lihat status pelayan, pemantauan keselamatan WAF, statistik pelawat laman web dan status kesihatan proses di aplikasi mudah alih secara masa nyata.',

            clusterTitle1: 'Penyebaran Utama-Hamba',
            clusterContent1:
                'Menyokong penciptaan contoh utama-hamba MySQL/Postgres/Redis pada nod yang berbeza, secara automatik melengkapkan perhubungan utama-hamba dan permulaan',
            clusterTitle2: 'Pengurusan Utama-Hamba',
            clusterContent2:
                'Halaman terpadu untuk menguruskan pelbagai nod utama-hamba, lihat peranan, status berjalan, dsb.',
            clusterTitle3: 'Status Replikasi',
            clusterContent3:
                'Memaparkan status replikasi utama-hamba dan maklumat kelewatan, membantu menyelesaikan masalah sinkronisasi',
        },
        node: {
            master: 'Nod Utama',
            masterBackup: 'Sandaran Nod Master',
            backupNode: 'Nod Sandaran',
            backupFrequency: 'Kekerapan Sandaran (jam)',
            backupCopies: 'Bilangan salinan sandaran yang disimpan',
            noBackupNode: 'Nod sandaran kosong. Sila pilih nod sandaran untuk disimpan dan cuba lagi!',
            masterBackupAlert:
                'Sandaran nod master belum dikonfigurasikan. Untuk memastikan keselamatan data, sila sediakan nod sandaran secepat mungkin untuk memudahkan pertukaran manual ke nod master baru sekiranya berlaku kegagalan.',
            node: 'Nod',
            addr: 'Alamat',
            nodeUnhealthy: 'Status nod tidak normal',
            deletedNode: 'Nod {0} yang telah dipadam tidak menyokong operasi naik taraf buat masa ini!',
            nodeUnhealthyHelper: 'Status nod tidak normal dikesan. Sila semak dalam [Pengurusan Nod] dan cuba lagi!',
            nodeUnbind: 'Nod tidak terikat pada lesen',
            nodeUnbindHelper:
                'Terdeteksi nod ini tidak terikat pada lesen. Sila ikat dalam menu [Tetapan Panel - Lesen] dan cuba lagi!',
            memTotal: 'Jumlah Memori',
            nodeManagement: 'Pengurusan Nod',
            addNode: 'Tambah Nod',
            connInfo: 'Maklumat Sambungan',
            nodeInfo: 'Maklumat Nod',
            syncInfo: 'Penyegerakan data,',
            syncHelper: 'Apabila data nod induk berubah, ia akan disegerakkan ke nod anak ini secara masa nyata,',
            syncBackupAccount: 'Tetapan akaun sandaran',
            syncWithMaster:
                'Selepas menaik taraf ke Pro, semua data akan diselaraskan secara lalai. Dasar penyelarasan boleh disesuaikan secara manual dalam pengurusan nod.',
            syncProxy: 'Tetapan proksi sistem',
            syncProxyHelper: 'Penyelarasan tetapan proksi sistem memerlukan mulakan semula Docker',
            syncProxyHelper1: 'Memulakan semula Docker mungkin menjejaskan perkhidmatan kontena yang sedang berjalan.',
            syncProxyHelper2: 'Anda boleh mulakan semula secara manual di halaman Kontena - Konfigurasi.',
            syncProxyHelper3:
                'Penyelarasan tetapan proksi sistem memerlukan mulakan semula Docker, yang mungkin menjejaskan perkhidmatan kontena yang sedang berjalan',
            syncProxyHelper4:
                'Penyelarasan tetapan proksi sistem memerlukan mulakan semula Docker. Anda boleh mulakan semula secara manual di halaman Kontena - Konfigurasi nanti.',
            syncCustomApp: 'Segerakan Repositori Aplikasi Tersuai',
            syncAlertSetting: 'Tetapan amaran sistem',
            syncNodeInfo: 'Data asas nod,',
            nodeSyncHelper: 'Penyelarasan maklumat nod akan menyelaraskan maklumat berikut:',
            nodeSyncHelper1: '1. Maklumat akaun sandaran awam',
            nodeSyncHelper2: '2. Maklumat sambungan antara nod utama dan nod sub',

            nodeCheck: 'Semakan ketersediaan',
            checkSSH: 'Periksa sambungan SSH nod',
            checkUserPermission: 'Semak kebenaran pengguna nod',
            isNotRoot: 'Dikesan sudo tanpa kata laluan tidak disokong pada nod ini dan pengguna semasa bukan root',
            checkLicense: 'Periksa status lesen nod',
            checkService: 'Periksa maklumat perkhidmatan sedia ada pada nod',
            checkPort: 'Periksa kebolehcapaian port nod',
            panelExist:
                'Mengesan nod ini sedang menjalankan perkhidmatan 1Panel V1. Sila naik taraf ke V2 menggunakan skrip migrasi sebelum menambah.',
            coreExist:
                'Nod semasa telah didayakan sebagai nod induk dan tidak boleh ditambah terus sebagai nod hamba. Sila turun taraf kepada nod hamba terlebih dahulu sebelum menambah, rujuk dokumentasi untuk butiran.',
            agentExist:
                'Mengesan 1panel-agent telah dipasang pada nod ini. Penerusan akan mengekalkan data sedia ada dan hanya menggantikan perkhidmatan 1panel-agent.',
            oldDataExist:
                'Mengesan data sejarah 1Panel V2 pada nod ini. Maklumat berikut akan digunakan untuk menimpa tetapan semasa:',
            errLicense: 'Lesen yang terikat pada nod ini tidak tersedia. Sila semak dan cuba lagi!',
            errNodePort:
                'Port nod [ {0} ] dikesan tidak boleh diakses. Sila semak sama ada firewall atau kumpulan keselamatan telah membenarkan port ini.',

            reinstallHelper: 'Pasang semula nod {0}, adakah anda ingin meneruskan?',
            unhealthyCheck: 'Pemeriksaan Tidak Normal',
            fixOperation: 'Operasi Pembetulan',
            checkName: 'Item Pemeriksaan',
            checkSSHConn: 'Periksa Ketersediaan Sambungan SSH',
            fixSSHConn: 'Edit nod secara manual untuk mengesahkan maklumat sambungan',
            checkConnInfo: 'Periksa Maklumat Sambungan Ejen',
            checkStatus: 'Periksa Ketersediaan Perkhidmatan Nod',
            fixStatus:
                'Jalankan "systemctl status 1panel-agent.service" untuk memeriksa sama ada perkhidmatan sedang berjalan.',
            checkAPI: 'Periksa Ketersediaan API Nod',
            fixAPI: 'Semak log nod dan periksa sama ada port firewall dibuka dengan betul.',
            forceDelete: 'Hapus Secara Paksa',
            operateHelper: 'Operasi {0} akan dilakukan pada nod berikut, adakah anda ingin meneruskan?',
            forceDeleteHelper:
                'Hapus secara paksa akan mengabaikan ralat penghapusan nod dan menghapus metadata pangkalan data',
            uninstall: 'Padam data nod',
            uninstallHelper: 'Ini akan memadamkan semua data berkaitan 1Panel bagi nod. Pilih dengan berhati-hati!',
            baseDir: 'Direktori Pemasangan',
            baseDirHelper: 'Apabila direktori pemasangan kosong, secara lalai akan dipasang di direktori /opt',
            nodePort: 'Port Nod',
            offline: 'Mod luar talian',
            freeCount: 'Had percuma [{0}]',
            offlineHelper: 'Digunakan apabila nod berada dalam persekitaran luar talian',
        },
        customApp: {
            name: 'Repositori Aplikasi Khusus',
            appStoreType: 'Sumber Pakej App Store',
            appStoreUrl: 'URL Repositori',
            local: 'Laluan Tempatan',
            remote: 'Pautan Jauh',
            imagePrefix: 'Awalan Imej',
            imagePrefixHelper:
                'Fungsi: Sesuaikan awalan imej dan ubah medan imej dalam fail compose. Contohnya, apabila awalan imej ditetapkan kepada 1panel/custom, medan imej untuk MaxKB akan bertukar kepada 1panel/custom/maxkb:v1.10.0',
            closeHelper: 'Batalkan penggunaan repositori aplikasi khusus',
            appStoreUrlHelper: 'Hanya format .tar.gz disokong',
            postNode: 'Segerakkan ke sub-node',
            postNodeHelper:
                'Segerakan pakej kedai tersuai ke tmp/customApp/apps.tar.gz dalam direktori pemasangan nod anak',
            nodes: 'Pilih Node',
            selectNode: 'Pilih Node',
            selectNodeError: 'Sila pilih node',
            licenseHelper: 'Versi Pro menyokong fungsi gudang aplikasi tersuai',
        },
        alert: {
            isAlert: 'Amaran',
            alertCount: 'Bilangan Amaran',
            clamHelper: 'Hantar amaran apabila terdapat fail yang dijangkiti semasa imbasan',
            cronJobHelper: 'Hantar amaran apabila pelaksanaan tugas gagal',
            licenseHelper: 'Versi profesional menyokong amaran SMS',
            alertCountHelper: 'Kekerapan maksimum amaran harian',
            alert: 'Amaran SMS',
            logs: 'Log Amaran',
            list: 'Senarai Amaran',
            addTask: 'Cipta Amaran',
            editTask: 'Edit Amaran',
            alertMethod: 'Kaedah',
            alertMsg: 'Mesej Amaran',
            alertRule: 'Peraturan Amaran',
            titleSearchHelper: 'Masukkan tajuk amaran untuk pencarian kabur',
            taskType: 'Jenis',
            ssl: 'Sijil (SSL) Tamat Tempoh',
            siteEndTime: 'Tamat Tempoh Laman Web',
            panelPwdEndTime: 'Kata Laluan Panel Tamat Tempoh',
            panelUpdate: 'Versi Panel Baharu Tersedia',
            cpu: 'Amaran CPU Pelayan',
            memory: 'Amaran Memori Pelayan',
            load: 'Amaran Beban Pelayan',
            disk: 'Amaran Cakera Pelayan',
            website: 'Laman Web',
            certificate: 'Sijil SSL',
            remainingDays: 'Hari yang Tinggal',
            sendCount: 'Bilangan Hantar',
            sms: 'SMS',
            wechat: 'WeChat',
            dingTalk: 'DingTalk',
            feiShu: 'FeiShu',
            mail: 'E-mel',
            weCom: 'WeCom',
            sendCountRulesHelper: 'Jumlah amaran dihantar sebelum tamat tempoh (sekali sehari)',
            panelUpdateRulesHelper: 'Jumlah amaran dihantar untuk versi panel baharu (sekali sehari)',
            oneDaySendCountRulesHelper: 'Maksimum amaran dihantar setiap hari',
            siteEndTimeRulesHelper: 'Laman web yang tidak pernah tamat tempoh tidak akan mencetuskan amaran',
            autoRenewRulesHelper:
                'Sijil dengan pembaharuan automatik diaktifkan dan baki hari kurang daripada 31 tidak akan mencetuskan amaran',
            panelPwdEndTimeRulesHelper:
                'Amaran tamat tempoh kata laluan panel tidak tersedia jika tiada tempoh tamat ditetapkan',
            sslRulesHelper: 'Semua Sijil SSL',
            diskInfo: 'Cakera',
            monitoringType: 'Jenis Pemantauan',
            autoRenew: 'Pembaharuan Automatik',
            useDisk: 'Penggunaan Cakera',
            usePercentage: 'Peratusan Penggunaan',
            changeStatus: 'Tukar Status',
            disableMsg:
                'Menghentikan tugas amaran akan menghalang tugas ini daripada menghantar mesej amaran. Adakah anda ingin meneruskan?',
            enableMsg:
                'Mengaktifkan tugas amaran akan membolehkan tugas ini menghantar mesej amaran. Adakah anda ingin meneruskan?',
            useExceed: 'Penggunaan Melebihi',
            useExceedRulesHelper: 'Cetuskan amaran apabila penggunaan melebihi nilai yang ditetapkan',
            cpuUseExceedAvg: 'Penggunaan CPU purata melebihi nilai yang ditetapkan',
            memoryUseExceedAvg: 'Penggunaan memori purata melebihi nilai yang ditetapkan',
            loadUseExceedAvg: 'Penggunaan beban purata melebihi nilai yang ditetapkan',
            cpuUseExceedAvgHelper: 'Penggunaan CPU purata dalam masa tertentu melebihi nilai yang ditetapkan',
            memoryUseExceedAvgHelper: 'Penggunaan memori purata dalam masa tertentu melebihi nilai yang ditetapkan',
            loadUseExceedAvgHelper: 'Penggunaan beban purata dalam masa tertentu melebihi nilai yang ditetapkan',
            resourceAlertRulesHelper: 'Nota: Amaran berterusan dalam masa 30 minit hanya akan menghantar satu SMS',
            specifiedTime: 'Masa Tertentu',
            deleteTitle: 'Padam Amaran',
            deleteMsg: 'Adakah anda pasti ingin memadam tugas amaran?',

            allSslTitle: 'Semua Amaran Tamat Tempoh Sijil SSL Laman Web',
            sslTitle: 'Amaran Tamat Tempoh Sijil SSL untuk Laman Web {0}',
            allSiteEndTimeTitle: 'Semua Amaran Tamat Tempoh Laman Web',
            siteEndTimeTitle: 'Amaran Tamat Tempoh Laman Web {0}',
            panelPwdEndTimeTitle: 'Amaran Tamat Tempoh Kata Laluan Panel',
            panelUpdateTitle: 'Pemberitahuan Versi Panel Baharu',
            cpuTitle: 'Amaran Penggunaan CPU Tinggi',
            memoryTitle: 'Amaran Penggunaan Memori Tinggi',
            loadTitle: 'Amaran Beban Tinggi',
            diskTitle: 'Amaran Penggunaan Cakera Tinggi untuk Direktori {0}',
            allDiskTitle: 'Amaran Penggunaan Cakera Tinggi',

            timeRule: 'Masa baki kurang daripada {0} hari (jika tidak diurus, akan dihantar semula keesokan hari)',
            panelUpdateRule:
                'Hantar satu amaran apabila versi panel baharu dikesan (jika tidak diurus, akan dihantar semula keesokan hari)',
            avgRule:
                'Purata penggunaan {1} melebihi {2}% dalam {0} minit, mencetuskan amaran, dihantar {3} kali sehari',
            diskRule:
                'Penggunaan cakera untuk direktori {0} melebihi {1}{2}, mencetuskan amaran, dihantar {3} kali sehari',
            allDiskRule: 'Penggunaan cakera melebihi {0}{1}, mencetuskan amaran, dihantar {2} kali sehari',

            cpuName: ' CPU ',
            memoryName: 'Memori',
            loadName: 'Beban',
            diskName: 'Cakera',

            syncAlertInfo: 'Tekan Manual',
            syncAlertInfoMsg: 'Adakah anda ingin menekan tugas amaran secara manual?',
            pushError: 'Tekanan Gagal',
            pushSuccess: 'Tekanan Berjaya',
            syncError: 'Penyelarasan Gagal',
            success: 'Amaran Berjaya',
            pushing: 'Sedang menghantar...',
            error: 'Amaran gagal',
            cleanLog: 'Bersihkan Log',
            cleanAlertLogs: 'Bersihkan Log Amaran',
            daily: 'Bilangan Amaran Harian: {0}',
            cumulative: 'Bilangan Amaran Kumulatif: {0}',
            clams: 'Imbasan Virus',
            taskName: 'Nama Tugas',
            cronJobType: 'Jenis Tugas',
            clamPath: 'Direktori Imbasan',
            cronjob: 'Cronjob',
            app: 'Sandaran Aplikasi',
            web: 'Sandaran Laman Web',
            database: 'Sandaran Pangkalan Data',
            directory: 'Sandaran Direktori',
            log: 'Log Sandaran',
            snapshot: 'Snapshot Sistem',
            clamsRulesHelper: 'Tugas imbasan virus yang memerlukan amaran',
            cronJobRulesHelper: 'Jenis tugas berjadual ini perlu dikonfigurasikan',
            clamsTitle: 'Tugas imbasan virus 「 {0} 」 mengesan amaran fail dijangkiti',
            cronJobAppTitle: 'Cronjob - Sandaran Aplikasi 「 {0} 」 Amaran Kegagalan Tugas',
            cronJobWebsiteTitle: 'Cronjob - Sandaran Laman Web 「 {0} 」 Amaran Kegagalan Tugas',
            cronJobDatabaseTitle: 'Cronjob - Sandaran Pangkalan Data 「 {0} 」 Amaran Kegagalan Tugas',
            cronJobDirectoryTitle: 'Cronjob - Sandaran Direktori 「 {0} 」 Amaran Kegagalan Tugas',
            cronJobLogTitle: 'Cronjob - Sandaran Log 「 {0} 」 Amaran Kegagalan Tugas',
            cronJobSnapshotTitle: 'Cronjob - Sandaran Snapshot 「 {0} 」 Amaran Kegagalan Tugas',
            cronJobShellTitle: 'Cronjob - Skrip Shell 「 {0} 」 Amaran Kegagalan Tugas',
            cronJobCurlTitle: 'Cronjob - Akses URL 「 {0} 」 Amaran Kegagalan Tugas',
            cronJobCutWebsiteLogTitle: 'Cronjob - Potong log laman web 「 {0} 」 Amaran Kegagalan Tugas',
            cronJobCleanTitle: 'Cronjob - Pembersihan Cache 「 {0} 」 Amaran Kegagalan Tugas',
            cronJobNtpTitle: 'Cronjob - Penyelarasan masa pelayan 「 {0} 」 Amaran Kegagalan Tugas',
            clamsRule: 'Imbasan virus mengesan fail dijangkiti, dihantar {0} kali sehari',
            cronJobAppRule: 'Amaran kegagalan tugas sandaran aplikasi, dihantar {0} kali sehari',
            cronJobWebsiteRule: 'Amaran kegagalan tugas sandaran laman web, dihantar {0} kali sehari',
            cronJobDatabaseRule: 'Amaran kegagalan tugas sandaran pangkalan data, dihantar {0} kali sehari',
            cronJobDirectoryRule: 'Amaran kegagalan tugas sandaran direktori, dihantar {0} kali sehari',
            cronJobLogRule: 'Amaran kegagalan tugas sandaran log, dihantar {0} kali sehari',
            cronJobSnapshotRule: 'Amaran kegagalan tugas sandaran snapshot, dihantar {0} kali sehari',
            cronJobShellRule: 'Amaran kegagalan tugas skrip shell, dihantar {0} kali sehari',
            cronJobCurlRule: 'Amaran kegagalan tugas akses URL, dihantar {0} kali sehari',
            cronJobCutWebsiteLogRule: 'Amaran kegagalan tugas potong log laman web, dihantar {0} kali sehari',
            cronJobCleanRule: 'Amaran kegagalan tugas pembersihan cache, dihantar {0} kali sehari',
            cronJobNtpRule: 'Amaran kegagalan tugas penyelarasan masa pelayan, dihantar {0} kali sehari',
            alertSmsHelper: 'Had SMS: jumlah {0} mesej, {1} telah digunakan',
            goBuy: 'Beli Lebih Banyak',
            phone: 'Telefon',
            phoneHelper: 'Berikan nombor telefon sebenar untuk mesej amaran',
            dailyAlertNum: 'Had Amaran Harian',
            dailyAlertNumHelper: 'Bilangan maksimum amaran sehari (sehingga 100)',
            timeRange: 'Julat Masa',
            sendTimeRange: 'Hantar julat masa',
            sendTimeRangeHelper: 'Boleh tekan julat masa {0}',
            to: '-',
            startTime: 'Masa Mula',
            endTime: 'Masa Tamat',
            defaultPhone: 'Secara lalai ke nombor telefon akaun terikat lesen',
            noticeAlert: 'Amaran Pemberitahuan',
            resourceAlert: 'Amaran Sumber',
            agentOfflineAlertHelper:
                'Apabila amaran luar talian diaktifkan untuk nod, nod utama akan mengimbas setiap 30 minit untuk melaksanakan tugas amaran.',
            offline: 'Amaran Luar Talian',
            offlineHelper:
                'Apabila ditetapkan kepada amaran luar talian, nod utama akan mengimbas setiap 30 minit untuk melaksanakan tugas amaran.',
            offlineOff: 'Aktifkan Amaran Luar Talian',
            offlineOffHelper:
                'Mengaktifkan amaran luar talian akan menyebabkan nod utama mengimbas setiap 30 minit untuk melaksanakan tugas amaran.',
            offlineClose: 'Lumpuhkan Amaran Luar Talian',
            offlineCloseHelper:
                'Melumpuhkan amaran luar talian memerlukan subnod untuk mengendalikan amaran secara kendiri. Sila pastikan sambungan rangkaian adalah baik untuk mengelakkan kegagalan amaran.',
            alertNotice: 'Pemberitahuan Amaran',
            methodConfig: 'Konfigurasi Kaedah Pemberitahuan',
            commonConfig: 'Konfigurasi Global',
            smsConfig: 'SMS',
            smsConfigHelper: 'Konfigurasi nombor untuk pemberitahuan SMS',
            emailConfig: 'E-mel',
            emailConfigHelper: 'Konfigurasi perkhidmatan penghantaran e-mel SMTP',
            deleteConfigTitle: 'Padam Konfigurasi Amaran',
            deleteConfigMsg: 'Adakah anda pasti mahu memadam konfigurasi amaran?',
            test: 'Ujian',
            alertTestOk: 'Pemberitahuan ujian berjaya',
            alertTestFailed: 'Pemberitahuan ujian gagal',
            displayName: 'Nama Paparan',
            sender: 'Alamat Pengirim',
            password: 'Kata Laluan',
            host: 'Pelayan SMTP',
            port: 'Nombor Port',
            encryption: 'Kaedah Penyulitan',
            recipient: 'Penerima',
            licenseTime: 'Peringatan Tamat Tempoh Lesen',
            licenseTimeTitle: 'Peringatan Tamat Tempoh Lesen',
            displayNameHelper: 'Nama paparan pengirim e-mel',
            senderHelper: 'Alamat e-mel yang digunakan untuk menghantar e-mel',
            passwordHelper: 'Kod kebenaran untuk perkhidmatan e-mel',
            hostHelper: 'Alamat pelayan SMTP, contoh: smtp.qq.com',
            portHelper: 'SSL biasanya 465, TLS biasanya 587',
            sslHelper: 'Jika port SMTP ialah 465, SSL biasanya diperlukan',
            tlsHelper: 'Jika port SMTP ialah 587, TLS biasanya diperlukan',
        },
        theme: {
            lingXiaGold: 'Ling Xia Emas',
            classicBlue: 'Biru Klasik',
            freshGreen: 'Hijau Segar',
            customColor: 'Warna Tersuai',
            setDefault: 'Lalai',
            setDefaultHelper: 'Skema warna tema akan dipulihkan ke keadaan asalnya. Adakah anda ingin meneruskan?',
            setHelper: 'Skema warna tema yang dipilih sekarang akan disimpan. Adakah anda ingin meneruskan?',
        },
        exchange: {
            exchange: 'Pertukaran Fail',
            exchangeConfirm: 'Adakah anda mahu memindahkan fail/folder {1} dari node {0} ke direktori {3} node {2}?',
        },
        cluster: {
            cluster: 'Aplikasi Tinggi Ketersediaan',
            name: 'Nama Kluster',
            addCluster: 'Tambah Kluster',
            installNode: 'Pasang Node',
            master: 'Node Utama',
            slave: 'Node Hamba',
            replicaStatus: 'Utama-Hamba Status',
            unhealthyDeleteError: 'Status nod pemasangan tidak normal, sila periksa senarai nod dan cuba lagi!',
            replicaStatusError: 'Pengambilan status tidak normal, sila periksa nod utama.',
            masterHostError: 'IP nod utama tidak boleh 127.0.0.1',
        },
    },
};

export default {
    ...fit2cloudEnLocale,
    ...message,
};
