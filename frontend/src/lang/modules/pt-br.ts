import fit2cloudPtBrLocale from 'fit2cloud-ui-plus/src/locale/lang/pt-br';

const message = {
    commons: {
        true: 'verdade<PERSON>',
        false: 'falso',
        colon: ': ',
        example: 'Por exemplo, ',
        fit2cloud: 'FIT2CLOUD',
        lingxia: 'Lingxia',
        button: {
            run: 'Executar',
            create: '<PERSON>ria<PERSON>',
            add: 'Adicionar',
            save: '<PERSON>var',
            set: 'Editar configuração',
            sync: 'Sincronizar',
            delete: 'Excluir',
            edit: 'Editar',
            enable: 'Ativar',
            disable: 'Desativar',
            confirm: 'Confirmar',
            cancel: 'Cancelar',
            reset: 'Redefinir',
            restart: 'Reiniciar',
            conn: 'Conectar',
            disConn: 'Desconectar',
            clean: 'Limpar',
            login: 'Entrar',
            close: 'Fechar',
            off: 'Fechar',
            stop: 'Parar',
            start: 'Iniciar',
            view: 'Visualizar',
            watch: 'Monitorar',
            handle: 'Disparar',
            clone: 'Clonar',
            expand: 'Expandir',
            collapse: 'Recolher',
            log: 'Logs',
            back: 'Voltar',
            backup: 'Backup',
            recover: 'Restaurar',
            retry: 'Tentar novamente',
            upload: 'Fazer upload',
            download: 'Fazer download',
            init: 'Inicializar',
            verify: 'Verificar',
            saveAndEnable: 'Salvar e ativar',
            import: 'Importar',
            export: 'Exportar',
            power: 'Autorização',
            search: 'Pesquisar',
            refresh: 'Atualizar',
            get: 'Obter',
            upgrade: 'Atualizar versão',
            update: 'atualizar',
            ignore: 'Ignorar atualização',
            install: 'instalar',
            copy: 'Copiar',
            random: 'Aleatório',
            uninstall: 'Desinstalar',
            fullscreen: 'Entrar em tela cheia',
            quitFullscreen: 'Sair da tela cheia',
            showAll: 'Exibir tudo',
            hideSome: 'Ocultar alguns',
            agree: 'Concordo',
            notAgree: 'Não concordo',
            preview: 'Pré-visualizar',
            open: 'Abrir',
            notSave: 'Não salvar',
            createNewFolder: 'Criar nova pasta',
            createNewFile: 'Criar novo arquivo',
            helpDoc: 'Documento de ajuda',
            unbind: 'Desvincular',
            cover: 'Substituir',
            skip: 'Pular',
            fix: 'Corrigir',
            down: 'Parar',
            up: 'Iniciar',
            sure: 'Confirmar',
            show: 'Exibir',
            hide: 'Ocultar',
        },
        operate: {
            start: 'Iniciar',
            stop: 'Parar',
            restart: 'Reiniciar',
            reload: 'Recarregar',
            rebuild: 'Reconstruir',
            sync: 'Sincronizar',
            up: 'Iniciar',
            down: 'Parar',
            delete: 'Excluir',
        },
        search: {
            timeStart: 'Hora inicial',
            timeEnd: 'Hora final',
            timeRange: 'Até',
            dateStart: 'Data inicial',
            dateEnd: 'Data final',
        },
        table: {
            all: 'Todos',
            total: 'Total de {0}',
            name: 'Nome',
            type: 'Tipo',
            status: 'Status',
            records: 'Registros',
            group: 'Grupo',
            createdAt: 'Data de criação',
            publishedAt: 'Data de publicação',
            date: 'Data',
            updatedAt: 'Data de atualização',
            operate: 'Operações',
            message: 'Mensagem',
            description: 'Descrição',
            interval: 'Intervalo',
            user: 'Responsável',
            title: 'Título',
            port: 'Porta',
            forward: 'Encaminhar',
            protocol: 'Protocolo',
            tableSetting: 'Configuração da tabela',
            refreshRate: 'Taxa de atualização',
            selectColumn: 'Selecionar coluna',
            local: 'Local',
            serialNumber: 'Número de série',
            manageGroup: 'Gerenciar Grupos',
            backToList: 'Voltar à Lista',
            keepEdit: 'Continuar Editando',
        },
        loadingText: {
            Upgrading: 'Atualizando o sistema, por favor, aguarde...',
            Restarting: 'Reiniciando o sistema, por favor, aguarde...',
            Recovering: 'Restaurando a partir de um snapshot, por favor, aguarde...',
            Rollbacking: 'Revertendo para um snapshot, por favor, aguarde...',
        },
        msg: {
            noneData: 'Nenhum dado disponível',
            delete: 'Esta operação de exclusão não pode ser desfeita. Deseja continuar?',
            clean: 'Esta operação de limpeza não pode ser desfeita. Deseja continuar?',
            closeDrawerHelper: 'O sistema pode não salvar as alterações que você fez. Deseja continuar?',
            deleteSuccess: 'Excluído com sucesso',
            loginSuccess: 'Login realizado com sucesso',
            operationSuccess: 'Operação concluída com sucesso',
            copySuccess: 'Copiado com sucesso',
            notSupportOperation: 'Esta operação não é suportada',
            requestTimeout: 'A solicitação expirou, tente novamente mais tarde',
            infoTitle: 'Aviso',
            notRecords: 'Nenhum registro de execução foi gerado para a tarefa atual',
            sureLogOut: 'Tem certeza de que deseja sair?',
            createSuccess: 'Criado com sucesso',
            updateSuccess: 'Atualizado com sucesso',
            uploadSuccess: 'Enviado com sucesso',
            operateConfirm: 'Se você tem certeza da operação, insira-a manualmente: ',
            inputOrSelect: 'Por favor, selecione ou insira',
            copyFailed: 'Falha ao copiar',
            operatorHelper: 'A operação "{1}" será realizada em "{0}" e não poderá ser desfeita. Deseja continuar?',
            notFound: 'Desculpe, a página solicitada não existe.',
            unSupportType: 'O tipo de arquivo atual não é suportado.',
            unSupportSize: 'O arquivo enviado excede {0}M, por favor confirme!',
            fileExist: 'O arquivo já existe na pasta atual. Não é possível enviar novamente.',
            fileNameErr:
                'Você pode enviar apenas arquivos cujo nome contenha de 1 a 256 caracteres, incluindo letras, números ou os caracteres (. - _)',
            confirmNoNull: 'Certifique-se de que o valor {0} não está vazio.',
            errPort: 'Informação de porta incorreta, por favor confirme!',
            remove: 'Remover',
            backupHelper: 'A operação atual fará o backup de {0}. Deseja continuar?',
            recoverHelper: 'Restaurando a partir do arquivo {0}. Esta operação é irreversível. Deseja continuar?',
            refreshSuccess: 'Atualizado com sucesso',
            rootInfoErr: 'Já está no diretório raiz',
            resetSuccess: 'Redefinido com sucesso',
            creatingInfo: 'Criando, não é necessário realizar esta operação',
        },
        login: {
            username: 'Usuário',
            password: 'Senha',
            welcome: 'Bem-vindo de volta, insira seu usuário e senha para acessar!',
            errorAuthInfo: 'O nome de usuário ou senha que você inseriu está incorreto, tente novamente!',
            errorMfaInfo: 'Informações de autenticação incorretas, tente novamente!',
            captchaHelper: 'Captcha',
            errorCaptcha: 'Erro no código Captcha!',
            notSafe: 'Acesso Negado',
            safeEntrance1: 'O login seguro foi habilitado no ambiente atual',
            safeEntrance2:
                'Digite o seguinte comando no terminal SSH para visualizar a entrada do painel: 1pctl user-info',
            errIP1: 'O acesso por endereço IP autorizado está habilitado no ambiente atual',
            errDomain1: 'O acesso por nome de domínio vinculado está habilitado no ambiente atual',
            errHelper: 'Para redefinir as informações de vinculação, execute o seguinte comando no terminal SSH:',
            codeInput: 'Por favor, insira o código de verificação de 6 dígitos do validador MFA',
            mfaTitle: 'Autenticação MFA',
            mfaCode: 'Código de verificação MFA',
            title: 'Painel de Gerenciamento de Servidores Linux',
            licenseHelper: '<Acordo de Licença Comunitária>',
            errorAgree: 'Clique para concordar com o Acordo de Licença de Software Comunitário',
            logout: 'Sair',
            agreeTitle: 'Termo de Aceite',
            agreeContent:
                'Para proteger melhor seus direitos e interesses legítimos, leia e concorde com o seguinte termo &laquo; <a href="https://www.fit2cloud.com/legal/licenses.html" target="_blank">Acordo de Licença Comunitária</a> &raquo;',
        },
        rule: {
            username: 'Insira um nome de usuário',
            password: 'Insira uma senha',
            rePassword: 'A confirmação da senha não corresponde à senha.',
            requiredInput: 'Este campo é obrigatório.',
            requiredSelect: 'Selecione um item na lista',
            illegalChar: 'Atualmente não há suporte para injeção dos caracteres & ; $ \' ` ( ) " > < |',
            illegalInput: 'Este campo não deve conter caracteres ilegais.',
            commonName:
                'Este campo deve começar com caracteres não especiais e consistir em letras, números, ".", "-", e "_" com comprimento de 1-128.',
            userName: 'Suporta não começar com caracteres especiais, inglês, chinês, números e _, comprimento 3-30',
            simpleName:
                'Este campo não deve começar com "_" e deve conter letras, números e "_" com comprimento de 3-30.',
            simplePassword:
                'Este campo não deve começar com "_" e deve conter letras, números e "_" com comprimento de 1-30.',
            dbName: 'Este campo não deve começar com "_" e deve conter letras, números e "_" com comprimento de 1-64.',
            imageName: 'Suporta não começar com caracteres especiais, inglês, números, :@/.-_, comprimento 1-256',
            composeName:
                'Deve começar com caracteres não especiais, conter letras minúsculas, números, "-" e "_" com comprimento de 1-256.',
            volumeName: 'Este campo deve conter letras, números, ".", "-", e "_" com comprimento de 2-30.',
            supervisorName:
                'Este campo deve começar com caracteres não especiais e conter letras, números, "-" e "_" com comprimento de 1-128.',
            complexityPassword:
                'Este campo deve conter letras, números com comprimento de 8-30 e pelo menos dois caracteres especiais.',
            commonPassword: 'O comprimento deste campo deve ser maior que 6.',
            linuxName:
                'O comprimento deste campo deve estar entre 1 e 128. Não pode conter os seguintes caracteres especiais: "{0}".',
            email: 'Este campo deve ser um endereço de email válido.',
            number: 'Este campo deve ser um número.',
            integer: 'Este campo deve ser um número inteiro positivo.',
            ip: 'Este campo deve ser um endereço IP válido.',
            host: 'Este campo deve ser um endereço IP ou nome de domínio válido.',
            hostHelper: 'Suporta a entrada de endereço IP ou nome de domínio',
            port: 'Este campo deve ser um número de porta válido.',
            selectHelper: 'Selecione o arquivo {0} correto',
            domain: 'Este campo deve estar no formato: exemplo.com ou exemplo.com:8080.',
            databaseName: 'Este campo deve conter letras, números e "_" com comprimento de 1-30.',
            ipErr: 'Este campo deve ser um endereço IP válido.',
            numberRange: 'Este campo deve ser um número entre {0} e {1}.',
            paramName: 'Este campo deve conter letras, números, ".", "-", e "_" com comprimento de 2-30.',
            paramComplexity:
                'Este campo não deve começar ou terminar com caracteres especiais e deve conter letras, números e "{0}" com comprimento de 6-128.',
            paramUrlAndPort: 'Este campo deve estar no formato "http(s)://(nome do domínio/IP):(porta)".',
            nginxDoc: 'Este campo deve conter letras, números e ".".',
            appName:
                'Este campo não deve começar ou terminar com "-" ou "_" e deve conter letras, números, "-", e "_" com comprimento de 2-30.',
            containerName:
                'Suporta letras, números, "-", "_" e "."; não pode começar com "-", "_" ou "."; comprimento: 2-128.',
            mirror: 'O endereço de aceleração do mirror deve começar com http(s)://, suportar letras (maiúsculas e minúsculas), números, ".", "/" e "-", e não deve conter linhas em branco.',
            disableFunction: 'Suporta apenas letras, underscores e,',
            leechExts: 'Suporta apenas letras, números e,',
            paramSimple: 'Suporta letras minúsculas e números, comprimento 1-128',
            filePermission: 'Erro de permissão de arquivo',
            formatErr: 'Erro de formato, verifique e tente novamente',
            phpExtension: 'Suporta apenas _, letras minúsculas e números',
            paramHttp: 'Deve começar com http:// ou https://',
            phone: 'O formato do número de telefone está incorreto',
            authBasicPassword: 'Suporta letras, números e caracteres especiais comuns, comprimento 1-72',
            length128Err: 'O comprimento não pode exceder 128 caracteres',
            maxLength: 'O comprimento não pode exceder {0} caracteres',
            alias: 'Suporta letras, números, - e _, comprimento de 1 a 30, e não pode começar ou terminar com -_.',
        },
        res: {
            paramError: 'A solicitação falhou, por favor, tente novamente mais tarde!',
            forbidden: 'O usuário atual não tem permissão',
            serverError: 'Exceção no serviço',
            notFound: 'O recurso não existe',
            commonError: 'A solicitação falhou',
        },
        service: {
            serviceNotStarted: `O serviço {0} não está iniciado.`,
        },
        status: {
            running: 'Em execução',
            done: 'Concluído',
            scanFailed: 'Incompleto',
            success: 'Sucesso',
            waiting: 'Aguardando',
            waiting1: 'Aguardando',
            failed: 'Falhou',
            stopped: 'Parado',
            error: 'Erro',
            created: 'Criado',
            restarting: 'Reiniciando',
            uploading: 'Enviando',
            unhealthy: 'Indisponível',
            removing: 'Removendo',
            paused: 'Pausado',
            exited: 'Finalizado',
            dead: 'Morto',
            installing: 'Instalando',
            enabled: 'Habilitado',
            disabled: 'Desabilitado',
            normal: 'Normal',
            building: 'Construindo',
            upgrading: 'Atualizando',
            pending: 'Aguardando Edição',
            rebuilding: 'Reconstruindo',
            deny: 'Negado',
            accept: 'Aceito',
            used: 'Usado',
            unUsed: 'Não usado',
            starting: 'Iniciando',
            recreating: 'Reconstruindo',
            creating: 'Criando',
            init: 'Aguardando aplicação',
            ready: 'Normal',
            applying: 'Aplicando',
            uninstalling: 'Desinstalando',
            lost: 'Perdido',
            bound: 'Vinculado',
            unbind: 'Desvinculado',
            exceptional: 'Excepcional',
            free: 'Livre',
            enable: 'Habilitado',
            disable: 'Desabilitado',
            deleted: 'Excluído',
            downloading: 'Baixando',
            packing: 'Empacotando',
            sending: 'Enviando',
            healthy: 'Saudável',
            executing: 'Executando',
            installerr: 'Falha na instalação',
            applyerror: 'Falha na aplicação',
            systemrestart: 'Interrompido',
            starterr: 'Falha na inicialização',
            uperr: 'Falha na inicialização',
        },
        units: {
            second: 'segundo | segundos | segundos',
            minute: 'minuto | minutos | minutos',
            hour: 'hora | horas | horas',
            day: 'dia | dias | dias',
            week: 'semana | semanas | semanas',
            month: 'mês | meses | meses',
            year: 'ano | anos | anos',
            time: 'rqm',
            core: 'núcleo | núcleos | núcleos',
            secondUnit: 's',
            minuteUnit: 'min',
            hourUnit: 'h',
            dayUnit: 'd',
            millisecond: 'Milissegundo',
        },
    },
    menu: {
        home: 'Visão Geral',
        apps: 'Loja de Aplicativos',
        website: 'Site | Sites',
        project: 'Projeto | Projetos',
        config: 'Configuração | Configurações',
        ssh: 'Configurações SSH',
        firewall: 'Firewall',
        ssl: 'Certificado | Certificados',
        database: 'Banco de Dados | Bancos de Dados',
        aiTools: 'AI',
        mcp: 'MCP',
        container: 'Container | Containers',
        cronjob: 'Tarefa Cron | Tarefas Cron',
        system: 'Sistema',
        security: 'Segurança',
        files: 'Arquivos',
        monitor: 'Monitoramento',
        terminal: 'Terminal',
        settings: 'Configuração | Configurações',
        toolbox: 'Caixa de Ferramentas',
        logs: 'Log | Logs',
        runtime: 'Tempo de Execução | Tempos de Execução',
        processManage: 'Processo | Processos',
        process: 'Processo | Processos',
        network: 'Rede | Redes',
        supervisor: 'Supervisor',
        tamper: 'À prova de violação',
        app: 'Aplicativo',
        msgCenter: 'Central de Tarefas',
    },
    home: {
        restart_1panel: 'Reiniciar painel',
        restart_system: 'Reiniciar servidor',
        operationSuccess: 'Operação bem-sucedida, reiniciando, por favor, atualize o navegador manualmente mais tarde!',
        entranceHelper:
            'A entrada de segurança não está ativada. Você pode ativá-la em "Configurações -> Segurança" para melhorar a segurança do sistema.',
        appInstalled: 'Aplicações',
        systemInfo: 'Informações do sistema',
        hostname: 'Nome do host',
        platformVersion: 'Sistema operacional',
        kernelVersion: 'Versão do Kernel',
        kernelArch: 'Arquitetura',
        network: 'Rede',
        io: 'Leitura/Gravação de Disco',
        ip: 'IP local',
        proxy: 'Proxy do sistema',
        baseInfo: 'Informações básicas',
        totalSend: 'Total enviado',
        totalRecv: 'Total recebido',
        rwPerSecond: 'Operações de I/O',
        ioDelay: 'Latência de I/O',
        uptime: 'Tempo de atividade',
        runningTime: 'Tempo de execução',
        mem: 'Memória',
        swapMem: 'Partição Swap',

        runSmoothly: 'Baixo carregamento',
        runNormal: 'Carregamento moderado',
        runSlowly: 'Carregamento alto',
        runJam: 'Carregamento pesado',

        core: 'Núcleo físico',
        logicCore: 'Núcleo lógico',
        loadAverage: 'Média de carga nos últimos 1 minuto | Média de carga nos últimos {n} minutos',
        load: 'Carga',
        mount: 'Ponto de montagem',
        fileSystem: 'Sistema de arquivos',
        total: 'Total',
        used: 'Usado',
        cache: 'Cache',
        free: 'Livre',
        shard: 'Fragmentado',
        available: 'Disponível',
        percent: 'Utilização',
        goInstall: 'Ir para instalação',

        networkCard: 'Placa de rede',
        disk: 'Disco',
    },
    tabs: {
        more: 'Mais',
        hide: 'Esconder',
        closeLeft: 'Fechar à esquerda',
        closeRight: 'Fechar à direita',
        closeCurrent: 'Fechar atual',
        closeOther: 'Fechar outros',
        closeAll: 'Fechar todos',
    },
    header: {
        logout: 'Logout',
    },
    database: {
        manage: 'Gerenciamento',
        deleteBackupHelper: 'Excluir backups do banco de dados simultaneamente',
        delete: 'A operação de exclusão não pode ser desfeita, insira "',
        deleteHelper: '" para excluir este banco de dados',
        create: 'Criar banco de dados',
        noMysql: 'Serviço de banco de dados (MySQL ou MariaDB)',
        noPostgresql: 'Serviço de banco de dados PostgreSQL',
        goUpgrade: 'Ir para atualização',
        goInstall: 'Ir para instalação',
        isDelete: 'Excluído',
        permission: 'Permissões',
        permissionForIP: 'IP',
        permissionAll: 'Todos (% de)',
        localhostHelper:
            'Configurar permissões de banco de dados como "localhost" para implantação em contêiner impedirá o acesso externo ao contêiner. Por favor, escolha com cuidado!',
        databaseConnInfo: 'Informações de conexão',
        rootPassword: 'Senha root',
        serviceName: 'Nome do serviço',
        serviceNameHelper: 'Acesso entre containers na mesma rede.',
        backupList: 'Backup',
        loadBackup: 'Importar',
        remoteAccess: 'Acesso remoto',
        remoteHelper: 'Vários IPs separados por vírgula, exemplo: *************, *************',
        remoteConnHelper:
            'Conectar-se ao MySQL como usuário root pode representar riscos de segurança. Realize esta operação com cautela.',
        changePassword: 'Senha',
        changeConnHelper: 'Esta operação modificará o banco de dados atual {0}. Deseja continuar?',
        changePasswordHelper:
            'O banco de dados está associado a um aplicativo. Alterar a senha alterará a senha do banco de dados do aplicativo ao mesmo tempo. A mudança surtirá efeito após a reinicialização do aplicativo.',

        confChange: 'Configuração',
        confNotFound:
            'Não foi possível encontrar o arquivo de configuração. Atualize o aplicativo para a versão mais recente na loja de aplicativos e tente novamente!',

        portHelper:
            'Esta porta é a porta exposta do container. Você precisa salvar a modificação separadamente e reiniciar o container!',

        loadFromRemote: 'Sincronizar',
        userBind: 'Vincular usuário',
        pgBindHelper: `Esta operação cria um novo usuário e o vincula ao banco de dados alvo. A seleção de usuários já existentes no banco de dados não é suportada.`,
        pgSuperUser: 'Superusuário',
        loadFromRemoteHelper:
            'Isso sincronizará as informações do banco de dados no servidor para o 1Panel. Deseja continuar?',
        passwordHelper: 'Não é possível obter, por favor, modifique',
        remote: 'Remoto',
        remoteDB: 'Servidor remoto | Servidores remotos',
        createRemoteDB: 'Vincular @.lower:database.remoteDB',
        unBindRemoteDB: 'Desvincular @.lower:database.remoteDB',
        unBindForce: 'Forçar desvinculação',
        unBindForceHelper:
            'Ignorar todos os erros durante o processo de desvinculação para garantir que a operação final seja bem-sucedida',
        unBindRemoteHelper:
            'Desvincular o banco de dados remoto removerá apenas a relação de vinculação e não excluirá diretamente o banco de dados remoto',
        editRemoteDB: 'Editar servidor remoto',
        localDB: 'Banco de dados local',
        address: 'Endereço do banco de dados',
        version: 'Versão do banco de dados',
        userHelper:
            'O usuário root ou um usuário do banco de dados com privilégios de root pode acessar o banco de dados remoto.',
        pgUserHelper: 'Usuário do banco de dados com privilégios de superusuário.',
        ssl: 'Usar SSL',
        clientKey: 'Chave privada do cliente',
        clientCert: 'Certificado do cliente',
        caCert: 'Certificado CA',
        hasCA: 'Possui certificado CA',
        skipVerify: 'Ignorar verificação de validade do certificado',

        formatHelper:
            'O conjunto de caracteres atual do banco de dados é {0}, a inconsistência no conjunto de caracteres pode causar falha na recuperação',
        selectFile: 'Selecionar arquivo',
        dropHelper: 'Você pode arrastar e soltar o arquivo carregado aqui ou',
        clickHelper: 'clicar para fazer upload',
        supportUpType: 'Apenas arquivos sql, sql.gz e tar.gz são suportados',
        zipFormat: 'Estrutura do pacote comprimido tar.gz: o pacote comprimido test.tar.gz deve conter test.sql',

        currentStatus: 'Estado atual',
        baseParam: 'Parâmetro básico',
        performanceParam: 'Parâmetro de desempenho',
        runTime: 'Tempo de inicialização',
        connections: 'Conexões totais',
        bytesSent: 'Bytes enviados',
        bytesReceived: 'Bytes recebidos',
        queryPerSecond: 'Consultas por segundo',
        txPerSecond: 'Tx por segundo',
        connInfo: 'conexões ativas/pico',
        connInfoHelper: 'Se o valor for muito grande, aumente "max_connections".',
        threadCacheHit: 'Acuracidade do cache de threads',
        threadCacheHitHelper: 'Se for muito baixo, aumente "thread_cache_size".',
        indexHit: 'Acuracidade de índice',
        indexHitHelper: 'Se for muito baixo, aumente "key_buffer_size".',
        innodbIndexHit: 'Taxa de acerto de índice InnoDB',
        innodbIndexHitHelper: 'Se for muito baixo, aumente "innodb_buffer_pool_size".',
        cacheHit: 'Acuracidade da consulta em cache',
        cacheHitHelper: 'Se for muito baixo, aumente "query_cache_size".',
        tmpTableToDB: 'Tabela temporária para disco',
        tmpTableToDBHelper: 'Se for muito grande, tente aumentar "tmp_table_size".',
        openTables: 'Tabelas abertas',
        openTablesHelper: 'O valor da configuração de "table_open_cache" deve ser maior ou igual a este valor.',
        selectFullJoin: 'Join completo de seleção',
        selectFullJoinHelper: `Se o valor não for 0, verifique se o índice da tabela de dados está correto.`,
        selectRangeCheck: 'Número de joins sem índice',
        selectRangeCheckHelper: `Se o valor não for 0, verifique se o índice da tabela de dados está correto.`,
        sortMergePasses: 'Número de mesclagens ordenadas',
        sortMergePassesHelper: 'Se o valor for muito grande, aumente "sort_buffer_size".',
        tableLocksWaited: 'Número de bloqueios de tabela',
        tableLocksWaitedHelper: 'Se o valor for muito grande, considere aumentar o desempenho do banco de dados.',

        performanceTuning: 'Ajuste de desempenho',
        optimizationScheme: 'Plano de otimização',
        keyBufferSizeHelper: 'Tamanho do buffer para índice',
        queryCacheSizeHelper: 'Cache de consulta. Se essa função estiver desativada, defina este parâmetro como 0.',
        tmpTableSizeHelper: 'Tamanho do cache de tabela temporária',
        innodbBufferPoolSizeHelper: 'Tamanho do buffer InnoDB',
        innodbLogBufferSizeHelper: 'Tamanho do buffer de log InnoDB',
        sortBufferSizeHelper: '* conexões, buffer de cada thread de ordenação',
        readBufferSizeHelper: '* conexões, tamanho do buffer de leitura',
        readRndBufferSizeHelper: '* conexões, tamanho do buffer de leitura aleatória',
        joinBufferSizeHelper: '* conexões, tamanho do cache da tabela de associação',
        threadStackelper: '* conexões, tamanho do stack por thread',
        binlogCacheSizeHelper: '* conexões, tamanho do cache de log binário (múltiplos de 4096)',
        threadCacheSizeHelper: 'Tamanho do pool de threads',
        tableOpenCacheHelper: 'Cache de tabelas',
        maxConnectionsHelper: 'Conexões máximas',
        restart: 'Reiniciar',

        slowLog: 'Logs lentos',
        noData: 'Ainda não há logs lentos.',

        isOn: 'Ligado',
        longQueryTime: 'limite (segundos)',
        thresholdRangeHelper: 'Por favor, insira o limite correto (1 - 600).',

        timeout: 'Tempo limite(segundos)',
        timeoutHelper: 'Período de timeout de conexão ociosa. 0 indica que a conexão permanece ativa continuamente.',
        maxclients: 'Máximo de clientes',
        requirepassHelper:
            'Deixe este campo em branco para indicar que nenhuma senha foi definida. Mudanças precisam ser salvas separadamente e o container reiniciado!',
        databases: 'Número de bancos de dados',
        maxmemory: 'Uso máximo de memória',
        maxmemoryHelper: '0 indica sem restrição.',
        tcpPort: 'Porta de escuta atual.',
        uptimeInDays: 'Dias de operação.',
        connectedClients: 'Número de clientes conectados.',
        usedMemory: 'Uso atual de memória do Redis.',
        usedMemoryRss: 'Tamanho da memória solicitado ao sistema operacional.',
        usedMemoryPeak: 'Pico de consumo de memória do Redis.',
        memFragmentationRatio: 'Taxa de fragmentação de memória.',
        totalConnectionsReceived: 'Total de clientes conectados desde a execução.',
        totalCommandsProcessed: 'Número total de comandos executados desde a execução.',
        instantaneousOpsPerSec: 'Número de comandos executados pelo servidor por segundo.',
        keyspaceHits: 'Número de vezes que uma chave de banco de dados foi encontrada.',
        keyspaceMisses: 'Número de tentativas falhas de encontrar a chave do banco de dados.',
        hit: 'Taxa de acerto de chave de banco de dados.',
        latestForkUsec: 'Número de microssegundos gastos na última operação fork()',
        redisCliHelper: `"redis-cli" não foi detectado. Habilite o serviço primeiro.`,
        redisQuickCmd: 'Comandos rápidos Redis',
        recoverHelper: 'Isso sobrescreverá os dados com [{0}]. Deseja continuar?',
        submitIt: 'Sobrescrever os dados',

        baseConf: 'Básico',
        allConf: 'Todos',
        restartNow: 'Reiniciar agora',
        restartNowHelper1:
            'Você precisa reiniciar o sistema após as mudanças na configuração entrarem em vigor. Se seus dados precisarem ser persistidos, execute primeiro a operação de salvar.',
        restartNowHelper: 'Isso só terá efeito após o sistema reiniciar.',

        persistence: 'Persistência',
        rdbHelper1: 'segundo(s), inserir',
        rdbHelper2: 'registros de dados',
        rdbHelper3: 'Atender a qualquer uma das condições acionará a persistência RDB.',
        rdbInfo: 'Certifique-se de que o valor na lista de regras esteja entre 1 e 100000',

        containerConn: 'Conexão do contêiner',
        connAddress: 'Endereço',
        containerConnHelper:
            'Este endereço de conexão pode ser utilizado por aplicações que estão em execução nos ambientes do site (PHP, etc.) ou no contêiner.',
        remoteConn: 'Conexão externa',
        remoteConnHelper2: 'Use este endereço para ambientes não-container ou conexões externas.',
        remoteConnHelper3:
            'O endereço de acesso padrão é o IP do host. Para modificá-lo, acesse o item de configuração "Endereço de Acesso Padrão" na página de configurações do painel.',
        localIP: 'IP local',
    },
    aiTools: {
        model: {
            model: 'Modelo',
            create: 'Adicionar Modelo',
            create_helper: 'Puxar "{0}"',
            ollama_doc: 'Você pode visitar o site oficial da Ollama para pesquisar e encontrar mais modelos.',
            container_conn_helper: 'Use este endereço para acesso ou conexão entre contêineres',
            ollama_sync:
                'Menyelaraskan model Ollama mendapati model berikut tidak wujud, adakah anda ingin memadamnya?',
            from_remote: 'Model ini tidak dimuat turun melalui 1Panel, tiada log pengambilan berkaitan.',
            no_logs: 'Log pengambilan untuk model ini telah dipadam dan tidak dapat dilihat.',
        },
        proxy: {
            proxy: 'Melhoria de Proxy AI',
            proxyHelper1: 'Vincule o domínio e habilite o HTTPS para aumentar a segurança na transmissão',
            proxyHelper2: 'Limite o acesso por IP para evitar exposição na internet pública',
            proxyHelper3: 'Habilite a transmissão em fluxo',
            proxyHelper4: 'Após a criação, você pode visualizar e gerenciar no lista de sites',
            proxyHelper6: 'Para desativar a configuração de proxy, você pode excluí-la da lista de sites.',
            whiteListHelper: 'Restringir o acesso apenas aos IPs na lista branca',
        },
        gpu: {
            gpu: 'Monitor de GPU',
            base: 'Informações Básicas',
            gpuHelper:
                'Comando NVIDIA-SMI ou XPU-SMI não detectado no sistema atual. Por favor, verifique e tente novamente!',
            driverVersion: 'Versão do Driver',
            cudaVersion: 'Versão do CUDA',
            process: 'Informações do Processo',
            type: 'Tipo',
            typeG: 'Gráficos',
            typeC: 'Cálculo',
            typeCG: 'Cálculo + Gráficos',
            processName: 'Nome do Processo',
            processMemoryUsage: 'Uso de Memória',
            temperatureHelper: 'Temperaturas altas da GPU podem causar limitação de frequência da GPU.',
            performanceStateHelper: 'De P0 (máximo desempenho) a P12 (mínimo desempenho).',
            busID: 'ID do Barramento',
            persistenceMode: 'Modo de Persistência',
            enabled: 'Ativado',
            disabled: 'Desativado',
            persistenceModeHelper:
                'O modo de persistência permite respostas mais rápidas às tarefas, mas aumenta o consumo de energia em standby.',
            displayActive: 'Placa Gráfica Inicializada',
            displayActiveT: 'Sim',
            displayActiveF: 'Não',
            ecc: 'Tecnologia de Correção e Verificação de Erros',
            computeMode: 'Modo de Cálculo',
            default: 'Padrão',
            exclusiveProcess: 'Processo Exclusivo',
            exclusiveThread: 'Thread Exclusivo',
            prohibited: 'Proibido',
            defaultHelper: 'Padrão: Processos podem ser executados simultaneamente.',
            exclusiveProcessHelper:
                'Processo Exclusivo: Apenas um contexto CUDA pode usar a GPU, mas pode ser compartilhado por múltiplas threads.',
            exclusiveThreadHelper: 'Thread Exclusivo: Apenas uma thread em um contexto CUDA pode usar a GPU.',
            prohibitedHelper: 'Proibido: Não é permitido que processos sejam executados simultaneamente.',
            migModeHelper: 'Usado para criar instâncias MIG para isolamento físico da GPU no nível do usuário.',
            migModeNA: 'Não Suportado',
        },
        mcp: {
            server: 'Servidor MCP',
            create: 'Adicionar Servidor',
            edit: 'Editar Servidor',
            commandHelper: 'Por exemplo: npx -y {0}',
            baseUrl: 'Caminho de Acesso Externo',
            baseUrlHelper: 'Por exemplo: http://***********:8000',
            ssePath: 'Caminho SSE',
            ssePathHelper: 'Por exemplo: /sse, tome cuidado para não duplicar com outros servidores',
            environment: 'Variáveis de Ambiente',
            envKey: 'Nome da Variável',
            envValue: 'Valor da Variável',
            externalUrl: 'Endereço de Conexão Externo',
            operatorHelper: 'Será realizada a operação {1} no {0}, continuar?',
            domain: 'Endereço de Acesso Padrão',
            domainHelper: 'Por exemplo: *********** ou example.com',
            bindDomain: 'Vincular Site',
            commandPlaceHolder: 'Atualmente, apenas comandos de inicialização npx e binários são suportados',
            importMcpJson: 'Importar Configuração do Servidor MCP',
            importMcpJsonError: 'A estrutura mcpServers está incorreta',
            bindDomainHelper:
                'Após vincular o site, ele modificará o endereço de acesso de todos os servidores MCP instalados e fechará o acesso externo às portas',
            outputTransport: 'Tipo de Saída',
            streamableHttpPath: 'Caminho de Streaming',
            streamableHttpPathHelper: 'Por exemplo: /mcp, certifique-se de que não se sobreponha a outros Servidores',
        },
    },
    container: {
        create: 'Criar contêiner',
        edit: 'Editar contêiner',
        updateHelper1: 'Detectamos que este contêiner vem da loja de aplicativos. Observe os seguintes dois pontos:',
        updateHelper2:
            '1. As modificações atuais não serão sincronizadas com os aplicativos instalados na loja de aplicativos.',
        updateHelper3:
            '2. Se você modificar o aplicativo na página de instalados, o conteúdo atualmente editado se tornará inválido.',
        updateHelper4:
            'Editar o contêiner requer reconstrução, e qualquer dado não persistente será perdido. Deseja continuar?',
        containerList: 'Lista de contêineres',
        operatorHelper: '{0} será realizado no seguinte contêiner. Deseja continuar?',
        operatorAppHelper:
            'A operação "{0}" será realizada no(s) seguinte(s) contêiner(es) e pode afetar os serviços em execução. Deseja continuar?',
        start: 'Iniciar',
        stop: 'Parar',
        restart: 'Reiniciar',
        kill: 'Finalizar',
        pause: 'Pausar',
        unpause: 'Retomar',
        rename: 'Renomear',
        remove: 'Remover',
        removeAll: 'Remover todos',
        containerPrune: 'Limpar',
        containerPruneHelper1: 'Isso excluirá todos os contêineres que estão no estado parado.',
        containerPruneHelper2:
            'Se os contêineres forem da loja de aplicativos, você precisará ir para "Loja de Aplicativos -> Instalados" e clicar no botão "Reconstruir" para reinstalá-los após a limpeza.',
        containerPruneHelper3: 'Esta operação não pode ser desfeita. Deseja continuar?',
        imagePrune: 'Limpar',
        imagePruneSome: 'Limpar sem rótulo',
        imagePruneSomeEmpty: 'Não há imagens com a tag "none" para limpar.',
        imagePruneSomeHelper: 'Limpar as imagens com a tag "none" que não são usadas por nenhum contêiner.',
        imagePruneAll: 'Limpar não utilizadas',
        imagePruneAllEmpty: 'Não há imagens não utilizadas para limpar.',
        imagePruneAllHelper: 'Limpar as imagens que não são usadas por nenhum contêiner.',
        networkPrune: 'Limpar',
        networkPruneHelper: 'Isso removerá todas as redes não utilizadas. Deseja continuar?',
        volumePrune: 'Limpar',
        volumePruneHelper: 'Isso removerá todos os volumes locais não utilizados. Deseja continuar?',
        cleanSuccess: 'A operação foi bem-sucedida, o número desta limpeza: {0}!',
        cleanSuccessWithSpace:
            'A operação foi bem-sucedida. O número de discos limpos desta vez é {0}. O espaço em disco liberado foi {1}!',
        unExposedPort: 'O endereço atual de mapeamento de porta é 127.0.0.1, o que não permite o acesso externo.',
        upTime: 'Tempo de atividade',
        fetch: 'Buscar',
        lines: 'Linhas',
        linesHelper: 'Por favor, insira o número correto de logs a serem recuperados!',
        lastDay: 'Último dia',
        last4Hour: 'Últimas 4 horas',
        lastHour: 'Última hora',
        last10Min: 'Últimos 10 minutos',
        cleanLog: 'Limpar log',
        downLogHelper1: 'Isso fará o download de todos os logs do contêiner {0}. Deseja continuar?',
        downLogHelper2: 'Isso fará o download dos últimos {0} logs do contêiner {0}. Deseja continuar?',
        cleanLogHelper: 'Isso exigirá a reinicialização do contêiner e não poderá ser desfeito. Deseja continuar?',
        newName: 'Novo nome',
        source: 'Uso de recursos',
        cpuUsage: 'Uso de CPU',
        cpuTotal: 'CPU total',
        core: 'Núcleo',
        memUsage: 'Uso de memória',
        memTotal: 'Limite de memória',
        memCache: 'Cache de memória',
        ip: 'Endereço IP',
        cpuShare: 'Atribuição de CPU',
        cpuShareHelper:
            'O mecanismo de contêiner usa um valor base de 1024 para a atribuição de CPU. Você pode aumentá-lo para dar mais tempo de CPU ao contêiner.',
        inputIpv4: 'Exemplo: ***********',
        inputIpv6: 'Exemplo: 2001:0db8:85a3:0000:0000:8a2e:0370:7334',

        containerFromAppHelper:
            'Detectamos que este contêiner vem da loja de aplicativos. As operações no aplicativo podem fazer com que as edições atuais sejam invalidadas.',
        containerFromAppHelper1:
            'Clique no botão [Param] na lista de aplicativos instalados para acessar a página de edição e modificar o nome do contêiner.',
        command: 'Comando',
        console: 'Interação com o contêiner',
        tty: 'Atribuir pseudo-TTY (-t)',
        openStdin: 'Manter STDIN aberto mesmo que não esteja anexado (-i)',
        custom: 'Personalizado',
        emptyUser: 'Se estiver vazio, você fará login com o padrão',
        privileged: 'Privilegiado',
        privilegedHelper:
            'Permite que o contêiner execute determinadas operações privilegiadas no host, o que pode aumentar os riscos do contêiner. Use com cautela!',
        editComposeHelper:
            'Nota: As variáveis de ambiente definidas serão gravadas no arquivo 1panel.env por padrão.\nSe você quiser usar esses parâmetros no contêiner, também precisará adicionar manualmente uma referência env_file no arquivo compose.',
        upgradeHelper: 'Nome do Repositório/Nome da Imagem: Versão da Imagem',
        upgradeWarning2:
            'A operação de upgrade requer a reconstrução do contêiner, e qualquer dado não persistente será perdido. Deseja continuar?',
        oldImage: 'Imagem atual',
        sameImageContainer: 'Contêineres com mesma imagem',
        sameImageHelper: 'Contêineres usando a mesma imagem podem ser atualizados em lote após seleção',
        targetImage: 'Imagem alvo',
        imageLoadErr: 'Nenhum nome de imagem detectado para o contêiner',
        appHelper: 'O contêiner vem da loja de aplicativos, e o upgrade pode tornar o serviço indisponível.',
        input: 'Entrada manual',
        forcePull: 'Sempre puxar imagem',
        forcePullHelper:
            'Isso ignorará as imagens existentes no servidor e puxará a imagem mais recente do repositório.',
        server: 'Host',
        serverExample: '80, 80-88, ip:80 ou ip:80-88',
        containerExample: '80 ou 80-88',
        exposePort: 'Expor porta',
        exposeAll: 'Expor todas',
        cmdHelper: 'Exemplo: nginx -g "daemon off;"',
        entrypointHelper: 'Exemplo: docker-entrypoint.sh',
        autoRemove: 'Remover automaticamente',
        cpuQuota: 'Número de núcleos de CPU',
        memoryLimit: 'Memória',
        limitHelper: 'Se definido como 0, significa que não há limitação. O valor máximo é {0}',
        mount: 'Montagem',
        volumeOption: 'Volume',
        hostOption: 'Host',
        serverPath: 'Caminho do servidor',
        containerDir: 'Caminho do contêiner',
        volumeHelper: 'Certifique-se de que o conteúdo do volume de armazenamento está correto',
        modeRW: 'RW',
        modeR: 'R',
        mode: 'Modo',
        env: 'Ambientes',
        restartPolicy: 'Política de reinício',
        always: 'sempre',
        unlessStopped: 'a menos que parado',
        onFailure: 'em falha (cinco vezes por padrão)',
        no: 'nunca',
        refreshTime: 'Intervalo de atualização',
        cache: 'Cache',
        image: 'Imagem | Imagens',
        imagePull: 'Puxar',
        imagePush: 'Enviar',
        imageDelete: 'Excluir imagem',
        imageTagDeleteHelper: 'Remover outras tags associadas a este ID de imagem',
        repoName: 'Registro de contêiner',
        imageName: 'Nome da imagem',
        pull: 'Puxar',
        path: 'Caminho',
        importImage: 'Importar',
        build: 'Construir',
        imageBuild: 'Construção de imagem',
        pathSelect: 'Caminho',
        label: 'Etiqueta',
        imageTag: 'Tag de imagem',
        push: 'Enviar',
        fileName: 'Nome do arquivo',
        export: 'Exportar',
        exportImage: 'Exportar imagem',
        size: 'Tamanho',
        tag: 'Tags',
        tagHelper: 'Uma por linha. Por exemplo,\nchave1=valor1\nchave2=valor2',
        imageNameHelper: 'Nome da imagem e tag, por exemplo: nginx:latest',
        cleanBuildCache: 'Limpar cache de construção',
        delBuildCacheHelper:
            'Isso excluirá todos os artefatos em cache gerados durante as construções e não poderá ser desfeito. Deseja continuar?',
        urlWarning: 'O prefixo da URL não precisa incluir http:// ou https://. Por favor, modifique.',

        network: 'Rede | Redes',
        networkHelper:
            'Isso pode fazer com que alguns aplicativos e ambientes de execução não funcionem corretamente. Deseja continuar?',
        createNetwork: 'Criar',
        networkName: 'Nome',
        driver: 'Driver',
        option: 'Opção',
        attachable: 'Anexável',
        subnet: 'Sub-rede',
        scope: 'Escopo IP',
        gateway: 'Gateway',
        auxAddress: 'Excluir IP',

        volume: 'Volume | Volumes',
        volumeDir: 'Diretório do volume',
        nfsEnable: 'Habilitar armazenamento NFS',
        nfsAddress: 'Endereço',
        mountpoint: 'Ponto de montagem',
        mountpointNFSHelper: 'Exemplo: /nfs, /nfs-share',
        options: 'Opções',
        createVolume: 'Criar',

        repo: 'Registries',
        createRepo: 'Adicionar',
        httpRepoHelper: 'Operar um repositório do tipo HTTP requer reinicialização do serviço Docker.',
        httpRepo:
            'Escolher o protocolo HTTP requer reiniciar o serviço Docker para adicioná-lo a registries inseguros.',
        delInsecure: 'Remover da lista de segurança',
        delInsecureHelper:
            'Isso reiniciará o serviço Docker para removê-lo dos registries inseguros. Deseja continuar?',
        downloadUrl: 'Servidor',
        imageRepo: 'Repositório de imagens',
        repoHelper: 'Inclui repositório espelho/organização/projeto?',
        auth: 'Exigir autenticação',
        mirrorHelper:
            'Se houver múltiplos espelhos, devem ser exibidos em novas linhas, por exemplo:\nhttp://xxxxxx.m.daocloud.io \nhttps://xxxxxx.mirror.aliyuncs.com',
        registrieHelper:
            'Se houver múltiplos repositórios privados, eles devem ser exibidos em novas linhas, por exemplo:\n*************:8081 \n*************:8081',

        compose: 'Compose | Composições',
        fromChangeHelper: 'Trocar a origem limpará o conteúdo editado atual. Deseja continuar?',
        composePathHelper: 'Caminho de salvamento do arquivo de configuração: {0}',
        composeHelper:
            'A composição criada através do editor ou template do 1Panel será salva no diretório {0}/docker/compose.',
        deleteFile: 'Excluir arquivo',
        deleteComposeHelper:
            'Excluir todos os arquivos relacionados à composição do container, incluindo arquivos de configuração e arquivos persistentes. Prossiga com cautela!',
        deleteCompose: 'Excluir esta composição.',
        createCompose: 'Criar',
        composeDirectory: 'Diretório',
        template: 'Template',
        composeTemplate: 'Template de composição | Templates de composição',
        createComposeTemplate: 'Criar',
        content: 'Conteúdo',
        contentEmpty: 'O conteúdo da composição não pode estar vazio, por favor, insira algo e tente novamente!',
        containerNumber: 'Número de containers',
        containerStatus: 'Status do container',
        exited: 'Finalizado',
        running: 'Em execução ( {0} / {1} )',
        composeDetailHelper:
            'A composição foi criada externamente ao 1Panel. As operações de iniciar e parar não são suportadas.',
        composeOperatorHelper: 'A operação {1} será realizada no {0}. Deseja continuar?',
        composeDownHelper:
            'Isso irá parar e remover todos os containers e redes sob a composição {0}. Deseja continuar?',

        setting: 'Configuração | Configurações',
        operatorStatusHelper: 'Isso irá "{0}" o serviço Docker. Deseja continuar?',
        dockerStatus: 'Serviço Docker',
        daemonJsonPathHelper:
            'Certifique-se de que o caminho de configuração seja o mesmo especificado no docker.service.',
        mirrors: 'Espelhos de registro',
        mirrorsHelper: '',
        mirrorsHelper2: 'Para mais detalhes, consulte a documentação oficial.',
        registries: 'Registries inseguros',
        ipv6Helper:
            'Ao habilitar o IPv6, é necessário adicionar uma rede de containers IPv6. Consulte a documentação oficial para etapas específicas de configuração.',
        ipv6CidrHelper: 'Faixa de endereços IPv6 para containers',
        ipv6TablesHelper: 'Configuração automática do Docker IPv6 para regras do iptables.',
        experimentalHelper:
            'Habilitar ip6tables requer que esta configuração esteja ativada; caso contrário, ip6tables será ignorado',
        cutLog: 'Opção de log',
        cutLogHelper1: 'A configuração atual afetará apenas containers recém-criados.',
        cutLogHelper2: 'Containers existentes precisam ser recriados para que a configuração tenha efeito.',
        cutLogHelper3:
            'Observe que recriar containers pode resultar em perda de dados. Se seus containers contiverem dados importantes, faça backup antes de realizar a operação de reconstrução.',
        maxSize: 'Tamanho máximo',
        maxFile: 'Arquivo máximo',
        liveHelper:
            'Por padrão, quando o daemon Docker termina, ele desliga os containers em execução. Você pode configurar o daemon para que os containers permaneçam em execução se o daemon ficar indisponível. Essa funcionalidade é chamada de "restauração ao vivo". A opção de restauração ao vivo ajuda a reduzir o tempo de inatividade dos containers devido a falhas do daemon, interrupções planejadas ou atualizações.',
        liveWithSwarmHelper: 'A configuração de restauração ao vivo é incompatível com o modo swarm.',
        iptablesDisable: 'Desabilitar iptables',
        iptablesHelper1: 'Configuração automática das regras do iptables para Docker.',
        iptablesHelper2:
            'Desabilitar iptables fará com que os containers não consigam se comunicar com redes externas.',
        daemonJsonPath: 'Caminho de configuração',
        serviceUnavailable: 'O serviço Docker não foi iniciado no momento.',
        startIn: 'para iniciar',
        sockPath: 'Caminho do socket Unix',
        sockPathHelper: 'Canal de comunicação entre o daemon Docker e o cliente.',
        sockPathHelper1: 'Caminho padrão: /var/run/docker-x.sock',
        sockPathMsg:
            'Salvar a configuração do Caminho do Socket pode tornar o serviço Docker indisponível. Deseja continuar?',
        sockPathErr: 'Por favor, selecione ou insira o caminho correto do arquivo do Docker sock',
        related: 'Relacionado',
        includeAppstore: 'Exibir containers da loja de aplicativos',
        excludeAppstore: 'Ocultar Contêiner da Loja de Aplicativos',

        cleanDockerDiskZone: 'Limpar o espaço em disco usado pelo Docker',
        cleanImagesHelper: '(Limpar todas as imagens que não são usadas por nenhum container)',
        cleanContainersHelper: '(Limpar todos os containers parados)',
        cleanVolumesHelper: '(Limpar todos os volumes locais não usados)',

        makeImage: 'Criar imagem',
        newImageName: 'Novo nome da imagem',
        commitMessage: 'Mensagem de commit',
        author: 'Autor',
        ifPause: 'Pausar container durante a criação',
        ifMakeImageWithContainer: 'Criar nova imagem a partir deste container?',
    },
    cronjob: {
        create: 'Criar tarefa cron',
        edit: 'Editar tarefa cron',
        errImport: 'Conteúdo do arquivo anormal:',
        errImportFormat:
            'Os dados ou formato da tarefa agendada estão anormais. Por favor, verifique e tente novamente!',
        importHelper:
            'Tarefas agendadas duplicadas serão automaticamente ignoradas durante a importação. As tarefas serão definidas como status 【Desativado】 por padrão, e como status 【Aguardando Edição】 quando a associação de dados for anormal.',
        changeStatus: 'Alterar status',
        disableMsg: 'Isso irá parar a execução automática da tarefa agendada. Você deseja continuar?',
        enableMsg: 'Isso permitirá que a tarefa agendada seja executada automaticamente. Você deseja continuar?',
        taskType: 'Tipo',
        record: 'Registros',
        viewRecords: 'Visualizar registros',
        shell: 'Shell',
        log: 'Logs de backup',
        logHelper: 'Backup do log do sistema',
        ogHelper1: '1. Log do sistema 1Panel',
        logHelper2: '2. Log de login SSH do servidor',
        logHelper3: '3. Todos os logs do site',
        containerCheckBox: 'No container (não é necessário inserir o comando do container)',
        containerName: 'Nome do container',
        ntp: 'Sincronização de tempo',
        ntp_helper: 'Você pode configurar o servidor NTP na página de Configuração Rápida da Caixa de Ferramentas.',
        app: 'Backup de app',
        website: 'Backup de site',
        rulesHelper: 'Suporta múltiplas regras de exclusão, separadas por vírgulas inglesas , ex.: *.log,*.sql',
        lastRecordTime: 'Última execução',
        all: 'Todos',
        failedRecord: 'Registros de falha',
        successRecord: 'Registros de sucesso',
        database: 'Backup de banco de dados',
        missBackupAccount: 'A conta de backup não foi encontrada',
        syncDate: 'Data de sincronização',
        clean: 'Limpeza de cache',
        curl: 'URL de acesso',
        taskName: 'Nome',
        cronSpec: 'Ciclo de execução',
        cronSpeDoc:
            'Ciclos de execução personalizados suportam apenas o formato [minuto hora dia mês semana], por exemplo, 0 0 * * *. Consulte a documentação oficial para obter detalhes.',
        cronSpecHelper: 'Digite o período correto de execução',
        cleanHelper:
            'Esta operação registra todos os registros de execução de tarefas, arquivos de backup e logs. Você deseja continuar?',
        directory: 'Diretório de backup',
        sourceDir: 'Diretório de backup',
        snapshot: 'Snapshot do sistema',
        allOptionHelper:
            'O plano de tarefa atual é fazer backup de todos os [{0}]. O download direto não é suportado no momento. Você pode verificar a lista de backups no menu [{0}].',
        exclusionRules: 'Regras de exclusão',
        exclusionRulesHelper: 'As regras de exclusão se aplicam a todas as operações de compressão deste backup.',
        default_download_path: 'Link de download padrão',
        saveLocal: 'Manter backups locais (o mesmo número de cópias na nuvem)',
        url: 'Endereço URL',
        targetHelper: 'As contas de backup são mantidas nas configurações do painel.',
        withImageHelper:
            'Fazer backup das imagens da loja de aplicativos, mas isso aumentará o tamanho do arquivo de snapshot.',
        ignoreApp: 'Excluir aplicativos',
        withImage: 'Fazer backup de todas as imagens de aplicativos',
        retainCopies: 'Manter cópias',
        retryTimes: 'Tentativas de Repetição',
        timeout: 'Tempo Limite',
        ignoreErr: 'Ignorar erros',
        ignoreErrHelper: 'Ignorar erros durante o backup para garantir a execução de todas as tarefas de backup',
        retryTimesHelper: '0 significa não repetir após falha',
        retainCopiesHelper: 'Número de cópias a serem mantidas para registros de execução e logs',
        retainCopiesHelper1: 'Número de cópias a serem mantidas para arquivos de backup',
        retainCopiesUnit: ' cópias (Visualizar)',
        cronSpecRule: 'O formato do período de execução na linha {0} está incorreto. Verifique e tente novamente!',
        perMonth: 'Todo mês',
        perWeek: 'Toda semana',
        perHour: 'Toda hora',
        perNDay: 'A cada N dia(s)',
        perDay: 'Todo dia',
        perNHour: 'A cada N hora(s)',
        perNMinute: 'A cada N minuto(s)',
        perNSecond: 'A cada N segundo(s)',
        per: 'A cada ',
        handle: '',
        day: 'dia(s)',
        dayUnit: 'd',
        monday: 'Segunda-feira',
        tuesday: 'Terça-feira',
        wednesday: 'Quarta-feira',
        thursday: 'Quinta-feira',
        friday: 'Sexta-feira',
        saturday: 'Sábado',
        sunday: 'Domingo',
        shellContent: 'Script',
        errRecord: 'Registro incorreto',
        errHandle: 'Falha na execução do Cronjob',
        noRecord: 'Acione a tarefa Cron e você verá os registros aqui.',
        cleanData: 'Limpar dados',
        cleanRemoteData: 'Excluir dados remotos',
        cleanDataHelper: 'Excluir o arquivo de backup gerado durante esta tarefa.',
        noLogs: 'Ainda não há saída de tarefa...',
        errPath: 'Caminho de backup [{0}] com erro, não é possível fazer o download!',
        cutWebsiteLog: 'Rotação de log do site',
        cutWebsiteLogHelper: 'Os arquivos de log rotacionados serão salvos no diretório de backup do 1Panel.',

        requestExpirationTime: 'Tempo de expiração da solicitação de upload (Horas)',
        unitHours: 'Unidade: Horas',
        alertTitle: 'Tarefa Planejada - {0} 「{1}」 Alerta de Falha na Tarefa',
        library: {
            script: 'Script',
            isInteractive: 'Interativo',
            interactive: 'Script interativo',
            interactiveHelper:
                'Requer entrada do usuário durante a execução e não pode ser usado em tarefas agendadas.',
            library: 'Biblioteca de Scripts',
            create: 'Adicionar Script',
            edit: 'Editar Script',
            groupHelper:
                'Defina grupos diferentes com base nas características do script, o que permite operações de filtragem de scripts mais rápidas.',
            handleHelper: 'Executar o script {1} em {0}, continuar?',
            noSuchApp:
                'O serviço {0} não foi detectado. Por favor, instale-o rapidamente usando a biblioteca de scripts primeiro!',
            syncHelper:
                'Preparando para sincronizar a biblioteca de scripts do sistema. Esta operação afeta apenas scripts do sistema. Continuar?',
        },
    },
    monitor: {
        globalFilter: 'Filtro global',
        enableMonitor: 'Ativar',
        storeDays: 'Dias de expiração',
        cleanMonitor: 'Limpar registros de monitoramento',

        avgLoad: 'Média de carga',
        loadDetail: 'Detalhes da carga',
        resourceUsage: 'Utilização',
        networkCard: 'Interface de rede',
        read: 'Leitura',
        write: 'Gravação',
        readWriteCount: 'Operações de I/O',
        readWriteTime: 'Latência de I/O',
        today: 'Hoje',
        yesterday: 'Ontem',
        lastNDay: 'Últimos {0} dia(s)',
        memory: 'Memória',
        cache: 'Cache',
        disk: 'Disco',
        network: 'Rede',
        up: 'Para cima',
        down: 'Para baixo',
        interval: 'Intervalo (minuto)',

        gpuUtil: 'Utilização da GPU',
        temperature: 'Temperatura',
        performanceState: 'Estado de desempenho',
        powerUsage: 'Uso de energia',
        memoryUsage: 'Uso de memória',
        fanSpeed: 'Velocidade do ventilador',
    },
    terminal: {
        local: 'Local',
        localHelper: 'O nome local é usado apenas para identificação local do sistema.',
        connLocalErr:
            'Невозможно автоматически аутентифицироваться, пожалуйста, заполните информацию для входа на локальный сервер.',
        testConn: 'Testar conexão',
        saveAndConn: 'Salvar e conectar',
        connTestOk: 'Informações de conexão disponíveis',
        connTestFailed: 'Conexão indisponível, por favor, verifique as informações de conexão.',
        host: 'Host | Hosts',
        createConn: 'Nova conexão',
        manageGroup: 'Gerenciar grupos',
        noHost: 'Nenhum host',
        groupChange: 'Alterar grupo',
        expand: 'Expandir todos',
        fold: 'Contrair tudo',
        batchInput: 'Processamento em lote',
        quickCommand: 'Comando rápido | Comandos rápidos',
        quickCommandHelper: 'Você pode usar os comandos rápidos na parte inferior de "Terminais -> Terminais".',
        groupDeleteHelper:
            'Após o grupo ser removido, todas as conexões no grupo serão migradas para o grupo padrão. Você deseja continuar?',
        command: 'Comando',
        quickCmd: 'Comando rápido',
        addHost: 'Adicionar',
        localhost: 'Localhost',
        ip: 'Endereço',
        authMode: 'Autenticação',
        passwordMode: 'Senha',
        rememberPassword: 'Lembrar informações de autenticação',
        keyMode: 'Chave privada',
        key: 'Chave privada',
        keyPassword: 'Senha da chave privada',
        emptyTerminal: 'Nenhum terminal está conectado no momento.',
    },
    toolbox: {
        common: {
            toolboxHelper: 'Para alguns problemas de instalação e uso, consulte',
        },
        swap: {
            swap: 'Partição Swap',
            swapHelper1:
                'O tamanho do swap deve ser de 1 a 2 vezes a memória física, ajustável conforme os requisitos específicos;',
            swapHelper2:
                'Antes de criar um arquivo swap, verifique se o disco do sistema tem espaço disponível suficiente, pois o tamanho do arquivo swap ocupará o espaço correspondente no disco;',
            swapHelper3:
                'Swap pode ajudar a aliviar a pressão de memória, mas é apenas uma alternativa. A dependência excessiva de swap pode levar a uma diminuição no desempenho do sistema. É recomendável priorizar o aumento de memória ou otimizar o uso de memória do aplicativo;',
            swapHelper4:
                'É aconselhável monitorar regularmente o uso de swap para garantir o funcionamento normal do sistema.',
            swapDeleteHelper:
                'Esta operação removerá a partição Swap {0}. Por motivos de segurança do sistema, o arquivo correspondente não será excluído automaticamente. Se a exclusão for necessária, por favor, faça manualmente!',
            saveHelper: 'Por favor, salve as configurações atuais primeiro!',
            saveSwap:
                'Salvar a configuração atual ajustará o tamanho da partição Swap {0} para {1}. Você deseja continuar?',
            swapMin: 'O tamanho mínimo da partição é 40 KB. Por favor, modifique e tente novamente!',
            swapMax: 'O valor máximo para o tamanho da partição é {0}. Por favor, modifique e tente novamente!',
            swapOff: 'O tamanho mínimo da partição é 40 KB. Definir como 0 desabilitará a partição Swap.',
        },
    },
    device: {
        dnsHelper: 'Servidor DNS',
        dnsAlert:
            'Atenção! Modificar a configuração do arquivo /etc/resolv.conf restaurará o arquivo para seus valores padrão após a reinicialização do sistema.',
        dnsHelper1:
            'Quando houver várias entradas DNS, elas devem ser exibidas em novas linhas. Exemplo:\n114.114.114.114\n8.8.8.8',
        hostsHelper: 'Resolução de hostname',
        hosts: 'Domínio',
        hostAlert:
            'Registros comentados ocultos, clique no botão "Todas as configurações" para visualizar ou configurar',
        toolbox: 'Configurações rápidas',
        hostname: 'Nome do host',
        passwd: 'Senha do sistema',
        passwdHelper: 'Os caracteres de entrada não podem incluir $ e &',
        timeZone: 'Fuso horário',
        localTime: 'Hora do servidor',
        timeZoneChangeHelper:
            'Modificar o fuso horário do sistema requer a reinicialização do serviço. Deseja continuar?',
        timeZoneHelper: `Se você não instalar o comando "timedatectl", poderá não conseguir alterar o fuso horário. O sistema usa esse comando para alterar o fuso horário.`,
        timeZoneCN: 'Pequim',
        timeZoneAM: 'Los Angeles',
        timeZoneNY: 'Nova York',
        ntpALi: 'Alibaba',
        ntpGoogle: 'Google',
        syncSite: 'Servidor NTP',
        hostnameHelper: `A modificação do nome do host depende do comando "hostnamectl". Se o comando não estiver instalado, a modificação pode falhar.`,
        userHelper: `O nome de usuário depende do comando "whoami" para recuperação. Se o comando não estiver instalado, a recuperação pode falhar.`,
        passwordHelper: `A modificação da senha depende do comando "chpasswd". Se o comando não estiver instalado, a modificação pode falhar.`,
        hostHelper: 'Há um valor vazio no conteúdo fornecido. Verifique e tente novamente após a modificação!',
        dnsCheck: 'Testar disponibilidade',
        dnsOK: 'As informações de configuração do DNS estão disponíveis!',
        dnsTestFailed: `As informações de configuração do DNS não estão disponíveis.`,
    },
    fail2ban: {
        sshPort: 'Porta de escuta do SSH',
        sshPortHelper: 'O Fail2ban atual escuta a porta de conexão SSH do host',
        unActive: `O serviço Fail2ban não está ativado no momento.`,
        operation: 'Você realizará a operação "{0}" no serviço Fail2ban. Deseja continuar?',
        fail2banChange: 'Modificação da configuração do Fail2ban',
        ignoreHelper: 'A lista de IPs na lista de permissão será ignorada para bloqueio. Deseja continuar?',
        bannedHelper: 'A lista de IPs na lista de bloqueio será bloqueada pelo servidor. Deseja continuar?',
        maxRetry: 'Máximo de tentativas de reclusão',
        banTime: 'Tempo de banimento',
        banTimeHelper: 'O tempo de banimento padrão é 10 minutos, -1 indica banimento permanente',
        banTimeRule: 'Por favor, insira um tempo de banimento válido ou -1',
        banAllTime: 'Banimento permanente',
        findTime: 'Período de descoberta',
        banAction: 'Ação de bloqueio',
        banActionOption: 'Bloquear endereços IP especificados usando {0}',
        allPorts: ' (Todas as portas)',
        ignoreIP: 'Lista de permissão de IP',
        bannedIP: 'Lista de bloqueio de IP',
        logPath: 'Caminho do log',
        logPathHelper: 'O padrão é /var/log/secure ou /var/log/auth.log',
    },
    ftp: {
        ftp: 'Conta FTP | Contas FTP',
        notStart: 'O serviço FTP não está em execução, por favor, inicie-o primeiro!',
        operation: 'Isso realizará a operação "{0}" no serviço FTP. Deseja continuar?',
        noPasswdMsg: 'Não foi possível obter a senha atual da conta FTP, por favor, defina a senha e tente novamente!',
        enableHelper: 'Ativar a conta FTP selecionada restaurará suas permissões de acesso. Deseja continuar?',
        disableHelper: 'Desativar a conta FTP selecionada revogará suas permissões de acesso. Deseja continuar?',
        syncHelper: 'Sincronizar os dados da conta FTP entre o servidor e o banco de dados. Deseja continuar?',
        dirSystem:
            'Este diretório é reservado do sistema. Modificações podem causar falhas no sistema. Por favor, modifique e tente novamente!',
        dirHelper: 'Habilitar FTP requer alterações nas permissões do diretório - por favor, escolha com cuidado',
        dirMsg: 'Habilitar FTP modificará as permissões de todo o diretório {0}. Continuar?',
    },
    clam: {
        clam: 'Scan de vírus',
        cron: 'Scan agendado',
        cronHelper: 'A versão profissional suporta a funcionalidade de scan agendado',
        specErr: 'Erro no formato do agendamento, por favor, verifique e tente novamente!',
        disableMsg:
            'Parar a execução agendada impedirá que esta tarefa de scan seja executada automaticamente. Deseja continuar?',
        enableMsg:
            'Ativar a execução agendada permitirá que esta tarefa de scan seja executada automaticamente em intervalos regulares. Deseja continuar?',
        showFresh: 'Mostrar serviço de atualização de assinaturas',
        hideFresh: 'Ocultar serviço de atualização de assinaturas',
        clamHelper:
            'A configuração mínima recomendada para o ClamAV é: 3 GiB de RAM ou mais, CPU de 1 núcleo com 2.0 GHz ou superior, e pelo menos 5 GiB de espaço livre no disco rígido.',
        notStart: 'O serviço ClamAV não está em execução, por favor, inicie-o primeiro!',
        removeRecord: 'Excluir arquivos de relatório',
        noRecords: 'Clique no botão "Acionar" para iniciar o scan e você verá registros aqui.',
        removeResultHelper:
            'Excluir arquivos de relatório gerados durante a execução da tarefa para liberar espaço de armazenamento.',
        removeInfected: 'Excluir arquivos infectados',
        removeInfectedHelper:
            'Excluir arquivos de vírus detectados durante a tarefa para garantir a segurança e o funcionamento normal do servidor.',
        clamCreate: 'Criar regra de scan',
        infectedStrategy: 'Estratégia de arquivos infectados',
        removeHelper: 'Excluir arquivos de vírus, escolha com cuidado!',
        move: 'Mover',
        moveHelper: 'Mover arquivos de vírus para o diretório especificado',
        copyHelper: 'Copiar arquivos de vírus para o diretório especificado',
        none: 'Não fazer nada',
        noneHelper: 'Não tomar nenhuma ação sobre arquivos de vírus',
        scanDir: 'Diretório de scan',
        infectedDir: 'Diretório de arquivos infectados',
        scanDate: 'Data do scan',
        scanResult: 'Últimos logs de scan',
        tail: 'Linhas',
        infectedFiles: 'Arquivos infectados',
        log: 'Detalhes',
        clamConf: 'Daemon Clam AV',
        clamLog: '@:toolbox.clam.clamConf logs',
        freshClam: 'FreshClam',
        freshClamLog: '@:toolbox.clam.freshClam logs',
        alertHelper: 'A versão profissional suporta scan agendado e alerta por SMS',
        alertTitle: 'Tarefa de scan de vírus 「{0}」 detectou alerta de arquivo infectado',
    },
    logs: {
        core: 'Serviço de Painel',
        agent: 'Monitoramento de Nós',
        panelLog: 'Logs do painel',
        operation: 'Logs de operação',
        login: 'Logs de login',
        loginIP: 'IP de login',
        loginAddress: 'Endereço de login',
        loginAgent: 'Agente de login',
        loginStatus: 'Status',
        system: 'Logs do sistema',
        deleteLogs: 'Limpar logs',
        resource: 'Recurso',
        detail: {
            ai: 'AI',
            groups: 'Grupos',
            hosts: 'Hosts',
            apps: 'Aplicativos',
            websites: 'Sites',
            containers: 'Contêineres',
            files: 'Gerenciamento de Arquivos',
            runtimes: 'Ambientes de Execução',
            process: 'Gerenciamento de Processos',
            toolbox: 'Caixa de Ferramentas',
            backups: 'Backup / Restauração',
            tampers: 'Proteção contra Alterações',
            xsetting: 'Configurações da Interface',
            logs: 'Auditoria de Logs',
            settings: 'Configurações do Painel',
            cronjobs: 'Tarefas Agendadas',
            waf: 'WAF',
            databases: 'Bancos de Dados',
            licenses: 'licenças',
            nodes: 'nós',
            commands: 'Comandos Rápidos',
        },
        websiteLog: 'Logs do website',
        runLog: 'Logs de execução',
        errLog: 'Logs de erro',
    },
    file: {
        fileDirNum: '{0} diretórios, {1} arquivos,',
        currentDir: 'Diretório atual',
        dir: 'Pasta',
        upload: 'Carregar',
        uploadFile: '@:file.upload @.lower:file.file',
        uploadDirectory: '@:file.upload @.lower:file.dir',
        download: 'Baixar',
        fileName: 'Nome do arquivo',
        search: 'Pesquisar',
        mode: 'Permissões',
        editPermissions: '@:file.mode',
        owner: 'Proprietário',
        file: 'Arquivo',
        remoteFile: 'Baixar de remoto',
        share: 'Compartilhar',
        sync: 'Sincronização de dados',
        size: 'Tamanho',
        updateTime: 'Modificado',
        rename: 'Renomear',
        role: 'Permissões',
        info: 'Atributos',
        linkFile: 'Link simbólico',
        batchoperation: 'Operação em lote',
        shareList: 'Lista de compartilhamento',
        zip: 'Compactado',
        group: 'Grupo',
        path: 'Caminho',
        public: 'Outros',
        setRole: 'Configurar permissões',
        link: 'Link do arquivo',
        rRole: 'Leitura',
        wRole: 'Escrita',
        xRole: 'Executável',
        name: 'Nome',
        compress: 'Compactar',
        deCompress: 'Descompactar',
        compressType: 'Formato de compactação',
        compressDst: 'Caminho de compactação',
        replace: 'Substituir arquivos existentes',
        compressSuccess: 'Compactado com sucesso',
        deCompressSuccess: 'Descompactado com sucesso',
        deCompressDst: 'Caminho de descompactação',
        linkType: 'Tipo de link',
        softLink: 'Link simbólico',
        hardLink: 'Link físico',
        linkPath: 'Caminho do link',
        selectFile: 'Selecionar arquivo',
        downloadUrl: 'URL remota',
        downloadStart: 'Download iniciado',
        moveSuccess: 'Movido com sucesso',
        copySuccess: 'Copiado com sucesso',
        move: 'Mover',
        calculate: 'Calcular',
        canNotDeCompress: 'Não é possível descompactar este arquivo',
        uploadSuccess: 'Upload bem-sucedido',
        downloadProcess: 'Progresso do download',
        downloading: 'Baixando...',
        infoDetail: 'Propriedades do arquivo',
        root: 'Diretório raiz',
        list: 'Lista de arquivos',
        sub: 'Subpastas',
        downloadSuccess: 'Baixado com sucesso',
        theme: 'Tema',
        language: 'Idioma',
        eol: 'Fim de linha',
        copyDir: 'Copiar',
        paste: 'Colar',
        changeOwner: 'Modificar usuário e grupo de usuários',
        containSub: 'Aplicar mudança de permissões recursivamente',
        ownerHelper:
            'O usuário padrão do ambiente PHP: o grupo de usuários é 1000:1000, é normal que os usuários dentro e fora do container mostrem inconsistências',
        searchHelper: 'Suporte a curingas como *',
        uploadFailed: '[{0}] Falha no upload do arquivo',
        fileUploadStart: 'Carregando [{0}]....',
        currentSelect: 'Selecionado atualmente: ',
        unsupportedType: 'Tipo de arquivo não suportado',
        deleteHelper:
            'Tem certeza de que deseja excluir os seguintes arquivos? Por padrão, eles irão para a lixeira após a exclusão',
        fileHelper: `Nota:\n1. Os resultados da pesquisa não podem ser ordenados.\n2. Pastas não podem ser ordenadas por tamanho.`,
        forceDeleteHelper: 'Excluir permanentemente o arquivo (sem entrar na lixeira, excluí-lo diretamente)',
        recycleBin: 'Lixeira',
        sourcePath: 'Caminho original',
        deleteTime: 'Hora da exclusão',
        confirmReduce: 'Tem certeza de que deseja restaurar os seguintes arquivos?',
        reduceSuccess: 'Restaurado com sucesso',
        reduce: 'Restaurar',
        reduceHelper:
            'Se um arquivo ou diretório com o mesmo nome existir no caminho original, ele será substituído. Deseja continuar?',
        clearRecycleBin: 'Limpar',
        clearRecycleBinHelper: 'Você deseja limpar a lixeira?',
        favorite: 'Favoritos',
        removeFavorite: 'Remover dos favoritos?',
        addFavorite: 'Adicionar/Remover aos favoritos',
        clearList: 'Limpar lista',
        deleteRecycleHelper: 'Tem certeza de que deseja excluir permanentemente os seguintes arquivos?',
        typeErrOrEmpty: '[{0}] tipo de arquivo errado ou pasta vazia',
        dropHelper: 'Arraste os arquivos que deseja carregar aqui',
        fileRecycleBin: 'Habilitar lixeira',
        fileRecycleBinMsg: '{0} lixeira',
        wordWrap: 'Quebra automática de linha',
        deleteHelper2:
            'Tem certeza de que deseja excluir o arquivo selecionado? A operação de exclusão não pode ser desfeita',
        ignoreCertificate: 'Permitir conexões inseguras com o servidor',
        ignoreCertificateHelper:
            'Permitir conexões inseguras com o servidor pode levar a vazamento ou adulteração de dados. Use esta opção apenas quando confiar na fonte de download.',
        uploadOverLimit: 'O número de arquivos excede 1000! Por favor, compacte e envie novamente',
        clashDitNotSupport: 'Os nomes de arquivos são proibidos de conter .1panel_clash',
        clashDeleteAlert: 'A pasta "Lixeira" não pode ser excluída',
        clashOpenAlert: 'Clique no botão "Lixeira" para abrir o diretório da lixeira',
        right: 'Avançar',
        back: 'Voltar',
        top: 'Voltar ao topo',
        up: 'Voltar',
        openWithVscode: 'Abrir com VS Code',
        vscodeHelper:
            'Por favor, certifique-se de que o VS Code está instalado localmente e o plugin SSH Remote está configurado',
        saveContentAndClose: 'O arquivo foi modificado, deseja salvar e fechar?',
        saveAndOpenNewFile: 'O arquivo foi modificado, deseja salvar e abrir o novo arquivo?',
        noEdit: 'O arquivo não foi modificado, não é necessário fazer isso!',
        noNameFolder: 'Pasta sem nome',
        noNameFile: 'Arquivo sem nome',
        minimap: 'Mini mapa de código',
        fileCanNotRead: 'O arquivo não pode ser lido',
        panelInstallDir: 'O diretório de instalação do 1Panel não pode ser excluído',
        wgetTask: 'Tarefa de Download',
        existFileTitle: 'Aviso de arquivo com o mesmo nome',
        existFileHelper: 'O arquivo enviado contém um arquivo com o mesmo nome. Deseja substituí-lo?',
        existFileSize: 'Tamanho do arquivo (novo -> antigo)',
        existFileDirHelper: 'O arquivo/pasta selecionado tem um nome duplicado. Por favor, prossiga com cautela!\n',
        coverDirHelper: 'As pastas selecionadas para substituição serão copiadas para o caminho de destino!',
        noSuchFile: 'O arquivo ou diretório não foi encontrado. Por favor, verifique e tente novamente.',
        setting: 'configuração',
        showHide: 'Mostrar arquivos ocultos',
        noShowHide: 'Não mostrar arquivos ocultos',
        cancelUpload: 'Cancelar Upload',
        cancelUploadHelper: 'Deseja cancelar o upload, após o cancelamento, a lista de upload será limpa.',
    },
    ssh: {
        autoStart: 'Início automático',
        enable: 'Habilitar início automático',
        disable: 'Desabilitar início automático',
        sshAlert:
            'Os dados da lista são classificados com base na data de login. Alterar o fuso horário ou realizar outras operações pode causar desvios na data dos logs de login.',
        sshAlert2:
            'Você pode usar o "Fail2ban" na "Caixa de ferramentas" para bloquear endereços IP que tentam ataques de força bruta, o que aumentará a segurança do host.',
        sshOperate: 'A operação "{0}" no serviço SSH será realizada. Você deseja continuar?',
        sshChange: 'Configuração SSH',
        sshChangeHelper: 'Esta ação alterou "{0}" para "{1}". Você deseja continuar?',
        sshFileChangeHelper:
            'Modificar o arquivo de configuração pode afetar a disponibilidade do serviço. Tenha cautela ao realizar esta operação. Você deseja continuar?',
        port: 'Porta',
        portHelper: 'Especifique a porta na qual o serviço SSH escutará.',
        listenAddress: 'Endereço de escuta',
        allV4V6: '0.0.0.0:{0}(IPv4) e :::{0}(IPv6)',
        listenHelper:
            'Deixar os campos de IPv4 e IPv6 em branco fará com que o serviço escute em "0.0.0.0:{0}(IPv4)" e ":::{0}(IPv6)"',
        addressHelper: 'Especifique o endereço em que o serviço SSH irá escutar.',
        permitRootLogin: 'Permitir login de usuário root',
        rootSettingHelper: 'O método de login padrão para o usuário root é "Permitir login SSH".',
        rootHelper1: 'Permitir login SSH',
        rootHelper2: 'Desabilitar login SSH',
        rootHelper3: 'Somente login com chave é permitido',
        rootHelper4: 'Somente comandos pré-definidos podem ser executados. Nenhuma outra operação pode ser realizada.',
        passwordAuthentication: 'Autenticação por senha',
        pwdAuthHelper: 'Se deve ou não habilitar a autenticação por senha. Esse parâmetro está habilitado por padrão.',
        pubkeyAuthentication: 'Autenticação por chave',
        privateKey: 'Chave Privada',
        publicKey: 'Chave Pública',
        password: 'Senha',
        createMode: 'Método de Criação',
        generate: 'Gerar Automaticamente',
        unSyncPass: 'Senha da chave não pode ser sincronizada',
        syncHelper:
            'A operação de sincronização limpará chaves inválidas e sincronizará novos pares de chaves completos. Continuar?',
        input: 'Entrada Manual',
        import: 'Upload de Arquivo',
        pubkey: 'Informações da chave',
        pubKeyHelper: 'A informação da chave atual só tem efeito para o usuário {0}',
        encryptionMode: 'Modo de criptografia',
        passwordHelper: 'Pode conter de 6 a 10 dígitos e letras maiúsculas e minúsculas',
        reGenerate: 'Regenerar chave',
        keyAuthHelper: 'Se deve ou não habilitar a autenticação por chave.',
        useDNS: 'Usar DNS',
        dnsHelper:
            'Controla se a função de resolução DNS está habilitada no servidor SSH para verificar a identidade da conexão.',
        analysis: 'Informações estatísticas',
        denyHelper:
            "Realizando uma operação de 'negar' nos seguintes endereços. Após a configuração, o IP será proibido de acessar o servidor. Você deseja continuar?",
        acceptHelper:
            "Realizando uma operação de 'aceitar' nos seguintes endereços. Após a configuração, o IP recuperará o acesso normal. Você deseja continuar?",
        noAddrWarning: 'Nenhum endereço [{0}] foi selecionado atualmente. Por favor, verifique e tente novamente!',
        loginLogs: 'Logs de login',
        loginMode: 'Modo',
        authenticating: 'Chave',
        publickey: 'Chave',
        belong: 'Pertence',
        local: 'Local',
        session: 'Sessão | Sessões',
        loginTime: 'Hora do login',
        loginIP: 'IP de login',
        stopSSHWarn: 'Deseja desconectar esta conexão SSH?',
    },
    setting: {
        panel: 'Painel',
        user: 'Usuário do painel',
        userChange: 'Alterar usuário do painel',
        userChangeHelper: 'Alterar o usuário do painel irá desconectá-lo. Continuar?',
        passwd: 'Senha do painel',
        emailHelper: 'Para recuperação de senha',
        title: 'Alias do painel',
        panelPort: 'Porta do painel',
        titleHelper:
            'Suporta nomes com comprimento de 3 a 30 caracteres, incluindo letras, caracteres chineses, números, espaços e caracteres especiais comuns',
        portHelper:
            'O intervalo recomendado de portas é de 8888 a 65535. Nota: Se o servidor tiver um grupo de segurança, permita a nova porta do grupo de segurança antecipadamente',
        portChange: 'Alteração de porta',
        portChangeHelper: 'Modificar a porta do serviço e reiniciar o serviço. Deseja continuar?',
        theme: 'Tema',
        menuTabs: 'Guias do menu',
        dark: 'Escuro',
        darkGold: 'Ouro escuro',
        light: 'Claro',
        auto: 'Seguir o sistema',
        language: 'Idioma',
        languageHelper: 'Por padrão, segue o idioma do navegador. Este parâmetro tem efeito apenas no navegador atual',
        sessionTimeout: 'Tempo limite de sessão',
        sessionTimeoutError: 'O tempo mínimo de sessão é de 300 segundos',
        sessionTimeoutHelper: 'O painel será desconectado automaticamente após {0} segundo(s) de inatividade.',
        systemIP: 'Endereço de acesso padrão',
        systemIPHelper:
            'Redirecionamentos de aplicativos, acesso a containers e outras funcionalidades usarão este endereço para roteamento. Cada nó pode ser configurado com um endereço diferente.',
        proxy: 'Proxy do servidor',
        proxyHelper: 'Será eficaz nos seguintes cenários após configurar o servidor proxy:',
        proxyHelper1:
            'Download de pacotes de instalação e sincronização da loja de aplicativos (apenas edição profissional)',
        proxyHelper2: 'Atualização do sistema e recuperação de informações de atualização (apenas edição profissional)',
        proxyHelper4: 'A rede Docker será acessada por meio de um servidor proxy (apenas edição profissional)',
        proxyHelper3: 'Verificação e sincronização da licença do sistema',
        proxyHelper5:
            'Download e sincronização unificados para bibliotecas de scripts do tipo sistema (Recurso da Edição Profissional)',
        proxyHelper6: 'Solicitar certificado (Funcionalidade da versão Pro)',
        proxyType: 'Tipo de proxy',
        proxyUrl: 'Endereço do proxy',
        proxyPort: 'Porta do proxy',
        proxyPasswdKeep: 'Lembrar senha',
        proxyDocker: 'Proxy Docker',
        proxyDockerHelper:
            'Sincronize a configuração do servidor proxy com o Docker, suportando operações de puxar imagens de servidor offline e outras',
        syncToNode: 'Sincronizar para o nó filho',
        syncToNodeHelper: 'Sincronizar de alerta para outros nós',
        nodes: 'Nós',
        selectNode: 'Selecionar nó',
        selectNodeError: 'Por favor, selecione um nó',
        apiInterface: 'Habilitar API',
        apiInterfaceClose: 'Uma vez fechado, as interfaces da API não poderão ser acessadas. Deseja continuar?',
        apiInterfaceHelper: 'Permitir que aplicativos de terceiros acessem a API.',
        apiInterfaceAlert1:
            'Não habilite em ambientes de produção, pois pode aumentar os riscos de segurança do servidor.',
        apiInterfaceAlert2:
            'Não use aplicativos de terceiros para chamar a API, para evitar potenciais ameaças à segurança.',
        apiInterfaceAlert3: 'Documentação da API',
        apiInterfaceAlert4: 'Documentação de uso',
        apiKey: 'Chave API',
        apiKeyHelper: 'A chave da API é usada para aplicativos de terceiros acessarem a API.',
        ipWhiteList: 'Lista de IPs permitidos',
        ipWhiteListEgs: 'Um por linha. Exemplo: \n172.161.10.111\n172.161.10.0/24',
        ipWhiteListHelper:
            'IPs na lista de permitidos podem acessar a API, 0.0.0.0/0 (todos os IPv4), ::/0 (todos os IPv6)',
        apiKeyReset: 'Redefinir chave da interface',
        apiKeyResetHelper:
            'O serviço associado à chave se tornará inválido. Por favor, adicione uma nova chave ao serviço',
        confDockerProxy: 'Configurar proxy do Docker',
        restartNowHelper: 'Configurar o proxy do Docker exige reiniciar o serviço Docker.',
        restartNow: 'Reiniciar imediatamente',
        restartLater: 'Reiniciar manualmente mais tarde',
        systemIPWarning:
            'O nó atual não tem um endereço de acesso padrão configurado. Por favor, vá para as configurações do painel para configurá-lo!',
        systemIPWarning1:
            'O endereço atual do servidor está configurado como {0}, e o redirecionamento rápido não é possível!',
        defaultNetwork: 'Placa de rede',
        syncTime: 'Hora do servidor',
        timeZone: 'Fuso horário',
        timeZoneChangeHelper: 'Alterar o fuso horário exige reiniciar o serviço. Deseja continuar?',
        timeZoneHelper:
            'A modificação de fuso horário depende do serviço timedatectl do sistema. Entrará em vigor após reiniciar o serviço 1Panel.',
        timeZoneCN: 'Pequim',
        timeZoneAM: 'Los Angeles',
        timeZoneNY: 'Nova York',
        ntpALi: 'Alibaba',
        ntpGoogle: 'Google',
        syncSite: 'Servidor NTP',
        syncSiteHelper:
            'Esta operação usará {0} como fonte para a sincronização do horário do sistema. Deseja continuar?',
        changePassword: 'Alterar senha',
        oldPassword: 'Senha original',
        newPassword: 'Nova senha',
        retryPassword: 'Confirmar senha',
        noSpace: 'As informações inseridas não podem conter caracteres de espaço',
        duplicatePassword: 'A nova senha não pode ser igual à senha original, por favor, insira novamente!',
        diskClean: 'Limpeza de cache',
        developerMode: 'Modo de desenvolvimento',
        developerModeHelper:
            'Você terá a oportunidade de testar novos recursos e correções antes de serem amplamente lançados e fornecer feedback precoce.',
        thirdParty: 'Contas de terceiros',
        noTypeForCreate: 'Nenhum tipo de backup foi criado até o momento',
        LOCAL: 'Disco do servidor',
        OSS: 'Ali OSS',
        S3: 'Amazon S3',
        mode: 'Modo',
        MINIO: 'MinIO',
        SFTP: 'SFTP',
        WebDAV: 'WebDAV',
        WebDAVAlist: 'Conectar WebDAV Alist pode referir-se à documentação oficial',
        OneDrive: 'Microsoft OneDrive',
        isCN: 'Internet da China',
        isNotCN: 'Versão internacional',
        client_id: 'ID do cliente',
        client_secret: 'Segredo do cliente',
        redirect_uri: 'URL de redirecionamento',
        onedrive_helper: 'A configuração personalizada pode ser referida na documentação oficial',
        refreshTime: 'Tempo de atualização do token',
        refreshStatus: 'Status da atualização do token',
        backupDir: 'Diretório de backup',
        codeWarning: 'O formato atual do código de autorização está incorreto, por favor, verifique novamente!',
        code: 'Código de autorização',
        codeHelper:
            'Clique no botão "Adquirir", faça login no OneDrive e copie o conteúdo após "code" no link redirecionado. Cole-o neste campo. Para instruções específicas, consulte a documentação oficial.',
        loadCode: 'Obter',
        COS: 'Tencent COS',
        ap_beijing_1: 'Beijing Zona 1',
        ap_beijing: 'Pequim',
        ap_nanjing: 'Nanjing',
        ap_shanghai: 'Xangai',
        ap_guangzhou: 'Cantão',
        ap_chengdu: 'Chengdu',
        ap_chongqing: 'Chongqing',
        ap_shenzhen_fsi: 'Shenzhen Financeiro',
        ap_shanghai_fsi: 'Xangai Financeiro',
        ap_beijing_fsi: 'Pequim Financeiro',
        ap_hongkong: 'Hong Kong, China',
        ap_singapore: 'Cingapura',
        ap_mumbai: 'Bombaim',
        ap_jakarta: 'Jacarta',
        ap_seoul: 'Seul',
        ap_bangkok: 'Bangkok',
        ap_tokyo: 'Tóquio',
        na_siliconvalley: 'Silicon Valley (EUA Oeste)',
        na_ashburn: 'Ashburn (EUA Leste)',
        na_toronto: 'Toronto',
        sa_saopaulo: 'São Paulo',
        eu_frankfurt: 'Frankfurt',
        KODO: 'Qiniu Kodo',
        scType: 'Tipo de armazenamento',
        typeStandard: 'Padrão',
        typeStandard_IA: 'Padrão_IA',
        typeArchive: 'Arquivo',
        typeDeep_Archive: 'Arquivo Profundo',
        scLighthouse: 'Padrão, O armazenamento de objetos leve só suporta este tipo de armazenamento',
        scStandard:
            'O armazenamento padrão é adequado para cenários de negócios com grande volume de arquivos quentes que exigem acesso em tempo real, interação frequente de dados, etc.',
        scStandard_IA:
            'O armazenamento de baixa frequência é adequado para cenários de negócios com frequência de acesso relativamente baixa, e armazena dados por pelo menos 30 dias.',
        scArchive:
            'Armazenamento para arquivos é adequado para cenários de negócios com frequência de acesso extremamente baixa.',
        scDeep_Archive:
            'Armazenamento frio durável é adequado para cenários de negócios com frequência de acesso extremamente baixa.',
        archiveHelper:
            'Arquivos de armazenamento arquivado não podem ser baixados diretamente e devem ser restaurados primeiro através do site do provedor de serviços de nuvem correspondente. Use com cautela!',
        backupAlert:
            'Se um provedor de nuvem for compatível com o protocolo S3, você pode usar diretamente o Amazon S3 para backup.',
        domain: 'Domínio de aceleração',
        backupAccount: 'Conta de backup | Contas de backup',
        loadBucket: 'Obter bucket',
        accountName: 'Nome da conta',
        accountKey: 'Chave da conta',
        address: 'Endereço',
        path: 'Caminho',

        safe: 'Segurança',
        bindInfo: 'Informações de Vinculação',
        bindAll: 'Ouvir Todos',
        bindInfoHelper:
            'Alterar o endereço de escuta ou protocolo do serviço pode resultar em indisponibilidade do serviço. Você deseja continuar?',
        ipv6: 'Ouvir IPv6',
        bindAddress: 'Endereço de escuta',
        entrance: 'Entrada',
        showEntrance: 'Mostrar alerta desativado na página "Visão Geral"',
        entranceHelper:
            'Habilitar a entrada de segurança permitirá o login no painel apenas através do ponto de entrada de segurança especificado.',
        entranceError:
            'Por favor, insira um ponto de entrada seguro de 5-116 caracteres, somente números ou letras são suportados.',
        entranceInputHelper: 'Deixe em branco para desativar a entrada de segurança.',
        randomGenerate: 'Aleatório',
        expirationTime: 'Data de Expiração',
        unSetting: 'Não definido',
        noneSetting:
            'Defina o tempo de expiração da senha do painel. Após a expiração, será necessário redefinir a senha.',
        expirationHelper:
            'Se o tempo de expiração da senha for [0] dias, a função de expiração da senha estará desativada.',
        days: 'Dias de Expiração',
        expiredHelper: 'A senha atual expirou. Por favor, altere a senha novamente.',
        timeoutHelper:
            '[ {0} dias ] A senha do painel está prestes a expirar. Após a expiração, será necessário redefinir a senha.',
        complexity: 'Validação de Complexidade',
        complexityHelper:
            'Após ativar, a regra de validação de senha será: 8-30 caracteres, incluindo letras, números e pelo menos dois caracteres especiais.',
        bindDomain: 'Vincular domínio',
        unBindDomain: 'Desvincular domínio',
        panelSSL: 'SSL do Painel',
        unBindDomainHelper:
            'A ação de desvincular um domínio pode causar insegurança no sistema. Você deseja continuar?',
        bindDomainHelper: 'Após vincular o domínio, somente esse domínio poderá acessar o serviço 1Panel.',
        bindDomainHelper1: 'Deixe em branco para desabilitar o vínculo de domínio.',
        bindDomainWarning:
            'Após vincular o domínio, você será desconectado e poderá acessar o serviço 1Panel apenas através do domínio especificado nas configurações. Você deseja continuar?',
        allowIPs: 'IP Autorizado',
        unAllowIPs: 'IP Não Autorizado',
        unAllowIPsWarning:
            'Autorizar um IP vazio permitirá que todos os IPs acessem o sistema, o que pode causar insegurança. Você deseja continuar?',
        allowIPsHelper:
            'Após definir a lista de IPs autorizados, apenas os IPs da lista poderão acessar o serviço do painel.',
        allowIPsWarning:
            'Após definir a lista de IPs autorizados, somente os IPs da lista poderão acessar o serviço do painel. Você deseja continuar?',
        allowIPsHelper1: 'Deixe em branco para desabilitar a restrição de IP.',
        allowIPEgs: 'Um por linha. Por exemplo,\n*************\n***********/24',
        mfa: 'Autenticação de dois fatores (2FA)',
        mfaClose: 'Desabilitar MFA reduzirá a segurança do serviço. Você deseja continuar?',
        secret: 'Segredo',
        mfaInterval: 'Intervalo de atualização (s)',
        mfaTitleHelper:
            'O título é usado para distinguir diferentes hosts do 1Panel. Escaneie novamente ou adicione manualmente a chave secreta após modificar o título.',
        mfaIntervalHelper:
            'Escaneie novamente ou adicione manualmente a chave secreta após modificar o intervalo de atualização.',
        mfaAlert:
            'O token de uso único é um número dinâmico de 6 dígitos e baseado no tempo atual. Certifique-se de que o horário do servidor esteja sincronizado.',
        mfaHelper: 'Após ativá-lo, o token de uso único precisará ser verificado.',
        mfaHelper1: 'Baixe um aplicativo autenticador, por exemplo,',
        mfaHelper2:
            'Para obter o token de uso único, escaneie o código QR abaixo usando seu aplicativo autenticador ou copie a chave secreta para o aplicativo de autenticação.',
        mfaHelper3: 'Digite os seis dígitos do aplicativo',
        mfaCode: 'Token de uso único',
        sslChangeHelper: 'Modificar a configuração de https e reiniciar o serviço. Você deseja continuar?',
        sslDisable: 'Desabilitar',
        sslDisableHelper:
            'Se o serviço https for desabilitado, será necessário reiniciar o painel para que a alteração tenha efeito. Você deseja continuar?',
        noAuthSetting: 'Configuração não autorizada',
        noAuthSettingHelper:
            'Quando os usuários não fizerem login com a entrada de segurança especificada, ou não acessarem o painel a partir de IP ou domínio especificado, essa resposta pode ocultar características do painel.',
        responseSetting: 'Configuração de resposta',
        help200: 'Página de ajuda',
        error400: 'Requisição inválida',
        error401: 'Não autorizado',
        error403: 'Proibido',
        error404: 'Não encontrado',
        error408: 'Tempo de solicitação expirado',
        error416: 'Faixa não satisfatória',
        error444: 'Conexão fechada',
        error500: 'Erro no servidor',

        https: 'Configurar o acesso via protocolo HTTPS para o painel pode melhorar a segurança do acesso ao painel.',
        certType: 'Tipo de certificado',
        selfSigned: 'Autoassinado',
        selfSignedHelper:
            'Os navegadores podem não confiar em certificados autoassinados e podem exibir avisos de segurança.',
        select: 'Selecionar',
        domainOrIP: 'Domínio ou IP:',
        timeOut: 'Tempo limite',
        rootCrtDownload: 'Download do certificado raiz',
        primaryKey: 'Chave primária',
        certificate: 'Certificado',
        backupJump:
            'Arquivos de backup não estão na lista de backup atual, tente fazer o download do diretório de arquivos e importar para o backup.',

        snapshot: 'Snapshot | Snapshots',
        noAppData: 'Nenhum aplicativo do sistema disponível para seleção',
        noBackupData: 'Nenhum dado de backup disponível para seleção',
        stepBaseData: 'Dados Básicos',
        stepAppData: 'Aplicativos do Sistema',
        stepPanelData: 'Dados do Sistema',
        stepBackupData: 'Dados de Backup',
        stepOtherData: 'Outros Dados',
        operationLog: 'Manter logs de operações',
        loginLog: 'Manter logs de acesso',
        systemLog: 'Manter logs do sistema',
        taskLog: 'Manter logs de tarefas',
        monitorData: 'Manter dados de monitoramento',
        dockerConf: 'Manter Configuração do Docker',
        selectAllImage: 'Fazer backup de todas as imagens de aplicativos',
        logLabel: 'Log',
        agentLabel: 'Configuração do Nó',
        appDataLabel: 'Dados de Aplicativos',
        appImage: 'Imagem do Aplicativo',
        appBackup: 'Backup de Aplicativo',
        backupLabel: 'Diretório de Backup',
        confLabel: 'Arquivos de Configuração',
        dockerLabel: 'Contêineres',
        taskLabel: 'Tarefas Agendadas',
        resourceLabel: 'Diretório de Recursos do Aplicativo',
        runtimeLabel: 'Ambiente de Execução',
        appLabel: 'Aplicativo',
        databaseLabel: 'Banco de Dados',
        snapshotLabel: 'Arquivos de Snapshot',
        websiteLabel: 'Site',
        directoryLabel: 'Diretório',
        appStoreLabel: 'Loja de Aplicativos',
        shellLabel: 'Script',
        tmpLabel: 'Diretório Temporário',
        sslLabel: 'Diretório de Certificados',
        reCreate: 'Falha ao criar snapshot',
        reRollback: 'Falha ao reverter snapshot',
        deleteHelper:
            'Todos os arquivos de snapshot, incluindo os da conta de backup de terceiros, serão excluídos. Você deseja continuar?',
        status: 'Status do snapshot',
        ignoreRule: 'Ignorar regra',
        editIgnoreRule: '@:commons.button.edit @.lower:setting.ignoreRule',
        ignoreHelper:
            'Esta regra será usada para comprimir e fazer backup do diretório de dados do 1Panel durante a criação do snapshot. Por padrão, arquivos de socket são ignorados.',
        ignoreHelper1: 'Um por linha. Exemplo,\n*.log\n/opt/1panel/cache',
        panelInfo: 'Escrever informações básicas do 1Panel',
        panelBin: 'Fazer backup dos arquivos do sistema 1Panel',
        daemonJson: 'Fazer backup do arquivo de configuração do Docker',
        appData: 'Fazer backup dos aplicativos instalados do 1Panel',
        panelData: 'Fazer backup do diretório de dados do 1Panel',
        backupData: 'Fazer backup do diretório de backup local do 1Panel',
        compress: 'Criar arquivo de snapshot',
        upload: 'Fazer upload do arquivo de snapshot',
        recoverDetail: 'Detalhes da recuperação',
        createSnapshot: 'Criar snapshot',
        importSnapshot: 'Sincronizar snapshot',
        importHelper: 'Diretório do snapshot: ',
        lastRecoverAt: 'Última recuperação realizada',
        lastRollbackAt: 'Último rollback realizado',
        reDownload: 'Baixar o arquivo de backup novamente',
        recoverErrArch: 'A recuperação de snapshot entre diferentes arquiteturas de servidor não é suportada!',
        recoverErrSize:
            'Espaço em disco insuficiente detectado, por favor, verifique ou libere espaço e tente novamente!',
        recoverHelper:
            'Iniciando a recuperação do snapshot {0}, por favor, confirme as seguintes informações antes de prosseguir:',
        recoverHelper1: 'A recuperação requer reiniciar os serviços Docker e 1Panel',
        recoverHelper2:
            'Por favor, assegure-se de que há espaço suficiente em disco no servidor (Tamanho do arquivo de snapshot: {0}, Espaço disponível: {1})',
        recoverHelper3:
            'Por favor, assegure-se de que a arquitetura do servidor corresponda à arquitetura do servidor onde o snapshot foi criado (Arquitetura do servidor atual: {0})',
        rollback: 'Rollback',
        rollbackHelper:
            'Reverter essa recuperação substituirá todos os arquivos dessa recuperação e pode exigir reiniciar os serviços Docker e 1Panel. Você deseja continuar?',

        upgradeHelper: 'A atualização requer reiniciar o serviço 1Panel. Você deseja continuar?',
        rollbackLocalHelper:
            'O nó principal não suporta rollback direto. Por favor, execute manualmente o comando [1pctl restore] para fazer o rollback!',
        noUpgrade: 'Esta é a versão mais recente',
        upgradeNotes: 'Notas de versão',
        upgradeNow: 'Atualizar agora',
        source: 'Fonte para download',
        versionNotSame:
            'A versão do nó não corresponde à do nó principal. Atualize na Gestão de Nós antes de tentar novamente.',
        versionCompare:
            'Detectado que o nó {0} já está na última versão atualizável. Por favor, verifique a versão do nó principal e tente novamente!',

        about: 'Sobre',
        project: 'GitHub',
        issue: 'Problema',
        doc: 'Documento oficial',
        star: 'Estrela',
        description: 'Painel de Servidor Linux',
        forum: 'Discussões',
        doc2: 'Docs',
        currentVersion: 'Versão',

        license: 'Licença',
        bindNode: 'Vincular Nó',
        menuSetting: 'Configurações do Menu',
        menuSettingHelper: 'Quando apenas 1 submenu existir, a barra de menus exibirá apenas esse submenu',
        showAll: 'Mostrar Tudo',
        hideALL: 'Ocultar Tudo',
        ifShow: 'Exibir?',
        menu: 'Menu',
        confirmMessage: 'A página será atualizada para atualizar a lista de menus avançados. Continuar?',
        compressPassword: 'Senha de compressão',
        backupRecoverMessage:
            'Por favor, insira a senha de compressão ou descompressão (deixe em branco para não definir)',
    },
    license: {
        community: 'Gratuito',
        oss: 'Open Source Software',
        pro: 'Pro',
        trial: 'Teste',
        add: 'Adicionar Edição Comunitária',
        licenseAlert:
            'Nós da Edição Comunitária só podem ser adicionados quando a licença está devidamente vinculada a um nó. Apenas nós devidamente vinculados à licença suportam troca.',
        licenseUnbindHelper:
            'Nós da Edição Comunitária detectados para esta licença. Por favor, desvincule e tente novamente!',
        subscription: 'Assinatura',
        perpetual: 'Licença Perpétua',
        versionConstraint: '{0} Compra do versão',
        forceUnbind: 'Forçar Desvinculação',
        forceUnbindHelper:
            'Forçar a desvinculação ignorará quaisquer erros que ocorram durante o processo de desvinculação e, em última análise, liberará a vinculação da licença.',
        updateForce:
            'Atualização forçada (ignora todos os erros durante o desvinculamento para garantir o sucesso da operação final)',
        trialInfo: 'Versão',
        authorizationId: 'ID de autorização',
        authorizedUser: 'Usuário autorizado',
        lostHelper:
            'A licença atingiu o número máximo de tentativas de reenvio. Por favor, clique manualmente no botão de sincronização para garantir que os recursos da versão profissional estão funcionando corretamente. detalhes: ',
        disableHelper:
            'A verificação de sincronização da licença falhou. Por favor, clique manualmente no botão de sincronização para garantir que os recursos da versão profissional estão funcionando corretamente. detalhes: ',
        quickUpdate: 'Atualização rápida',
        power: 'Autorizar',
        unbindHelper:
            'Todas as configurações relacionadas ao Pro serão limpas após a desvinculação. Você deseja continuar?',
        importLicense: 'Licença',
        importHelper: 'Clique ou arraste o arquivo de licença aqui',
        technicalAdvice: 'Consultoria técnica',
        advice: 'Consultoria',
        levelUpPro: 'Upgrade para Pro',
        licenseSync: 'Sincronização de Licença',
        knowMorePro: 'Saiba mais',
        closeAlert: 'A página atual pode ser fechada nas configurações do painel',
        introduce: 'Introdução de recursos',
        waf: 'O upgrade para a versão profissional pode fornecer recursos como mapa de intercepção, logs, registros de bloqueio, bloqueio por localização geográfica, regras personalizadas, páginas de intercepção personalizadas, etc.',
        tamper: 'O upgrade para a versão profissional pode proteger sites contra modificações ou adulterações não autorizadas.',
        setting:
            'O upgrade para a versão profissional permite a personalização do logo do painel, mensagem de boas-vindas e outras informações.',
        monitor:
            'Upgrade para a versão profissional para visualizar o status em tempo real do site, tendências de visitantes, fontes de visitantes, logs de solicitações e outras informações.',
        alert: 'Upgrade para a versão profissional para receber informações de alarme via SMS e visualizar logs de alarmes, controlar completamente vários eventos chave e garantir a operação sem preocupações do sistema',
        fileExchange:
            'Atualize para a Edição Profissional para transferir arquivos rapidamente entre vários servidores.',
        app: 'Upgrade para a versão profissional para visualizar informações do serviço, monitoramento anômalo, etc., através do aplicativo móvel.',
        cluster:
            'A atualização para a Edição Profissional permite gerenciar clusters mestre-escravo MySQL/Postgres/Reids.',
    },
    clean: {
        scan: 'Iniciar escaneamento',
        scanHelper: 'Limpeza fácil de arquivos inúteis gerados durante a execução do 1Panel',
        clean: 'Limpar agora',
        reScan: 'Repetir escaneamento',
        cleanHelper: 'Isso limpará os arquivos inúteis selecionados e não poderá ser desfeito. Você deseja continuar?',
        statusSuggest: '(Limpeza recomendada)',
        statusClean: '(Muito limpo)',
        statusEmpty: 'Muito limpo, nenhuma limpeza necessária!',
        statusWarning: '(Prossiga com cautela)',
        lastCleanTime: 'Última limpeza: {0}',
        lastCleanHelper: 'Arquivos e diretórios limpos: {0}, total limpo: {1}',
        cleanSuccessful: 'Limpeza realizada com sucesso',
        currentCleanHelper: 'Arquivos e diretórios limpos nesta sessão: {0}, total limpo: {1}',
        suggest: '(Recomendado)',
        totalScan: 'Total de arquivos inúteis a serem limpos: ',
        selectScan: 'Total de arquivos inúteis selecionados: ',

        system: 'Arquivos inúteis do sistema',
        systemHelper:
            'Arquivos temporários gerados durante snapshots, atualizações e conteúdos obsoletos durante iterações de versão',
        panelOriginal: 'Arquivos de backup de recuperação de snapshot do sistema',
        backup: 'Diretório de backup temporário',
        upgrade: 'Arquivos de backup de atualização do sistema',
        upgradeHelper: '(Recomenda-se manter o backup de atualização mais recente para rollback do sistema)',
        cache: 'Arquivos de cache do sistema',
        cacheHelper: '(Prossiga com cautela, a limpeza exige reinício do serviço)',
        rollback: 'Arquivos de backup antes da recuperação',

        upload: 'Arquivos temporários de upload',
        uploadHelper: 'Arquivos temporários enviados da lista de backup do sistema',
        download: 'Arquivos temporários de download',
        downloadHelper: 'Arquivos temporários baixados de contas de backup de terceiros pelo sistema',
        directory: 'Diretório',

        systemLog: 'Arquivos de log do sistema',
        systemLogHelper:
            'Informações de log do sistema, logs de construção de container ou pull de imagem, e logs gerados em tarefas agendadas',
        dockerLog: 'Arquivos de log de operação de container',
        taskLog: 'Arquivos de log de execução de tarefas agendadas',
        containerShell: 'Tarefas agendadas de script Shell interno do container',

        containerTrash: 'Lixeira do container',
        volumes: 'Volumes',
        buildCache: 'Cache de construção do container',
    },
    app: {
        app: 'Aplicativo | Aplicativos',
        installName: 'Nome',
        installed: 'Instalado',
        all: 'Todos',
        version: 'Versão',
        detail: 'Detalhes',
        params: 'Editar',
        author: 'Autor',
        source: 'Fonte',
        appName: 'Nome do Aplicativo',
        deleteWarn:
            'A operação de exclusão excluirá todos os dados e backups juntos. Esta operação não pode ser desfeita. Deseja continuar?',
        syncSuccess: 'Sincronizado com sucesso',
        canUpgrade: 'Atualizações',
        backupName: 'Nome do Arquivo',
        backupPath: 'Caminho do Arquivo',
        backupdate: 'Hora do Backup',
        versionSelect: 'Por favor, selecione uma versão',
        operatorHelper: 'A operação {0} será realizada no aplicativo selecionado. Deseja continuar?',
        startOperatorHelper: 'O aplicativo será iniciado. Deseja continuar?',
        stopOperatorHelper: 'O aplicativo será parado. Deseja continuar?',
        restartOperatorHelper: 'O aplicativo será reiniciado. Deseja continuar?',
        reloadOperatorHelper: 'O aplicativo será recarregado. Deseja continuar?',
        checkInstalledWarn: `"{0}" não foi detectado. Vá para "Loja de Aplicativos" para instalar.`,
        gotoInstalled: 'Ir para instalar',
        limitHelper: 'O aplicativo já foi instalado.',
        deleteHelper: `"{0}" foi associado aos seguintes recursos. Por favor, verifique e tente novamente!`,
        checkTitle: 'Dica',
        defaultConfig: 'Configuração padrão',
        defaultConfigHelper: 'Foi restaurado para a configuração padrão, ela entrará em vigor após salvar',
        forceDelete: 'Excluir forçadamente',
        forceDeleteHelper: 'A exclusão forçada ignorará erros durante o processo de exclusão e eliminará os metadados.',
        deleteBackup: 'Excluir backup',
        deleteBackupHelper: 'Excluir também o backup do aplicativo',
        deleteDB: 'Excluir banco de dados',
        deleteDBHelper: 'Excluir também o banco de dados',
        noService: 'Sem {0}',
        toInstall: 'Ir para instalar',
        param: 'Parâmetros',
        alreadyRun: 'Idade',
        syncAppList: 'Sincronizar',
        less1Minute: 'Menos de 1 minuto',
        appOfficeWebsite: 'Site oficial',
        github: 'Github',
        document: 'Documento',
        updatePrompt: 'Nenhuma atualização disponível',
        installPrompt: 'Nenhum aplicativo instalado ainda',
        updateHelper: 'Editar parâmetros pode causar falha no início do aplicativo. Por favor, proceda com cautela.',
        updateWarn: 'Os parâmetros de atualização precisam reconstruir o aplicativo. Deseja continuar?',
        busPort: 'Porta',
        syncStart: 'Iniciando a sincronização! Por favor, atualize a loja de aplicativos mais tarde',
        advanced: 'Configurações avançadas',
        cpuCore: 'núcleo(s)',
        containerName: 'Nome do container',
        containerNameHelper: 'O nome do container será gerado automaticamente quando não definido',
        allowPort: 'Acesso externo',
        allowPortHelper: 'Permitir o acesso externo irá liberar a porta do firewall',
        appInstallWarn: `O aplicativo não expõe a porta de acesso externo por padrão. Clique em "Configurações Avançadas" para expô-la.`,
        upgradeStart: 'Iniciando a atualização! Por favor, atualize a página mais tarde',
        toFolder: 'Abrir o diretório de instalação',
        editCompose: 'Editar arquivo compose',
        editComposeHelper: 'Editar o arquivo compose pode resultar em falha na instalação do software',
        composeNullErr: 'O compose não pode estar vazio',
        takeDown: 'Retirar',
        allReadyInstalled: 'Instalado',
        installHelper: 'Se houver problemas ao puxar a imagem, configure a aceleração da imagem.',
        upgradeHelper:
            'Coloque aplicativos anormais de volta ao estado normal antes de atualizar. Se a atualização falhar, vá para "Logs > Logs do Sistema" para verificar a razão da falha.',
        installWarn: `O acesso externo não foi habilitado, o que impede que o aplicativo seja acessado via redes externas. Deseja continuar?`,
        showIgnore: 'Exibir aplicativos ignorados',
        cancelIgnore: 'Cancelar ignorar',
        ignoreList: 'Aplicativos ignorados',
        appHelper:
            'Vá para a página de detalhes do aplicativo para aprender as instruções de instalação para alguns aplicativos especiais.',
        backupApp: 'Fazer backup do aplicativo antes de atualizar',
        backupAppHelper:
            'Se a atualização falhar, o backup será automaticamente revertido. Verifique a razão da falha no log de auditoria-sistema. O backup manterá as últimas 3 cópias por padrão',
        openrestyDeleteHelper: 'Excluir forçadamente o OpenResty excluirá todos os sites. Deseja continuar?',
        downloadLogHelper1: 'Todos os logs do aplicativo {0} estão prestes a ser baixados. Deseja continuar?',
        downloadLogHelper2: 'Os últimos {1} logs do aplicativo {0} estão prestes a ser baixados. Deseja continuar?',
        syncAllAppHelper: 'Todos os aplicativos serão sincronizados. Deseja continuar?',
        hostModeHelper:
            'O modo de rede atual do aplicativo é o modo host. Se precisar abrir a porta, abra-a manualmente na página do firewall.',
        showLocal: 'Exibir aplicativos locais',
        reload: 'Recarregar',
        upgradeWarn:
            'Atualizar o aplicativo substituirá o arquivo docker-compose.yml. Se houver alterações, você pode clicar para visualizar a comparação do arquivo',
        newVersion: 'Nova versão',
        oldVersion: 'Versão atual',
        composeDiff: 'Comparação de arquivo',
        showDiff: 'Exibir comparação',
        useNew: 'Usar versão personalizada',
        useDefault: 'Usar versão padrão',
        useCustom: 'Personalizar docker-compose.yml',
        useCustomHelper:
            'Usar um arquivo docker-compose.yml personalizado pode causar falha na atualização do aplicativo. Se não for necessário, não marque esta opção.',
        diffHelper:
            'O lado esquerdo é a versão antiga, o lado direito é a nova versão. Após editar, clique para salvar a versão personalizada',
        pullImage: 'Puxar Imagem',
        pullImageHelper: 'Execute o comando docker pull para puxar a imagem antes de iniciar o aplicativo',
        deleteImage: 'Excluir Imagem',
        deleteImageHelper:
            'Exclua a imagem relacionada ao aplicativo. A tarefa não será encerrada se a exclusão falhar.',
        requireMemory: 'Requisito de Memória',
        supportedArchitectures: 'Arquiteturas Suportadas',
        link: 'Link',
        showCurrentArch: 'Aplicações da arquitetura atual do servidor',
        syncLocalApp: 'Sincronizar Aplicativo Local',
        memoryRequiredHelper: 'O aplicativo atual requer {0} de memória',
        gpuConfig: 'Ativar Suporte a GPU',
        gpuConfigHelper:
            'Certifique-se de que a máquina possui uma GPU NVIDIA e tenha os drivers NVIDIA e NVIDIA Docker Container Toolkit instalados',
        webUI: 'Endereço de Acesso Web',
        webUIPlaceholder: 'Exemplo: example.com:8080/login',
        defaultWebDomain: 'Endereço de Acesso Padrão',
        defaultWebDomainHepler: 'Se a porta do aplicativo for 8080, o endereço será http(s)://endereço padrão:8080',
        webUIConfig:
            'O nó atual não tem um endereço de acesso padrão configurado. Por favor, defina-o nos parâmetros do aplicativo ou vá para as configurações do painel para configurar!',
        toLink: 'Ir para',
        customAppHelper:
            'Antes de instalar um pacote de loja de aplicativos personalizado, certifique-se de que não há aplicativos instalados.',
        forceUninstall: 'Desinstalação Forçada',
        syncCustomApp: 'Sincronizar Aplicativo Personalizado',
        ignoreAll: 'Ignorar todas as versões subsequentes',
        ignoreVersion: 'Ignorar versão especificada',
        specifyIP: 'Vincular IP do Host',
        specifyIPHelper:
            'Defina o endereço do host/interface de rede para vincular a porta (se você não tiver certeza sobre isso, por favor, não preencha)',
        uninstallDeleteBackup: 'Desinstalar Aplicativo - Excluir Backup',
        uninstallDeleteImage: 'Desinstalar Aplicativo - Excluir Imagem',
        upgradeBackup: 'Fazer Backup do Aplicativo Antes de Atualizar',
    },
    website: {
        primaryDomain: 'Domínio principal',
        otherDomains: 'Outros domínios',
        static: 'Estático',
        deployment: 'Implantação',
        supportUpType: 'Somente arquivos .tar.gz são suportados',
        zipFormat: 'Estrutura de pacote comprimido .tar.gz: o pacote comprimido test.tar.gz deve conter o arquivo {0}',
        proxy: 'Proxy reverso',
        alias: 'Alias',
        ftpUser: 'Conta FTP',
        ftpPassword: 'Senha FTP',
        ftpHelper:
            'Após criar um site, uma conta FTP correspondente será criada e o diretório FTP será vinculado ao diretório do site.',
        remark: 'Observação',
        manageGroup: 'Gerenciar grupos',
        groupSetting: 'Gerenciamento de grupos',
        createGroup: 'Criar grupo',
        appNew: 'Novo Aplicativo',
        appInstalled: 'Aplicativo instalado',
        create: 'Criar site',
        delete: 'Excluir site',
        deleteApp: 'Excluir Aplicativo',
        deleteBackup: 'Excluir Backup',
        domain: 'Domínio',
        domainHelper: 'Um domínio por linha.\nSuporta curinga "*" e endereço IP.\nSuporta adição de porta.',
        addDomain: 'Adicionar',
        domainConfig: 'Domínios',
        defaultDoc: 'Documento',
        perserver: 'Concorrência',
        perserverHelper: 'Limitar a concorrência máxima do site atual',
        perip: 'IP único',
        peripHelper: 'Limitar o número máximo de acessos simultâneos a um único IP',
        rate: 'Limites de tráfego',
        rateHelper: 'Limitar o fluxo de cada requisição (unidade: KB)',
        limitHelper: 'Habilitar controle de fluxo',
        other: 'Outro',
        currentSSL: 'Certificado atual',
        dnsAccount: 'Conta DNS',
        applySSL: 'Solicitação de certificado',
        SSLList: 'Lista de certificados',
        createDnsAccount: 'Conta DNS',
        aliyun: 'Aliyun',
        manual: 'Análise manual',
        key: 'Chave',
        check: 'Ver',
        acmeAccountManage: 'Contas ACME',
        email: 'Email',
        acmeAccount: 'Conta ACME',
        provider: 'Método de verificação',
        dnsManual: 'Resolução manual',
        expireDate: 'Data de expiração',
        brand: 'Organização',
        deploySSL: 'Implantação',
        deploySSLHelper: 'Tem certeza de que deseja implantar o certificado?',
        ssl: 'Certificado | Certificados',
        dnsAccountManage: 'Provedores DNS',
        renewSSL: 'Renovar',
        renewHelper: 'Tem certeza de que deseja renovar o certificado?',
        renewSuccess: 'Certificado renovado',
        enableHTTPS: 'Habilitar',
        aliasHelper: 'Alias é o nome do diretório do site',
        lastBackupAt: 'Último backup realizado em',
        null: 'nenhum',
        nginxConfig: 'Configuração Nginx',
        websiteConfig: 'Configurações do site',
        basic: 'Básico',
        source: 'Configuração',
        security: 'Segurança',
        nginxPer: 'Ajuste de desempenho',
        neverExpire: 'Nunca',
        setDefault: 'Definir como padrão',
        default: 'Padrão',
        deleteHelper: 'O status do aplicativo relacionado está anômalo, por favor verifique',
        toApp: 'Ir para a lista de aplicativos instalados',
        cycle: 'Ciclo',
        frequency: 'Frequência',
        ccHelper:
            'Solicitar cumulativamente a mesma URL mais de {1} vezes em {0} segundos, aciona a defesa CC, bloqueia este IP',
        mustSave: 'A modificação precisa ser salva para ter efeito',
        fileExt: 'Extensão de arquivo',
        fileExtBlock: 'Lista negra de extensões de arquivo',
        value: 'valor',
        enable: 'Habilitar',
        proxyAddress: 'Endereço Proxy',
        proxyHelper: 'Exemplo: 127.0.0.1:8080',
        forceDelete: 'Excluir forçadamente',
        forceDeleteHelper: 'A exclusão forçada ignorará erros durante o processo de exclusão e excluirá os metadados.',
        deleteAppHelper: 'Excluir aplicativos associados e backups de aplicativos ao mesmo tempo',
        deleteBackupHelper: 'Excluir também backups do site.',
        deleteConfirmHelper: `A operação de exclusão não pode ser desfeita. Digite <span style="color:red"> "{0}" </span> para confirmar a exclusão.`,
        staticPath: 'O diretório principal correspondente é ',
        limit: 'Plano',
        blog: 'Fórum/Blog',
        imageSite: 'Site de Imagens',
        downloadSite: 'Site de Download',
        shopSite: 'Loja',
        doorSite: 'Portal',
        qiteSite: 'Empresarial',
        videoSite: 'Site de Vídeo',
        errLog: 'Erro de log',
        accessLog: 'Log do site',
        stopHelper:
            'Após parar o site, ele não poderá ser acessado normalmente, e o usuário verá a página de parada do site atual ao visitar. Deseja continuar?',
        startHelper: 'Após ativar o site, os usuários podem acessar o conteúdo do site normalmente, deseja continuar?',
        sitePath: 'Diretório',
        siteAlias: 'Alias do site',
        primaryPath: 'Diretório raiz',
        folderTitle: 'O site contém principalmente as seguintes pastas',
        wafFolder: 'Regras de firewall',
        indexFolder: 'Diretório raiz do site',
        logFolder: 'Log do site',
        sslFolder: 'Certificado do site',
        enableOrNot: 'Habilitar',
        oldSSL: 'Certificado existente',
        manualSSL: 'Importar certificado',
        select: 'Selecionar',
        selectSSL: 'Selecionar certificado',
        privateKey: 'Chave (KEY)',
        certificate: 'Certificado (formato PEM)',
        HTTPConfig: 'Opções HTTP',
        HTTPSOnly: 'Bloquear requisições HTTP',
        HTTPToHTTPS: 'Redirecionar para HTTPS',
        HTTPAlso: 'Permitir requisições HTTP diretas',
        sslConfig: 'Opções SSL',
        disableHTTPS: 'Desabilitar HTTPS',
        disableHTTPSHelper: 'Desabilitar HTTPS removerá a configuração do certificado relacionada, deseja continuar?',
        SSLHelper:
            'Nota: Não use certificados SSL para sites ilegais.\nSe o acesso HTTPS não funcionar após a ativação, verifique se o grupo de segurança liberou corretamente a porta 443.',
        SSLConfig: 'Configurações do certificado',
        SSLProConfig: 'Configurações do protocolo',
        supportProtocol: 'Versão do protocolo',
        encryptionAlgorithm: 'Algoritmo de criptografia',
        notSecurity: '(não seguro)',
        encryptHelper:
            "O Let's Encrypt tem um limite de frequência para a emissão de certificados, mas é suficiente para atender necessidades normais. Operações muito frequentes podem causar falha na emissão. Para restrições específicas, consulte <a target='_blank' href='https://letsencrypt.org/zh-cn/docs/rate-limits/'>documentação oficial</a>",
        ipValue: 'Valor',
        ext: 'Extensão de arquivo',
        wafInputHelper: 'Digite os dados por linha, uma linha por vez',
        data: 'dados',
        ever: 'permanente',
        nextYear: 'Um ano depois',
        noLog: 'Nenhum log encontrado',
        defaultServer: 'Site padrão',
        noDefaultServer: 'Não definido',
        defaultServerHelper:
            'Após definir o site padrão, todos os nomes de domínio e IPs desvinculados serão redirecionados para o site padrão\nIsso pode evitar eficazmente a resolução maliciosa\nNo entanto, isso também pode causar falha na interceptação de nomes de domínio não autorizados pelo WAF',
        restoreHelper: 'Tem certeza de que deseja restaurar com este backup?',
        websiteDeploymentHelper: 'Use um aplicativo instalado ou crie um novo aplicativo para criar um site.',
        websiteStatictHelper: 'Crie um diretório de site no host.',
        websiteProxyHelper:
            'Use proxy reverso para redirecionar um serviço existente. Por exemplo, se um serviço estiver instalado e rodando na porta 8080, o endereço do proxy será "http://127.0.0.1:8080".',
        runtimeProxyHelper: 'Use o ambiente de execução do site para criar um site.',
        runtime: 'Ambiente de execução',
        deleteRuntimeHelper:
            'O aplicativo de Runtime precisa ser excluído junto com o site, por favor, manuseie com cuidado',
        proxyType: 'Tipo de rede',
        unix: 'Rede Unix',
        tcp: 'Rede TCP/IP',
        phpFPM: 'Configuração FPM',
        phpConfig: 'Configuração PHP',
        updateConfig: 'Atualizar configurações',
        isOn: 'Ligado',
        isOff: 'Desligado',
        rewrite: 'Pseudo-estático',
        rewriteMode: 'Plano',
        current: 'Atual',
        rewriteHelper:
            'Se configurar pseudo-estático e o site se tornar inacessível, tente reverter para as configurações padrão.',
        runDir: 'Diretório de execução',
        runUserHelper:
            'Para sites implantados através do ambiente de execução PHP, você deve definir o proprietário e o grupo dos arquivos e pastas de índice e subdiretórios para 1000. Para o ambiente PHP local, consulte as configurações de usuário e grupo do PHP-FPM local',
        userGroup: 'Usuário/Grupo',
        uGroup: 'Grupo',
        proxyPath: 'Caminho do proxy',
        proxyPass: 'URL de destino',
        cache: 'Cache',
        cacheTime: 'Duração do cache',
        enableCache: 'Habilitar cache',
        proxyHost: 'Host do proxy',
        disabled: 'Parado',
        startProxy: 'Isso iniciará o proxy reverso. Deseja continuar?',
        stopProxy: 'Isso interromperá o proxy reverso. Deseja continuar?',
        sourceFile: 'Fonte',
        proxyHelper1: 'Ao acessar este diretório, o conteúdo da URL de destino será retornado e exibido.',
        proxyPassHelper: 'A URL de destino deve ser válida e acessível.',
        proxyHostHelper: 'Passe o nome de domínio no cabeçalho da requisição para o servidor proxy.',
        replacementHelper:
            'Até 5 substituições podem ser adicionadas, deixe em branco se não houver substituição necessária.',
        modifier: 'Regras de correspondência',
        modifierHelper:
            'Exemplo: "=" é correspondência exata, "~" é correspondência regular, "^~" corresponde ao início do caminho, etc.',
        replace: 'Substituições de texto',
        addReplace: 'Adicionar',
        replaced: 'String de busca (não pode estar vazia)',
        replaceText: 'Substituir por string',
        replacedErr: 'A string de busca não pode estar vazia',
        replacedErr2: 'A string de busca não pode ser repetida',
        basicAuth: 'Autenticação básica',
        editBasicAuthHelper:
            'A senha é criptografada de forma assimétrica e não pode ser exibida. A edição requer a redefinição da senha',
        antiLeech: 'Anti-leech',
        extends: 'Extensão',
        browserCache: 'Cache',
        leechLog: 'Registrar log anti-leech',
        accessDomain: 'Domínios permitidos',
        leechReturn: 'Recurso de resposta',
        noneRef: 'Permitir referrer vazio',
        disable: 'não habilitado',
        disableLeechHelper: 'Se deseja desabilitar o anti-leech',
        disableLeech: 'Desabilitar anti-leech',
        ipv6: 'Ouvir IPv6',
        leechReturnError: 'Por favor, preencha o código de status HTTP',
        selectAcme: 'Selecionar conta Acme',
        imported: 'Criado manualmente',
        importType: 'Tipo de importação',
        pasteSSL: 'Colar código',
        localSSL: 'Selecionar arquivo do servidor',
        privateKeyPath: 'Caminho do arquivo da chave privada',
        certificatePath: 'Caminho do arquivo do certificado',
        ipWhiteListHelper:
            'O papel da lista de permissões de IP: todas as regras são inválidas para a lista de permissões de IP',
        redirect: 'Redirecionar',
        sourceDomain: 'Domínio de origem',
        targetURL: 'Endereço da URL de destino',
        keepPath: 'Parâmetros URI',
        path: 'caminho',
        redirectType: 'Tipo de redirecionamento',
        redirectWay: 'Modo',
        keep: 'manter',
        notKeep: 'Não manter',
        redirectRoot: 'Redirecionar para a página inicial',
        redirectHelper: 'Redirecionamento permanente 301, redirecionamento temporário 302',
        changePHPVersionWarn:
            'Trocar a versão do PHP vai excluir o contêiner PHP original (o código do site montado não será perdido), continuar?',
        changeVersion: 'Trocar versão',
        retainConfig: 'Deseja manter os arquivos php-fpm.conf e php.ini?',
        runDirHelper2:
            'Por favor, certifique-se de que o diretório de execução secundário esteja no diretório do índice',
        openrestyHelper:
            'Porta HTTP padrão do OpenResty: {0} Porta HTTPS: {1}, o que pode afetar o acesso ao domínio do site e o redirecionamento forçado para HTTPS',
        primaryDomainHelper: 'Exemplo: exemplo.com ou exemplo.com:8080',
        acmeAccountType: 'Tipo de conta',
        keyType: 'Algoritmo de chave',
        tencentCloud: 'Tencent Cloud',
        containWarn: 'O nome de domínio contém o domínio principal, por favor, reentre',
        rewriteHelper2:
            'Aplicações como o WordPress instaladas a partir da loja de aplicativos geralmente vêm com configuração de pseudo-estática predefinida. Reconfigurá-las pode causar erros.',
        websiteBackupWarn:
            'Somente suporta importar backups locais, importar backups de outras máquinas pode causar falha na recuperação',
        ipWebsiteWarn:
            'Sites com IP como nomes de domínio precisam ser configurados como site padrão para serem acessados normalmente',
        hstsHelper: 'Ativar HSTS pode aumentar a segurança do site',
        includeSubDomains: 'SubDomains',
        hstsIncludeSubDomainsHelper:
            'Quando ativado, a política HSTS será aplicada a todos os subdomínios do domínio atual.',
        defaultHtml: 'Página padrão',
        website404: 'Página de erro 404 do site',
        domain404: 'O domínio do site não existe',
        indexHtml: 'Índice para site estático',
        stopHtml: 'Site parado',
        indexPHP: 'Índice para site PHP',
        sslExpireDate: 'Data de expiração do certificado',
        website404Helper:
            'A página de erro 404 do site suporta apenas sites com ambiente de execução PHP e sites estáticos',
        sni: 'SNI de origem',
        sniHelper:
            'Quando o proxy reverso de backend for HTTPS, você pode precisar configurar o SNI de origem. Consulte a documentação do provedor de serviços CDN para mais detalhes.',
        huaweicloud: 'Huawei Cloud',
        createDb: 'Criar Banco de Dados',
        enableSSLHelper: 'A falha ao ativar o SSL não afetará a criação do site.',
        batchAdd: 'Adicionar Domínios em Lote',
        generateDomain: 'Gerar',
        global: 'Global',
        subsite: 'Subsite',
        subsiteHelper:
            'Um subsite pode selecionar o diretório de um site PHP ou estático existente como seu diretório raiz.',
        parentWebsite: 'Site Pai',
        deleteSubsite: 'Para excluir o site atual, você deve primeiro excluir o subsite {0}.',
        loadBalance: 'Balanceamento de Carga',
        server: 'Nó',
        algorithm: 'Algoritmo',
        ipHash: 'Hash de IP',
        ipHashHelper:
            'Distribui as solicitações para um servidor específico com base no endereço IP do cliente, garantindo que um cliente específico seja sempre roteado para o mesmo servidor.',
        leastConn: 'Menos Conexões',
        leastConnHelper: 'Envia solicitações para o servidor com o menor número de conexões ativas.',
        leastTime: 'Menor Tempo',
        leastTimeHelper: 'Envia solicitações para o servidor com o menor tempo de conexão ativa.',
        defaultHelper:
            'Método padrão, as solicitações são distribuídas uniformemente para cada servidor. Se o servidor tiver configuração de peso, as solicitações são distribuídas de acordo com o peso especificado. Servidores com pesos mais altos recebem mais solicitações.',
        weight: 'Peso',
        maxFails: 'Máximo de Falhas',
        maxConns: 'Máximo de Conexões',
        strategy: 'Estratégia',
        strategyDown: 'Desativar',
        strategyBackup: 'Backup',
        staticChangePHPHelper: 'Atualmente um site estático, pode ser alterado para um site PHP.',
        proxyCache: 'Cache de Proxy Reverso',
        cacheLimit: 'Limite de Espaço de Cache',
        shareCache: 'Tamanho da Memória de Contagem de Cache',
        cacheExpire: 'Tempo de Expiração do Cache',
        shareCacheHelper: '1M de memória pode armazenar aproximadamente 8000 objetos de cache.',
        cacheLimitHelper: 'Exceder o limite excluirá automaticamente os caches antigos.',
        cacheExpireHelper: 'Caches não acessados dentro do tempo de expiração serão excluídos.',
        realIP: 'IP Real',
        ipFrom: 'Fonte do IP',
        ipFromHelper:
            'Ao configurar fontes de IP confiáveis, o OpenResty analisará as informações de IP no cabeçalho HTTP para identificar e registrar com precisão o endereço IP real do visitante, incluindo nos logs de acesso.',
        ipFromExample1:
            'Se o frontend for uma ferramenta como o Frp, você pode preencher o endereço IP do Frp, como 127.0.0.1.',
        ipFromExample2: 'Se o frontend for um CDN, você pode preencher o intervalo de IPs do CDN.',
        ipFromExample3:
            'Se não tiver certeza, você pode preencher 0.0.0.0/0 (IPv4) ou ::/0 (IPv6). [Nota: Permitir qualquer fonte de IP não é seguro.]',
        http3Helper:
            'O HTTP/3 é uma versão atualizada do HTTP/2, fornecendo velocidades de conexão mais rápidas e melhor desempenho. No entanto, nem todos os navegadores suportam HTTP/3, e ativá-lo pode fazer com que alguns navegadores não consigam acessar o site.',
        changeDatabase: 'Alterar Banco de Dados',
        changeDatabaseHelper1: 'A associação do banco de dados é usada para backup e restauração do site.',
        changeDatabaseHelper2: 'Alternar para outro banco de dados pode tornar backups anteriores irrecuperáveis.',
        saveCustom: 'Salvar como Modelo',
        rainyun: 'Rainyun',
        volcengine: 'Volcengine',
        runtimePortHelper: 'O ambiente de runtime atual possui várias portas. Por favor, selecione uma porta de proxy.',
        runtimePortWarn: 'O ambiente de execução atual não possui portos, não é possível proxiar',
        cacheWarn: 'Por favor, desligue o interruptor de cache no proxy reverso primeiro',
        loadBalanceHelper:
            'Após criar o balanceamento de carga, vá para "Proxy Reverso", adicione um proxy e configure o endereço de backend para: http://<nome do balanceamento de carga>.',
        favorite: 'Favorito',
        cancelFavorite: 'Cancelar Favorito',
        useProxy: 'Usar Proxy',
        useProxyHelper: 'Usar o endereço do servidor proxy nas configurações do painel',
        westCN: 'West Digital',
        openBaseDir: 'Prevenir Ataques entre Sites',
        openBaseDirHelper:
            'open_basedir é usado para restringir o caminho de acesso a arquivos PHP, ajudando a prevenir acesso entre sites e aumentar a segurança',
        serverCacheTime: 'Tempo de Cache do Servidor',
        serverCacheTimeHelper:
            'O tempo que uma requisição é armazenada em cache no servidor. Durante este período, requisições idênticas retornarão o resultado em cache diretamente, sem pedir ao servidor de origem.',
        browserCacheTime: 'Tempo de Cache do Navegador',
        browserCacheTimeHelper:
            'O tempo que os recursos estáticos são armazenados em cache localmente no navegador, reduzindo requisições redundantes. Os usuários usarão o cache local diretamente antes de expirar ao atualizar a página.',
        donotLinkeDB: 'Não Vincular Banco de Dados',
        toWebsiteDir: 'Entrar no Diretório do Site',
    },
    php: {
        short_open_tag: 'Suporte para short tags',
        max_execution_time: 'Tempo máximo de execução do script',
        max_input_time: 'Tempo máximo de entrada de dados',
        memory_limit: 'Limite de memória para scripts',
        post_max_size: 'Tamanho máximo para dados enviados via POST',
        file_uploads: 'Permitir upload de arquivos',
        upload_max_filesize: 'Tamanho máximo permitido para upload de arquivos',
        max_file_uploads: 'Número máximo de arquivos permitidos para upload simultâneo',
        default_socket_timeout: 'Tempo limite do socket',
        error_reporting: 'Nível de relatório de erros',
        display_errors: 'Exibir informações detalhadas de erro',
        cgi_fix_pathinfo: 'Ativar suporte a pathinfo',
        date_timezone: 'Fuso horário',
        disableFunction: 'Desabilitar função',
        disableFunctionHelper:
            'Informe a função a ser desabilitada, como exec. Para múltiplas funções, separe com vírgulas',
        uploadMaxSize: 'Limite de upload',
        indexHelper:
            'Para garantir o funcionamento normal do site em PHP, coloque o código no diretório de índice e evite renomeações',
        extensions: 'Modelos de extensão',
        extension: 'Extensão',
        extensionHelper: 'Para múltiplas extensões, separe com vírgulas',
        toExtensionsList: 'Ver lista de extensões',
        containerConfig: 'Configuração do Contêiner',
        containerConfigHelper:
            'Variáveis de ambiente e outras informações podem ser modificadas em Configuração - Configuração do Contêiner após a criação',
        dateTimezoneHelper: 'Exemplo: TZ=Asia/Shanghai (Adicione conforme necessário)',
    },
    nginx: {
        serverNamesHashBucketSizeHelper: 'The hash table size of the server name',
        clientHeaderBufferSizeHelper: 'The header buffer size requested by the client',
        clientMaxBodySizeHelper: 'Maximum Upload File',
        keepaliveTimeoutHelper: 'Connection Timeout',
        gzipMinLengthHelper: 'Minimum Compressed File',
        gzipCompLevelHelper: 'Compression Rate',
        gzipHelper: 'Enable compression for transmission',
        connections: 'Active connections',
        accepts: 'Accepts',
        handled: 'Handled',
        requests: 'Requests',
        reading: 'Reading',
        writing: 'Writing',
        waiting: 'Waiting',
        status: 'Current Status',
        configResource: 'Configuration',
        saveAndReload: 'Save and reload',
        clearProxyCache: 'Clean reverse proxy cache',
        clearProxyCacheWarn:
            'All websites that have configured with cache will be affected and "OpenResty" will be restarted. Do you want to continue?',
        create: 'Criar Módulo',
        update: 'Editar Módulo',
        params: 'Parâmetros',
        packages: 'Pacotes',
        script: 'Script',
        module: 'Módulo',
        build: 'Construir',
        buildWarn:
            'Construir OpenResty requer a reserva de certa quantidade de CPU e memória, e o processo pode ser demorado, por favor, seja paciente.',
        mirrorUrl: 'Fonte de Software',
        paramsHelper: 'Por exemplo: --add-module=/tmp/ngx_brotli',
        packagesHelper: 'Por exemplo: git,curl separados por vírgulas',
        scriptHelper:
            'Script a ser executado antes da compilação, geralmente para baixar o código-fonte do módulo, instalar dependências, etc.',
        buildHelper:
            'Clique em Construir após adicionar/modificar um módulo. Construção bem-sucedida reiniciará automaticamente o OpenResty.',
        defaultHttps: 'HTTPS Anti-tampering',
        defaultHttpsHelper1: 'A ativação desta opção pode resolver problemas de adulteração HTTPS.',
    },
    ssl: {
        create: 'Solicitar',
        provider: 'Tipo',
        manualCreate: 'Criado manualmente',
        acmeAccount: 'Conta ACME',
        resolveDomain: 'Resolver nome de domínio',
        err: 'Erro',
        value: 'Valor do registro',
        dnsResolveHelper: 'Vá ao provedor de serviço de DNS para adicionar os seguintes registros de resolução:',
        detail: 'Detalhes',
        msg: 'Informação',
        ssl: 'Certificado',
        key: 'Chave privada',
        startDate: 'Data de início',
        organization: 'Organização emissora',
        renewConfirm: 'Isso renovará um novo certificado para o domínio {0}. Deseja continuar?',
        autoRenew: 'Renovação automática',
        autoRenewHelper: 'Renova automaticamente 30 dias antes da expiração',
        renewSuccess: 'Renovação bem-sucedida',
        renewWebsite: 'Este certificado está associado aos seguintes sites e será aplicado a eles simultaneamente',
        createAcme: 'Criar conta',
        acmeHelper: 'A conta ACME é usada para solicitar certificados gratuitos',
        upload: 'Importar',
        applyType: 'Tipo',
        apply: 'Renovar',
        applyStart: 'Início da solicitação do certificado',
        getDnsResolve: 'Obtendo o valor de resolução DNS, por favor, aguarde...',
        selfSigned: 'CA autoassinado',
        ca: 'Autoridade certificadora',
        commonName: 'Nome comum',
        caName: 'Nome da autoridade certificadora',
        company: 'Nome da organização',
        department: 'Nome da unidade organizacional',
        city: 'Nome da localidade',
        province: 'Estado ou província',
        country: 'Nome do país (código de 2 letras)',
        commonNameHelper: 'Por exemplo, ',
        selfSign: 'Emitir certificado',
        days: 'Período de validade',
        domainHelper: 'Um domínio por linha, suporta * e endereços IP',
        pushDir: 'Enviar o certificado para o diretório local',
        dir: 'Diretório',
        pushDirHelper:
            'O arquivo do certificado "fullchain.pem" e o arquivo de chave "privkey.pem" serão gerados neste diretório.',
        organizationDetail: 'Detalhes da organização',
        fromWebsite: 'Do site',
        dnsMauanlHelper:
            'No modo de resolução manual, você precisa clicar no botão de solicitação após a criação para obter o valor de resolução DNS',
        httpHelper:
            'O uso do modo HTTP requer a instalação do OpenResty e não suporta a solicitação de certificados de domínio curinga.',
        buypassHelper: 'O Buypass não está acessível na China continental',
        googleHelper: 'Como obter EAB HmacKey e EAB kid',
        googleCloudHelper: 'A API do Google Cloud não está acessível na maior parte da China continental',
        skipDNSCheck: 'Pular verificação DNS',
        skipDNSCheckHelper:
            'Marque esta opção apenas se enfrentar problemas de timeout durante a solicitação de certificação.',
        cfHelper: 'Não use a Global API Key',
        deprecated: 'será descontinuado',
        deprecatedHelper:
            'A manutenção foi interrompida e pode ser abandonada em uma versão futura. Use o método Tencent Cloud para análise',
        disableCNAME: 'Desativar CNAME',
        disableCNAMEHelper: 'Marque esta opção se o domínio tiver um registro CNAME e a solicitação falhar.',
        nameserver: 'Servidor DNS',
        nameserverHelper: 'Use um servidor DNS personalizado para verificar os nomes de domínio.',
        edit: 'Editar certificado',
        execShell: 'Executar o script após a solicitação de certificação.',
        shell: 'Conteúdo do script',
        shellHelper:
            'O diretório padrão de execução do script é o diretório de instalação do 1Panel. Se um certificado for enviado para o diretório local, o diretório de execução será o diretório de envio do certificado. O tempo limite padrão de execução é de 30 minutos.',
        customAcme: 'Serviço ACME Personalizado',
        customAcmeURL: 'URL do Serviço ACME',
        baiduCloud: 'Baidu Cloud',
    },
    firewall: {
        create: 'Criar regra',
        edit: 'Editar regra',
        ccDeny: 'Proteção contra CC',
        ipWhiteList: 'Lista de IPs permitidos',
        ipBlockList: 'Lista de IPs bloqueados',
        fileExtBlockList: 'Lista de extensões de arquivo bloqueadas',
        urlWhiteList: 'Lista de URLs permitidas',
        urlBlockList: 'Lista de URLs bloqueadas',
        argsCheck: 'Verificação de parâmetros GET',
        postCheck: 'Verificação de parâmetros POST',
        cookieBlockList: 'Lista de cookies bloqueados',

        dockerHelper:
            'O firewall do Linux "{0}" não pode desativar o mapeamento de portas do Docker. O aplicativo pode editar os parâmetros na página "App Store -> Instalados" para controlar se a porta será liberada.',
        quickJump: 'Acesso rápido',
        used: 'Usado',
        unUsed: 'Não usado',
        firewallHelper: 'Firewall do sistema {0}',
        firewallNotStart: 'O firewall do sistema não está habilitado atualmente. Habilite-o primeiro.',
        restartFirewallHelper: 'Esta operação reiniciará o firewall atual. Deseja continuar?',
        stopFirewallHelper: 'Isso fará com que o servidor perca a proteção de segurança. Deseja continuar?',
        startFirewallHelper:
            'Depois que o firewall for habilitado, a segurança do servidor será melhor protegida. Deseja continuar?',
        noPing: 'Desativar ping',
        noPingTitle: 'Desativar ping',
        noPingHelper: 'Isso desativará o ping, e o servidor não responderá ao ICMP. Deseja continuar?',
        onPingHelper: 'Isso ativará o ping, permitindo que hackers descubram seu servidor. Deseja continuar?',
        changeStrategy: 'Alterar a estratégia {0}',
        changeStrategyIPHelper1:
            'Altere a estratégia de endereço IP para [negar]. Após definir o endereço IP, o acesso ao servidor será proibido. Deseja continuar?',
        changeStrategyIPHelper2:
            'Altere a estratégia de endereço IP para [permitir]. Após definir o endereço IP, o acesso normal será restaurado. Deseja continuar?',
        changeStrategyPortHelper1:
            'Altere a política de porta para [bloquear]. Após definir a política de porta, o acesso externo será negado. Deseja continuar?',
        changeStrategyPortHelper2:
            'Altere a política de porta para [aceitar]. Após definir a política de porta, o acesso normal será restaurado. Deseja continuar?',
        stop: 'Parar',
        portFormatError: 'Este campo deve ser uma porta válida.',
        portHelper1: 'Várias portas, ex.: 8080 e 8081',
        portHelper2: 'Faixa de portas, ex.: 8080-8089',
        changeStrategyHelper:
            'Alterar a estratégia [{1}] {0} para [{2}]. Após a definição, {0} acessará {2} externamente. Deseja continuar?',
        portHelper: 'Várias portas podem ser inseridas, ex.: 80,81, ou faixas de portas, ex.: 80-88',
        strategy: 'Estratégia',
        accept: 'Aceitar',
        drop: 'Bloquear',
        anyWhere: 'Qualquer',
        address: 'IPs especificados',
        addressHelper: 'Suporta endereço IP ou segmento de IP',
        allow: 'Permitir',
        deny: 'Negar',
        addressFormatError: 'Este campo deve ser um endereço IP válido.',
        addressHelper1: 'Suporta endereço IP ou intervalo de IP. Por exemplo, "************" ou "***********/24".',
        addressHelper2: 'Para vários endereços IP, separe por vírgula. Por exemplo, "************, **********/24".',
        allIP: 'Todos os IPs',
        portRule: 'Regra | Regras',
        createPortRule: '@:commons.button.create @.lower:firewall.portRule',
        forwardRule: 'Regra de redirecionamento de porta | Regras de redirecionamento de porta',
        createForwardRule: '@:commons.button.create @:firewall.forwardRule',
        ipRule: 'Regra de IP | Regras de IP',
        createIpRule: '@:commons.button.create @:firewall.ipRule',
        userAgent: 'Filtro User-Agent',
        sourcePort: 'Porta de origem',
        targetIP: 'IP de destino',
        targetPort: 'Porta de destino',
        forwardHelper1:
            'Se você deseja redirecionar para a porta local, o IP de destino deve ser definido como "127.0.0.1".',
        forwardHelper2: 'Deixe o IP de destino em branco para redirecionar para a porta local.',
        forwardHelper3: 'Somente suporta redirecionamento de porta IPv4.',
    },
    runtime: {
        runtime: 'Runtime',
        workDir: 'Diretório de trabalho',
        create: 'Criar runtime',
        localHelper: 'O ambiente local precisa ser instalado manualmente',
        versionHelper: 'Versão do PHP, por exemplo, v8.0',
        buildHelper:
            'Quanto mais extensões, maior será o uso de CPU durante a criação da imagem. As extensões podem ser instaladas após a criação do ambiente.',
        openrestyWarn: 'É necessário atualizar o PHP para OpenResty versão ******** ou superior para usar',
        toupgrade: 'Atualizar',
        edit: 'Editar runtime',
        extendHelper:
            'Extensões não listadas podem ser inseridas e selecionadas manualmente. Por exemplo, digite "sockets" e escolha a primeira opção da lista suspensa para visualizar a lista de extensões.',
        rebuildHelper: 'Após editar as extensões, é necessário recriar a aplicação PHP para aplicar as alterações',
        rebuild: 'Recriar Aplicação PHP',
        source: 'Fonte de extensões PHP',
        ustc: 'Universidade de Ciência e Tecnologia da China',
        netease: 'Netease',
        aliyun: 'Alibaba Cloud',
        default: 'Padrão',
        tsinghua: 'Universidade Tsinghua',
        xtomhk: 'Estação de Espelhos XTOM (Hong Kong)',
        xtom: 'Estação de Espelhos XTOM (Global)',
        phpsourceHelper: 'Escolha uma fonte adequada de acordo com o ambiente de rede.',
        appPort: 'Porta da aplicação',
        externalPort: 'Porta externa',
        packageManager: 'Gerenciador de pacotes',
        codeDir: 'Diretório do código',
        appPortHelper: 'A porta usada pela aplicação.',
        externalPortHelper: 'A porta exposta para o ambiente externo.',
        runScript: 'Executar script',
        runScriptHelper:
            'A lista de comandos de inicialização é gerada a partir do arquivo package.json no diretório de origem.',
        open: 'Abrir',
        operatorHelper: 'A operação {0} será realizada no ambiente selecionado. Deseja continuar?',
        taobao: 'Taobao',
        tencent: 'Tencent',
        imageSource: 'Fonte da imagem',
        moduleManager: 'Gerenciamento de Módulos',
        module: 'Módulo',
        nodeOperatorHelper:
            'Deseja {0} o módulo {1}? Esta operação pode causar instabilidade no ambiente, confirme antes de prosseguir.',
        customScript: 'Comando de inicialização personalizado',
        customScriptHelper: 'Forneça um comando completo de inicialização. Por exemplo, "npm run start".',
        portError: `Evite repetir a mesma porta.`,
        systemRestartHelper:
            'Descrição do status: Interrupção - falha ao adquirir status devido à reinicialização do sistema.',
        javaScriptHelper:
            'Forneça um comando completo de inicialização. Por exemplo, "java -jar halo.jar -Xmx1024M -Xms256M".',
        javaDirHelper: 'O diretório deve conter arquivos jar, subdiretórios também são aceitos.',
        goHelper: 'Forneça um comando completo de inicialização. Por exemplo, "go run main.go" ou "./main".',
        goDirHelper: 'O diretório ou subdiretório deve conter arquivos Go ou binários.',
        pythonHelper:
            'Forneça um comando completo de inicialização. Por exemplo, "pip install -r requirements.txt && python manage.py runserver 0.0.0.0:5000".',
        dotnetHelper: 'Por favor, preencha o comando completo de inicialização, por exemplo, dotnet MyWebApp.dll',
        dirHelper: 'Nota: Preencha o caminho do diretório dentro do contêiner',
        concurrency: 'Esquema de Concorrência',
        loadStatus: 'Status de Carga',
    },
    process: {
        pid: 'ID do Processo',
        ppid: 'ID do Processo Pai',
        numThreads: 'Threads',
        memory: 'Memória',
        diskRead: 'Leitura de Disco',
        diskWrite: 'Escrita de Disco',
        netSent: 'Envio (uplink)',
        netRecv: 'Recebimento (downlink)',
        numConnections: 'Conexões',
        startTime: 'Hora de Início',
        state: 'Estado',
        running: 'Executando',
        sleep: 'Dormindo',
        stop: 'Parado',
        idle: 'Ocioso',
        zombie: 'Processo zumbi',
        wait: 'Aguardando',
        lock: 'Travado',
        blocked: 'Bloqueado',
        cmdLine: 'Comando de Inicialização',
        basic: 'Básico',
        mem: 'Memória',
        openFiles: 'Arquivos Abertos',
        env: 'Ambientes',
        noenv: 'Nenhum',
        net: 'Conexões de Rede',
        laddr: 'Endereço/porta de origem',
        raddr: 'Endereço/porta de destino',
        stopProcess: 'Encerrar',
        viewDetails: 'Detalhes',
        stopProcessWarn: 'Tem certeza de que deseja encerrar este processo (PID:{0})?',
        processName: 'Nome do Processo',
    },
    tool: {
        supervisor: {
            loadStatusErr: 'Falha ao recuperar o status do processo, verifique o status do serviço do supervisor.',
            notSupport:
                'Serviço Supervisor não detectado. Vá para a página da biblioteca de scripts para instalar manualmente',
            list: 'Processo daemon | Processos daemon',
            config: 'Configuração do Supervisor',
            primaryConfig: 'Localização do arquivo de configuração principal',
            notSupportCtl: `O supervisorctl não foi detectado. Vá para a página da biblioteca de scripts para instalar manualmente.`,
            user: 'Usuário',
            command: 'Comando',
            dir: 'Diretório',
            numprocs: 'Nº de processos',
            initWarn:
                'Isso irá modificar o valor "files" na seção "[include"] do arquivo de configuração principal. O diretório de outros arquivos de configuração será: "{diretório de instalação do 1Panel}/1panel/tools/supervisord/supervisor.d/".',
            operatorHelper: 'A operação {1} será realizada em {0}, deseja continuar? ',
            uptime: 'Tempo de execução',
            notStartWarn: `O Supervisor não foi iniciado. Inicie-o primeiro.`,
            serviceName: 'Nome do serviço',
            initHelper:
                'O serviço Supervisor foi detectado, mas não está inicializado. Clique no botão de inicialização na barra de status superior para configurá-lo.',
            serviceNameHelper:
                'Nome do serviço do Supervisor gerenciado pelo systemctl, geralmente supervisor ou supervisord',
            restartHelper:
                'Isso reiniciará o serviço após a inicialização, o que fará com que todos os processos daemon existentes sejam interrompidos.',
            RUNNING: 'Executando',
            STOPPED: 'Parado',
            STOPPING: 'Parando',
            STARTING: 'Iniciando',
            FATAL: 'Falha ao iniciar',
            BACKOFF: 'Exceção ao iniciar',
            ERROR: 'Erro',
            statusCode: 'Código de status',
            manage: 'Gerenciamento',
            autoRestart: 'Reinicialização Automática',
            EXITED: 'Saiu',
            autoRestartHelper: 'Se o programa falhar, reiniciar automaticamente',
            autoStart: 'Início Automático',
            autoStartHelper: 'Se o serviço deve ser iniciado automaticamente após o Supervisor iniciar',
        },
    },
    xpack: {
        expiresTrialAlert:
            'Lembrete: Sua versão de avaliação profissional expirará em {0} dias. Após isso, todas as funcionalidades da versão profissional não estarão mais disponíveis. Por favor, renove ou faça upgrade para a versão oficial a tempo.',
        expiresAlert:
            'Lembrete: Sua licença profissional expirará em {0} dias. Após isso, todas as funcionalidades da versão profissional não estarão mais disponíveis. Por favor, renove sua licença para garantir o uso contínuo.',
        menu: 'Pro',
        upage: 'Construtor de Sites com IA',
        app: {
            app: 'APP',
            title: 'Apelido do Painel',
            titleHelper: 'O alias do painel é usado para exibição no APP (alias do painel padrão)',
            qrCode: 'QR Code',
            apiStatusHelper: 'O APP do Painel precisa habilitar a funcionalidade da interface API',
            apiInterfaceHelper:
                'Suporta acesso à interface de API do painel (essa funcionalidade precisa ser ativada no aplicativo do painel)',
            apiInterfaceHelper1:
                'O acesso ao aplicativo do painel requer que o visitante seja adicionado à lista de permissões; para IPs não fixos, recomenda-se adicionar 0.0.0.0/0(todos os IPv4), ::/0 (todos os IPv6)',
            qrCodeExpired: 'Tempo de atualização',
            apiLeakageHelper: 'Não revele o QR code. Garanta que ele seja usado apenas em ambientes confiáveis.',
        },
        waf: {
            name: 'WAF',
            blackWhite: 'Lista negra e branca',
            globalSetting: 'Configurações globais',
            websiteSetting: 'Configurações do site',
            blockRecords: 'Registros de bloqueio',
            world: 'Mundo',
            china: 'China',
            intercept: 'Interceptar',
            request: 'Requisição',
            count4xx: 'Quantidade de 4xx',
            count5xx: 'Quantidade de 5xx',
            todayStatus: 'Status de hoje',
            reqMap: 'Mapa de interceptação (30 dias)',
            resource: 'Origem',
            count: 'Quantidade',
            hight: 'Alto',
            low: 'Baixo',
            reqCount: 'Requisições',
            interceptCount: 'Número de interceptações',
            requestTrends: 'Tendências de requisições (últimos 7 dias)',
            interceptTrends: 'Tendências de interceptação (últimos 7 dias)',
            whiteList: 'Lista branca',
            blackList: 'Lista negra',
            ipBlackListHelper: 'Endereços IP na lista negra estão bloqueados de acessar o site',
            ipWhiteListHelper: 'Endereços IP na lista branca contornam todas as restrições',
            uaBlackListHelper: 'Requisições com valores de User-Agent na lista negra serão bloqueadas',
            uaWhiteListHelper: 'Requisições com valores de User-Agent na lista branca contornam todas as restrições',
            urlBlackListHelper: 'Requisições para URLs na lista negra serão bloqueadas',
            urlWhiteListHelper: 'Requisições para URLs na lista branca contornam todas as restrições',
            ccHelper:
                'Se um site receber mais de {1} requisições do mesmo IP dentro de {0} segundos, o IP será bloqueado por {2}',
            blockTime: 'Duração do bloqueio',
            attackHelper:
                'Se as interceptações acumuladas excederem {1} dentro de {0} segundos, o IP será bloqueado por {2}',
            notFoundHelper:
                'Se requisições acumuladas retornarem erros 404 mais de {1} vezes dentro de {0} segundos, o IP será bloqueado por {2}',
            frequencyLimit: 'Limite de frequência',
            regionLimit: 'Limite de região',
            defaultRule: 'Regras padrão',
            accessFrequencyLimit: 'Limite de frequência de acesso',
            attackLimit: 'Limite de frequência de ataque',
            notFoundLimit: 'Limite de frequência de 404',
            urlLimit: 'Limite de frequência de URL',
            urlLimitHelper: 'Defina a frequência de acesso para uma única URL',
            sqliDefense: 'Proteção contra injeção SQL',
            sqliHelper: 'Detectar injeção SQL em requisições e bloqueá-las',
            xssHelper: 'Detectar XSS em requisições e bloqueá-las',
            xssDefense: 'Proteção contra XSS',
            uaDefense: 'Regras de User-Agent maliciosos',
            uaHelper: 'Inclui regras para identificar bots maliciosos comuns',
            argsDefense: 'Regras de parâmetros maliciosos',
            argsHelper: 'Bloqueia requisições que contenham parâmetros maliciosos',
            cookieDefense: 'Regras de cookies maliciosos',
            cookieHelper: 'Proíbe cookies maliciosos de serem carregados nas requisições',
            headerDefense: 'Regras de cabeçalhos maliciosos',
            headerHelper: 'Proíbe requisições que contenham cabeçalhos maliciosos',
            httpRule: 'Regras de método de requisição HTTP',
            httpHelper:
                'Defina os tipos de métodos permitidos para acessar. Se você deseja restringir certos tipos de acesso, desative esse tipo de botão. Por exemplo: se apenas o acesso do tipo GET for permitido, desative todos os outros botões, exceto o GET',
            geoRule: 'Restrições de acesso regional',
            geoHelper:
                'Restringe o acesso ao seu site a partir de certas regiões, por exemplo: se o acesso for permitido a partir da China continental, então requisições de fora da China continental serão bloqueadas',
            ipLocation: 'Localização do IP',
            action: 'Ação',
            ruleType: 'Tipo de ataque',
            ipHelper: 'Digite o endereço IP',
            attackLog: 'Log de ataques',
            rule: 'Regra',
            ipArr: 'Intervalo IPV4',
            ipStart: 'IP de início',
            ipEnd: 'IP de fim',
            ipv4: 'IPv4',
            ipv6: 'IPv6',
            urlDefense: 'Regras de URL',
            urlHelper: 'URL proibida',
            dirFilter: 'Filtro de diretório',
            sqlInject: 'Injeção de SQL',
            xss: 'XSS',
            phpExec: 'Execução de script PHP',
            oneWordTrojan: 'Cavalo de Troia de uma palavra',
            appFilter: 'Filtragem de diretórios perigosos',
            webshell: 'Webshell',
            args: 'Parâmetros maliciosos',
            protocolFilter: 'Filtro de protocolo',
            javaFilter: 'Filtragem de arquivos Java perigosos',
            scannerFilter: 'Filtro de scanner',
            escapeFilter: 'Filtro de escape',
            customRule: 'Regras personalizadas',
            httpMethod: 'Filtro de método HTTP',
            fileExt: 'Limite de extensão de arquivo',
            fileExtHelper: 'Extensões de arquivos proibidas para upload',
            deny: 'Proibido',
            allow: 'Permitir',
            field: 'Objeto',
            pattern: 'Condição',
            ruleContent: 'Conteúdo',
            contain: 'incluir',
            equal: 'igual',
            regex: 'expressão regular',
            notEqual: 'Diferente de',
            customRuleHelper: 'Realize ações com base nas condições especificadas',
            actionAllow: 'Permitir',
            blockIP: 'Bloquear IP',
            code: 'Código de status retornado',
            noRes: 'Desconectar (444)',
            badReq: 'Parâmetros inválidos (400)',
            forbidden: 'Acesso proibido (403)',
            serverErr: 'Erro no servidor (500)',
            resHtml: 'Página de resposta',
            allowHelper: 'Permitir acesso pulará as regras subsequentes do WAF, use com cautela',
            captcha: 'verificação humano-máquina',
            fiveSeconds: 'Verificação de 5 segundos',
            location: 'Região',
            redisConfig: 'Configuração do Redis',
            redisHelper: 'Ativar o Redis para persistir IPs temporariamente bloqueados',
            wafHelper: 'Todos os sites perderão proteção após o fechamento',
            attackIP: 'IP atacante',
            attackParam: 'Detalhes do ataque',
            execRule: 'Regra atingida',
            acl: 'ACL',
            sql: 'Injeção de SQL',
            cc: 'Limite de frequência de acesso',
            isBlocking: 'Bloqueado',
            isFree: 'Desbloqueado',
            unLock: 'Desbloquear',
            unLockHelper: 'Você quer desbloquear o IP: {0}?',
            saveDefault: 'Salvar padrão',
            saveToWebsite: 'Aplicar ao site',
            saveToWebsiteHelper: 'Aplicar as configurações atuais a todos os sites?',
            websiteHelper:
                'Aqui estão as configurações padrão para criar um site. As modificações precisam ser aplicadas ao site para terem efeito',
            websiteHelper2:
                'Aqui estão as configurações padrão para criar um site. Por favor, modifique a configuração específica no site',
            ipGroup: 'Grupo de IP',
            ipGroupHelper:
                'Um IP ou segmento de IP por linha, suporta IPv4 e IPv6, por exemplo: *********** ou ***********/24',
            ipBlack: 'Lista negra de IP',
            openRestyAlert: 'A versão do OpenResty precisa ser superior a {0}',
            initAlert:
                'A inicialização é necessária para o primeiro uso, o arquivo de configuração do site será modificado e a configuração original do WAF será perdida. Por favor, faça backup do OpenResty com antecedência',
            initHelper:
                'A operação de inicialização apagará a configuração WAF existente. Tem certeza de que deseja inicializar?',
            mainSwitch: 'Interruptor principal',
            websiteAlert: 'Por favor, crie um site primeiro',
            defaultUrlBlack: 'Regras de URL',
            htmlRes: 'Página de interceptação',
            urlSearchHelper: 'Por favor, insira a URL para suportar a pesquisa difusa',
            toCreate: 'Criar',
            closeWaf: 'Fechar WAF',
            closeWafHelper: 'Fechar o WAF fará com que o site perca a proteção, você deseja continuar?',
            addblack: 'Negro',
            addwhite: 'Adicionar branco',
            addblackHelper: 'Adicionar IP:{0} à lista negra padrão?',
            addwhiteHelper: 'Adicionar IP:{0} à lista branca padrão?',
            defaultUaBlack: 'Regra do User-Agent',
            defaultIpBlack: 'Grupo de IPs Maliciosos',
            cookie: 'Regras de Cookies',
            urlBlack: 'Lista negra de URLs',
            uaBlack: 'Lista negra de User-Agent',
            attackCount: 'Limite de frequência de ataques',
            fileExtCheck: 'Limite de upload de arquivos',
            geoRestrict: 'Restrição de acesso regional',
            attacklog: 'Registro de Interceptação',
            unknownWebsite: 'Acesso a nome de domínio não autorizado',
            geoRuleEmpty: 'A região não pode estar vazia',
            unknown: 'Site não existe',
            geo: 'Restrição Regional',
            revertHtml: 'Você deseja restaurar {0} como a página padrão?',
            five_seconds: 'Verificação de 5 segundos',
            header: 'Regras de cabeçalho',
            methodWhite: 'Regras HTTP',
            expiryDate: 'Data de expiração',
            expiryDateHelper: 'Após passar na verificação, não será mais verificado dentro do período de validade',
            defaultIpBlackHelper: 'Alguns IPs maliciosos coletados da Internet para impedir o acesso',
            notFoundCount: 'Limite de Frequência 404',
            matchValue: 'Valor de correspondência',
            headerName: 'Suporta inglês, números, -, comprimento de 3-30',
            cdnHelper: 'Sites que usam CDN podem abrir aqui para obter o IP de origem correto',
            clearLogWarn: 'Limpar o log não será possível, você deseja continuar?',
            commonRuleHelper: 'A regra é correspondência difusa',
            blockIPHelper:
                'IPs bloqueados são armazenados temporariamente no OpenResty e serão desbloqueados quando você reiniciar o OpenResty. Eles podem ser bloqueados permanentemente através da função de bloqueio',
            addWhiteUrlHelper: 'Adicionar URL {0} à lista branca?',
            dashHelper:
                'A versão comunitária também pode usar as funções nas configurações globais e configurações de site',
            wafStatusHelper: 'O WAF não está ativado, por favor, ative-o nas configurações globais',
            ccMode: 'Modo',
            global: 'Modo Global',
            uriMode: 'Modo URL',
            globalHelper:
                'Modo Global: Ativado quando o número total de solicitações para qualquer URL dentro de uma unidade de tempo excede o limite',
            uriModeHelper:
                'Modo URL: Ativado quando o número de solicitações para uma única URL dentro de uma unidade de tempo excede o limite',

            ip: 'Lista negra de IPs',
            globalSettingHelper:
                'Configurações com a tag [Website] precisam ser ativadas em [Configurações do Website], e as configurações globais são apenas as configurações padrão para sites recém-criados',
            globalSettingHelper2:
                'As configurações precisam ser ativadas tanto em [Configurações Globais] quanto em [Configurações do Website] ao mesmo tempo',
            urlCCHelper: 'Mais de {1} solicitações para este URL dentro de {0} segundos, bloqueando este IP {2}',
            urlCCHelper2: 'O URL não pode conter parâmetros',
            notContain: 'Não contém',
            urlcc: 'Limitação de frequência de URL',
            method: 'Tipo de solicitação',
            addIpsToBlock: 'Bloquear IPs em massa',
            addUrlsToWhite: 'Adicionar URLs à lista branca em massa',
            noBlackIp: 'O IP já está bloqueado, não é necessário bloquear novamente',
            noWhiteUrl: 'O URL já está na lista branca, não é necessário adicionar novamente',
            spiderIpHelper:
                'Inclui Baidu, Bing, Google, 360, Shenma, Sogou, ByteDance, DuckDuckGo, Yandex. Fechar isso bloqueará todos os acessos de spiders.',
            spiderIp: 'Pool de IPs de spiders',
            geoIp: 'Biblioteca de endereços IP',
            geoIpHelper: 'Usado para determinar a localização geográfica do IP',
            stat: 'Relatório de ataques',
            statTitle: 'Relatório',
            attackIp: 'IP de ataque',
            attackCountNum: 'Número de ataques',
            percent: 'Porcentagem',
            addblackUrlHelper: 'Adicionar URL: {0} à lista negra padrão?',
            rce: 'Execução remota de código',
            software: 'Software',
            cveHelper: 'Contém vulnerabilidades comuns de software e frameworks',
            vulnCheck: 'Regras complementares',
            ssrf: 'Vulnerabilidade SSRF',
            afr: 'Leitura arbitrária de arquivos',
            ua: 'Acesso não autorizado',
            id: 'Dicas de informações',
            aa: 'Desvio de autenticação',
            dr: 'Trasversal de diretórios',
            xxe: 'Vulnerabilidade XXE',
            suid: 'Vulnerabilidade de serialização',
            dos: 'Vulnerabilidade de negação de serviço',
            afd: 'Download arbitrário de arquivos',
            sqlInjection: 'Injeção de SQL',
            afw: 'Escrita arbitrária de arquivos',
            il: 'Dicas de informações',
            clearAllLog: 'Limpar todos os logs',
            exportLog: 'Exportar logs',
            appRule: 'Regras de aplicação',
            appRuleHelper:
                'Regras comuns de aplicações, habilitar pode reduzir falsos positivos, um site pode usar apenas uma regra',
            logExternal: 'Excluir tipos de registro',
            ipWhite: 'Lista branca de IPs',
            urlWhite: 'Lista branca de URLs',
            uaWhite: 'Lista branca de User-Agent',
            logExternalHelper:
                'Tipos de registro excluídos não serão registrados nos logs, listas negras/brancas, restrições de acesso de região e regras personalizadas geram muitos logs, recomenda-se excluir',
            ssti: 'Ataque SSTI',
            crlf: 'Injeção CRLF',
            strict: 'Modo estrito',
            strictHelper: 'Usa regras mais rigorosas para validar solicitações',
            saveLog: 'Salvar log',
            remoteURLHelper: 'O URL remoto precisa garantir um IP por linha e nenhum outro caractere',
            notFound: 'Not Found (404)',
            serviceUnavailable: 'Serviço Indisponível (503)',
            gatewayTimeout: 'Tempo Limite da Porta de Entrada (504)',
            belongToIpGroup: 'Pertence ao Grupo de IP',
            notBelongToIpGroup: 'Não pertence ao Grupo de IP',
            unknownWebsiteKey: 'Domínio Desconhecido',
            special: 'Domínio Especial',
        },
        monitor: {
            name: 'Monitoramento de Websites',
            pv: 'Visualizações de Página',
            uv: 'Visitantes Únicos',
            flow: 'Fluxo de Tráfego',
            ip: 'IP',
            spider: 'Spider',
            visitors: 'Tendências de Visitantes',
            today: 'Hoje',
            last7days: 'Últimos 7 Dias',
            last30days: 'Últimos 30 Dias',
            uvMap: 'Mapa de Visitantes (30 dias)',
            qps: 'Requisições em Tempo Real (por minuto)',
            flowSec: 'Tráfego em Tempo Real (por minuto)',
            excludeCode: 'Excluir Códigos de Status',
            excludeUrl: 'Excluir URLs',
            excludeExt: 'Excluir Extensões',
            cdnHelper: 'Obter o IP real do Header fornecido pela CDN',
            reqRank: 'Ranking de Visitas',
            refererDomain: 'Domínio de Referência',
            os: 'Sistema',
            browser: 'Navegador/Cliente',
            device: 'Dispositivo',
            showMore: 'Mais',
            unknown: 'Outro',
            pc: 'Computador',
            mobile: 'Dispositivo Móvel',
            wechat: 'WeChat',
            machine: 'Máquina',
            tencent: 'Navegador Tencent',
            ucweb: 'Navegador UC',
            '2345explorer': 'Navegador 2345',
            huaweibrowser: 'Navegador Huawei',
            log: 'Logs de Requisição',
            statusCode: 'Código de Status',
            requestTime: 'Tempo de Resposta',
            flowRes: 'Tráfego de Resposta',
            method: 'Método de Requisição',
            statusCodeHelper: 'Insira o código de status acima',
            statusCodeError: 'Tipo de código de status inválido',
            methodHelper: 'Insira o método de requisição acima',
            all: 'Todos',
            baidu: 'Baidu',
            google: 'Google',
            bing: 'Bing',
            bytes: 'Manchetes do Dia',
            sogou: 'Sogou',
            failed: 'Erro',
            ipCount: 'Contagem de IPs',
            spiderCount: 'Requisições de Spider',
            averageReqTime: 'Tempo Médio de Resposta',
            totalFlow: 'Fluxo Total',
            logSize: 'Tamanho do Arquivo de Log',
            realIPType: 'Método de Obtenção do IP Real',
            fromHeader: 'Obter do Header HTTP',
            fromHeaders: 'Obter da lista de Headers',
            header: 'Header HTTP',
            cdnConfig: 'Configuração da CDN',
            xff1: 'Proxy de Primeiro Nível de X-Forwarded-For',
            xff2: 'Proxy de Segundo Nível de X-Forwarded-For',
            xff3: 'Proxy de Terceiro Nível de X-Forwarded-For',
            xffHelper:
                'Por exemplo: X-Forwarded-For: <cliente>,<proxy1>,<proxy2>,<proxy3>. O proxy superior pegará o último IP <proxy3>',
            headersHelper:
                'Obter o IP real dos Headers HTTP comuns fornecidos pela CDN, selecionando o primeiro valor disponível',
            monitorCDNHelper:
                'Modificar a configuração da CDN para monitoramento do site também atualizará as configurações de CDN do WAF',
            wafCDNHelper:
                'Modificar a configuração de CDN do WAF também atualizará as configurações de CDN do monitoramento do site',
            statusErr: 'Formato de código de status inválido',
            shenma: 'Busca Shenma',
            duckduckgo: 'DuckDuckGo',
            '360': 'Busca 360',
            excludeUri: 'Excluir URIs',
            top100Helper: 'Mostrar os 100 principais dados',
            logSaveDay: 'Período de Retenção de Logs (dias)',
            cros: 'Chrome OS',
            theworld: 'Navegador TheWorld',
            edge: 'Microsoft Edge',
            maxthon: 'Navegador Maxthon',
            monitorStatusHelper: 'O monitoramento não está habilitado, por favor habilite-o nas configurações',
            excludeIp: 'Excluir Endereços IP',
            excludeUa: 'Excluir User-Agent',
            remotePort: 'Porta Remota',
            unknown_browser: 'Desconhecido',
            unknown_os: 'Desconhecido',
            unknown_device: 'Desconhecido',
            logSaveSize: 'Tamanho Máximo de Salvamento de Log',
            logSaveSizeHelper: 'Este é o tamanho de salvamento de log para um único site',
            '360se': 'Navegador de Segurança 360',
            websites: 'Lista de Sites',
            trend: 'Estatísticas de Tendência',
            reqCount: 'Contagem de Solicitações',
            uriHelper: 'Você pode usar /test/* ou /*/index.php para excluir Uri',
        },
        tamper: {
            tamper: 'Proteção contra adulteração do site',
            ignoreTemplate: 'Excluir modelo de diretório',
            protectTemplate: 'Proteger modelo de arquivo',
            templateContent: 'Conteúdo do modelo',
            template: 'Modelo',
            tamperHelper1:
                'Para sites com implantação de um clique, recomenda-se ativar a função de proteção contra adulteração do diretório de aplicativos; se o site não funcionar corretamente ou houver falhas no backup e na recuperação, desative primeiro a função de proteção contra adulteração;',
            tamperHelper2:
                'Serão limitadas as operações de leitura, gravação, exclusão, alteração de permissões e proprietário de arquivos protegidos fora dos diretórios excluídos',
            tamperPath: 'Diretório protegido',
            tamperPathEdit: 'Modificar caminho',
            log: 'Registro de interceptação',
            totalProtect: 'Proteção total',
            todayProtect: 'Proteção de hoje',
            addRule: 'Adicionar regra',
            ignore: 'Excluir diretório',
            ignoreHelper: 'Um por linha, por exemplo: \ntmp\n./tmp',
            ignoreTemplateHelper:
                'Adicione os nomes das pastas a serem ignoradas, separando por vírgulas, por exemplo: tmp,cache',
            templateRule: 'Comprimento de 1-512, o nome não pode conter símbolos como {0}',
            ignoreHelper1: 'Adicione os nomes das pastas ou caminhos específicos a serem ignorados',
            ignoreHelper2: 'Para ignorar uma pasta específica, use um caminho relativo que comece com ./',
            protect: 'Proteger arquivo',
            protectHelper: 'Um por linha, por exemplo: \npng\n./test.css',
            protectTemplateHelper:
                'Adicione os nomes dos arquivos ou extensões a serem ignorados, separando por vírgulas, por exemplo: conf,.css',
            protectHelper1:
                'É possível especificar o nome do arquivo, a extensão ou um arquivo específico para proteção',
            protectHelper2: 'Para proteger um arquivo específico, use um caminho relativo que comece com ./',
            enableHelper:
                'A função de proteção contra adulteração será ativada para os seguintes sites para aumentar a segurança do site, deseja continuar?',
            disableHelper:
                'A função de proteção contra adulteração será desativada para os seguintes sites, deseja continuar?',
        },
        setting: {
            setting: 'Configurações do Painel',
            title: 'Descrição do Painel',
            titleHelper:
                'Será exibido na página de login do usuário (ex.: Painel de gerenciamento de manutenção de servidores Linux, recomendado 8-15 caracteres)',
            logo: 'Logo (Sem Texto)',
            logoHelper:
                'Será exibido no canto superior esquerdo da página de gerenciamento quando o menu estiver recolhido (tamanho recomendado da imagem: 82px*82px)',
            logoWithText: 'Logo (Com Texto)',
            logoWithTextHelper:
                'Será exibido no canto superior esquerdo da página de gerenciamento quando o menu estiver expandido (tamanho recomendado da imagem: 185px*55px)',
            favicon: 'Ícone do Site',
            faviconHelper: 'Ícone do site (tamanho recomendado da imagem: 16px*16px)',
            reUpload: 'Selecionar Arquivo',
            setDefault: 'Restaurar Padrão',
            setHelper: 'As configurações atuais serão salvas. Deseja continuar?',
            setDefaultHelper: 'Todas as configurações do painel serão restauradas para o padrão. Deseja continuar?',
            logoGroup: 'Logo',
            imageGroup: 'Imagem',
            loginImage: 'Imagem da Página de Login',
            loginImageHelper: 'Será exibida na página de login (Tamanho recomendado: 500x416px)',
            loginBgType: 'Tipo de Fundo da Página de Login',
            loginBgImage: 'Imagem de Fundo da Página de Login',
            loginBgImageHelper:
                'Será exibida como imagem de fundo na página de login (Tamanho recomendado: 1920x1080px)',
            loginBgColor: 'Cor de Fundo da Página de Login',
            loginBgColorHelper: 'Será exibida como cor de fundo na página de login',
            image: 'Imagem',
            bgColor: 'Cor de Fundo',
            loginGroup: 'Página de login',
            loginBtnLinkColor: 'Cor do botão/link',
            loginBtnLinkColorHelper: 'Será exibido como a cor do botão/link na página de login',
        },
        helper: {
            wafTitle1: 'Mapa de Interceptação',
            wafContent1: 'Exibe a distribuição geográfica das interceptações nos últimos 30 dias',
            wafTitle2: 'Restrições de Acesso Regional',
            wafContent2: 'Restringe o acesso ao site com base em localizações geográficas',
            wafTitle3: 'Página de Interceptação Personalizada',
            wafContent3: 'Crie uma página personalizada para exibir após uma solicitação ser interceptada',
            wafTitle4: 'Regras Personalizadas (ACL)',
            wafContent4: 'Intercepta solicitações com base em regras personalizadas',

            tamperTitle1: 'Monitoramento de Integridade de Arquivos',
            tamperContent1:
                'Monitora a integridade dos arquivos do site, incluindo arquivos principais, scripts e arquivos de configuração.',
            tamperTitle2: 'Varredura e Detecção em Tempo Real',
            tamperContent2:
                'Detecta arquivos anormais ou alterados por meio de varredura em tempo real do sistema de arquivos do site.',
            tamperTitle3: 'Configurações de Permissão de Segurança',
            tamperContent3:
                'Restringe o acesso aos arquivos do site através de configurações adequadas de permissões e políticas de controle de acesso, reduzindo a superfície de ataque.',
            tamperTitle4: 'Registro e Análise',
            tamperContent4:
                'Registra logs de acesso e operações em arquivos para auditoria e análise posteriores, ajudando administradores a identificar ameaças de segurança potenciais.',

            settingTitle1: 'Mensagem de Boas-vindas Personalizada',
            settingContent1: 'Defina uma mensagem de boas-vindas personalizada na página de login do 1Panel.',
            settingTitle2: 'Logo Personalizado',
            settingContent2: 'Permite o upload de imagens de logotipo contendo nomes de marcas ou outros textos.',
            settingTitle3: 'Ícone Personalizado do Site',
            settingContent3:
                'Permite o upload de ícones personalizados para substituir o ícone padrão do navegador, melhorando a experiência do usuário.',

            monitorTitle1: 'Tendência de Visitantes',
            monitorContent1: 'Estatísticas e exibição das tendências de visitantes do site',
            monitorTitle2: 'Mapa de Visitantes',
            monitorContent2: 'Estatísticas e exibição da distribuição geográfica dos visitantes do site',
            monitorTitle3: 'Estatísticas de Acesso',
            monitorContent3:
                'Estatísticas sobre informações de solicitação do site, incluindo robôs de busca, dispositivos de acesso, status das solicitações, etc.',
            monitorTitle4: 'Monitoramento em Tempo Real',
            monitorContent4:
                'Monitoramento em tempo real de informações de solicitações do site, incluindo número de solicitações, tráfego, etc.',

            alertTitle1: 'Alertas por SMS',
            alertContent1:
                'Quando houver uso anormal de recursos do servidor, expiração de sites e certificados, nova versão disponível, expiração de senha, entre outros, os usuários serão notificados via SMS para garantir um processamento oportuno.',
            alertTitle2: 'Registro de Alertas',
            alertContent2:
                'Oferece aos usuários a função de visualizar logs de alertas, facilitando o rastreamento e a análise de eventos históricos de alerta.',
            alertTitle3: 'Configurações de Alertas',
            alertContent3:
                'Permite que os usuários configurem números de telefone personalizados, frequência de envio diário e horários de envio, facilitando a criação de alertas mais adequados.',

            nodeTitle1: 'Adição de Nó com Um Clique',
            nodeContent1: 'Integre rapidamente vários nós de servidor',
            nodeTitle2: 'Atualização em Lote',
            nodeContent2: 'Sincronize e atualize todos os nós com uma única operação',
            nodeTitle3: 'Monitoramento de Status do Nó',
            nodeContent3: 'Acompanhe em tempo real o status operacional de cada nó',
            nodeTitle4: 'Conexão Remota Rápida',
            nodeContent4: 'Conecte-se diretamente a terminais remotos de nós com um clique',

            fileExchangeTitle1: 'Transmissão de Autenticação por Chave',
            fileExchangeContent1: 'Autentique via chaves SSH para garantir a segurança da transmissão.',
            fileExchangeTitle2: 'Sincronização de Arquivos Eficiente',
            fileExchangeContent2:
                'Sincronize apenas o conteúdo alterado para melhorar significativamente a velocidade e estabilidade da transmissão.',
            fileExchangeTitle3: 'Suporte a Troca Entre Múltiplos Nós',
            fileExchangeContent3:
                'Transfira facilmente arquivos de projeto entre diferentes nós, gerencie vários servidores de forma flexível.',

            appTitle1: 'Gerenciamento Flexível do Painel',
            appContent1: 'Gerencie facilmente seu servidor 1Panel a qualquer hora e em qualquer lugar.',
            appTitle2: 'Informações Completas do Serviço',
            appContent2:
                'Gerencie aplicativos, sites, Docker, bancos de dados, etc. pelo app móvel e crie rapidamente aplicativos e sites.',
            appTitle3: 'Monitoramento Anômalo em Tempo Real',
            appContent3:
                'Veja o status do servidor, monitoramento de segurança WAF, estatísticas de visitas ao site e saúde dos processos em tempo real no aplicativo móvel.',

            clusterTitle1: 'Implantação Mestre-Escravo',
            clusterContent1:
                'Suporta a criação de instâncias mestre-escravo MySQL/Postgres/Redis em diferentes nós, completando automaticamente a associação e inicialização mestre-escravo',
            clusterTitle2: 'Gestão Mestre-Escravo',
            clusterContent2:
                'Página unificada para gerir centralmente vários nós mestre-escravo, visualizar seus papéis, status de execução, etc.',
            clusterTitle3: 'Estado de Replicação',
            clusterContent3:
                'Exibe o estado de replicação mestre-escravo e informações de atraso, auxiliando na resolução de problemas de sincronização',
        },
        node: {
            master: 'Nó Principal',
            masterBackup: 'Backup do Nó Mestre',
            backupNode: 'Nó de Backup',
            backupFrequency: 'Frequência de Backup (horas)',
            backupCopies: 'Número de cópias de backup a reter',
            noBackupNode:
                'O nó de backup está vazio atualmente. Selecione um nó de backup para salvar e tente novamente!',
            masterBackupAlert:
                'O backup do nó mestre não está configurado atualmente. Para garantir a segurança dos dados, configure um nó de backup o mais rápido possível para facilitar a troca manual para um novo nó mestre em caso de falha.',
            node: 'Nó',
            addr: 'Endereço',
            nodeUnhealthy: 'Estado do nó anormal',
            deletedNode: 'O nó excluído {0} não suporta atualmente operações de atualização!',
            nodeUnhealthyHelper:
                'Estado anormal do nó detectado. Por favor verifique em [Gestão de Nós] e tente novamente!',
            nodeUnbind: 'Nó não vinculado à licença',
            nodeUnbindHelper:
                'Detectamos que este nó não está vinculado a uma licença. Por favor vincule no menu [Configurações do Painel - Licença] e tente novamente!',
            memTotal: 'Memória Total',
            nodeManagement: 'Gerenciamento de Nó',
            addNode: 'Adicionar Nó',
            connInfo: 'Informações de Conexão',
            nodeInfo: 'Informações do Nó',
            syncInfo: 'Sincronização de dados,',
            syncHelper: 'Quando os dados do nó mestre mudam, são sincronizados em tempo real para este nó filho,',
            syncBackupAccount: 'Configurações de conta de backup',
            syncWithMaster:
                'Após atualizar para Pro, todos os dados serão sincronizados por padrão. As políticas de sincronização podem ser ajustadas manualmente no gerenciamento de nós.',
            syncProxy: 'Configurações de proxy do sistema',
            syncProxyHelper: 'Sincronizar configurações de proxy do sistema requer reinicialização do Docker',
            syncProxyHelper1: 'Reiniciar o Docker pode afetar os serviços de contêiner em execução.',
            syncProxyHelper2: 'Você pode reiniciar manualmente na página Contêineres - Configuração.',
            syncProxyHelper3:
                'Sincronizar configurações de proxy do sistema requer reinicialização do Docker, o que pode afetar os serviços de contêiner em execução',
            syncProxyHelper4:
                'Sincronizar configurações de proxy do sistema requer reinicialização do Docker. Você pode reiniciar manualmente mais tarde na página Contêineres - Configuração.',
            syncCustomApp: 'Sincronizar Repositório de Aplicativos Personalizados',
            syncAlertSetting: 'Configurações de alerta do sistema',
            syncNodeInfo: 'Dados básicos do nó,',
            nodeSyncHelper: 'A sincronização das informações do nó irá sincronizar as seguintes informações:',
            nodeSyncHelper1: '1. Informações da conta de backup pública',
            nodeSyncHelper2: '2. Informações de conexão entre o nó principal e os sub-nós',

            nodeCheck: 'Verificação de disponibilidade',
            checkSSH: 'Verificar conexão SSH do nó',
            checkUserPermission: 'Verificar permissões de usuário do nó',
            isNotRoot: 'Detectado que sudo sem senha não é suportado neste nó e o usuário atual não é root',
            checkLicense: 'Verificar status da licença do nó',
            checkService: 'Verificar informações de serviço existentes no nó',
            checkPort: 'Verificar acessibilidade da porta do nó',
            panelExist:
                'Detectado que este nó está executando o serviço 1Panel V1. Atualize para V2 usando o script de migração antes de adicionar.',
            coreExist:
                'O nó atual já está habilitado como nó mestre e não pode ser adicionado diretamente como nó escravo. Por favor, faça o downgrade para nó escravo primeiro antes de adicionar, consulte a documentação para detalhes.',
            agentExist:
                'Detectado que 1panel-agent já está instalado neste nó. Continuar irá reter os dados existentes e apenas substituir o serviço 1panel-agent.',
            oldDataExist:
                'Detectados dados históricos do 1Panel V2 neste nó. As seguintes informações serão usadas para sobrescrever as configurações atuais:',
            errLicense: 'A licença vinculada a este nó está indisponível. Por favor verifique e tente novamente!',
            errNodePort:
                'A porta do nó [ {0} ] foi detectada como inacessível. Verifique se o firewall ou grupo de segurança liberou esta porta.',

            reinstallHelper: 'Reinstalar o nó {0}, deseja continuar?',
            unhealthyCheck: 'Verificação Anormal',
            fixOperation: 'Operação de Correção',
            checkName: 'Item de Verificação',
            checkSSHConn: 'Verificar Disponibilidade da Conexão SSH',
            fixSSHConn: 'Edite o nó manualmente para confirmar as informações de conexão',
            checkConnInfo: 'Verificar Informações de Conexão do Agente',
            checkStatus: 'Verificar Disponibilidade do Serviço do Nó',
            fixStatus: 'Execute "systemctl status 1panel-agent.service" para verificar se o serviço está em execução.',
            checkAPI: 'Verificar Disponibilidade da API do Nó',
            fixAPI: 'Verifique os logs do nó e confirme se as portas do firewall estão devidamente abertas.',
            forceDelete: 'Excluir Forçadamente',
            operateHelper: 'A operação {0} será realizada nos seguintes nós, deseja continuar?',
            forceDeleteHelper:
                'Excluir forçadamente ignorará erros de exclusão do nó e removerá os metadados do banco de dados',
            uninstall: 'Excluir dados do nó',
            uninstallHelper: 'Isso excluirá todos os dados relacionados ao 1Panel do nó. Escolha com cuidado!',
            baseDir: 'Diretório de Instalação',
            baseDirHelper: 'Quando o diretório de instalação está vazio, será instalado por padrão no diretório /opt',
            nodePort: 'Porta do Nó',
            offline: 'Modo offline',
            freeCount: 'Cota gratuita [{0}]',
            offlineHelper: 'Usado quando o nó está em ambiente offline',
        },
        customApp: {
            name: 'Repositório de Aplicativos Personalizados',
            appStoreType: 'Fonte do Pacote da App Store',
            appStoreUrl: 'URL do Repositório',
            local: 'Caminho Local',
            remote: 'Link Remoto',
            imagePrefix: 'Prefixo de Imagem',
            imagePrefixHelper:
                'Função: Personalize o prefixo da imagem e modifique o campo de imagem no arquivo compose. Por exemplo, quando o prefixo da imagem é configurado como 1panel/custom, o campo de imagem do MaxKB mudará para 1panel/custom/maxkb:v1.10.0',
            closeHelper: 'Cancelar o uso do repositório de aplicativos personalizados',
            appStoreUrlHelper: 'Apenas formato .tar.gz é suportado',
            postNode: 'Sincronizar para subnó',
            postNodeHelper:
                'Sincronize o pacote da loja personalizada para tmp/customApp/apps.tar.gz no diretório de instalação do sub-nó',
            nodes: 'Selecionar Nós',
            selectNode: 'Selecionar Node',
            selectNodeError: 'Por favor, selecione um nó',
            licenseHelper: 'A versão Pro suporta o recurso de repositório de aplicativos personalizados',
        },
        alert: {
            isAlert: 'Alerta',
            alertCount: 'Contagem de Alertas',
            clamHelper: 'Dispara alerta via ao detectar arquivos infectados durante a varredura',
            cronJobHelper: 'Dispara alerta via ao falhar na execução de tarefas',
            licenseHelper: 'A versão profissional suporta alertas via SMS',
            alertCountHelper: 'Frequência máxima diária de alertas',
            alert: 'Alerta por SMS',
            logs: 'Registros de Alerta',
            list: 'Lista de Alertas',
            addTask: 'Criar Alerta',
            editTask: 'Editar Alerta',
            alertMethod: 'Método',
            alertMsg: 'Mensagem de Alerta',
            alertRule: 'Regras de Alerta',
            titleSearchHelper: 'Digite o título do alerta para busca aproximada',
            taskType: 'Tipo',
            ssl: 'Expiração do Certificado (SSL)',
            siteEndTime: 'Expiração do Site',
            panelPwdEndTime: 'Expiração da Senha do Painel',
            panelUpdate: 'Nova Versão do Painel Disponível',
            cpu: 'Alerta de CPU do Servidor',
            memory: 'Alerta de Memória do Servidor',
            load: 'Alerta de Carga do Servidor',
            disk: 'Alerta de Disco do Servidor',
            website: 'Site',
            certificate: 'Certificado SSL',
            remainingDays: 'Dias Restantes',
            sendCount: 'Contagem de Envio',
            sms: 'SMS',
            wechat: 'WeChat',
            dingTalk: 'DingTalk',
            feiShu: 'FeiShu',
            mail: 'E-mail',
            weCom: 'WeCom',
            sendCountRulesHelper: 'Alertas totais enviados antes da expiração (uma vez por dia)',
            panelUpdateRulesHelper: 'Alertas totais enviados para nova versão do painel (uma vez por dia)',
            oneDaySendCountRulesHelper: 'Número máximo de alertas enviados por dia',
            siteEndTimeRulesHelper: 'Sites que nunca expiram não dispararão alertas',
            autoRenewRulesHelper:
                'Certificados com renovação automática ativada e menos de 31 dias restantes não dispararão alertas',
            panelPwdEndTimeRulesHelper:
                'Alertas de expiração da senha do painel não estão disponíveis se nenhuma expiração estiver definida',
            sslRulesHelper: 'Todos os Certificados SSL',
            diskInfo: 'Disco',
            monitoringType: 'Tipo de Monitoramento',
            autoRenew: 'Renovação Automática',
            useDisk: 'Uso de Disco',
            usePercentage: 'Porcentagem de Uso',
            changeStatus: 'Alterar Status',
            disableMsg:
                'Parar a tarefa de alerta impedirá que esta tarefa envie mensagens de alerta. Deseja continuar?',
            enableMsg:
                'Ativar a tarefa de alerta permitirá que esta tarefa envie mensagens de alerta. Deseja continuar?',
            useExceed: 'Uso Excedeu',
            useExceedRulesHelper: 'Dispara alerta quando o uso excede o valor configurado',
            cpuUseExceedAvg: 'O uso médio da CPU excede o valor especificado',
            memoryUseExceedAvg: 'O uso médio da memória excede o valor especificado',
            loadUseExceedAvg: 'O uso médio da carga excede o valor especificado',
            cpuUseExceedAvgHelper: 'O uso médio da CPU dentro do tempo especificado excede o valor especificado',
            memoryUseExceedAvgHelper: 'O uso médio da memória dentro do tempo especificado excede o valor especificado',
            loadUseExceedAvgHelper: 'O uso médio da carga dentro do tempo especificado excede o valor especificado',
            resourceAlertRulesHelper: 'Nota: Alertas contínuos em 30 minutos enviarão apenas um SMS',
            specifiedTime: 'Hora Especificada',
            deleteTitle: 'Excluir Alerta',
            deleteMsg: 'Tem certeza de que deseja excluir a tarefa de alerta?',

            allSslTitle: 'Alertas de Expiração de Todos os Certificados SSL do Site',
            sslTitle: 'Alerta de Expiração do Certificado SSL do Site {0}',
            allSiteEndTimeTitle: 'Alertas de Expiração de Todos os Sites',
            siteEndTimeTitle: 'Alerta de Expiração do Site {0}',
            panelPwdEndTimeTitle: 'Alerta de Expiração da Senha do Painel',
            panelUpdateTitle: 'Notificação de Nova Versão do Painel',
            cpuTitle: 'Alerta de Alto Uso de CPU',
            memoryTitle: 'Alerta de Alto Uso de Memória',
            loadTitle: 'Alerta de Alta Carga',
            diskTitle: 'Alerta de Alto Uso de Disco para o Diretório de Montagem {0}',
            allDiskTitle: 'Alerta de Alto Uso de Disco',

            timeRule: 'Tempo restante menor que {0} dias (se não tratado, será reenviado no próximo dia)',
            panelUpdateRule:
                'Envia um alerta quando uma nova versão do painel é detectada (se não tratado, será reenviado no próximo dia)',
            avgRule: 'Uso médio de {1} excede {2}% em {0} minutos, dispara alerta, envia {3} vezes por dia',
            diskRule:
                'Uso de disco para o diretório de montagem {0} excede {1}{2}, dispara alerta, envia {3} vezes por dia',
            allDiskRule: 'Uso de disco excede {0}{1}, dispara alerta, envia {2} vezes por dia',

            cpuName: 'CPU',
            memoryName: 'Memória',
            loadName: 'Carga',
            diskName: 'Disco',

            syncAlertInfo: 'Envio Manual',
            syncAlertInfoMsg: 'Deseja enviar a tarefa de alerta manualmente?',
            pushError: 'Envio Falhou',
            pushSuccess: 'Envio Bem-sucedido',
            syncError: 'Sincronização Falhou',
            success: 'Alerta Bem-sucedido',
            pushing: 'Enviando...',
            error: 'Falha no alerta',
            cleanLog: 'Limpar Registros',
            cleanAlertLogs: 'Limpar Registros de Alertas',
            daily: 'Contagem Diária de Alertas: {0}',
            cumulative: 'Contagem Acumulada de Alertas: {0}',
            clams: 'Varredura de Vírus',
            taskName: 'Nome da Tarefa',
            cronJobType: 'Tipo de Tarefa',
            clamPath: 'Diretório de Varredura',
            cronjob: 'Tarefa Cron',
            app: 'Backup do Aplicativo',
            web: 'Backup do Site',
            database: 'Backup do Banco de Dados',
            directory: 'Backup do Diretório',
            log: 'Registros de Backup',
            snapshot: 'Instantâneo do Sistema',
            clamsRulesHelper: 'Tarefas de varredura de vírus que exigem alertas por',
            cronJobRulesHelper: 'Este tipo de tarefa cron precisa ser configurado',
            clamsTitle: 'Tarefa de Varredura de Vírus 「 {0} 」 detectou arquivo infectado',
            cronJobAppTitle: 'Tarefa Cron - Falha no Backup do Aplicativo 「 {0} 」',
            cronJobWebsiteTitle: 'Tarefa Cron - Falha no Backup do Site 「 {0} 」',
            cronJobDatabaseTitle: 'Tarefa Cron - Falha no Backup do Banco de Dados 「 {0} 」',
            cronJobDirectoryTitle: 'Tarefa Cron - Falha no Backup do Diretório 「 {0} 」',
            cronJobLogTitle: 'Tarefa Cron - Falha nos Registros de Backup 「 {0} 」',
            cronJobSnapshotTitle: 'Tarefa Cron - Falha no Instantâneo do Sistema 「 {0} 」',
            cronJobShellTitle: 'Tarefa Cron - Falha no Script Shell 「 {0} 」',
            cronJobCurlTitle: 'Tarefa Cron - Falha no Acesso à URL 「 {0} 」',
            cronJobCutWebsiteLogTitle: 'Tarefa Cron - Falha no Corte do Registro do Site 「 {0} 」',
            cronJobCleanTitle: 'Tarefa Cron - Falha na Limpeza de Cache 「 {0} 」',
            cronJobNtpTitle: 'Tarefa Cron - Falha na Sincronização do Horário do Servidor 「 {0} 」',
            clamsRule: 'Varredura de vírus detectou arquivo infectado, enviado {0} vezes por dia',
            cronJobAppRule: 'Falha na tarefa de backup do aplicativo, enviada {0} vezes por dia',
            cronJobWebsiteRule: 'Falha na tarefa de backup do site, enviada {0} vezes por dia',
            cronJobDatabaseRule: 'Falha na tarefa de backup do banco de dados, enviada {0} vezes por dia',
            cronJobDirectoryRule: 'Falha na tarefa de backup do diretório, enviada {0} vezes por dia',
            cronJobLogRule: 'Falha na tarefa de backup de registros, enviada {0} vezes por dia',
            cronJobSnapshotRule: 'Falha na tarefa de backup de instantâneo, enviada {0} vezes por dia',
            cronJobShellRule: 'Falha na tarefa de script shell, enviada {0} vezes por dia',
            cronJobCurlRule: 'Falha na tarefa de acesso à URL, enviada {0} vezes por dia',
            cronJobCutWebsiteLogRule: 'Falha na tarefa de corte do registro do site, enviada {0} vezes por dia',
            cronJobCleanRule: 'Falha na tarefa de limpeza de cache, enviada {0} vezes por dia',
            cronJobNtpRule: 'Falha na tarefa de sincronização do horário do servidor, enviada {0} vezes por dia',
            alertSmsHelper: 'Limite de SMS: total de {0} mensagens, {1} já usadas',
            goBuy: 'Comprar Mais',
            phone: 'Telefone',
            phoneHelper: 'Forneça um número de telefone real para mensagens de alerta',
            dailyAlertNum: 'Número de alertas diários',
            dailyAlertNumHelper: 'Número total de alertas diários, até um máximo de 100 alertas',
            timeRange: 'Intervalo de Tempo',
            sendTimeRange: 'Intervalo de tempo para envio de',
            sendTimeRangeHelper: 'Pode enviar alerta dentro do intervalo de tempo {0}',
            to: '-',
            startTime: 'hora de início',
            endTime: 'hora de término',
            defaultPhone: 'Número de telefone padrão vinculado à conta de licença',
            noticeAlert: 'Alerta de Notificação',
            resourceAlert: 'Alerta de Recursos',
            agentOfflineAlertHelper:
                'Quando o alerta offline estiver ativado para o nó, o nó principal verificará a cada 30 minutos para executar as tarefas de alerta.',
            offline: 'Alerta Offline',
            offlineHelper:
                'Ao definir como alerta offline, o nó principal verificará a cada 30 minutos para executar tarefas de alerta.',
            offlineOff: 'Ativar Alerta Offline',
            offlineOffHelper:
                'Ao ativar o alerta offline, o nó principal verificará a cada 30 minutos para executar as tarefas de alerta.',
            offlineClose: 'Desativar Alerta Offline',
            offlineCloseHelper:
                'Ao desativar o alerta offline, os nós secundários deverão lidar com os alertas por conta própria. Certifique-se de que a conectividade de rede está boa para evitar falhas.',
            alertNotice: 'Notificação de Alerta',
            methodConfig: 'Configuração do Método de Notificação',
            commonConfig: 'Configuração Global',
            smsConfig: 'SMS',
            smsConfigHelper: 'Configure os números para notificação por SMS',
            emailConfig: 'E-mail',
            emailConfigHelper: 'Configure o serviço de envio de e-mail SMTP',
            deleteConfigTitle: 'Excluir Configuração de Alerta',
            deleteConfigMsg: 'Tem certeza de que deseja excluir a configuração de alerta?',
            test: 'Testar',
            alertTestOk: 'Notificação de teste bem-sucedida',
            alertTestFailed: 'Falha na notificação de teste',
            displayName: 'Nome de Exibição',
            sender: 'Endereço do Remetente',
            password: 'Senha',
            host: 'Servidor SMTP',
            port: 'Porta',
            encryption: 'Método de Criptografia',
            recipient: 'Destinatário',
            licenseTime: 'Lembrete de Expiração da Licença',
            licenseTimeTitle: 'Lembrete de Expiração da Licença',
            displayNameHelper: 'Nome exibido do remetente do e-mail',
            senderHelper: 'Endereço de e-mail usado para envio',
            passwordHelper: 'Código de autorização do serviço de e-mail',
            hostHelper: 'Endereço do servidor SMTP, ex: smtp.qq.com',
            portHelper: 'SSL geralmente usa 465, TLS geralmente usa 587',
            sslHelper: 'Se a porta SMTP for 465, normalmente é necessário SSL',
            tlsHelper: 'Se a porta SMTP for 587, normalmente é necessário TLS',
        },
        theme: {
            lingXiaGold: 'Ling Xia Gold',
            classicBlue: 'Azul Clássico',
            freshGreen: 'Verde Fresco',
            customColor: 'Cor do Tema Personalizada',
            setDefault: 'Restaurar Padrão',
            setDefaultHelper: 'O esquema de cores do tema será restaurado para o estado inicial. Deseja continuar?',
            setHelper: 'O esquema de cores do tema selecionado será salvo. Deseja continuar?',
        },
        exchange: {
            exchange: 'Troca de Arquivos',
            exchangeConfirm: 'Deseja transferir o arquivo/pasta {1} do nó {0} para o diretório {3} do nó {2}?',
        },
        cluster: {
            cluster: 'Alta Disponibilidade de Aplicações',
            name: 'Nome do Cluster',
            addCluster: 'Adicionar Cluster',
            installNode: 'Instalar Nó',
            master: 'Nó Mestre',
            slave: 'Nó Escravo',
            replicaStatus: 'Status Mestre-Escravo',
            unhealthyDeleteError:
                'O status do nó de instalação está anormal, verifique a lista de nós e tente novamente!',
            replicaStatusError: 'A aquisição do status está anormal, verifique o nó mestre.',
            masterHostError: 'O IP do nó mestre não pode ser 127.0.0.1',
        },
    },
};

export default {
    ...fit2cloudPtBrLocale,
    ...message,
};
