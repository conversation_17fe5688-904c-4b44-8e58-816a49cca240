import fit2cloudEnLocale from 'fit2cloud-ui-plus/src/locale/lang/en';

const message = {
    commons: {
        true: 'doğru',
        false: 'yanl<PERSON><PERSON>',
        colon: ': ',
        example: 'Örneğin, ',
        fit2cloud: 'FIT2CLOUD',
        lingxia: 'Ling<PERSON>',
        button: {
            run: '<PERSON><PERSON><PERSON><PERSON>tı<PERSON>',
            prev: '<PERSON><PERSON><PERSON>',
            next: '<PERSON><PERSON><PERSON>',
            create: '<PERSON><PERSON><PERSON><PERSON> ',
            add: 'E<PERSON> ',
            save: '<PERSON><PERSON> ',
            set: '<PERSON>yar<PERSON>',
            sync: 'Senkronize Et ',
            delete: 'Sil',
            edit: 'Düzenle ',
            enable: 'Etkinleştir',
            disable: '<PERSON><PERSON> Dışı Bırak',
            confirm: '<PERSON><PERSON><PERSON>',
            cancel: '<PERSON>pta<PERSON>',
            reset: 'Sıfırla',
            setDefault: 'Varsayılanı Geri Yükle',
            restart: 'Yeniden Başlat',
            conn: 'Bağlan',
            disconn: 'Bağlant<PERSON>y<PERSON> Ke<PERSON>',
            clean: 'Temizle',
            login: '<PERSON><PERSON><PERSON>',
            close: '<PERSON><PERSON><PERSON>',
            stop: 'Durdur',
            start: '<PERSON><PERSON><PERSON>',
            view: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
            watch: '<PERSON><PERSON>',
            handle: '<PERSON><PERSON><PERSON>',
            clone: '<PERSON><PERSON><PERSON>',
            expand: 'Genişlet',
            collapse: 'Daralt',
            log: 'Günlükleri görüntüle',
            back: 'Geri',
            backup: 'Yedekle',
            recover: 'Kurtar',
            retry: 'Tekrar Dene',
            upload: 'Yükle',
            download: 'İndir',
            init: 'Başlat',
            verify: 'Doğrula',
            saveAndEnable: 'Kaydet ve etkinleştir',
            import: 'İçe Aktar',
            export: 'Dışa Aktar',
            power: 'Yetkilendirme',
            search: 'Ara',
            refresh: 'Yenile',
            get: 'Al',
            upgrade: 'Yükselt',
            update: 'Güncelle',
            ignore: 'Yükseltmeyi yoksay',
            copy: 'Kopyala',
            random: 'Rastgele',
            install: 'Yükle',
            uninstall: 'Kaldır',
            fullscreen: 'Tam ekrana geç',
            quitFullscreen: 'Tam ekrandan çık',
            showAll: 'Tümünü Göster',
            hideSome: 'Bazılarını Gizle',
            agree: 'Kabul Et',
            notAgree: 'Kabul Etme',
            preview: 'Önizleme',
            open: 'Aç',
            notSave: 'Kaydetme',
            createNewFolder: 'Yeni klasör oluştur',
            createNewFile: 'Yeni dosya oluştur',
            helpDoc: 'Yardım Belgesi',
            bind: 'Bağla',
            unbind: 'Bağlantıyı Çöz',
            cover: 'kapla',
            skip: 'atla',
            fix: 'Düzelt',
            down: 'Durdur',
            up: 'Başlat',
            sure: 'Onayla',
            show: 'Göster',
            hide: 'Gizle',
        },
        operate: {
            start: 'Başlat',
            stop: 'Durdur',
            restart: 'Yeniden Başlat',
            reload: 'Yeniden Yükle',
            rebuild: 'Yeniden İnşa Et',
            sync: 'Senkronize Et',
            up: 'Yukarı',
            down: 'Aşağı',
            delete: 'Sil',
        },
        search: {
            timeStart: 'Başlangıç zamanı',
            timeEnd: 'Bitiş zamanı',
            timeRange: 'İle',
            dateStart: 'Başlangıç tarihi',
            dateEnd: 'Bitiş tarihi',
        },
        table: {
            all: 'Tümü',
            total: 'Toplam {0}',
            name: 'Ad',
            type: 'Tür',
            status: 'Durum',
            statusSuccess: 'Başarılı',
            statusFailed: 'Başarısız',
            statusWaiting: 'Bekliyor...',
            records: 'Kayıtlar',
            group: 'Grup',
            default: 'Varsayılan',
            createdAt: 'Oluşturulma zamanı',
            publishedAt: 'Yayınlanma zamanı',
            date: 'Tarih',
            updatedAt: 'Güncellenme zamanı',
            operate: 'İşlemler',
            message: 'Mesaj',
            description: 'Açıklama',
            interval: 'Aralık',
            user: 'Sahip',
            title: 'Başlık',
            port: 'Port',
            forward: 'İleri',
            protocol: 'Protokol',
            tableSetting: 'Tablo ayarı',
            refreshRate: 'Yenileme hızı',
            noRefresh: 'Yenileme yok',
            selectColumn: 'Sütun seç',
            local: 'yerel',
            serialNumber: 'Seri numarası',
            manageGroup: 'Grupları Yönet',
            backToList: 'Listeye Dön',
            keepEdit: 'Düzenlemeye Devam Et',
        },
        loadingText: {
            Upgrading: 'Sistem yükseltiliyor, lütfen bekleyin...',
            Restarting: 'Sistem yeniden başlatılıyor, lütfen bekleyin...',
            Recovering: 'Anlık görüntüden kurtarılıyor, lütfen bekleyin...',
            Rollbacking: 'Anlık görüntüden geri alınıyor, lütfen bekleyin...',
        },
        msg: {
            noneData: 'Veri mevcut değil',
            delete: 'Bu silme işlemi geri alınamaz. Devam etmek istiyor musunuz?',
            clean: 'Bu temizleme işlemi geri alınamaz. Devam etmek istiyor musunuz?',
            closeDrawerHelper: 'Sistem yaptığınız değişiklikleri kaydetmeyebilir. Devam etmek istiyor musunuz?',
            deleteSuccess: 'Başarıyla silindi',
            loginSuccess: 'Başarıyla giriş yapıldı',
            operationSuccess: 'Başarıyla tamamlandı',
            copySuccess: 'Başarıyla kopyalandı',
            notSupportOperation: 'Bu işlem desteklenmiyor',
            requestTimeout: 'İstek zaman aşımına uğradı, lütfen daha sonra tekrar deneyin',
            infoTitle: 'İpucu',
            notRecords: 'Mevcut görev için yürütme kaydı oluşturulmadı',
            sureLogOut: 'Çıkış yapmak istediğinizden emin misiniz?',
            createSuccess: 'Başarıyla oluşturuldu',
            updateSuccess: 'Başarıyla güncellendi',
            uploadSuccess: 'Başarıyla yüklendi',
            operateConfirm: 'İşlemden eminseniz, lütfen manuel olarak girin : ',
            inputOrSelect: 'Lütfen seçin veya girin',
            copyFailed: 'Kopyalama başarısız',
            operatorHelper:
                '"{0}" üzerinde "{1}" işlemi gerçekleştirilecek ve geri alınamaz. Devam etmek istiyor musunuz?',
            notFound: 'Üzgünüz, istediğiniz sayfa mevcut değil.',
            unSupportType: 'Mevcut dosya türü desteklenmiyor.',
            unSupportSize: 'Yüklenen dosya {0}Myi aşıyor, lütfen onaylayın!',
            fileExist: 'Dosya mevcut klasörde zaten var. Tekrar yükleme desteklenmiyor.',
            fileNameErr:
                'Sadece 1 ile 256 karakter arasında İngilizce, Çince, rakam veya nokta (.-_) içeren dosyalar yükleyebilirsiniz',
            confirmNoNull: '{0} değerinin boş olmadığından emin olun.',
            errPort: 'Yanlış port bilgisi, lütfen onaylayın!',
            remove: 'Kaldır',
            backupHelper: 'Mevcut işlem {0}i yedekleyecek. Devam etmek istiyor musunuz?',
            recoverHelper: '{0} dosyasından geri yükleniyor. Bu işlem geri alınamaz. Devam etmek istiyor musunuz?',
            refreshSuccess: 'Yenileme başarılı',
            rootInfoErr: 'Zaten kök dizinde',
            resetSuccess: 'Sıfırlama başarılı',
            creatingInfo: 'Oluşturuluyor, bu işlem gerekli değil',
            installSuccess: 'Yükleme başarılı',
            uninstallSuccess: 'Kaldırma başarılı',
        },
        login: {
            username: 'Kullanıcı adı',
            password: 'Şifre',
            welcome: 'Tekrar hoş geldiniz, giriş yapmak için kullanıcı adınızı ve şifrenizi girin!',
            errorAuthInfo: 'Girdiğiniz kullanıcı adı veya şifre yanlış, lütfen tekrar girin!',
            errorMfaInfo: 'Yanlış kimlik doğrulama bilgisi, lütfen tekrar deneyin!',
            captchaHelper: 'Güvenlik kodu',
            errorCaptcha: 'Güvenlik kodu hatası!',
            notSafe: 'Erişim Reddedildi',
            safeEntrance1: 'Mevcut ortamda güvenli giriş etkinleştirildi',
            safeEntrance2: 'Panel girişini görüntülemek için SSH terminalinde şu komutu girin: 1pctl user-info',
            errIP1: 'Mevcut ortamda yetkili IP adresi erişimi etkinleştirildi',
            errDomain1: 'Mevcut ortamda erişim alan adı bağlama etkinleştirildi',
            errHelper: 'Bağlama bilgilerini sıfırlamak için SSH terminalinde şu komutu çalıştırın: ',
            codeInput: 'MFA doğrulayıcının 6 haneli doğrulama kodunu girin',
            mfaTitle: 'MFA Sertifikasyonu',
            mfaCode: 'MFA doğrulama kodu',
            title: 'Linux Sunucu Yönetim Paneli',
            licenseHelper: '<Topluluk Lisans Sözleşmesi>',
            errorAgree: 'Topluluk Yazılım Lisansını kabul etmek için tıklayın',
            logout: 'Çıkış',
            agreeTitle: 'Sözleşme',
            agreeContent:
                'Meşru hak ve çıkarlarınızı daha iyi korumak için lütfen aşağıdaki sözleşmeyi okuyun ve kabul edin &laquo; <a href = "https://www.fit2cloud.com/legal/licenses.html" target = "_blank" > Topluluk Lisans Sözleşmesi </a> &raquo;',
        },
        rule: {
            username: 'Kullanıcı adı girin',
            password: 'Şifre girin',
            rePassword: 'Şifre onayı, şifre ile tutarsız.',
            requiredInput: 'Bu alan gereklidir.',
            requiredSelect: 'Listeden bir öğe seçin',
            illegalChar: '& ; $ ` ( ) " > < | karakterlerinin enjekte edilmesi şu anda desteklenmiyor',
            illegalInput: 'Bu alan yasadışı karakterler içermemelidir.',
            commonName:
                'Bu alan özel olmayan karakterlerle başlamalı ve İngilizce, Çince, rakam, ".", "-", ve "_" karakterlerinden oluşmalı, uzunluk 1-128 olmalıdır.',
            userName: 'Bu alan İngilizce, Çince, rakam ve "_" karakterlerinden oluşmalı, uzunluk 3-30 olmalıdır.',
            simpleName:
                'Bu alan alt çizgi karakteriyle başlamamalı ve İngilizce, rakam, ve "_" karakterlerinden oluşmalı, uzunluk 3-30 olmalıdır.',
            simplePassword:
                'Bu alan alt çizgi karakteriyle başlamamalı ve İngilizce, rakam, ve "_" karakterlerinden oluşmalı, uzunluk 1-30 olmalıdır.',
            dbName: 'Bu alan alt çizgi karakteriyle başlamamalı ve İngilizce, rakam, ve "_" karakterlerinden oluşmalı, uzunluk 1-64 olmalıdır.',
            imageName:
                'Bu alan İngilizce, rakam, ":", "@", "/", ".", "-", ve "_" karakterlerinden oluşmalı, uzunluk 1-256 olmalıdır.',
            volumeName: 'Bu alan İngilizce, rakam, ".", "-", ve "_" karakterlerinden oluşmalı, uzunluk 2-30 olmalıdır.',
            supervisorName:
                'Bu alan özel olmayan karakterlerle başlamalı ve İngilizce, rakam, "-", ve "_" karakterlerinden oluşmalı, uzunluk 1-128 olmalıdır.',
            composeName:
                'Başlangıçta özel olmayan karakterleri, küçük harfleri, rakamları, - ve _ destekler, uzunluk 1-256',
            complexityPassword:
                'Bu alan İngilizce, rakamlardan oluşmalı, uzunluk 8-30 olmalı ve en az iki özel karakter içermelidir.',
            commonPassword: 'Bu alanın uzunluğu 6dan fazla olmalıdır.',
            linuxName:
                'Bu alanın uzunluğu 1 ile 128 arasında olmalıdır. Alan şu özel karakterleri içermemelidir: "{0}".',
            email: 'Bu alan geçerli bir e-posta adresi olmalıdır.',
            number: 'Bu alan bir sayı olmalıdır.',
            integer: 'Bu alan pozitif bir tam sayı olmalıdır.',
            ip: 'Bu alan geçerli bir IP adresi olmalıdır.',
            host: 'Bu alan geçerli bir IP adresi veya alan adı olmalıdır.',
            hostHelper: 'IP adres veya alan adı girişini destekler',
            port: 'Bu alan geçerli bir port numarası olmalıdır.',
            selectHelper: 'Lütfen doğru {0} dosyasını seçin',
            domain: 'Bu alan şu şekilde olmalıdır: example.com veya example.com:8080.',
            databaseName: 'Bu alan İngilizce, rakam, ve "_" karakterlerinden oluşmalı, uzunluk 1-30 olmalıdır.',
            ipErr: 'Bu alan geçerli bir IP adresi olmalıdır.',
            numberRange: 'Bu alan {0} ile {1} arasında bir sayı olmalıdır.',
            paramName: 'Bu alan İngilizce, rakam, ".", "-", ve "_" karakterlerinden oluşmalı, uzunluk 2-30 olmalıdır.',
            paramComplexity:
                'Bu alan özel karakterlerle başlayıp bitmemeli ve İngilizce, rakam, "{0}" karakterlerinden oluşmalı, uzunluk 6-128 olmalıdır.',
            paramUrlAndPort: 'Bu alan "http(s)://(alan adı/ip):(port)" formatında olmalıdır.',
            nginxDoc: 'Bu alan İngilizce, rakam ve "." karakterlerinden oluşmalıdır.',
            appName:
                'Bu alan "-" ve "_" karakterleriyle başlayıp bitmemeli ve İngilizce, rakam, "-", ve "_" karakterlerinden oluşmalı, uzunluk 2-30 olmalıdır.',
            containerName: 'Harf, rakam, -, _ ve . destekler; - _ veya . ile başlayamaz; uzunluk: 2-128',
            mirror: 'Ayna hızlandırma adresi http(s):// ile başlamalı, İngilizce harfleri (büyük ve küçük), rakam, . / ve - desteklemeli, boş satır içermemelidir.',
            disableFunction: 'Sadece harf, alt çizgi ve virgül destekler',
            leechExts: 'Sadece harf, rakam ve virgül destekler',
            paramSimple: 'Küçük harf ve rakam destekler, uzunluk 1-128',
            filePermission: 'Dosya İzin Hatası',
            formatErr: 'Format hatası, lütfen kontrol edin ve tekrar deneyin',
            phpExtension: 'Sadece , _ küçük İngilizce ve rakam destekler',
            paramHttp: 'http:// veya https:// ile başlamalıdır',
            phone: 'Telefon numarası formatı yanlış',
            authBasicPassword: 'Harf, rakam ve yaygın özel karakterler destekler, uzunluk 1-72',
            length128Err: 'Uzunluk 128 karakteri geçemez',
            maxLength: 'Uzunluk {0} karakteri geçemez',
            alias: 'İngilizce, rakamlar, - ve _ destekler, uzunluk 1-30, ve -_ ile başlayamaz veya bitiremez.',
        },
        res: {
            paramError: 'İstek başarısız, lütfen daha sonra tekrar deneyin!',
            forbidden: 'Mevcut kullanıcının izni yok',
            serverError: 'Servis istisnası',
            notFound: 'Kaynak mevcut değil',
            commonError: 'İstek başarısız',
        },
        service: {
            serviceNotStarted: '{0} servisi başlatılmadı.',
        },
        status: {
            running: 'Çalışıyor',
            done: 'Tamamlandı',
            scanFailed: 'Eksik',
            success: 'Başarılı',
            waiting: 'Bekliyor',
            failed: 'Başarısız',
            stopped: 'Durduruldu',
            error: 'Hata',
            created: 'Oluşturuldu',
            restarting: 'Yeniden Başlatılıyor',
            uploading: 'Yükleniyor',
            unhealthy: 'Sağlıksız',
            removing: 'Kaldırılıyor',
            paused: 'Duraklatıldı',
            exited: 'Çıkıldı',
            dead: 'Ölü',
            installing: 'Yükleniyor',
            enabled: 'Etkinleştirildi',
            disabled: 'Devre Dışı',
            normal: 'Normal',
            building: 'İnşa Ediliyor',
            upgrading: 'Yükseltiliyor',
            pending: 'Düzenleme Bekliyor',
            rebuilding: 'Yeniden İnşa Ediliyor',
            deny: 'Reddedildi',
            accept: 'Kabul Edildi',
            used: 'Kullanıldı',
            unused: 'Kullanılmadı',
            starting: 'Başlatılıyor',
            recreating: 'Yeniden Oluşturuluyor',
            creating: 'Oluşturuluyor',
            init: 'Uygulama bekleniyor',
            ready: 'normal',
            applying: 'Uygulanıyor',
            uninstalling: 'Kaldırılıyor',
            lost: 'İletişim Kesildi',
            bound: 'Bağlandı',
            unbind: 'Bağlantısı Kesildi',
            exceptional: 'İstisnai',
            free: 'Ücretsiz',
            enable: 'Etkinleştirildi',
            disable: 'Devre Dışı',
            deleted: 'Silindi',
            downloading: 'İndiriliyor',
            packing: 'Paketleniyor',
            sending: 'Gönderiliyor',
            healthy: 'Normal',
            executing: 'Yürütülüyor',
            installerr: 'Yükleme başarısız',
            applyerror: 'Uygulama başarısız',
            systemrestart: 'Kesintiye Uğradı',
            starterr: 'Başlatma başarısız',
            uperr: 'Başlatma başarısız',
        },
        units: {
            second: ' saniye | saniye | saniye',
            minute: 'dakika | dakika | dakika',
            hour: 'saat | saat | saat',
            day: 'gün | gün | gün',
            week: 'hafta | hafta | hafta',
            month: 'ay | ay | ay',
            year: 'yıl | yıl | yıl',
            time: 'rqm',
            core: 'çekirdek | çekirdek | çekirdek',
            millisecond: 'milisaniye | milisaniye',
            secondUnit: 's',
            minuteUnit: 'dk',
            hourUnit: 'sa',
            dayUnit: 'g',
        },
    },
    menu: {
        home: 'Genel Bakış',
        apps: 'Uygulama Mağazası',
        website: 'Web Sitesi | Web Siteleri',
        project: 'Proje | Projeler',
        config: 'Yapılandırma | Yapılandırmalar',
        ssh: 'SSH Ayarları',
        firewall: 'Güvenlik Duvarı',
        ssl: 'Sertifika | Sertifikalar',
        database: 'Veritabanı | Veritabanları',
        aiTools: 'AI',
        mcp: 'MCP',
        container: 'Konteyner | Konteynerler',
        cronjob: 'Cron İşi | Cron İşleri',
        system: 'Sistem',
        security: 'Güvenlik',
        files: 'Dosya Tarayıcısı',
        monitor: 'İzleme',
        terminal: 'Terminal | Terminaller',
        settings: 'Ayar | Ayarlar',
        toolbox: 'Araç Kutusu',
        logs: 'Günlük | Günlükler',
        runtime: 'Çalışma Zamanı | Çalışma Zamanları',
        processManage: 'Süreç | Süreçler',
        process: 'Süreç | Süreçler',
        network: 'Ağ | Ağlar',
        supervisor: 'Supervisor',
        tamper: 'Kurcalama Koruması',
        app: 'Uygulama',
        msgCenter: 'Görev Merkezi',
    },
    home: {
        recommend: 'önerilen',
        dir: 'dizin',
        restart_1panel: 'Paneli yeniden başlat',
        restart_system: 'Sunucuyu yeniden başlat',
        operationSuccess: 'İşlem başarılı, yeniden başlatılıyor, lütfen tarayıcıyı daha sonra manuel olarak yenileyin!',
        entranceHelper:
            'Güvenlik girişi etkinleştirilmedi. Sistem güvenliğini artırmak için "Ayarlar -> Güvenlik" bölümünden etkinleştirebilirsiniz.',
        appInstalled: 'Uygulamalar',
        systemInfo: 'Sistem bilgisi',
        hostname: 'Host adı',
        platformVersion: 'İşletim sistemi',
        kernelVersion: 'Çekirdek',
        kernelArch: 'Mimari',
        network: 'Ağ',
        io: 'Disk G/Ç',
        ip: 'Yerel IP',
        proxy: 'Sistem proxy',
        baseInfo: 'Temel bilgi',
        totalSend: 'Toplam gönderilen',
        totalRecv: 'Toplam alınan',
        rwPerSecond: 'G/Ç işlemleri',
        ioDelay: 'G/Ç gecikmesi',
        uptime: 'Çalışma süresi',
        runningTime: 'Şu tarihten beri açık',
        mem: 'Sistem',
        swapMem: 'Swap Bölümü',

        runSmoothly: 'Düşük yük',
        runNormal: 'Orta yük',
        runSlowly: 'Yüksek yük',
        runJam: 'Ağır yük',

        core: 'Fiziksel çekirdek',
        logicCore: 'Mantıksal çekirdek',
        loadAverage: 'Son 1 dakikadaki yük ortalaması | Son {n} dakikadaki yük ortalaması',
        load: 'Yük',
        mount: 'Bağlama noktası',
        fileSystem: 'Dosya sistemi',
        total: 'Toplam',
        used: 'Kullanılan',
        cache: 'Önbellek',
        free: 'Boş',
        shard: 'Parçalı',
        available: 'Kullanılabilir',
        percent: 'Kullanım',
        goInstall: 'Yüklemeye git',

        networkCard: 'Ağ kartı',
        disk: 'Disk',
    },
    tabs: {
        more: 'Daha Fazla',
        hide: 'Gizle',
        closeLeft: 'Solları kapat',
        closeRight: 'Sağları kapat',
        closeCurrent: 'Mevcut olanı kapat',
        closeOther: 'Diğerlerini kapat',
        closeAll: 'Tümünü Kapat',
    },
    header: {
        logout: 'Çıkış',
    },
    database: {
        manage: 'Veritabanını yönet',
        deleteBackupHelper: 'Veritabanı yedeklerini aynı anda sil',
        delete: 'Silme işlemi geri alınamaz, lütfen "',
        deleteHelper: '" girerek bu veritabanını silin',
        create: 'Veritabanı oluştur',
        noMysql: 'Veritabanı hizmeti (MySQL veya MariaDB)',
        noPostgresql: 'Veritabanı hizmeti PostgreSQL',
        goUpgrade: 'Yükseltmeye git',
        goInstall: 'Yüklemeye git',
        isDelete: 'Silindi',
        permission: 'İzinleri değiştir',
        permissionForIP: 'IP',
        permissionAll: 'Tümü(%)',
        localhostHelper:
            'Konteyner dağıtımı için veritabanı izinlerini "localhost" olarak yapılandırmak konteynere dış erişimi engelleyecektir. Lütfen dikkatli seçin!',
        databaseConnInfo: 'Bağlantı bilgilerini görüntüle',
        rootPassword: 'Root şifresi',
        serviceName: 'Servis Adı',
        serviceNameHelper: 'Aynı ağdaki konteynerler arası erişim.',
        backupList: 'Yedekleme',
        loadBackup: 'İçe Aktar',
        remoteAccess: 'Uzaktan erişim',
        remoteHelper: 'Birden fazla IP virgülle ayrılır, örnek: *************, *************',
        remoteConnHelper:
            'Root kullanıcısı olarak MySQLe uzaktan bağlantı güvenlik riski oluşturabilir. Bu nedenle bu işlemi dikkatli yapın.',
        changePassword: 'Şifre değiştir',
        changeConnHelper: 'Bu işlem mevcut {0} veritabanını değiştirecek. Devam etmek istiyor musunuz?',
        changePasswordHelper:
            'Veritabanı bir uygulamayla ilişkilendirildi. Şifre değiştirmek aynı zamanda uygulamanın veritabanı şifresini de değiştirecek. Değişiklik uygulama yeniden başlatıldıktan sonra etkili olur.',

        confChange: 'Yapılandırma',
        confNotFound:
            'Yapılandırma dosyası bulunamadı. Lütfen uygulama mağazasından uygulamayı en son sürüme yükseltin ve tekrar deneyin!',

        portHelper:
            'Bu port konteynerin açığa çıkan portudur. Değişikliği ayrı olarak kaydetmeniz ve konteyneri yeniden başlatmanız gerekir!',

        loadFromRemote: 'Sunucudan senkronize et',
        userBind: 'Kullanıcı bağla',
        pgBindHelper:
            'Bu işlem yeni bir kullanıcı oluşturmak ve hedef veritabanına bağlamak için kullanılır. Şu anda veritabanında mevcut olan kullanıcıları seçmek desteklenmiyor.',
        pgSuperUser: 'Süper Kullanıcı',
        loadFromRemoteHelper:
            'Bu, sunucudaki veritabanı bilgilerini 1Panele senkronize edecek. Devam etmek istiyor musunuz?',
        passwordHelper: 'Alınamıyor, lütfen değiştirin',
        remote: 'Uzak',
        remoteDB: 'Uzak sunucu | Uzak sunucular',
        createRemoteDB: '@.lower:database.remoteDB Bağla',
        unBindRemoteDB: '@.lower:database.remoteDB Bağlantısını Çöz',
        unBindForce: 'Zorla bağlantıyı çöz',
        unBindForceHelper:
            'Son işlemin başarılı olmasını sağlamak için bağlantı çözme işlemi sırasındaki tüm hataları yoksay',
        unBindRemoteHelper:
            'Uzak veritabanının bağlantısını çözmek sadece bağlantı ilişkisini kaldıracak ve uzak veritabanını doğrudan silmeyecek',
        editRemoteDB: 'Uzak sunucuyu düzenle',
        localDB: 'Yerel veritabanı',
        address: 'Veritabanı adresi',
        version: 'Veritabanı sürümü',
        userHelper:
            'Root kullanıcı veya root yetkilerine sahip bir veritabanı kullanıcısı uzak veritabanına erişebilir.',
        pgUserHelper: 'Süper kullanıcı yetkilerine sahip veritabanı kullanıcısı.',
        ssl: 'SSL Kullan',
        clientKey: 'İstemci özel anahtarı',
        clientCert: 'İstemci sertifikası',
        caCert: 'CA sertifikası',
        hasCA: 'CA sertifikası var',
        skipVerify: 'Sertifika geçerlilik kontrolünü yoksay',
        formatHelper:
            'Mevcut veritabanı karakter seti {0}, karakter seti tutarsızlığı kurtarma işleminin başarısız olmasına neden olabilir',
        selectFile: 'Dosya seç',
        dropHelper: 'Yüklenen dosyayı buraya sürükleyip bırakabilir veya',
        clickHelper: 'yüklemek için tıklayın',
        supportUpType: 'Yalnızca sql, sql.gz ve tar.gz dosyaları desteklenir',
        zipFormat: 'tar.gz sıkıştırılmış paket yapısı: test.tar.gz sıkıştırılmış paketi test.sql içermelidir',

        currentStatus: 'Mevcut durum',
        baseParam: 'Temel parametre',
        performanceParam: 'Performans parametresi',
        runTime: 'Başlatma zamanı',
        connections: 'Toplam bağlantılar',
        bytesSent: 'Gönderilen baytlar',
        bytesReceived: 'Alınan baytlar',
        queryPerSecond: 'Saniye başına sorgu',
        txPerSecond: 'Saniye başına işlem',
        connInfo: 'aktif/en yüksek bağlantılar',
        connInfoHelper: 'Değer çok büyükse, "max_connections" değerini artırın.',
        threadCacheHit: 'Thread önbellek isabet oranı',
        threadCacheHitHelper: 'Çok düşükse, "thread_cache_size" değerini artırın.',
        indexHit: 'İndeks isabet oranı',
        indexHitHelper: 'Çok düşükse, "key_buffer_size" değerini artırın.',
        innodbIndexHit: 'Innodb indeks isabet oranı',
        innodbIndexHitHelper: 'Çok düşükse, "innodb_buffer_pool_size" değerini artırın.',
        cacheHit: 'Sorgu önbellek isabet oranı',
        cacheHitHelper: 'Çok düşükse, "query_cache_size" değerini artırın.',
        tmpTableToDB: 'Diske geçici tablo',
        tmpTableToDBHelper: 'Çok büyükse, "tmp_table_size" değerini artırmayı deneyin.',
        openTables: 'Açık tablolar',
        openTablesHelper: '"table_open_cache" yapılandırma değeri bu değerden büyük veya eşit olmalıdır.',
        selectFullJoin: 'Tam birleştirme seçimi',
        selectFullJoinHelper: 'Değer 0 değilse, veri tablosunun indeksinin doğru olup olmadığını kontrol edin.',
        selectRangeCheck: 'İndekssiz birleştirme sayısı',
        selectRangeCheckHelper: 'Değer 0 değilse, veri tablosunun indeksinin doğru olup olmadığını kontrol edin.',
        sortMergePasses: 'Sıralı birleştirme sayısı',
        sortMergePassesHelper: 'Değer çok büyükse, "sort_buffer_size" değerini artırın.',
        tableLocksWaited: 'Kilitli tablo sayısı',
        tableLocksWaitedHelper: 'Değer çok büyükse, veritabanı performansınızı artırmayı düşünün.',

        performanceTuning: 'Performans ayarlama',
        optimizationScheme: 'Optimizasyon şeması',
        keyBufferSizeHelper: 'İndeks için tampon boyutu',
        queryCacheSizeHelper: 'Sorgu önbelleği. Bu işlev devre dışıysa, bu parametreyi 0 olarak ayarlayın.',
        tmpTableSizeHelper: 'Geçici tablo önbellek boyutu',
        innodbBufferPoolSizeHelper: 'Innodb tampon boyutu',
        innodbLogBufferSizeHelper: 'Innodb log tampon boyutu',
        sortBufferSizeHelper: '* bağlantılar, thread başına sıralama tampon boyutu',
        readBufferSizeHelper: '* bağlantılar, okuma tampon boyutu',
        readRndBufferSizeHelper: '* bağlantılar, rastgele okuma tampon boyutu',
        joinBufferSizeHelper: '* bağlantılar, ilişki tablosu önbellek boyutu',
        threadStackelper: '* bağlantılar, thread başına yığın boyutu',
        binlogCacheSizeHelper: '* bağlantılar, ikili log önbellek boyutu (4096nın katları)',
        threadCacheSizeHelper: 'Thread havuzu boyutu',
        tableOpenCacheHelper: 'Tablo önbelleği',
        maxConnectionsHelper: 'Maksimum bağlantılar',
        restart: 'Yeniden başlat',

        slowLog: 'Yavaş loglar',
        noData: 'Henüz yavaş log yok.',

        isOn: 'Açık',
        longQueryTime: 'eşik(saniye)',
        thresholdRangeHelper: 'Lütfen doğru eşik değerini girin (1 - 600).',

        timeout: 'Zaman aşımı(saniye)',
        timeoutHelper: 'Boştaki bağlantı zaman aşımı süresi. 0, bağlantının sürekli açık olduğunu gösterir.',
        maxclients: 'Maksimum istemci',
        requirepassHelper:
            'Boş bırakırsanız şifre ayarlanmadığını gösterir. Değişikliklerin ayrı olarak kaydedilmesi ve konteyner yeniden başlatılması gerekir!',
        databases: 'Veritabanı sayısı',
        maxmemory: 'Maksimum bellek kullanımı',
        maxmemoryHelper: '0 kısıtlama olmadığını gösterir.',
        tcpPort: 'Mevcut dinleme portu.',
        uptimeInDays: 'Çalışma günü.',
        connectedClients: 'Bağlı istemci sayısı.',
        usedMemory: 'Redisin mevcut bellek kullanımı.',
        usedMemoryRss: 'İşletim sisteminden talep edilen bellek boyutu.',
        usedMemoryPeak: 'Redisin en yüksek bellek tüketimi.',
        memFragmentationRatio: 'Bellek parçalanma oranı.',
        totalConnectionsReceived: 'Çalışma başlangıcından itibaren bağlanan toplam istemci sayısı.',
        totalCommandsProcessed: 'Çalışma başlangıcından itibaren yürütülen toplam komut sayısı.',
        instantaneousOpsPerSec: 'Sunucunun saniye başına yürüttüğü komut sayısı.',
        keyspaceHits: 'Veritabanı anahtarının başarıyla bulunma sayısı.',
        keyspaceMisses: 'Veritabanı anahtarını bulma girişimlerinin başarısız olma sayısı.',
        hit: 'Veritabanı anahtarı isabet oranı.',
        latestForkUsec: 'Son fork() işleminde harcanan mikrosaniye sayısı.',
        redisCliHelper: '"redis-cli" servisi algılanmadı. Önce servisi etkinleştirin.',
        redisQuickCmd: 'Redis hızlı komutları',
        recoverHelper: 'Bu işlem verileri [{0}] ile üzerine yazacak. Devam etmek istiyor musunuz?',
        submitIt: 'Verilerin üzerine yaz',

        baseConf: 'Temel',
        allConf: 'Tümü',
        restartNow: 'Şimdi yeniden başlat',
        restartNowHelper1:
            'Yapılandırma değişikliklerinin etkili olması için sistemi yeniden başlatmanız gerekir. Verilerinizin kalıcı olması gerekiyorsa, önce kaydetme işlemini gerçekleştirin.',
        restartNowHelper: 'Bu yalnızca sistem yeniden başlatıldıktan sonra etkili olacaktır.',

        persistence: 'Kalıcılık',
        rdbHelper1: 'saniye, ekleme',
        rdbHelper2: 'veri parçası',
        rdbHelper3: 'Koşullardan herhangi birinin karşılanması RDB kalıcılığını tetikleyecektir.',
        rdbInfo: 'Kural listesindeki değerin 1 ile 100000 arasında olduğundan emin olun',

        containerConn: 'Konteyner bağlantısı',
        connAddress: 'Adres',
        containerConnHelper:
            'Bu bağlantı adresi PHP yürütme ortamı/konteyner kurulumunda çalışan uygulamalar tarafından kullanılır.',
        remoteConn: 'Harici bağlantı',
        remoteConnHelper2:
            'Bu bağlantı adresi konteyner dışında veya harici uygulamalarda çalışan uygulamalar tarafından kullanılabilir.',
        remoteConnHelper3:
            'Varsayılan erişim adresi ana bilgisayar IPsidir. Değiştirmek için panel ayarları sayfasındaki "Varsayılan Erişim Adresi" yapılandırma öğesine gidin.',
        localIP: 'Yerel IP',
    },
    aiTools: {
        model: {
            model: 'Model',
            create: 'Model Ekle',
            create_helper: '"{0}" çek',
            ollama_doc: 'Daha fazla model aramak ve bulmak için Ollama resmi web sitesini ziyaret edebilirsiniz.',
            container_conn_helper: 'Konteynerler arası erişim veya bağlantı için bu adresi kullanın',
            ollama_sync:
                'Ollama modelini senkronize ederken aşağıdaki modellerin mevcut olmadığı tespit edildi, bunları silmek istiyor musunuz?',
            from_remote: 'Bu model 1Panel aracılığıyla indirilmedi, ilgili çekme logları yok.',
            no_logs: 'Bu modelin çekme logları silindi ve görüntülenemiyor.',
        },
        proxy: {
            proxy: 'AI Proxy Geliştirmesi',
            proxyHelper1: 'Alan adı bağlayın ve gelişmiş iletim güvenliği için HTTPSi etkinleştirin',
            proxyHelper2: 'Genel internette maruz kalmayı önlemek için IP erişimini sınırlayın',
            proxyHelper3: 'Akışı etkinleştir',
            proxyHelper4: 'Oluşturulduktan sonra web sitesi listesinde görüntüleyebilir ve yönetebilirsiniz',
            proxyHelper5:
                'Etkinleştirdikten sonra, güvenliği artırmak için Uygulama Mağazası - Kurulu - Ollama - Parametrelerden porta harici erişimi devre dışı bırakabilirsiniz.',
            proxyHelper6: 'Proxy yapılandırmasını devre dışı bırakmak için web sitesi listesinden silebilirsiniz.',
            whiteListHelper: 'Erişimi yalnızca beyaz listedeki IPlerle sınırlayın',
        },
        gpu: {
            gpu: 'GPU Monitörü',
            base: 'Temel Bilgiler',
            gpuHelper:
                'Mevcut sistemde NVIDIA-SMI veya XPU-SMI komutu algılanmadı. Lütfen kontrol edin ve tekrar deneyin!',
            driverVersion: 'Sürücü Sürümü',
            cudaVersion: 'CUDA Sürümü',
            process: 'İşlem Bilgileri',
            type: 'Tür',
            typeG: 'Grafik',
            typeC: 'Hesaplama',
            typeCG: 'Hesaplama + Grafik',
            processName: 'İşlem Adı',
            processMemoryUsage: 'Bellek Kullanımı',
            temperatureHelper: 'Yüksek GPU sıcaklığı GPU frekans kısıtlamasına neden olabilir',
            performanceStateHelper: 'P0dan (maksimum performans) P12ye (minimum performans) kadar',
            busID: 'Bus ID',
            persistenceMode: 'Kalıcılık Modu',
            enabled: 'Etkin',
            disabled: 'Devre Dışı',
            persistenceModeHelper:
                'Kalıcılık modu daha hızlı görev yanıtlarına izin verir ancak bekleme güç tüketimini artırır.',
            displayActive: 'Grafik Kartı Başlatıldı',
            displayActiveT: 'Evet',
            displayActiveF: 'Hayır',
            ecc: 'Hata Düzeltme ve Kontrol Teknolojisi',
            computeMode: 'Hesaplama Modu',
            default: 'Varsayılan',
            exclusiveProcess: 'Özel İşlem',
            exclusiveThread: 'Özel Thread',
            prohibited: 'Yasaklı',
            defaultHelper: 'Varsayılan: İşlemler eşzamanlı olarak yürütülebilir',
            exclusiveProcessHelper:
                'Özel İşlem: Yalnızca bir CUDA bağlamı GPUyu kullanabilir, ancak birden fazla thread tarafından paylaşılabilir',
            exclusiveThreadHelper: 'Özel Thread: CUDA bağlamında yalnızca bir thread GPUyu kullanabilir',
            prohibitedHelper: 'Yasaklı: İşlemlerin eşzamanlı yürütülmesine izin verilmez',
            migModeHelper:
                'Kullanıcı düzeyinde GPUnun fiziksel izolasyonu için MIG örnekleri oluşturmak için kullanılır.',
            migModeNA: 'Desteklenmiyor',
        },
        mcp: {
            server: 'MCP Sunucusu',
            create: 'MCP Sunucusu Ekle',
            edit: 'MCP Sunucusunu Düzenle',
            commandHelper: 'Örneğin: npx -y {0}',
            baseUrl: 'Harici Erişim Yolu',
            baseUrlHelper: 'Örneğin: http://192.168.1.2:8000',
            ssePath: 'SSE Yolu',
            ssePathHelper: 'Örneğin: /sse, diğer sunucularla çoğaltmamaya dikkat edin',
            environment: 'Ortam Değişkenleri',
            envKey: 'Değişken Adı',
            envValue: 'Değişken Değeri',
            externalUrl: 'Harici Bağlantı Adresi',
            operatorHelper: '{0} üzerinde {1} işlemi gerçekleştirilecek, devam edilsin mi?',
            domain: 'Varsayılan Erişim Adresi',
            domainHelper: 'Örneğin: *********** veya example.com',
            bindDomain: 'Web Sitesi Bağla',
            commandPlaceHolder: 'Şu anda yalnızca npx ve ikili başlatma komutları desteklenir',
            importMcpJson: 'MCP Sunucu Yapılandırmasını İçe Aktar',
            importMcpJsonError: 'mcpServers yapısı yanlış',
            bindDomainHelper:
                'Web sitesini bağladıktan sonra, kurulu tüm MCP Sunucularının erişim adresini değiştirecek ve portlara harici erişimi kapatacaktır',
            outputTransport: 'Çıktı Türü',
            streamableHttpPath: 'Akış Yolu',
            streamableHttpPathHelper: 'Örneğin: /mcp, diğer Sunucularla çakışmaması gerektiğine dikkat edin',
        },
    },
    container: {
        create: 'Oluştur',
        createByCommand: 'Komutla oluştur',
        commandInput: 'Komut girişi',
        commandRule: 'Lütfen doğru docker run konteyner oluşturma komutunu girin!',
        commandHelper: 'Bu komut konteyneri oluşturmak için sunucuda çalıştırılacak. Devam etmek istiyor musunuz?',
        edit: 'Konteyneri düzenle',
        updateHelper1:
            'Bu konteynerin uygulama mağazasından geldiği tespit edildi. Lütfen aşağıdaki iki noktaya dikkat edin:',
        updateHelper2: '1. Mevcut değişiklikler uygulama mağazasındaki kurulu uygulamalarla senkronize edilmeyecektir.',
        updateHelper3:
            '2. Kurulu sayfasında uygulamayı değiştirirseniz, şu anda düzenlenen içerik geçersiz hale gelecektir.',
        updateHelper4:
            'Konteyneri düzenlemek yeniden oluşturma gerektirir ve kalıcı olmayan tüm veriler kaybedilecektir. Devam etmek istiyor musunuz?',
        containerList: 'Konteyner listesi',
        operatorHelper: 'Aşağıdaki konteynerde {0} işlemi gerçekleştirilecek, devam etmek istiyor musunuz?',
        operatorAppHelper:
            'Aşağıdaki konteyner(ler)de "{0}" işlemi gerçekleştirilecek ve çalışan hizmetleri etkileyebilir. Devam etmek istiyor musunuz?',
        start: 'Başlat',
        stop: 'Durdur',
        restart: 'Yeniden başlat',
        kill: 'Öldür',
        pause: 'Duraklat',
        unpause: 'Devam et',
        rename: 'Yeniden adlandır',
        remove: 'Kaldır',
        removeAll: 'Tümünü kaldır',
        containerPrune: 'Temizle',
        containerPruneHelper1: 'Bu, durmuş durumdaki tüm konteynerleri silecektir.',
        containerPruneHelper2:
            'Konteynerler uygulama mağazasından geliyorsa, temizleme işleminden sonra "Uygulama Mağazası -> Kurulu" bölümüne gidip "Yeniden Oluştur" düğmesine tıklayarak onları yeniden kurmanız gerekecektir.',
        containerPruneHelper3: 'Bu işlem geri alınamaz. Devam etmek istiyor musunuz?',
        imagePrune: 'Temizle',
        imagePruneSome: 'Etiketlenmemiş temizle',
        imagePruneSomeEmpty: '"none" etiketli temizlenebilecek imaj yok.',
        imagePruneSomeHelper: 'Herhangi bir konteyner tarafından kullanılmayan "none" etiketli imajları temizle.',
        imagePruneAll: 'Kullanılmayan temizle',
        imagePruneAllEmpty: 'Temizlenebilecek kullanılmayan imaj yok.',
        imagePruneAllHelper: 'Herhangi bir konteyner tarafından kullanılmayan imajları temizle.',
        networkPrune: 'Temizle',
        networkPruneHelper: 'Bu, kullanılmayan tüm ağları kaldıracaktır. Devam etmek istiyor musunuz?',
        volumePrune: 'Temizle',
        volumePruneHelper: 'Bu, kullanılmayan tüm yerel birimleri kaldıracaktır. Devam etmek istiyor musunuz?',
        cleanSuccess: 'İşlem başarılı, bu temizleme sayısı: {0}!',
        cleanSuccessWithSpace: 'İşlem başarılı. Bu sefer temizlenen disk sayısı {0}. Boşaltılan disk alanı {1}!',
        unExposedPort: 'Mevcut port eşleme adresi 127.0.0.1, dış erişimi etkinleştiremez.',
        upTime: 'Çalışma süresi',
        fetch: 'Getir',
        lines: 'Satırlar',
        linesHelper: 'Lütfen alınacak log sayısını doğru girin!',
        lastDay: 'Son gün',
        last4Hour: 'Son 4 saat',
        lastHour: 'Son saat',
        last10Min: 'Son 10 dakika',
        cleanLog: 'Log temizle',
        downLogHelper1: 'Bu, {0} konteynerinden tüm logları indirecektir. Devam etmek istiyor musunuz?',
        downLogHelper2: 'Bu, {0} konteynerinden son {0} logunu indirecektir. Devam etmek istiyor musunuz?',
        cleanLogHelper:
            'Bu, konteynerin yeniden başlatılmasını gerektirir ve geri alınamaz. Devam etmek istiyor musunuz?',
        newName: 'Yeni ad',
        workingDir: 'Çalışma Dizini',
        source: 'Kaynak kullanımı',
        cpuUsage: 'CPU kullanımı',
        cpuTotal: 'CPU toplam',
        core: 'Çekirdek',
        memUsage: 'Bellek kullanımı',
        memTotal: 'Bellek sınırı',
        memCache: 'Bellek önbelleği',
        ip: 'IP adresi',
        cpuShare: 'CPU paylaşımları',
        cpuShareHelper:
            'Konteyner motoru CPU paylaşımları için 1024 temel değerini kullanır. Konteynere daha fazla CPU zamanı vermek için bunu artırabilirsiniz.',
        inputIpv4: 'Örnek: ***********',
        inputIpv6: 'Örnek: 2001:0db8:85a3:0000:0000:8a2e:0370:7334',

        containerFromAppHelper:
            'Bu konteynerin uygulama mağazasından geldiği tespit edildi. Uygulama işlemleri mevcut düzenlemelerin geçersiz hale gelmesine neden olabilir.',
        containerFromAppHelper1:
            'Düzenleme sayfasına girmek ve konteyner adını değiştirmek için kurulu uygulamalar listesindeki [Param] düğmesine tıklayın.',
        command: 'Komut',
        console: 'Konteyner etkileşimi',
        tty: 'Sözde-TTY tahsis et (-t)',
        openStdin: 'Bağlı değilse bile STDINi açık tut (-i)',
        custom: 'Özel',
        emptyUser: 'Boş olduğunda, varsayılan olarak giriş yapacaksınız',
        privileged: 'Ayrıcalıklı',
        privilegedHelper:
            'Konteynerin ana bilgisayarda belirli ayrıcalıklı işlemler gerçekleştirmesine izin verir, bu da konteyner risklerini artırabilir. Dikkatli kullanın!',
        editComposeHelper:
            'Not: Ayarlanan ortam değişkenleri varsayılan olarak 1panel.env dosyasına yazılacaktır.\nBu parametreleri konteynerde kullanmak istiyorsanız, compose dosyasına manuel olarak env_file referansı eklemeniz gerekir.',

        upgradeHelper: 'Depo Adı/İmaj Adı: İmaj Sürümü',
        upgradeWarning2:
            'Yükseltme işlemi konteynerin yeniden oluşturulmasını gerektirir, kalıcı olmayan tüm veriler kaybedilecektir. Devam etmek istiyor musunuz?',
        oldImage: 'Mevcut imaj',
        sameImageContainer: 'Aynı imajlı konteynerler',
        sameImageHelper: 'Aynı imajı kullanan konteynerlar seçilerek toplu şekilde güncellenebilir',
        targetImage: 'Hedef imaj',
        imageLoadErr: 'Konteyner için imaj adı algılanmadı',
        appHelper: 'Konteyner uygulama mağazasından geliyor ve yükseltme hizmeti kullanılamaz hale getirebilir.',

        resource: 'Kaynak',
        input: 'Manuel giriş',
        forcePull: 'Her zaman imajı çek ',
        forcePullHelper: 'Bu, sunucudaki mevcut imajları yok sayacak ve kayıt defterinden en son imajı çekecektir.',
        server: 'Ana bilgisayar',
        serverExample: '80, 80-88, ip:80 veya ip:80-88',
        containerExample: '80 veya 80-88',
        exposePort: 'Portu göster',
        exposeAll: 'Tümünü göster',
        cmdHelper: 'Örnek: nginx -g "daemon off;"',
        entrypointHelper: 'Örnek: docker-entrypoint.sh',
        autoRemove: 'Otomatik kaldır',
        cpuQuota: 'CPU çekirdek sayısı',
        memoryLimit: 'Bellek',
        limitHelper: '0 olarak ayarlanırsa, sınırlama olmadığı anlamına gelir. Maksimum değer {0}',
        macAddr: 'MAC Adresi',
        mount: 'Bağla',
        volumeOption: 'Birim',
        hostOption: 'Ana bilgisayar',
        serverPath: 'Sunucu yolu',
        containerDir: 'Konteyner yolu',
        volumeHelper: 'Depolama biriminin içeriğinin doğru olduğundan emin olun',
        modeRW: 'RW',
        modeR: 'R',
        mode: 'Mod',
        env: 'Ortamlar',
        restartPolicy: 'Yeniden başlatma politikası',
        always: 'always',
        unlessStopped: 'unless-stopped',
        onFailure: 'on-failure (varsayılan olarak beş kere)',
        no: 'never',

        refreshTime: 'Yenileme aralığı',
        cache: 'Önbellek',

        image: 'İmaj | İmajlar',
        imagePull: 'Çek',
        imagePush: 'Gönder',
        imageDelete: 'İmaj sil',
        imageTagDeleteHelper: 'Bu imaj IDsi ile ilişkili diğer etiketleri kaldır',
        repoName: 'Konteyner kayıt defteri',
        imageName: 'İmaj adı',
        pull: 'Çek',
        path: 'Yol',
        importImage: 'İçe aktar',
        build: 'Oluştur',
        imageBuild: 'Oluştur',
        pathSelect: 'Yol',
        label: 'Etiket',
        imageTag: 'İmaj etiketi',
        push: 'Gönder',
        fileName: 'Dosya adı',
        export: 'Dışa aktar',
        exportImage: 'İmaj dışa aktarma',
        size: 'Boyut',
        tag: 'Etiketler',
        tagHelper: 'Satır başına bir tane. Örneğin,\nkey1=value1\nkey2=value2',
        imageNameHelper: 'İmaj adı ve Etiketi, örneğin: nginx:latest',
        cleanBuildCache: 'Oluşturma önbelleğini temizle',
        delBuildCacheHelper:
            'Bu, oluşturma sırasında üretilen tüm önbelleğe alınmış yapıları silecek ve geri alınamaz. Devam etmek istiyor musunuz?',
        urlWarning: 'URL öneki http:// veya https:// içermemelidir. Lütfen değiştirin.',

        network: 'Ağ | Ağlar',
        networkHelper:
            'Bu, bazı uygulamaların ve çalışma zamanı ortamlarının düzgün çalışmamasına neden olabilir. Devam etmek istiyor musunuz?',
        createNetwork: 'Oluştur',
        networkName: 'Ad',
        driver: 'Sürücü',
        option: 'Seçenek',
        attachable: 'Eklenebilir',
        subnet: 'Alt ağ',
        scope: 'IP kapsamı',
        gateway: 'Ağ geçidi',
        auxAddress: 'IP hariç tut',

        volume: 'Birim | Birimler',
        volumeDir: 'Birim dizini',
        nfsEnable: 'NFS depolamayı etkinleştir',
        nfsAddress: 'Adres',
        mountpoint: 'Bağlama noktası',
        mountpointNFSHelper: 'örn. /nfs, /nfs-share',
        options: 'Seçenekler',
        createVolume: 'Oluştur',

        repo: 'Konteyner kayıt defteri | Konteyner kayıt defterleri',
        createRepo: 'Ekle',
        httpRepoHelper: 'HTTP tipinde bir depo işlemi Docker servisinin yeniden başlatılmasını gerektirir.',
        httpRepo:
            'HTTP protokolü seçilmesi Docker servisinin güvenli olmayan kayıt defterlerine eklemek için yeniden başlatılmasını gerektirir.',
        delInsecure: 'Kredinin silinmesi',
        delInsecureHelper:
            'Bu, güvenli olmayan kayıt defterlerinden kaldırmak için Docker servisini yeniden başlatacaktır. Devam etmek istiyor musunuz?',
        downloadUrl: 'Sunucu',
        imageRepo: 'İmaj deposu',
        repoHelper: 'Ayna depo/organizasyon/proje içeriyor mu?',
        auth: 'Kimlik doğrulama gerekli',
        mirrorHelper:
            'Birden fazla ayna varsa, yeni satırlar gösterilmelidir, örneğin:\nhttp://xxxxxx.m.daocloud.io \nhttps://xxxxxx.mirror.aliyuncs.com',
        registrieHelper:
            'Birden fazla özel depo varsa, yeni satırlar gösterilmelidir, örneğin:\n*************:8081 \n*************:8081',

        compose: 'Compose | Composelar',
        fromChangeHelper:
            'Kaynağın değiştirilmesi mevcut düzenlenen içeriği temizleyecektir. Devam etmek istiyor musunuz?',
        composePathHelper: 'Yapılandırma dosyası kaydetme yolu: {0}',
        composeHelper:
            '1Panel editörü veya şablonu aracılığıyla oluşturulan kompozisyon {0}/docker/compose dizinine kaydedilecektir.',
        deleteFile: 'Dosyayı sil',
        deleteComposeHelper:
            'Yapılandırma dosyaları ve kalıcı dosyalar dahil olmak üzere konteyner compose ile ilgili tüm dosyaları silin. Lütfen dikkatli ilerleyin!',
        deleteCompose: '" Bu kompozisyonu sil.',
        createCompose: 'Oluştur',
        composeDirectory: 'Compose dizini',
        template: 'Şablon',
        composeTemplate: 'Compose şablonu | Compose şablonları',
        createComposeTemplate: 'Oluştur',
        content: 'İçerik',
        contentEmpty: 'Compose içeriği boş olamaz, lütfen girin ve tekrar deneyin!',
        containerNumber: 'Konteyner sayısı',
        containerStatus: 'Konteyner durumu',
        exited: 'Çıktı',
        running: 'Çalışıyor ( {0} / {1} )',
        composeDetailHelper: 'Compose, 1Panel dışında oluşturulmuştur. Başlatma ve durdurma işlemleri desteklenmez.',
        composeOperatorHelper: '{0} üzerinde {1} işlemi gerçekleştirilecek. Devam etmek istiyor musunuz?',
        composeDownHelper:
            'Bu, {0} compose altındaki tüm konteynerleri ve ağları durduracak ve kaldıracaktır. Devam etmek istiyor musunuz?',

        setting: 'Ayar | Ayarlar',
        goSetting: 'Düzenlemeye git',
        operatorStatusHelper: 'Bu, Docker servisini "{0}" yapacaktır. Devam etmek istiyor musunuz?',
        dockerStatus: 'Docker Servisi',
        daemonJsonPathHelper: 'Yapılandırma yolunun docker.servicede belirtilen ile aynı olduğundan emin olun.',
        mirrors: 'Kayıt defteri aynaları',
        mirrorsHelper: '',
        mirrorsHelper2: 'Ayrıntılar için resmi belgelere bakın. ',
        registries: 'Güvenli olmayan kayıt defterleri',
        ipv6Helper:
            'IPv6yı etkinleştirirken, bir IPv6 konteyner ağı eklemeniz gerekir. Belirli yapılandırma adımları için resmi belgelere bakın.',
        ipv6CidrHelper: 'Konteynerler için IPv6 adres havuzu aralığı',
        ipv6TablesHelper: 'iptables kuralları için Docker IPv6nın otomatik yapılandırması.',
        experimentalHelper:
            'ip6tablesı etkinleştirmek bu yapılandırmanın açılmasını gerektirir; aksi takdirde ip6tables yok sayılacaktır',
        cutLog: 'Log seçeneği',
        cutLogHelper1: 'Mevcut yapılandırma yalnızca yeni oluşturulan konteynerleri etkileyecektir.',
        cutLogHelper2: 'Mevcut konteynerler yapılandırmanın etkili olması için yeniden oluşturulmalıdır.',
        cutLogHelper3:
            'Konteynerleri yeniden oluşturmanın veri kaybına neden olabileceğini unutmayın. Konteynerleriniz önemli veriler içeriyorsa, yeniden oluşturma işlemini gerçekleştirmeden önce yedeklediğinizden emin olun.',
        maxSize: 'Maksimum boyut',
        maxFile: 'Maksimum dosya',
        liveHelper:
            'Varsayılan olarak, Docker daemonı sonlandığında, çalışan konteynerleri kapatır. Daemon kullanılamaz hale gelirse konteynerlerin çalışmaya devam etmesi için daemonı yapılandırabilirsiniz. Bu işlevsellik canlı geri yükleme olarak adlandırılır. Canlı geri yükleme seçeneği, daemon çökmesi, planlı kesintiler veya yükseltmeler nedeniyle konteyner kesinti süresini azaltmaya yardımcı olur.',
        liveWithSwarmHelper: 'live-restore daemon yapılandırması swarm modu ile uyumlu değildir.',
        iptablesDisable: 'iptables kapat',
        iptablesHelper1: 'Docker için iptables kurallarının otomatik yapılandırması.',
        iptablesHelper2:
            'iptablesı devre dışı bırakmak konteynerlerin dış ağlarla iletişim kuramamasına neden olacaktır.',
        daemonJsonPath: 'Yapılandırma Yolu',
        serviceUnavailable: 'Docker servisi şu anda başlatılmamış.',
        startIn: ' başlatmak için',
        sockPath: 'Unix domain socket',
        sockPathHelper: 'Docker daemon ile istemci arasındaki iletişim kanalı.',
        sockPathHelper1: 'Varsayılan yol: /var/run/docker-x.sock',
        sockPathMsg:
            'Socket Path ayarını kaydetmek Docker servisinin kullanılamaz hale gelmesine neden olabilir. Devam etmek istiyor musunuz?',
        sockPathErr: 'Lütfen doğru Docker sock dosya yolunu seçin veya girin',
        related: 'İlgili',
        includeAppstore: 'Uygulama mağazasından konteynerleri göster',
        excludeAppstore: 'Uygulama Mağazası Konteynerini Gizle',

        cleanDockerDiskZone: 'Docker tarafından kullanılan disk alanını temizle',
        cleanImagesHelper: '( Herhangi bir konteyner tarafından kullanılmayan tüm imajları temizle )',
        cleanContainersHelper: '( Durmuş olan tüm konteynerleri temizle )',
        cleanVolumesHelper: '( Kullanılmayan tüm yerel birimleri temizle )',

        makeImage: 'İmaj oluştur',
        newImageName: 'Yeni imaj adı',
        commitMessage: 'Commit mesajı',
        author: 'Yazar',
        ifPause: 'Oluşturma Sırasında Konteyneri Duraklat',
        ifMakeImageWithContainer: 'Bu Konteynerden Yeni İmaj Oluşturulsun mu?',
    },
    cronjob: {
        create: 'Cron görevi oluştur',
        edit: 'Cron görevini düzenle',
        errImport: 'Dosya içeriği anormal:',
        errImportFormat: 'Zamanlanmış görev verileri veya biçimi anormal. Lütfen kontrol edip tekrar deneyin!',
        importHelper:
            'İçe aktarım sırasında aynı isimli zamanlanmış görevler otomatik olarak atlanacaktır. Görevler varsayılan olarak 【Devre Dışı】 durumuna ayarlanır ve veri ilişkilendirme anormalse 【Düzenleme Bekliyor】 durumuna ayarlanır.',
        changeStatus: 'Durumu değiştir',
        disableMsg: 'Bu, zamanlanmış görevin otomatik olarak yürütülmesini durduracaktır. Devam etmek istiyor musunuz?',
        enableMsg:
            'Bu, zamanlanmış görevin otomatik olarak yürütülmesine izin verecektir. Devam etmek istiyor musunuz?',
        taskType: 'Tür',
        nextTime: 'Sonraki 5 yürütme',
        record: 'Kayıtlar',
        viewRecords: 'Kayıtları görüntüle',
        shell: 'Shell',
        log: 'Yedekleme logları',
        logHelper: 'Sistem logunu yedekle',
        ogHelper1: '1.1Panel Sistem logu ',
        logHelper2: '2. Sunucunun SSH giriş logu ',
        logHelper3: '3. Tüm site logları ',
        containerCheckBox: 'Konteynerde (konteyner komutunu girmeye gerek yok)',
        containerName: 'Konteyner adı',
        ntp: 'Zaman senkronizasyonu',
        ntp_helper: 'NTP sunucusunu Araç Kutusunun Hızlı Kurulum sayfasından yapılandırabilirsiniz.',
        app: 'Uygulamayı yedekle',
        website: 'Web sitesini yedekle',
        rulesHelper:
            'Birden fazla sıkıştırma hariç tutma kuralı olduğunda, satır sonları ile gösterilmeleri gerekir. Örneğin,\n*.log \n*.sql',
        lastRecordTime: 'Son yürütme zamanı',
        all: 'Tümü',
        failedRecord: 'Başarısız kayıtlar',
        successRecord: 'Başarılı kayıtlar',
        database: 'Veritabanını yedekle',
        missBackupAccount: 'Yedekleme hesabı bulunamadı',
        syncDate: 'Senkronizasyon zamanı ',
        clean: 'Önbellek temizleme',
        curl: 'URLe erişim',
        taskName: 'Ad',
        cronSpec: 'Tetikleme döngüsü',
        cronSpecDoc:
            'Özel çalışma döngüleri yalnızca [dakika saat gün ay hafta] formatını destekler, örneğin, 0 0 * * *. Ayrıntılar için resmi belgelere bakın.',
        cronSpecHelper: 'Doğru yürütme dönemini girin',
        cleanHelper:
            'Bu işlem tüm görev yürütme kayıtlarını, yedekleme dosyalarını ve log dosyalarını kaydeder. Devam etmek istiyor musunuz?',
        backupContent: 'Yedekleme içeriği',
        directory: 'Yedekleme dizini',
        sourceDir: 'Yedekleme dizini',
        snapshot: 'Sistem anlık görüntüsü',
        allOptionHelper:
            'Mevcut görev planı tüm [{0}] öğelerini yedeklemektir. Doğrudan indirme şu anda desteklenmiyor. [{0}] menüsünün yedekleme listesini kontrol edebilirsiniz.',
        exclusionRules: 'Hariç tutma kuralı',
        exclusionRulesHelper: 'Hariç tutma kuralları bu yedeğin tüm sıkıştırma işlemlerine uygulanacaktır.',
        default_download_path: 'Varsayılan indirme bağlantısı',
        saveLocal: 'Yerel yedeklemeleri sakla (bulut depolama kopyalarının sayısı ile aynı)',
        url: 'URL Adresi',
        targetHelper: 'Yedekleme hesapları panel ayarlarında sürdürülür.',
        withImageHelper: 'Uygulama mağazası imajlarını yedekle, ancak bu anlık görüntü dosya boyutunu artıracaktır.',
        ignoreApp: 'Uygulamaları hariç tut',
        withImage: 'Tüm uygulama imajlarını yedekle',
        retainCopies: 'Kayıtları sakla',
        retryTimes: 'Yeniden Deneme Girişimleri',
        timeout: 'Zaman aşımı',
        retryTimesHelper: '0, başarısızlık sonrası yeniden deneme yok demektir',
        retainCopiesHelper: 'Yürütme kayıtları ve loglar için saklanacak kopya sayısı',
        retainCopiesHelper1: 'Yedekleme dosyaları için saklanacak kopya sayısı',
        retainCopiesUnit: ' kopya (Görüntüle)',
        cronSpecRule: '{0} satırındaki yürütme dönemi biçimi yanlış. Lütfen kontrol edin ve tekrar deneyin!',
        cronSpecRule2: 'Yürütme dönemi biçimi yanlış, lütfen kontrol edin ve tekrar deneyin!',
        perMonthHelper: 'Her ayın {0}. günü {1}:{2}de yürüt',
        perWeekHelper: 'Her hafta {0}da {1}:{2}de yürüt',
        perDayHelper: 'Her gün {0}:{1}de yürüt',
        perHourHelper: 'Her saat {0}. dakikada yürüt',
        perNDayHelper: 'Her {0} günde bir {1}:{2}de yürüt',
        perNHourHelper: 'Her {0} saatte bir {1}de yürüt',
        perNMinuteHelper: 'Her {0} dakikada bir yürüt',
        perNSecondHelper: 'Her {0} saniyede bir yürüt',
        perMonth: 'Her ay',
        perWeek: 'Her hafta',
        perHour: 'Her saat',
        perNDay: 'Her N gün',
        perDay: 'Her gün',
        perNHour: 'Her N saat',
        perNMinute: 'Her N dakika',
        perNSecond: 'Her N saniye',
        day: 'gün',
        monday: 'Pazartesi',
        tuesday: 'Salı',
        wednesday: 'Çarşamba',
        thursday: 'Perşembe',
        friday: 'Cuma',
        saturday: 'Cumartesi',
        sunday: 'Pazar',
        shellContent: 'Betik',
        executor: 'Yürütücü',
        errRecord: 'Hatalı kayıt',
        errHandle: 'Cronjob yürütme başarısız',
        noRecord: 'Cron Görevini tetikleyin, kayıtları burada göreceksiniz.',
        cleanData: 'Veriyi temizle',
        cleanRemoteData: 'Uzak veriyi sil',
        cleanDataHelper: 'Bu görev sırasında oluşturulan yedekleme dosyasını sil.',
        noLogs: 'Henüz görev çıktısı yok...',
        errPath: 'Yedek yolu [{0}] hatası, indirilemez!',
        cutWebsiteLog: 'Website log döndürme',
        cutWebsiteLogHelper: 'Döndürülen log dosyaları 1Panel yedek dizinine yedeklenecektir.',

        requestExpirationTime: 'Yükleme isteği son kullanma süresi(Saat)',
        unitHours: 'Birim: Saat',
        alertTitle: 'Planlanmış Görev - {0} 「{1}」 Görev Başarısızlık Uyarısı',
        library: {
            script: 'Script',
            isInteractive: 'Etkileşimli',
            interactive: 'Etkileşimli script',
            interactiveHelper: 'Yürütme sırasında kullanıcı girişi gerektirir ve zamanlanmış görevlerde kullanılamaz.',
            library: 'Script Kütüphanesi',
            create: 'Script Ekle',
            edit: 'Script Düzenle',
            groupHelper:
                'Script özelliklerine göre farklı gruplar ayarlayın, bu daha hızlı script filtreleme işlemlerine olanak tanır.',
            handleHelper: '{0} üzerinde {1} scriptini çalıştır, devam edilsin mi?',
            noSuchApp: '{0} servisi algılanmadı. Lütfen önce script kütüphanesi kullanarak hızlıca yükleyin!',
            syncHelper:
                'Sistem script kütüphanesini senkronize etmek üzere. Bu işlem sadece sistem scriptlerini etkiler. Devam edilsin mi?',
        },
    },
    monitor: {
        globalFilter: 'Genel Filtre',
        enableMonitor: 'Etkinleştir',
        storeDays: 'Son kullanma günleri',
        defaultNetwork: 'Varsayılan Ağ Adaptörü',
        defaultNetworkHelper: 'Varsayılan izleme ve genel bakış arayüzünde görüntülenen ağ adaptörü seçeneği',
        cleanMonitor: 'İzleme kayıtlarını temizle',

        avgLoad: 'Ortalama yük',
        loadDetail: 'Yük detayı',
        resourceUsage: 'Kullanım',
        networkCard: 'Ağ arayüzü',
        read: 'Okuma',
        write: 'Yazma',
        readWriteCount: 'I/O işlemleri',
        readWriteTime: 'I/O gecikmesi',
        today: 'Bugün',
        yesterday: 'Dün',
        lastNDay: 'Son {0} gün',
        lastNMonth: 'Son {0} ay',
        lastHalfYear: 'Son yarı yıl',
        memory: 'Bellek',
        cache: 'Önbellek',
        disk: 'Disk',
        network: 'Ağ',
        up: 'Yukarı',
        down: 'Aşağı',
        interval: 'Aralık(dakika)',

        gpuUtil: 'GPU Kullanımı',
        temperature: 'Sıcaklık',
        performanceState: 'Performans durumu',
        powerUsage: 'Güç kullanımı',
        memoryUsage: 'Bellek kullanımı',
        fanSpeed: 'Fan hızı',
    },
    terminal: {
        local: 'Yerel',
        localHelper: '`local` adı sadece sistem yerel tanımlaması için kullanılır',
        connLocalErr: 'Otomatik kimlik doğrulama yapılamıyor, lütfen yerel sunucu giriş bilgilerini doldurun.',
        testConn: 'Bağlantıyı test et',
        saveAndConn: 'Kaydet ve bağlan',
        connTestOk: 'Bağlantı bilgileri mevcut',
        connTestFailed: 'Bağlantı mevcut değil, lütfen bağlantı bilgilerini kontrol edin.',
        host: 'Ana Bilgisayar | Ana Bilgisayarlar',
        createConn: 'Yeni bağlantı',
        noHost: 'Ana bilgisayar yok',
        groupChange: 'Grup değiştir',
        expand: 'Tümünü genişlet',
        fold: 'Tümünü daralt',
        batchInput: 'Toplu işleme',
        quickCommand: 'Hızlı komut | Hızlı komutlar',
        quickCommandHelper: '"Terminaller -> Terminaller" altındaki hızlı komutları kullanabilirsiniz.',
        groupDeleteHelper:
            'Grup kaldırıldıktan sonra, gruptaki tüm bağlantılar varsayılan gruba taşınacaktır. Devam etmek istiyor musunuz?',
        command: 'Komut',
        quickCmd: 'Hızlı komut',
        addHost: 'Ekle',
        localhost: 'Yerel sunucu',
        ip: 'Adres',
        authMode: 'Kimlik doğrulama',
        passwordMode: 'Şifre',
        rememberPassword: 'Kimlik doğrulama bilgilerini hatırla',
        keyMode: 'Özel Anahtar',
        key: 'Özel anahtar',
        keyPassword: 'Özel anahtar şifresi',
        emptyTerminal: 'Şu anda bağlı terminal yok.',
        lineHeight: 'Satır Yüksekliği',
        letterSpacing: 'Harf Aralığı',
        fontSize: 'Font Boyutu',
        cursorBlink: 'İmleç Yanıp Sönme',
        cursorStyle: 'İmleç Stili',
        cursorUnderline: 'Alt Çizgi',
        cursorBlock: 'Blok',
        cursorBar: 'Çubuk',
        scrollback: 'Geri Kaydırma',
        scrollSensitivity: 'Kaydırma Hassasiyeti',
        saveHelper: 'Mevcut terminal yapılandırmasını kaydetmek istediğinizden emin misiniz?',
    },
    toolbox: {
        common: {
            toolboxHelper: 'Bazı kurulum ve kullanım sorunları için lütfen başvurun',
        },
        swap: {
            swap: 'Swap Bölümü',
            swapHelper1: 'Swap boyutu fiziksel belleğin 1 ila 2 katı olmalı, özel gereksinimlere göre ayarlanabilir;',
            swapHelper2:
                'Swap dosyası oluşturmadan önce sistem diskinin yeterli kullanılabilir alanı olduğundan emin olun, çünkü swap dosya boyutu karşılık gelen disk alanını kaplayacaktır;',
            swapHelper3:
                'Swap bellek baskısını hafifletmeye yardımcı olabilir, ancak sadece bir alternatiftir. Swap`a aşırı bağımlılık sistem performansında düşüşe yol açabilir. Öncelikle bellek artırma veya uygulama bellek kullanımını optimize etme önerilir;',
            swapHelper4: 'Normal sistem çalışmasını sağlamak için swap kullanımını düzenli olarak izlemeniz önerilir.',
            swapDeleteHelper:
                'Bu işlem {0} Swap bölümünü kaldıracaktır. Sistem güvenliği nedeniyle, karşılık gelen dosya otomatik olarak silinmeyecektir. Silme gerekiyorsa, lütfen manuel olarak ilerleyin!',
            saveHelper: 'Lütfen önce mevcut ayarları kaydedin!',
            saveSwap:
                'Mevcut yapılandırmayı kaydetmek {0} Swap bölüm boyutunu {1} olarak ayarlayacaktır. Devam etmek istiyor musunuz?',
            swapMin: 'Minimum bölüm boyutu 40 KB`dir. Lütfen değiştirin ve tekrar deneyin!',
            swapMax: 'Bölüm boyutu için maksimum değer {0}`dır. Lütfen değiştirin ve tekrar deneyin!',
            swapOff: 'Minimum bölüm boyutu 40 KB`dir. 0`a ayarlamak Swap bölümünü devre dışı bırakacaktır.',
        },
        device: {
            dnsHelper: 'DNS sunucusu',
            dnsAlert:
                'Dikkat! /etc/resolv.conf dosyasının yapılandırmasını değiştirmek, sistem yeniden başlatıldıktan sonra dosyayı varsayılan değerlerine geri yükleyecektir.',
            dnsHelper1:
                'Birden fazla DNS girişi olduğunda, yeni satırlarda görüntülenmelidirler. örn.\n114.114.114.114\n8.8.8.8',
            hostsHelper: 'Ana bilgisayar adı çözümlemesi',
            hosts: 'Domain',
            hostAlert:
                'Gizli yorumlanmış kayıtlar, lütfen görüntülemek veya ayarlamak için Tüm yapılandırma düğmesine tıklayın',
            toolbox: 'Hızlı ayarlar',
            hostname: 'Ana bilgisayar adı',
            passwd: 'Sistem şifresi',
            passwdHelper: 'Giriş karakterleri $ ve & içeremez',
            timeZone: 'Sistem saat dilimi',
            localTime: 'Sunucu saati',
            timeZoneChangeHelper: 'Sistem saat dilimini değiştirmek servisi yeniden başlatmayı gerektirir. Devam et?',
            timeZoneHelper:
                '"timedatectl" komutunu yüklemezseniz, saat dilimini değiştiremeyebilirsiniz. Çünkü sistem saat dilimini değiştirmek için bu komutu kullanır.',
            timeZoneCN: 'Pekin',
            timeZoneAM: 'Los Angeles',
            timeZoneNY: 'New York',
            ntpALi: 'Alibaba',
            ntpGoogle: 'Google',
            syncSite: 'NTP sunucusu',
            hostnameHelper:
                'Ana bilgisayar adı değişikliği "hostnamectl" komutuna bağlıdır. Komut yüklü değilse, değişiklik başarısız olabilir.',
            userHelper:
                'Kullanıcı adı alma için "whoami" komutuna bağlıdır. Komut yüklü değilse, alma başarısız olabilir.',
            passwordHelper:
                'Şifre değişikliği "chpasswd" komutuna bağlıdır. Komut yüklü değilse, değişiklik başarısız olabilir.',
            hostHelper:
                'Sağlanan içerikte boş değer var. Lütfen kontrol edin ve değişiklik yaptıktan sonra tekrar deneyin!',
            dnsCheck: 'Kullanılabilirliği Test Et',
            dnsOK: 'DNS yapılandırma bilgileri mevcut!',
            dnsTestFailed: 'DNS yapılandırma bilgileri mevcut değil.',
        },
        fail2ban: {
            sshPort: 'SSH portunu dinle',
            sshPortHelper: 'Mevcut Fail2ban ana bilgisayarın SSH bağlantı portunu dinler',
            unActive: 'Fail2ban servisi şu anda etkin değil.',
            operation: 'Fail2ban servisinde "{0}" işlemini gerçekleştireceksiniz. Devam etmek istiyor musunuz?',
            fail2banChange: 'Fail2ban Yapılandırma Değişikliği',
            ignoreHelper:
                'İzin listesindeki IP listesi engelleme için göz ardı edilecektir. Devam etmek istiyor musunuz?',
            bannedHelper:
                'Engelleme listesindeki IP listesi sunucu tarafından engellenecektir. Devam etmek istiyor musunuz?',
            maxRetry: 'Maksimum yeniden deneme girişimi',
            banTime: 'Engelleme süresi',
            banTimeHelper: 'Varsayılan engelleme süresi 10 dakikadır, -1 kalıcı engellemeyi belirtir',
            banTimeRule: 'Lütfen geçerli bir engelleme süresi veya -1 girin',
            banAllTime: 'Kalıcı engelleme',
            findTime: 'Keşif dönemi',
            banAction: 'Engelleme eylemi',
            banActionOption: '{0} kullanarak belirtilen IP adreslerini engelle',
            allPorts: ' (Tüm Portlar)',
            ignoreIP: 'IP izin listesi',
            bannedIP: 'IP engelleme listesi',
            logPath: 'Log yolu',
            logPathHelper: 'Varsayılan /var/log/secure veya /var/log/auth.log`dur',
        },
        ftp: {
            ftp: 'FTP hesabı | FTP hesapları',
            notStart: 'FTP servisi şu anda çalışmıyor, lütfen önce başlatın!',
            operation: 'Bu FTP servisi üzerinde "{0}" işlemi gerçekleştirecektir. Devam etmek istiyor musunuz?',
            noPasswdMsg: 'Mevcut FTP hesap şifresi alınamıyor, lütfen şifreyi ayarlayın ve tekrar deneyin! ',
            enableHelper:
                'Seçilen FTP hesabını etkinleştirmek erişim izinlerini geri yükleyecektir. Devam etmek istiyor musunuz?',
            disableHelper:
                'Seçilen FTP hesabını devre dışı bırakmak erişim izinlerini iptal edecektir. Devam etmek istiyor musunuz?',
            syncHelper:
                'Sunucu ve veritabanı arasında FTP hesap verilerini senkronize et. Devam etmek istiyor musunuz?',
        },
        clam: {
            clam: 'Virüs taraması',
            cron: 'Zamanlanmış tarama',
            cronHelper: 'Profesyonel sürüm zamanlanmış tarama özelliğini destekler',
            specErr: 'Yürütme programı format hatası, lütfen kontrol edin ve tekrar deneyin!',
            disableMsg:
                'Zamanlanmış yürütmeyi durdurmak bu tarama görevinin otomatik olarak çalışmasını engelleyecektir. Devam etmek istiyor musunuz?',
            enableMsg:
                'Zamanlanmış yürütmeyi etkinleştirmek bu tarama görevinin düzenli aralıklarla otomatik olarak çalışmasına olanak tanıyacaktır. Devam etmek istiyor musunuz?',
            showFresh: 'İmza güncelleyici servisini göster',
            hideFresh: 'İmza güncelleyici servisini gizle',
            clamHelper:
                'ClamAV için önerilen minimum yapılandırma: 3 GiB RAM veya daha fazla, 2.0 GHz veya daha yüksek tek çekirdekli CPU ve en az 5 GiB kullanılabilir sabit disk alanı.',
            notStart: 'ClamAV servisi şu anda çalışmıyor, lütfen önce başlatın!',
            removeRecord: 'Rapor dosyalarını sil',
            noRecords: 'Taramayı başlatmak için "Tetikle" düğmesine tıklayın ve kayıtları burada göreceksiniz.',
            removeResultHelper:
                'Depolama alanını boşaltmak için görev yürütme sırasında oluşturulan rapor dosyalarını silin.',
            removeInfected: 'Virüs dosyalarını sil',
            removeInfectedHelper:
                'Sunucu güvenliğini ve normal çalışmasını sağlamak için görev sırasında tespit edilen virüs dosyalarını silin.',
            clamCreate: 'Tarama kuralı oluştur',
            infectedStrategy: 'Enfekte strateji',
            removeHelper: 'Virüs dosyalarını sil, dikkatli seçin!',
            move: 'Taşı',
            moveHelper: 'Virüs dosyalarını belirtilen dizine taşı',
            copyHelper: 'Virüs dosyalarını belirtilen dizine kopyala',
            none: 'Hiçbir şey yapma',
            noneHelper: 'Virüs dosyalarında herhangi bir işlem yapma',
            scanDir: 'Tarama dizini',
            infectedDir: 'Enfekte dizin',
            scanDate: 'Tarama Tarihi',
            scanResult: 'Tarama logları sonu',
            tail: 'Satırlar',
            infectedFiles: 'Enfekte dosyalar',
            log: 'Detaylar',
            clamConf: 'Clam AV daemon',
            clamLog: '@:toolbox.clam.clamConf logları',
            freshClam: 'FreshClam',
            freshClamLog: '@:toolbox.clam.freshClam logları',
            alertHelper: 'Profesyonel sürüm zamanlanmış tarama ve SMS uyarısını destekler',
            alertTitle: 'Virüs tarama görevi「{0}」enfekte dosya tespit uyarısı',
        },
    },
    logs: {
        core: 'Panel Servisi',
        agent: 'Düğüm İzleme',
        panelLog: 'Panel logları',
        operation: 'İşlem logları',
        login: 'Giriş logları',
        loginIP: 'Giriş IP',
        loginAddress: 'Giriş adresi',
        loginAgent: 'Giriş aracısı',
        loginStatus: 'Durum',
        system: 'Sistem logları',
        deleteLogs: 'Logları temizle',
        resource: 'Kaynak',
        detail: {
            ai: 'AI',
            groups: 'Grup',
            hosts: 'Ana Bilgisayar',
            apps: 'Uygulama',
            websites: 'Website',
            containers: 'Konteyner',
            files: 'Dosya',
            runtimes: 'Çalışma Zamanı',
            process: 'İşlem',
            toolbox: 'Araç Kutusu',
            backups: 'Yedekleme / Geri Yükleme',
            tampers: 'Kurcalama',
            xsetting: 'Arayüz Ayarları',
            logs: 'Log',
            settings: 'Ayar',
            cronjobs: 'Cronjob',
            databases: 'Veritabanı',
            waf: 'WAF',
            licenses: 'Lisans',
            nodes: 'Düğüm',
            commands: 'Hızlı Komutlar',
        },
        websiteLog: 'Website logları',
        runLog: 'Çalıştırma logları',
        errLog: 'Hata logları',
        task: 'Görev Logu',
        taskName: 'Görev Adı',
        taskRunning: 'Çalışıyor',
    },
    file: {
        fileDirNum: '{0} dizin, {1} dosya,',
        currentDir: 'Dizin',
        dir: 'Klasör',
        fileName: 'Dosya adı',
        search: 'Ara',
        mode: 'İzinler',
        editPermissions: '@.lower:file.mode düzenle',
        owner: 'Sahip',
        file: 'Dosya',
        remoteFile: 'Uzak sunucudan indir',
        share: 'Paylaş',
        sync: 'Veri Senkronizasyonu',
        size: 'Boyut',
        updateTime: 'Değiştirilme',
        rename: 'Yeniden adlandır',
        role: 'İzinler',
        info: 'Özellikleri görüntüle',
        linkFile: 'Yumuşak bağlantı',
        shareList: 'Paylaşım listesi',
        zip: 'Sıkıştırılmış',
        group: 'Grup',
        path: 'Yol',
        public: 'Diğerleri',
        setRole: 'İzinleri ayarla',
        link: 'Dosya bağlantısı',
        rRole: 'Oku',
        wRole: 'Yaz',
        xRole: 'Çalıştırılabilir',
        name: 'Ad',
        compress: 'Sıkıştır',
        deCompress: 'Sıkıştırmayı aç',
        compressType: 'Sıkıştırma formatı',
        compressDst: 'Sıkıştırma yolu',
        replace: 'Mevcut dosyaların üzerine yaz',
        compressSuccess: 'Başarıyla sıkıştırıldı',
        deCompressSuccess: 'Sıkıştırma başarıyla açıldı',
        deCompressDst: 'Sıkıştırma açma yolu',
        linkType: 'Bağlantı türü',
        softLink: 'Yumuşak bağlantı',
        hardLink: 'Sert bağlantı',
        linkPath: 'Bağlantı yolu',
        selectFile: 'Dosya seç',
        downloadUrl: 'Uzak URL',
        downloadStart: 'İndirme başladı',
        moveSuccess: 'Başarıyla taşındı',
        copySuccess: 'Başarıyla kopyalandı',
        move: 'Taşı',
        calculate: 'Hesapla',
        canNotDeCompress: 'Bu dosyanın sıkıştırması açılamaz',
        uploadSuccess: 'Başarıyla yüklendi',
        downloadProcess: 'İndirme ilerlemesi',
        downloading: 'İndiriliyor...',
        infoDetail: 'Dosya özellikleri',
        root: 'Kök dizin',
        list: 'Dosya listesi',
        sub: 'Alt dizinleri dahil et',
        downloadSuccess: 'Başarıyla indirildi',
        theme: 'Tema',
        language: 'Dil',
        eol: 'Satır sonu',
        copyDir: 'Kopyala',
        paste: 'Yapıştır',
        changeOwner: 'Kullanıcı ve kullanıcı grubunu değiştir',
        containSub: 'İzin değişikliğini özyinelemeli olarak uygula',
        ownerHelper:
            'PHP çalışma ortamının varsayılan kullanıcısı: kullanıcı grubu 1000:1000, kapsayıcı içindeki ve dışındaki kullanıcıların tutarsız görünmesi normaldir',
        searchHelper: '* gibi joker karakterleri destekler',
        uploadFailed: '[{0}] Dosya yükleme hatası',
        fileUploadStart: '[{0}] yükleniyor...',
        currentSelect: 'Geçerli seçim: ',
        unsupportedType: 'Desteklenmeyen dosya türü',
        deleteHelper:
            'Aşağıdaki dosyaları silmek istediğinizden emin misiniz? Varsayılan olarak, silme işleminden sonra geri dönüşüm kutusuna gider',
        fileHelper: 'Not:\n1. Arama sonuçları sıralanamaz.\n2. Klasörler boyuta göre sıralanamaz.',
        forceDeleteHelper: 'Dosyayı kalıcı olarak sil (geri dönüşüm kutusuna girmeden doğrudan silinir)',
        recycleBin: 'Geri dönüşüm kutusu',
        sourcePath: 'Orijinal yol',
        deleteTime: 'Silme zamanı',
        confirmReduce: 'Aşağıdaki dosyaları geri yüklemek istediğinizden emin misiniz?',
        reduceSuccess: 'Geri yükleme başarılı',
        reduce: 'Geri yükle',
        reduceHelper:
            'Orijinal yolda aynı ada sahip bir dosya veya dizin varsa, üzerine yazılacaktır. Devam etmek istiyor musunuz?',
        clearRecycleBin: 'Temizle',
        clearRecycleBinHelper: 'Geri dönüşüm kutusunu temizlemek istiyor musunuz?',
        favorite: 'Favoriler',
        removeFavorite: 'Favorilerden kaldır?',
        addFavorite: 'Favorilere Ekle/Kaldır',
        clearList: 'Listeyi temizle',
        deleteRecycleHelper: 'Aşağıdaki dosyaları kalıcı olarak silmek istediğinizden emin misiniz?',
        typeErrOrEmpty: '[{0}] dosya türü yanlış veya boş klasör',
        dropHelper: 'Yüklemek istediğiniz dosyaları buraya sürükleyin',
        fileRecycleBin: 'Geri dönüşüm kutusunu etkinleştir',
        fileRecycleBinMsg: '{0} geri dönüşüm kutusu',
        wordWrap: 'Otomatik satır sonu',
        deleteHelper2: 'Seçilen dosyayı silmek istediğinizden emin misiniz? Silme işlemi geri alınamaz',
        ignoreCertificate: 'Güvensiz sunucu bağlantılarına izin ver',
        ignoreCertificateHelper:
            'Güvensiz sunucu bağlantılarına izin vermek, veri sızıntısına veya değiştirilmesine yol açabilir. Bu seçeneği yalnızca indirme kaynağına güvendiğinizde kullanın.',
        uploadOverLimit: 'Dosya sayısı 1000’i aşıyor! Lütfen sıkıştırıp yükleyin',
        clashDitNotSupport: 'Dosya adlarının .1panel_clash içermesi yasaktır',
        clashDeleteAlert: '"Geri Dönüşüm Kutusu" klasörü silinemez',
        clashOpenAlert: 'Geri dönüşüm kutusu dizinini açmak için lütfen "Geri Dönüşüm Kutusu" düğmesine tıklayın',
        right: 'İleri',
        back: 'Geri',
        top: 'Yukarı',
        up: 'Geri dön',
        openWithVscode: 'VS Code ile aç',
        vscodeHelper:
            'Lütfen VS Code’un yerel olarak yüklü olduğundan ve SSH Remote eklentisinin yapılandırıldığından emin olun',
        saveContentAndClose: 'Dosya değiştirildi, kaydedip kapatmak istiyor musunuz?',
        saveAndOpenNewFile: 'Dosya değiştirildi, kaydedip yeni dosyayı açmak istiyor musunuz?',
        noEdit: 'Dosya değiştirilmedi, buna gerek yok!',
        noNameFolder: 'İsimsiz klasör',
        noNameFile: 'İsimsiz dosya',
        minimap: 'Kod mini haritası',
        fileCanNotRead: 'Dosya okunamıyor',
        panelInstallDir: '1Panel kurulum dizini silinemez',
        wgetTask: 'İndirme Görevi',
        existFileTitle: 'Aynı ada sahip dosya uyarısı',
        existFileHelper: 'Yüklenen dosya, aynı ada sahip bir dosya içeriyor, üzerine yazmak istiyor musunuz?',
        existFileSize: 'Dosya boyutu (yeni -> eski)',
        existFileDirHelper: 'Seçilen dosya/klasörün adı çakışıyor. Lütfen dikkatle ilerleyin! \n',
        coverDirHelper: 'Yerine geçecek seçilen klasörler hedef yola kopyalanacak!',
        noSuchFile: 'Dosya veya dizin bulunamadı. Lütfen kontrol edin ve tekrar deneyin.',
        setting: 'Ayar',
        showHide: 'Gizli dosyaları göster',
        noShowHide: 'Gizli dosyaları gösterme',
        cancelUpload: 'Yüklemeyi İptal Et',
        cancelUploadHelper: 'Yüklemeyi iptal etmek ister misiniz, iptal sonrası yükleme listesi temizlenecektir.',
    },
    ssh: {
        autoStart: 'Otomatik başlat',
        enable: 'Otomatik başlatmayı etkinleştir',
        disable: 'Otomatik başlatmayı devre dışı bırak',
        sshAlert:
            'Liste verileri, oturum açma tarihine göre sıralanır. Saat dilimini değiştirmek veya başka işlemler yapmak, oturum açma günlüklerinin tarihinde sapmalara neden olabilir.',
        sshAlert2:
            'Kaba kuvvet saldırılarını deneyen IP adreslerini engellemek için "Araç Kutusu"nda "Fail2ban" kullanabilirsiniz, bu ana bilgisayarın güvenliğini artırır.',
        sshOperate: 'SSH servisinde "{0}" işlemi gerçekleştirilecek. Devam etmek istiyor musunuz?',
        sshChange: 'SSH Ayarı',
        sshChangeHelper: 'Bu işlem "{0}" değerini "{1}" olarak değiştirdi. Devam etmek istiyor musunuz?',
        sshFileChangeHelper:
            'Yapılandırma dosyasını değiştirmek, hizmet kullanılabilirliğini etkileyebilir. Bu işlemi yaparken dikkatli olun. Devam etmek istiyor musunuz?',
        port: 'Port',
        portHelper: 'SSH servisinin dinlediği portu belirtir.',
        listenAddress: 'Dinleme adresi',
        allV4V6: '0.0.0.0:{0}(IPv4) ve :::{0}(IPv6)',
        listenHelper:
            'IPv4 ve IPv6 ayarlarını boş bırakmak, "0.0.0.0:{0}(IPv4)" ve ":::{0}(IPv6)" üzerinde dinlemeyi sağlar.',
        addressHelper: 'SSH servisinin dinlediği adresi belirtir.',
        permitRootLogin: 'Kök kullanıcı girişine izin ver',
        rootSettingHelper: 'Kök kullanıcı için varsayılan giriş yöntemi "SSH girişine izin ver"dir.',
        rootHelper1: 'SSH girişine izin ver',
        rootHelper2: 'SSH girişini devre dışı bırak',
        rootHelper3: 'Yalnızca anahtar girişine izin verilir',
        rootHelper4: 'Yalnızca önceden tanımlanmış komutlar çalıştırılabilir. Başka işlemler yapılamaz.',
        passwordAuthentication: 'Parola kimlik doğrulaması',
        pwdAuthHelper:
            'Parola kimlik doğrulamasının etkinleştirilip etkinleştirilmeyeceği. Bu parametre varsayılan olarak etkindir.',
        pubkeyAuthentication: 'Anahtar kimlik doğrulaması',
        privateKey: 'Özel Anahtar',
        publicKey: 'Genel Anahtar',
        password: 'Parola',
        createMode: 'Oluşturma Yöntemi',
        generate: 'Otomatik Oluştur',
        unSyncPass: 'Anahtar parolası senkronize edilemez',
        syncHelper:
            'Eşitleme işlemi geçersiz anahtarları temizleyecek ve yeni tam anahtar çiftlerini eşitleyecek. Devam edilsin mi?',
        input: 'Manuel Giriş',
        import: 'Dosya Yükleme',
        pubkey: 'Anahtar bilgisi',
        pubKeyHelper: 'Mevcut anahtar bilgileri yalnızca {0} kullanıcısı için geçerlidir',
        encryptionMode: 'Şifreleme modu',
        passwordHelper: '6 ila 10 hane ve İngilizce harfler içerebilir',
        reGenerate: 'Anahtarı yeniden oluştur',
        keyAuthHelper: 'Anahtar kimlik doğrulamasının etkinleştirilip etkinleştirilmeyeceği.',
        useDNS: 'DNS kullanımı',
        dnsHelper:
            'SSH sunucusunda DNS çözümleme işlevinin, bağlantının kimliğini doğrulamak için etkinleştirilip etkinleştirilmeyeceğini kontrol eder.',
        analysis: 'İstatistiksel bilgi',
        denyHelper:
            'Aşağıdaki adreslerde "reddet" işlemi gerçekleştiriliyor. Ayar yapıldıktan sonra IP, sunucuya erişimden yasaklanacak. Devam etmek istiyor musunuz?',
        acceptHelper:
            'Aşağıdaki adreslerde "kabul et" işlemi gerçekleştiriliyor. Ayar yapıldıktan sonra IP, normal erişimi geri kazanacak. Devam etmek istiyor musunuz?',
        noAddrWarning: 'Şu anda [{0}] adres seçilmedi. Lütfen kontrol edin ve tekrar deneyin!',
        loginLogs: 'Oturum açma günlükleri',
        loginMode: 'Mod',
        authenticating: 'Anahtar',
        publickey: 'Anahtar',
        belong: 'Ait',
        local: 'Yerel',
        session: 'Oturum | Oturumlar',
        loginTime: 'Oturum açma zamanı',
        loginIP: 'Oturum açma IP’si',
        stopSSHWarn: 'Bu SSH bağlantısını kesmek ister misiniz?',
    },
    setting: {
        panel: 'Panel',
        user: 'Panel kullanıcısı',
        userChange: 'Panel kullanıcısını değiştir',
        userChangeHelper: 'Panel kullanıcısını değiştirmek sizi oturumdan çıkaracak. Devam etmek istiyor musunuz?',
        passwd: 'Panel parolası',
        emailHelper: 'Parola kurtarma için',
        title: 'Panel takma adı',
        panelPort: 'Panel portu',
        titleHelper:
            '3 ila 30 karakter uzunluğunda, İngilizce harfler, Çince karakterler, sayılar, boşluklar ve yaygın özel karakterler destekler',
        portHelper:
            'Önerilen port aralığı 8888 ila 65535’tir. Not: Sunucuda bir güvenlik grubu varsa, yeni portu önceden güvenlik grubundan izin verin',
        portChange: 'Port değişikliği',
        portChangeHelper: 'Servis portunu değiştir ve servisi yeniden başlat. Devam etmek istiyor musunuz?',
        theme: 'Tema',
        menuTabs: 'Menü sekmeleri',
        dark: 'Koyu',
        darkGold: 'Koyu Altın',
        light: 'Açık',
        auto: 'Sistemi takip et',
        language: 'Dil',
        languageHelper:
            'Varsayılan olarak tarayıcı dilini takip eder. Bu parametre yalnızca geçerli tarayıcıda etkilidir',
        sessionTimeout: 'Oturum zaman aşımı',
        sessionTimeoutError: 'Minimum oturum zaman aşımı 300 saniyedir',
        sessionTimeoutHelper: 'Panel, {0} saniye boyunca işlem yapılmazsa otomatik olarak oturumu kapatır.',
        systemIP: 'Sistem adresi',
        proxy: 'Sunucu vekili',
        proxyHelper: 'Vekil sunucuyu ayarladıktan sonra aşağıdaki senaryolarda etkili olacaktır:',
        proxyHelper1: 'Uygulama mağazasından kurulum paketi indirme ve senkronizasyon (Yalnızca Profesyonel sürüm)',
        proxyHelper2: 'Sistem güncelleme ve güncelleme bilgisi alma (Yalnızca Profesyonel sürüm)',
        proxyHelper3: 'Sistem lisansı doğrulama ve senkronizasyon',
        proxyHelper4: 'Docker ağı vekil sunucu üzerinden erişilecek (Yalnızca Profesyonel sürüm)',
        proxyHelper5: 'Sistem tipi komut kütüphaneleri için birleşik indirme ve senkronizasyon (Profesyonel)',
        proxyHelper6: 'Sertifika başvurusu (Profesyonel)',
        proxyType: 'Vekil türü',
        proxyUrl: 'Vekil Adresi',
        proxyPort: 'Vekil Portu',
        proxyPasswdKeep: 'Parolayı Hatırla',
        proxyDocker: 'Docker Vekili',
        proxyDockerHelper:
            'Vekil sunucu yapılandırmasını Docker’a senkronize et, çevrimdışı sunucu imaj çekme gibi işlemleri destekler',
        syncToNode: 'Düğüme Senkronize Et',
        syncToNodeHelper: 'Ayarları diğer düğümlere senkronize et',
        nodes: 'Düğüm',
        selectNode: 'Düğüm Seç',
        selectNodeError: 'Lütfen bir düğüm seçin',
        apiInterface: 'API’yi Etkinleştir',
        apiInterfaceClose: 'Kapatıldığında, API arayüzlerine erişilemez. Devam etmek istiyor musunuz?',
        apiInterfaceHelper: 'Üçüncü taraf uygulamaların API’ye erişmesine izin ver.',
        apiInterfaceAlert1: 'Üretim ortamlarında etkinleştirmeyin çünkü bu, sunucu güvenlik risklerini artırabilir.',
        apiInterfaceAlert2:
            'Potansiyel güvenlik tehditlerini önlemek için üçüncü taraf uygulamaları API’yi çağırmak için kullanmayın.',
        apiInterfaceAlert3: 'API belgesi:',
        apiInterfaceAlert4: 'Kullanım belgesi:',
        apiKey: 'API anahtarı',
        apiKeyHelper: 'API anahtarı, üçüncü taraf uygulamaların API’ye erişmesi için kullanılır.',
        ipWhiteList: 'IP izin listesi',
        ipWhiteListEgs: 'Her satıra bir tane. Örneğin,\n172.161.10.111\n172.161.10.0/24',
        ipWhiteListHelper: 'İzin listesindeki IP’ler API’ye erişebilir, 0.0.0.0/0 (tüm IPv4), ::/0 (tüm IPv6)',
        apiKeyValidityTime: 'Arayüz anahtarının geçerlilik süresi',
        apiKeyValidityTimeEgs: 'Arayüz anahtarının geçerlilik süresi (dakika cinsinden)',
        apiKeyValidityTimeHelper:
            'Arayüz zaman damgası, mevcut zaman damgasıyla farkı (dakika cinsinden) izin verilen aralıktaysa geçerlidir. 0 değeri doğrulamayı devre dışı bırakır.',
        apiKeyReset: 'Arayüz anahtarı sıfırlama',
        apiKeyResetHelper: 'İlişkili anahtar servisi geçersiz hale gelecektir. Lütfen servise yeni bir anahtar ekleyin',
        confDockerProxy: 'Docker vekilini yapılandır',
        restartNowHelper: 'Docker vekilini yapılandırmak, Docker servisinin yeniden başlatılmasını gerektirir.',
        restartNow: 'Hemen yeniden başlat',
        restartLater: 'Daha sonra manuel olarak yeniden başlat',
        systemIPWarning: 'Sunucu adresi şu anda ayarlanmadı. Önce kontrol panelinde ayarlayın.',
        systemIPWarning1: 'Geçerli sunucu adresi {0} olarak ayarlandı ve hızlı yönlendirme mümkün değil!',
        syncTime: 'Sunucu Saati',
        timeZone: 'Saat Dilimi',
        timeZoneChangeHelper:
            'Saat dilimini değiştirmek servisin yeniden başlatılmasını gerektirir. Devam etmek istiyor musunuz?',
        timeZoneHelper:
            'Saat dilimi değişikliği, sistem timedatectl servisine bağlıdır. 1Panel servisi yeniden başlatıldıktan sonra etkili olur.',
        timeZoneCN: 'Pekin',
        timeZoneAM: 'Los Angeles',
        timeZoneNY: 'New York',
        ntpALi: 'Alibaba',
        ntpGoogle: 'Google',
        syncSite: 'NTP Sunucusu',
        syncSiteHelper:
            'Bu işlem, sistem saati senkronizasyonu için {0}’ı kaynak olarak kullanacak. Devam etmek istiyor musunuz?',
        changePassword: 'Parolayı Değiştir',
        oldPassword: 'Orijinal parola',
        newPassword: 'Yeni parola',
        retryPassword: 'Parolayı onayla',
        noSpace: 'Girilen bilgiler boşluk karakteri içeremez',
        duplicatePassword: 'Yeni parola, orijinal parolayla aynı olamaz, lütfen tekrar girin!',
        diskClean: 'Önbellek temizleme',
        developerMode: 'Önizleme Programı',
        developerModeHelper:
            'Yayınlanmadan önce yeni özellikleri ve düzeltmeleri deneyimleyebilir ve erken geri bildirim sağlayabilirsiniz.',
        thirdParty: 'Üçüncü taraf hesaplar',
        scope: 'Kapsam',
        public: 'Genel',
        publicHelper:
            'Genel tip yedekleme hesapları her alt düğüme senkronize edilir ve alt düğümler bunları birlikte kullanabilir',
        private: 'Özel',
        privateHelper:
            'Özel tip yedekleme hesapları yalnızca mevcut düğümde oluşturulur ve yalnızca mevcut düğümün kullanımı içindir',
        noTypeForCreate: 'Şu anda oluşturulmuş bir yedekleme türü yok',
        LOCAL: 'Sunucu diski',
        OSS: 'Ali OSS',
        S3: 'Amazon S3',
        mode: 'Mod',
        MINIO: 'MinIO',
        SFTP: 'SFTP',
        WebDAV: 'WebDAV',
        WebDAVAlist: 'WebDAV, Alist bağlantısı için resmi belgelere bakabilirsiniz',
        UPYUN: 'UPYUN',
        ALIYUN: 'Aliyun Drive',
        ALIYUNHelper:
            'Aliyun Drive’da istemci dışı indirmeler için mevcut maksimum sınır 100 MB’dir. Bu sınırı aşmak, istemci üzerinden indirme gerektirir.',
        ALIYUNRecover:
            'Aliyun Drive’da istemci dışı indirmeler için mevcut maksimum sınır 100 MB’dir. Bu sınırı aşmak, istemci üzerinden yerel cihaza indirme ve ardından anlık görüntü senkronizasyonu ile kurtarma gerektirir.',
        GoogleDrive: 'Google Drive',
        analysis: 'Analiz',
        analysisHelper:
            'Gerekli parçaları otomatik olarak ayrıştırmak için tüm token içeriğini yapıştırın. Özel işlemler için lütfen resmi belgelere bakın.',
        serviceName: 'Servis Adı',
        operator: 'Operatör',
        OneDrive: 'Microsoft OneDrive',
        isCN: 'Yüzyıl İnternet',
        isNotCN: 'Uluslararası Sürüm',
        client_id: 'İstemci Kimliği',
        client_secret: 'İstemci sırrı',
        redirect_uri: 'Yönlendirme URL’si',
        onedrive_helper: 'Özel yapılandırma için resmi belgelere bakılabilir',
        clickToRefresh: 'Yenilemek için tıklayın',
        refreshTime: 'Token Yenileme Zamanı',
        refreshStatus: 'Token Yenileme Durumu',
        backupDir: 'Yedekleme dizini',
        codeWarning: 'Geçerli yetkilendirme kodu formatı yanlış, lütfen tekrar kontrol edin!',
        code: 'Yetki kodu',
        codeHelper:
            '"Edin" düğmesine tıklayın, ardından {0}’a giriş yapın ve yönlendirilen bağlantıdaki "code" sonrası içeriği kopyalayın. Bu içeriği giriş kutusuna yapıştırın. Özel talimatlar için lütfen resmi belgelere bakın.',
        loadCode: 'Edin',
        COS: 'Tencent COS',
        ap_beijing_1: 'Pekin Bölgesi 1',
        ap_beijing: 'Pekin',
        ap_nanjing: 'Nanjing',
        ap_shanghai: 'Şanghay',
        ap_guangzhou: 'Guangzhou',
        ap_chengdu: 'Chengdu',
        ap_chongqing: 'Chongqing',
        ap_shenzhen_fsi: 'Shenzhen Finansal',
        ap_shanghai_fsi: 'Şanghay Finansal',
        ap_beijing_fsi: 'Pekin Finansal',
        ap_hongkong: 'Hong Kong, Çin',
        ap_singapore: 'Singapur',
        ap_mumbai: 'Mumbai',
        ap_jakarta: 'Cakarta',
        ap_seoul: 'Seul',
        ap_bangkok: 'Bangkok',
        ap_tokyo: 'Tokyo',
        na_siliconvalley: 'Silikon Vadisi (ABD Batı)',
        na_ashburn: 'Ashburn (ABD Doğu)',
        na_toronto: 'Toronto',
        sa_saopaulo: 'Sao Paulo',
        eu_frankfurt: 'Frankfurt',
        KODO: 'Qiniu Kodo',
        scType: 'Depolama türü',
        typeStandard: 'Standart',
        typeStandard_IA: 'Standart_IA',
        typeArchive: 'Arşiv',
        typeDeep_Archive: 'Derin_Arşiv',
        scLighthouse: 'Varsayılan, Hafif nesne depolama yalnızca bu depolama türünü destekler',
        scStandard:
            'Standart Depolama, gerçek zamanlı erişim gerektiren çok sayıda sıcak dosya içeren iş senaryoları için uygundur, sık veri etkileşimi vb.',
        scStandard_IA:
            'Düşük frekanslı depolama, nispeten düşük erişim frekansına sahip iş senaryoları için uygundur ve verileri en az 30 gün saklar.',
        scArchive: 'Arşiv depolama, son derece düşük erişim frekansına sahip iş senaryoları için uygundur.',
        scDeep_Archive:
            'Dayanıklı soğuk depolama, son derece düşük erişim frekansına sahip iş senaryoları için uygundur.',
        archiveHelper:
            'Arşiv depolama dosyaları doğrudan indirilemez ve önce ilgili bulut hizmeti sağlayıcısının web sitesi üzerinden geri yüklenmelidir. Lütfen dikkatli kullanın!',
        backupAlert:
            'Bir bulut sağlayıcısı S3 protokolüyle uyumluysa, yedekleme için doğrudan Amazon S3 kullanılabilir.',
        domain: 'Hızlandırma alanı',
        backupAccount: 'Yedekleme hesabı | Yedekleme hesapları',
        loadBucket: 'Kova al',
        accountName: 'Hesap adı',
        accountKey: 'Hesap anahtarı',
        address: 'Adres',
        path: 'Yol',
        safe: 'Güvenlik',
        bindInfo: 'Bağlama bilgisi',
        bindAll: 'Tümünü Dinle',
        bindInfoHelper:
            'Servis dinleme adresini veya protokolü değiştirmek, hizmetin kullanılamamasına neden olabilir. Devam etmek istiyor musunuz?',
        ipv6: 'IPv6’yı Dinle',
        bindAddress: 'Dinleme adresi',
        entrance: 'Giriş',
        showEntrance: '"Genel Bakış" sayfasında devre dışı uyarısını göster',
        entranceHelper:
            'Güvenlik girişini etkinleştirmek, panele yalnızca belirtilen güvenlik girişi üzerinden oturum açılmasına izin verecektir.',
        entranceError:
            'Lütfen 5-116 karakterlik bir güvenli oturum açma giriş noktası girin, yalnızca sayılar veya harfler desteklenir.',
        entranceInputHelper: 'Güvenlik girişini devre dışı bırakmak için boş bırakın.',
        randomGenerate: 'Rastgele',
        expirationTime: 'Son Kullanım Tarihi',
        unSetting: 'Ayarlanmadı',
        noneSetting:
            'Panel parolasının son kullanma tarihini ayarlayın. Son kullanma tarihinden sonra parolayı sıfırlamanız gerekir',
        expirationHelper: 'Parola son kullanma süresi [0] gün ise, parola son kullanma işlevi devre dışı bırakılır',
        days: 'Son Kullanım Günleri',
        expiredHelper: 'Geçerli parola süresi doldu. Lütfen parolayı tekrar değiştirin.',
        timeoutHelper:
            '[ {0} gün ] Panel parolası süresi dolmak üzere. Süre dolduktan sonra parolayı sıfırlamanız gerekir',
        complexity: 'Karmaşıklık doğrulaması',
        complexityHelper:
            'Etkinleştirildiğinde, parola doğrulama kuralı şu olacaktır: 8-30 karakter, İngilizce, sayılar ve en az iki özel karakter içerir.',
        bindDomain: 'Alan adı bağla',
        unBindDomain: 'Alan adı bağlamasını kaldır',
        panelSSL: 'Panel SSL',
        panelSSLHelper:
            'Panel SSL’nin otomatik yenilenmesinden sonra, değişikliklerin etkili olması için 1Panel servisini manuel olarak yeniden başlatmanız gerekir.',
        unBindDomainHelper:
            'Alan adı bağlamasını kaldırma işlemi sistem güvenliğini etkileyebilir. Devam etmek istiyor musunuz?',
        bindDomainHelper: 'Alan adı bağlandıktan sonra, yalnızca o alan adı 1Panel servisine erişebilir.',
        bindDomainHelper1: 'Alan adı bağlamasını devre dışı bırakmak için boş bırakın.',
        bindDomainWarning:
            'Alan adı bağlamasından sonra oturumunuz kapatılacak ve yalnızca ayarlarınızda belirtilen alan adı üzerinden 1Panel servisine erişebilirsiniz. Devam etmek istiyor musunuz?',
        allowIPs: 'Yetkili IP',
        unAllowIPs: 'Yetkisiz IP',
        unAllowIPsWarning:
            'Boş bir IP’yi yetkilendirmek, tüm IP’lerin sisteme erişmesine izin verecek ve bu, sistem güvenliğini etkileyebilir. Devam etmek istiyor musunuz?',
        allowIPsHelper:
            'Yetkili IP adres listesini ayarladıktan sonra, yalnızca listedeki IP adresi panel servisine erişebilir.',
        allowIPsWarning:
            'Yetkili IP adres listesini ayarladıktan sonra, yalnızca listedeki IP adresi panel servisine erişebilir. Devam etmek istiyor musunuz?',
        allowIPsHelper1: 'IP adresi kısıtlamasını devre dışı bırakmak için boş bırakın.',
        allowIPEgs: 'Her satıra bir tane. Örneğin,\n*************\n***********/24',
        mfa: 'İki faktörlü kimlik doğrulama (2FA)',
        mfaClose: 'MFA’yı devre dışı bırakmak servisin güvenliğini azaltabilir. Devam etmek istiyor musunuz?',
        secret: 'Gizli',
        mfaInterval: 'Yenileme aralığı(saniye)',
        mfaTitleHelper:
            'Başlık, farklı 1Panel ana bilgisayarlarını ayırt etmek için kullanılır. Başlığı değiştirdikten sonra QR kodunu tekrar tarayın veya gizli anahtarı manuel olarak ekleyin.',
        mfaIntervalHelper:
            'Yenileme süresini değiştirdikten sonra QR kodunu tekrar tarayın veya gizli anahtarı manuel olarak ekleyin.',
        mfaAlert:
            'Tek kullanımlık token, geçerli zamana dayalı olarak dinamik olarak oluşturulan 6 haneli bir sayıdır. Sunucu saatinin senkronize olduğundan emin olun.',
        mfaHelper: 'Etkinleştirildiğinde, tek kullanımlık token doğrulanmalıdır.',
        mfaHelper1: 'Bir kimlik doğrulama uygulaması indirin, örneğin,',
        mfaHelper2:
            'Tek kullanımlık token’ı elde etmek için, kimlik doğrulama uygulamanızla aşağıdaki QR kodunu tarayın veya gizli anahtarı kimlik doğrulama uygulamanıza kopyalayın.',
        mfaHelper3: 'Uygulamadan altı haneli rakamı girin',
        mfaCode: 'Tek kullanımlık token',
        sslChangeHelper: 'Https ayarını değiştir ve servisi yeniden başlat. Devam etmek istiyor musunuz?',
        sslDisable: 'Devre dışı bırak',
        sslDisableHelper:
            'Https servisi devre dışı bırakılırsa, değişikliğin etkili olması için panelin yeniden başlatılması gerekir. Devam etmek istiyor musunuz?',
        noAuthSetting: 'Yetkisiz ayar',
        noAuthSettingHelper:
            'Kullanıcılar belirtilen güvenlik girişiyle oturum açmadığında veya panele belirtilen IP veya alan adından erişmediğinde, bu yanıt panel özelliklerini gizleyebilir.',
        responseSetting: 'Yanıt ayarı',
        help200: 'Yardım Sayfası',
        error400: 'Hatalı İstek',
        error401: 'Yetkisiz',
        error403: 'Yasak',
        error404: 'Bulunamadı',
        error408: 'İstek Zaman Aşımı',
        error416: 'Aralık Karşılanamadı',
        error444: 'Bağlantı Kapalı',
        error500: 'Dahili Sunucu Hatası',
        https: 'Panel için HTTPS protokolü erişimini ayarlamak, panel erişiminin güvenliğini artırabilir.',
        certType: 'Sertifika türü',
        selfSigned: 'Kendi kendine imzalı',
        selfSignedHelper:
            'Tarayıcılar kendi kendine imzalı sertifikalara güvenmeyebilir ve güvenlik uyarıları gösterebilir.',
        select: 'Seç',
        domainOrIP: 'Alan adı veya IP:',
        timeOut: 'Zaman aşımı',
        rootCrtDownload: 'Kök sertifika indirme',
        primaryKey: 'Birincil anahtar',
        certificate: 'Sertifika',
        backupJump:
            'Geçerli yedekleme listesinde olmayan yedekleme dosyaları, lütfen dosya dizininden indirmeyi deneyin ve yedekleme için içe aktarın.',
        snapshot: 'Anlık Görüntü | Anlık Görüntüler',
        noAppData: 'Seçim için mevcut sistem uygulaması yok',
        noBackupData: 'Seçim için mevcut yedekleme verisi yok',
        stepBaseData: 'Temel Veri',
        stepAppData: 'Sistem Uygulaması',
        stepPanelData: 'Sistem Verisi',
        stepBackupData: 'Yedekleme Verisi',
        stepOtherData: 'Diğer Veri',
        operationLog: 'İşlem Günlüğünü Sakla',
        loginLog: 'Erişim Günlüğünü Sakla',
        systemLog: 'Sistem Günlüğünü Sakla',
        taskLog: 'Görev Günlüğünü Sakla',
        monitorData: 'İzleme Verilerini Sakla',
        selectAllImage: 'Tüm Uygulama Görüntülerini Yedekle',
        logLabel: 'Günlük',
        agentLabel: 'Düğüm Yapılandırması',
        appDataLabel: 'Uygulama Verisi',
        appImage: 'Uygulama Görüntüsü',
        appBackup: 'Uygulama Yedeklemesi',
        backupLabel: 'Yedekleme Dizini',
        confLabel: 'Yapılandırma Dosyası',
        dockerLabel: 'Konteyner',
        taskLabel: 'Zamanlanmış Görev',
        resourceLabel: 'Uygulama Kaynak Dizini',
        runtimeLabel: 'Çalışma Zamanı Ortamı',
        appLabel: 'Uygulama',
        databaseLabel: 'Veritabanı',
        snapshotLabel: 'Anlık Görüntü Dosyası',
        websiteLabel: 'Web sitesi',
        directoryLabel: 'Dizin',
        appStoreLabel: 'Uygulama Mağazası',
        shellLabel: 'Komut Dosyası',
        tmpLabel: 'Geçici Dizin',
        sslLabel: 'Sertifika Dizini',
        reCreate: 'Anlık görüntü oluşturma başarısız',
        reRollback: 'Anlık görüntü geri alma başarısız',
        deleteHelper:
            'Üçüncü taraf yedekleme hesabındaki dosyalar da dahil olmak üzere tüm anlık görüntü dosyaları silinecek. Devam etmek istiyor musunuz?',
        status: 'Anlık görüntü durumu',
        ignoreRule: 'Yoksayma kuralı',
        editIgnoreRule: '@:commons.button.edit @.lower:setting.ignoreRule',
        ignoreHelper:
            'Bu kural, anlık görüntü oluştururken 1Panel veri dizinini sıkıştırmak ve yedeklemek için kullanılacaktır. Varsayılan olarak, soket dosyaları yok sayılır.',
        ignoreHelper1: 'Her satıra bir tane. Örneğin,\n*.log\n/opt/1panel/cache',
        panelInfo: '1Panel temel bilgilerini yaz',
        panelBin: '1Panel sistem dosyalarını yedekle',
        daemonJson: 'Docker yapılandırma dosyasını yedekle',
        appData: '1Panel’den yüklenen uygulamaları yedekle',
        panelData: '1Panel veri dizinini yedekle',
        backupData: '1Panel için yerel yedekleme dizinini yedekle',
        compress: 'Anlık görüntü dosyası oluştur',
        upload: 'Anlık görüntü dosyasını yükle',
        recoverDetail: 'Kurtarma detayı',
        createSnapshot: 'Anlık görüntü oluştur',
        importSnapshot: 'Anlık görüntüyü senkronize et',
        importHelper: 'Anlık görüntü dizini: ',
        lastRecoverAt: 'Son kurtarma zamanı',
        lastRollbackAt: 'Son geri alma zamanı',
        reDownload: 'Yedekleme dosyasını tekrar indir',
        recoverErrArch: 'Farklı sunucu mimarileri arasında anlık görüntü kurtarma desteklenmez!',
        recoverErrSize: 'Yetersiz disk alanı tespit edildi, lütfen kontrol edin veya temizleyin ve tekrar deneyin!',
        recoverHelper:
            '{0} anlık görüntüsünden kurtarma başlatılıyor, lütfen devam etmeden önce aşağıdaki bilgileri onaylayın:',
        recoverHelper1: 'Kurtarma, Docker ve 1Panel servislerinin yeniden başlatılmasını gerektirir',
        recoverHelper2:
            'Sunucuda yeterli disk alanının olduğundan emin olun (Anlık görüntü dosyası boyutu: {0}, Mevcut alan: {1})',
        recoverHelper3:
            'Sunucu mimarisinin, anlık görüntünün oluşturulduğu sunucu mimarisiyle eşleştiğinden emin olun (Geçerli sunucu mimarisi: {0})',
        rollback: 'Geri al',
        rollbackHelper:
            'Bu kurtarmayı geri almak, bu kurtarmadan gelen tüm dosyaları değiştirecek ve Docker ile 1Panel servislerinin yeniden başlatılmasını gerektirebilir. Devam etmek istiyor musunuz?',
        upgradeRecord: 'Yükseltme kaydı',
        upgrading: 'Yükseltiliyor, lütfen bekleyin...',
        upgradeHelper: 'Yükseltme, 1Panel servisinin yeniden başlatılmasını gerektirir. Devam etmek istiyor musunuz?',
        noUpgrade: 'Şu anda en son sürüm',
        versionHelper:
            'Ad kuralları: [ana sürüm].[fonksiyonel sürüm].[Hata düzeltme sürümü], aşağıdaki örnekte gösterildiği gibi:',
        rollbackLocalHelper:
            'Birincil düğüm doğrudan geri almayı desteklemez. Lütfen geri almak için [1pctl restore] komutunu manuel olarak çalıştırın!',
        upgradeCheck: 'Güncellemeleri kontrol et',
        upgradeNotes: 'Sürüm notu',
        upgradeNow: 'Şimdi yükselt',
        source: 'İndirme kaynağı',
        versionNotSame: 'Düğüm sürümü ana düğümle uyuşmuyor. Lütfen Düğüm Yönetiminde yükseltin ve tekrar deneyin.',
        versionCompare:
            '{0} düğümünün zaten en son yükseltilebilir sürümde olduğu tespit edildi. Lütfen birincil düğüm sürümünü kontrol edin ve tekrar deneyin!',
        about: 'Hakkında',
        project: 'GitHub',
        issue: 'Geri bildirim',
        doc: 'Resmi belge',
        star: 'Yıldız',
        description: 'Linux Sunucu Paneli',
        forum: 'Tartışmalar',
        doc2: 'Belgeler',
        currentVersion: 'Sürüm',
        license: 'Lisans',
        bindNode: 'Düğüm Bağla',
        menuSetting: 'Menü Ayarları',
        menuSettingHelper: 'Yalnızca 1 alt menü varsa, menü çubuğu yalnızca o alt menüyü gösterecektir',
        showAll: 'Tümünü Göster',
        hideALL: 'Tümünü Gizle',
        ifShow: 'Gösterme Durumu',
        menu: 'Menü',
        confirmMessage: 'Gelişmiş menü listesini güncellemek için sayfa yenilenecek. Devam etmek istiyor musunuz?',
        compressPassword: 'Sıkıştırma parolası',
        backupRecoverMessage: 'Lütfen sıkıştırma veya sıkıştırma açma parolasını girin (ayarlamamak için boş bırakın)',
    },
    license: {
        community: 'OSS',
        oss: 'Açık Kaynak Yazılım',
        pro: 'Pro',
        trial: 'Deneme',
        add: 'Topluluk Sürümünü Ekle',
        licenseAlert:
            'Topluluk Sürümü düğümleri yalnızca lisans bir düğüme düzgün şekilde bağlandığında eklenebilir. Yalnızca düzgün şekilde bağlanmış düğümler geçiş yapmayı destekler.',
        licenseUnbindHelper:
            'Bu lisans için Topluluk Sürümü düğümleri tespit edildi. Lütfen bağlamayı kaldırın ve tekrar deneyin!',
        subscription: 'Abonelik',
        perpetual: 'Süresiz',
        versionConstraint: '{0} Sürüm Satın Alma',
        forceUnbind: 'Zorla Bağlantıyı Kaldır',
        forceUnbindHelper:
            'Zorla bağlantı kaldırma, bağlantı kaldırma işlemi sırasında oluşan hataları yok sayar ve nihayetinde lisans bağını serbest bırakır.',
        updateForce:
            'Zorla güncelle (bağlantı kaldırma sırasında tüm hataları yok sayarak son işlemin başarılı olmasını sağla)',
        trialInfo: 'Sürüm',
        authorizationId: 'Abonelik yetkilendirme kimliği',
        authorizedUser: 'Yetkili kullanıcı',
        lostHelper:
            'Lisans maksimum yeniden deneme sayısına ulaştı. Profesyonel sürüm özelliklerinin düzgün çalıştığından emin olmak için lütfen senkronizasyon düğmesine manuel olarak tıklayın.',
        exceptionalHelper:
            'Lisans senkronizasyon doğrulaması anormal. Profesyonel sürüm işlevlerinin düzgün çalıştığından emin olmak için lütfen senkronizasyon düğmesine manuel olarak tıklayın. detay: ',
        quickUpdate: 'Hızlı güncelleme',
        import: 'İçe aktar',
        power: 'Yetkilendir',
        unbindHelper: 'Bağlantı kaldırıldığında tüm Pro ile ilgili ayarlar temizlenecek. Devam etmek istiyor musunuz? ',
        importLicense: 'Lisansı içe aktar',
        importHelper: 'Lütfen lisans dosyasını buraya tıklayın veya sürükleyin',
        levelUpPro: 'Profesyonel Sürüme Yükselt',
        licenseSync: 'Lisans Senkronizasyonu',
        knowMorePro: 'Daha Fazla Bilgi Edinin',
        closeAlert: 'Geçerli sayfa panel ayarlarında kapatılabilir',
        introduce: 'Özellik Tanıtımı',
        waf: 'Profesyonel sürüme yükseltme, engelleme haritası, günlükler, engelleme kayıtları, coğrafi konum engelleme, özel kurallar, özel engelleme sayfaları gibi özellikler sağlayabilir.',
        tamper: 'Profesyonel sürüme yükseltme, web sitelerini yetkisiz değişikliklerden veya kurcalamalardan koruyabilir.',
        tamperHelper:
            'İşlem başarısız oldu, dosya veya klasörde kurcalama koruması etkin. Lütfen kontrol edin ve tekrar deneyin!',
        setting:
            'Profesyonel sürüme yükseltme, panel logosu, hoş geldiniz mesajı ve diğer bilgilerin özelleştirilmesine olanak tanır.',
        monitor:
            'Profesyonel sürüme yükseltme, web sitesinin gerçek zamanlı durumunu, ziyaretçi trendlerini, ziyaretçi kaynaklarını, istek günlüklerini ve diğer bilgileri görüntülemenize olanak tanır. ',
        alert: 'Profesyonel sürüme yükseltme, SMS yoluyla alarm bilgisi almanızı, alarm günlüklerini görüntülemenizi, çeşitli önemli olayları tamamen kontrol etmenizi ve sistemin sorunsuz çalışmasını sağlar.',
        node: 'Profesyonel sürüme yükseltme, birden fazla Linux sunucusunu 1Panel ile yönetmenize olanak tanır.',
        fileExchange: 'Profesyonel Sürüme yükseltme, birden fazla sunucu arasında hızlı dosya aktarımı sağlar.',
        app: 'Profesyonel sürüme yükseltme, mobil uygulama üzerinden hizmet bilgilerini, anormal izlemeyi vb. görüntülemenize olanak tanır. ',
        cluster: 'Profesyonel Sürüme Yükseltme, MySQL/Postgers/Reids ana-çalışan kümelerini yönetmenizi sağlar.',
    },
    clean: {
        scan: 'Taramayı başlat',
        scanHelper: '1Panel çalışma zamanında üretilen gereksiz dosyaları kolayca temizleyin',
        clean: 'Şimdi temizle',
        reScan: 'Yeniden tara',
        cleanHelper:
            'Bu, seçilen sistem gereksiz dosyalarını temizleyecek ve geri alınamaz. Devam etmek istiyor musunuz?',
        statusStatusSuggest: '(Temizlik Önerilir)',
        statusClean: '(Çok temiz)',
        statusEmpty: 'Çok temiz, temizlik gerekmiyor!',
        statusWarning: '(Dikkatli İlerleyin)',
        lastCleanTime: 'Son Temizleme: {0}',
        lastCleanHelper: 'Temizlenen dosya ve dizinler: {0}, toplam temizlenen: {1}',
        cleanSuccessful: 'Başarıyla temizlendi',
        currentCleanHelper: 'Bu oturumda temizlenen dosya ve dizinler: {0}, Toplam temizlenen: {1}',
        suggest: '(Önerilen)',
        totalScan: 'Temizlenecek toplam gereksiz dosya: ',
        selectScan: 'Seçilen toplam gereksiz dosya: ',

        system: 'Sistem Gereksiz Dosyaları',
        systemHelper:
            'Anlık görüntüler, yükseltmeler ve sürüm iterasyonları sırasında eski dosya içerikleri sırasında üretilen geçici dosyalar',
        panelOriginal: 'Sistem anlık görüntü kurtarma yedek dosyaları',
        backup: 'Geçici yedekleme dizini',
        upgrade: 'Sistem yükseltme yedek dosyaları',
        upgradeHelper: '(Sistem geri alımı için en son yükseltme yedeğini tutmanız önerilir)',
        cache: 'Sistem önbellek dosyaları',
        cacheHelper: '(Dikkatli ilerleyin, temizleme servis yeniden başlatılmasını gerektirir)',
        snapshot: 'Sistem anlık görüntü geçici dosyaları',
        rollback: 'Kurtarma öncesi yedek dosyaları',

        upload: 'Geçici Yükleme Dosyaları',
        uploadHelper: 'Sistem yedek listesinden yüklenen geçici dosyalar',
        download: 'Geçici İndirme Dosyaları',
        downloadHelper: 'Sistem tarafından üçüncü taraf yedek hesaplarından indirilen geçici dosyalar',
        directory: 'Dizin',

        systemLog: 'Sistem Günlük Dosyaları',
        systemLogHelper:
            'Sistem günlük bilgileri, konteyner oluşturma veya görüntü çekme günlük bilgileri ve zamanlanmış görevlerde üretilen günlük dosyaları',
        dockerLog: 'Konteyner işlem günlük dosyaları',
        taskLog: 'Zamanlanmış görev yürütme günlük dosyaları',
        shell: 'Shell betik zamanlanmış görevler',
        containerShell: 'Konteyner içi Shell betik zamanlanmış görevler',
        curl: 'CURL zamanlanmış görevler',

        containerTrash: 'Konteyner Çöpü',
        volumes: 'Birimler',
        buildCache: 'Konteyner Oluşturma Önbelleği',
    },
    app: {
        app: 'Uygulama | Uygulamalar',
        installName: 'Ad',
        installed: 'Kurulu',
        all: 'Tümü',
        version: 'Sürüm',
        detail: 'Detaylar',
        params: 'Parametreleri düzenle',
        author: 'Yazar',
        source: 'Kaynak',
        appName: 'Uygulama Adı',
        deleteWarn:
            'Silme işlemi tüm verileri ve yedekleri birlikte silecektir. Bu işlem geri alınamaz. Devam etmek istiyor musunuz? ',
        syncSuccess: 'Başarıyla senkronize edildi',
        canUpgrade: 'Güncellemeler',
        backupName: 'Dosya Adı',
        backupPath: 'Dosya Yolu',
        backupdate: 'Yedekleme zamanı',
        versionSelect: 'Lütfen bir the sürüm seçin',
        operatorHelper: 'Seçilen uygulamada {0} işlemi gerçekleştirilecek. Devam etmek istiyor musunuz?',
        startOperatorHelper: 'Uygulama başlatılacak. Devam etmek istiyor musunuz?',
        stopOperatorHelper: 'Uygulama durdurulacak. Devam etmek istiyor musunuz?',
        restartOperatorHelper: 'Uygulama yeniden başlatılacak. Devam etmek istiyor musunuz?',
        reloadOperatorHelper: 'Uygulama yeniden yüklenecek. Devam etmek istiyor musunuz?',
        checkInstalledWarn: '"{0}" tespit edilmedi. Kurulum için "Uygulama Mağazası"na gidin.',
        gotoInstalled: 'Kurulum için git',
        limitHelper: 'Uygulama zaten kurulmuş.',
        deleteHelper: '"{0}" aşağıdaki kaynak(lar) ile ilişkilendirilmiş ve silinemez',
        checkTitle: 'İpucu',
        defaultConfig: 'Varsayılan yapılandırma',
        defaultConfigHelper: 'Varsayılan yapılandırmaya geri yüklendi, kaydetmeden sonra etkili olacaktır',
        forceDelete: 'Zorla sil',
        forceDeleteHelper:
            'Zorla silme, silme işlemi sırasında oluşan hataları yok sayar ve nihayetinde meta verileri siler.',
        deleteBackup: 'Yedeği sil',
        deleteBackupHelper: 'Uygulama yedeğini de sil',
        deleteDB: 'Veritabanını sil',
        deleteDBHelper: 'Veritabanını da sil',
        noService: '{0} Yok',
        toInstall: 'Kurulum için git',
        param: 'Parametreler',
        alreadyRun: 'Yaş',
        syncAppList: 'Senkronize et',
        less1Minute: '1 dakikadan az',
        appOfficeWebsite: 'Web sitesi',
        github: 'Github',
        document: 'Belge',
        updatePrompt: 'Kullanılabilir güncelleme yok',
        installPrompt: 'Henüz uygulama kurulmadı',
        updateHelper:
            'Parametrelerin düzenlenmesi uygulamanın başlatılamamasına neden olabilir. Lütfen dikkatli ilerleyin.',
        updateWarn:
            'Parametre güncellemesi uygulamanın yeniden oluşturulmasını gerektirir, Devam etmek istiyor musunuz? ',
        busPort: 'Bağlantı Noktası',
        syncStart: 'Senkronizasyon başlıyor! Lütfen uygulama mağazasını daha sonra yenileyin',
        advanced: 'Gelişmiş ayarlar',
        cpuCore: 'çekirdek(ler)',
        containerName: 'Konteyner adı',
        containerNameHelper: 'Konteyner adı ayarlanmadığında otomatik olarak oluşturulacaktır',
        allowPort: 'Dış erişim',
        allowPortHelper:
            'Dış bağlantı noktası erişimine izin vermek, güvenlik duvarı bağlantı noktasını serbest bırakacaktır',
        appInstallWarn:
            'Uygulama varsayılan olarak dış erişim bağlantı noktasını açmaz. Açmak için "Gelişmiş ayarlar"a tıklayın.',
        upgradeStart: 'Yükseltme başlıyor! Lütfen sayfayı daha sonra yenileyin',
        toFolder: 'Kurulum dizinini aç',
        editCompose: 'Compose dosyasını düzenle',
        editComposeHelper: 'Compose dosyasını düzenlemek, yazılım kurulumunun başarısız olmasına neden olabilir',
        composeNullErr: 'compose boş olamaz',
        takeDown: 'Kaldır',
        allReadyInstalled: 'Kurulu',
        installHelper: 'Görüntü çekme sorunlarınız varsa, görüntü hızlandırmasını yapılandırın.',
        installWarn:
            'Dış erişim işaretlenmedi, bu uygulama dış ağ üzerinden erişilemez hale getirecek. Devam etmek istiyor musunuz?',
        showIgnore: 'Yoksayılan uygulamaları görüntüle',
        cancelIgnore: 'Yoksaymayı iptal et',
        ignoreList: 'Yoksayılan uygulamalar',
        appHelper: 'Bazı özel uygulamalar için kurulum talimatlarını öğrenmek için uygulama detayları sayfasına gidin.',
        backupApp: 'Yükseltmeden önce uygulamayı yedekle',
        backupAppHelper:
            'Yükseltme başarısız olursa, yedek otomatik olarak geri alınır. Lütfen hata nedenini günlük denetim-sistem günlüğünde kontrol edin',
        openrestyDeleteHelper:
            'OpenResty`nin zorla silinmesi tüm web sitelerini silecektir. Devam etmek istiyor musunuz?',
        downloadLogHelper1: '{0} uygulamasının tüm günlükleri indirilmek üzere. Devam etmek istiyor musunuz? ',
        downloadLogHelper2: '{0} uygulamasının en son {1} günlüğü indirilmek üzere. Devam etmek istiyor musunuz? ',
        syncAllAppHelper: 'Tüm uygulamalar senkronize edilecek. Devam etmek istiyor musunuz? ',
        hostModeHelper:
            'Geçerli uygulama ağ modu ana bilgisayar modudur. Bağlantı noktasını açmanız gerekiyorsa, lütfen güvenlik duvarı sayfasında manuel olarak açın.',
        showLocal: 'Yerel uygulamaları göster',
        reload: 'Yeniden yükle',
        upgradeWarn:
            'Uygulamanın yükseltilmesi docker-compose.yml dosyasını değiştirecektir. Herhangi bir değişiklik varsa, dosya karşılaştırmasını görüntülemek için tıklayabilirsiniz',
        newVersion: 'Yeni sürüm',
        oldVersion: 'Mevcut sürüm',
        composeDiff: 'Dosya karşılaştırması',
        showDiff: 'Karşılaştırmayı görüntüle',
        useNew: 'Özel sürümü kullan',
        useDefault: 'Varsayılan sürümü kullan',
        useCustom: 'docker-compose.yml dosyasını özelleştir',
        useCustomHelper:
            'Özel bir docker-compose.yml dosyası kullanmak, uygulama yükseltmesinin başarısız olmasına neden olabilir. Gerekli değilse, işaretlemeyin.',
        diffHelper:
            'Sol taraf eski sürüm, sağ taraf yeni sürümdür. Düzenlemeden sonra, özel sürümü kaydetmek için tıklayın',
        pullImage: 'Görüntüyü Çek',
        pullImageHelper: 'Uygulama başlamadan önce docker pull ile görüntüyü çekin',
        deleteImage: 'Görüntüyü Sil',
        deleteImageHelper: 'Uygulamayla ilgili görüntüyü silin. Silme başarısız olursa görev durmaz',
        requireMemory: 'Bellek',
        supportedArchitectures: 'Mimariler',
        link: 'Bağlantı',
        showCurrentArch: 'Mimari',
        syncLocalApp: 'Yerel Uygulamayı Senkronize Et',
        memoryRequiredHelper: 'Geçerli uygulama bellek gereksinimi {0}',
        gpuConfig: 'GPU Desteğini Etkinleştir',
        gpuConfigHelper:
            'Makinenin bir NVIDIA GPU`su olduğundan ve NVIDIA sürücülerinin ve NVIDIA Docker Container Toolkit`in kurulu olduğundan emin olun',
        webUI: 'Web Erişim Adresi',
        webUIPlaceholder: 'Örneğin: example.com:8080/login',
        defaultWebDomain: 'Varsayılan Erişim Adresi',
        defaultWebDomainHepler:
            'Uygulama bağlantı noktası 8080 ise, yönlendirme adresi http(s)://varsayılan erişim adresi:8080 olacaktır',
        webUIConfig:
            'Geçerli düğümde varsayılan erişim adresi yapılandırılmamış. Lütfen bunu uygulama parametrelerinde veya panel ayarlarına giderek yapılandırın!',
        toLink: 'Aç',
        customAppHelper: 'Özel uygulama mağazası paketini kurmadan önce, kurulu uygulama olmadığından emin olun.',
        forceUninstall: 'Zorla Kaldır',
        syncCustomApp: 'Özel Uygulamayı Senkronize Et',
        ignoreAll: 'Tüm sonraki sürümleri yoksay',
        ignoreVersion: 'Belirtilen sürümü yoksay',
        specifyIP: 'Ana Bilgisayar IP`sini Bağla',
        specifyIPHelper:
            'Bağlantı noktasını bağlamak için ana bilgisayar adresini/ağ arayüzünü ayarlayın (bunda emin değilseniz, lütfen doldurmayın)',
        uninstallDeleteBackup: 'Uygulamayı Kaldır - Yedeği Sil',
        uninstallDeleteImage: 'Uygulamayı Kaldır - Görüntüyü Sil',
        upgradeBackup: 'Yükseltmeden Önce Uygulamayı Yedekle',
    },
    website: {
        primaryDomain: 'Birincil alan adı',
        otherDomains: 'Diğer alan adları',
        static: 'Statik',
        deployment: 'Dağıtım',
        supportUpType: 'Yalnızca .tar.gz dosyaları desteklenir',
        zipFormat: '.tar.gz sıkıştırılmış paket yapısı: test.tar.gz sıkıştırılmış paket {0} dosyasını içermelidir',
        proxy: 'Ters vekil',
        alias: 'Takma ad',
        ftpUser: 'FTP hesabı',
        ftpPassword: 'FTP şifresi',
        ftpHelper:
            'Bir web sitesi oluşturulduktan sonra, ilgili bir FTP hesabı oluşturulacak ve FTP dizini web sitesi dizinine bağlanacaktır.',
        remark: 'Açıklama',
        groupSetting: 'Grup Yönetimi',
        createGroup: 'Grup Oluştur',
        appNew: 'Yeni Uygulama',
        appInstalled: 'Kurulu uygulama',
        create: 'Oluştur',
        delete: 'Web Sitesini Sil',
        deleteApp: 'Uygulamayı Sil',
        deleteBackup: 'Yedeği Sil',
        domain: 'Alan Adı',
        domainHelper:
            'Her satırda bir alan adı.\nJoker karakter "*" ve IP adresi desteklenir.\nBağlantı noktası ekleme desteklenir.',
        addDomain: 'Ekle',
        domainConfig: 'Alan Adları',
        defaultDoc: 'Belge',
        perserver: 'Eşzamanlılık',
        perserverHelper: 'Geçerli sitenin maksimum eşzamanlılığını sınırlandır',
        perip: 'Tek IP',
        peripHelper: 'Tek bir IP için eşzamanlı erişim sayısını sınırlandır',
        rate: 'Trafik sınırları',
        rateHelper: 'Her istek için akış sınırlandır (birim: KB)',
        limitHelper: 'Akış kontrolünü etkinleştir',
        other: 'Diğer',
        currentSSL: 'Geçerli Sertifika',
        dnsAccount: 'DNS hesabı',
        applySSL: 'Sertifika Başvurusu',
        SSLList: 'Sertifika Listesi',
        createDnsAccount: 'DNS hesabı',
        aliyun: 'Aliyun DNS',
        manual: 'Manuel çözümleme',
        key: 'Anahtar',
        check: 'Görüntüle',
        acmeAccountManage: 'ACME hesaplarını yönet',
        email: 'E-posta',
        acmeAccount: 'ACME hesabı',
        provider: 'Doğrulama yöntemi',
        dnsManual: 'Manuel Çözümleme',
        expireDate: 'Son kullanma tarihi',
        brand: 'Organizasyon',
        deploySSL: 'Dağıtım',
        deploySSLHelper: 'Sertifikayı dağıtmaya emin misiniz? ',
        ssl: 'Sertifika | Sertifikalar',
        dnsAccountManage: 'DNS sağlayıcılarını yönet',
        renewSSL: 'Yenile',
        renewHelper: 'Sertifikayı yenilemeye emin misiniz? ',
        renewSuccess: 'Sertifika yenileme',
        enableHTTPS: 'Etkinleştir',
        aliasHelper: 'Takma ad, web sitesinin dizin adıdır',
        lastBackupAt: 'son yedekleme zamanı',
        null: 'hiçbiri',
        nginxConfig: 'Nginx yapılandırması',
        websiteConfig: 'Web sitesi ayarları',
        basic: 'Temel',
        source: 'Yapılandırma',
        security: 'Güvenlik',
        nginxPer: 'Performans ayarı',
        neverExpire: 'Asla',
        setDefault: 'Varsayılan olarak ayarla',
        deleteHelper: 'İlgili uygulama durumu anormal, lütfen kontrol edin',
        toApp: 'Kurulu listeye git',
        cycle: 'Döngü',
        frequency: 'Frekans',
        ccHelper:
            '{0} saniye içinde aynı URL’ye {1} defadan fazla istekte bulunulursa, CC savunması tetiklenir ve bu IP engellenir',
        mustSave: 'Değişikliğin etkili olması için kaydedilmesi gerekiyor',
        fileExt: 'dosya uzantısı',
        fileExtBlock: 'dosya uzantısı engelleme listesi',
        value: 'değer',
        enable: 'Etkinleştir',
        proxyAddress: 'Vekil Adresi',
        proxyHelper: 'Örnek: 127.0.0.1:8080',
        forceDelete: 'Zorla Sil',
        forceDeleteHelper:
            'Zorla silme, silme işlemi sırasında oluşan hataları yok sayar ve nihayetinde meta verileri siler.',
        deleteAppHelper: 'İlgili uygulamaları ve uygulama yedeklerini aynı anda sil',
        deleteBackupHelper: 'Web sitesi yedeklerini de sil.',
        deleteConfirmHelper:
            'Silme işlemi geri alınamaz. Silmeyi onaylamak için <span style="color:red"> "{0}" </span> yazın.',
        staticPath: 'Karşılık gelen ana dizin ',
        limit: 'Şema',
        blog: 'Forum/Blog',
        imageSite: 'Resim Sitesi',
        downloadSite: 'İndirme Sitesi',
        shopSite: 'Alışveriş Merkezi',
        doorSite: 'Portal',
        qiteSite: 'Kurumsal',
        videoSite: 'Video',
        errLog: 'Hata günlüğü',
        accessLog: 'Web sitesi günlüğü',
        stopHelper:
            'Site durdurulduktan sonra normal şekilde erişilemeyecek ve kullanıcı siteyi ziyaret ettiğinde mevcut sitenin durdurma sayfası görüntülenecektir. Devam etmek istiyor musunuz? ',
        startHelper:
            'Site etkinleştirildikten sonra kullanıcılar site içeriğine normal şekilde erişebilir, devam etmek istiyor musunuz? ',
        sitePath: 'Dizin',
        siteAlias: 'Site takma adı',
        primaryPath: 'Kök dizin',
        folderTitle: 'Web sitesi esas olarak aşağıdaki klasörleri içerir',
        wafFolder: 'Güvenlik duvarı kuralları',
        indexFolder: 'Web sitesi kök dizini',
        logFolder: 'Web sitesi günlüğü',
        sslFolder: 'Web Sitesi Sertifikası',
        enableOrNot: 'Etkinleştir',
        oldSSL: 'Mevcut sertifika',
        manualSSL: 'Sertifikayı içe aktar',
        select: 'Seç',
        selectSSL: 'Sertifika Seç',
        privateKey: 'Anahtar (KEY)',
        certificate: 'Sertifika (PEM formatı)',
        HTTPConfig: 'HTTP Seçenekleri',
        HTTPSOnly: 'HTTP isteklerini engelle',
        HTTPToHTTPS: 'HTTPS’ye yönlendir',
        HTTPAlso: 'Doğrudan HTTP isteklerine izin ver',
        sslConfig: 'SSL seçenekleri',
        disableHTTPS: 'HTTPS’yi devre dışı bırak',
        disableHTTPSHelper:
            'HTTPS’nin devre dışı bırakılması, sertifika ile ilgili yapılandırmayı silecektir, Devam etmek istiyor musunuz?',
        SSLHelper:
            'Not: SSL sertifikalarını yasa dışı web siteleri için kullanmayın.\nHTTPS erişimi açıldıktan sonra kullanılamıyorsa, güvenlik grubunun 443 numaralı bağlantı noktasını doğru şekilde serbest bırakıp bırakmadığını kontrol edin.',
        SSLConfig: 'Sertifika ayarları',
        SSLProConfig: 'Protokol ayarları',
        supportProtocol: 'Protokol sürümü',
        encryptionAlgorithm: 'Şifreleme algoritması',
        notSecurity: '(güvenli değil)',
        encryptHelper:
            "Let's Encrypt, sertifika verme sıklığı için bir sınırlama getirir, ancak bu normal ihtiyaçları karşılamak için yeterlidir. Çok sık işlemler, verme işleminin başarısız olmasına neden olur. Özel kısıtlamalar için lütfen <a target='_blank' href='https://letsencrypt.org/zh-cn/docs /rate-limits/'>resmi belgeye</a> bakın ",
        ipValue: 'Değer',
        ext: 'dosya uzantısı',
        wafInputHelper: 'Verileri satır satır girin, her satır bir veri',
        data: 'veri',
        ever: 'kalıcı',
        nextYear: 'Bir yıl sonra',
        noLog: 'Günlük bulunamadı',
        defaultServer: 'Varsayılan siteyi ayarla',
        noDefaultServer: 'Ayarlanmadı',
        defaultServerHelper:
            'Varsayılan site ayarlandıktan sonra, bağlanmamış tüm alan adları ve IP’ler varsayılan siteye yönlendirilecektir\nBu, kötü niyetli çözümlemeyi etkili bir şekilde önleyebilir\nAncak, bu aynı zamanda WAF yetkisiz alan adı engellemesinin başarısız olmasına neden olur',
        restoreHelper: 'Bu yedeği kullanarak geri yüklemeye emin misiniz?',
        websiteDeploymentHelper:
            'Bir web sitesi oluşturmak için kurulu bir uygulamayı kullanın veya yeni bir uygulama oluşturun.',
        websiteStatictHelper: 'Ana bilgisayarda bir web sitesi dizini oluşturun.',
        websiteProxyHelper:
            'Mevcut bir servisi vekil etmek için ters vekil kullanın. Örneğin, bir servis 8080 numaralı bağlantı noktasında kurulu ve çalışıyorsa, vekil adresi "http://127.0.0.1:8080" olacaktır.',
        runtimeProxyHelper: 'Bir web sitesi oluşturmak için bir web sitesi çalışma zamanını kullanın.',
        runtime: 'Çalışma Zamanı',
        deleteRuntimeHelper:
            'Çalışma zamanı uygulaması, web sitesiyle birlikte silinmelidir, lütfen dikkatli bir şekilde işlem yapın',
        proxyType: 'Ağ Türü',
        unix: 'Unix Ağı',
        tcp: 'TCP/IP Ağı',
        phpFPM: 'FPM Yapılandırması',
        phpConfig: 'PHP Yapılandırması',
        updateConfig: 'Yapılandırmayı Güncelle',
        isOn: 'Açık',
        isOff: 'Kapalı',
        rewrite: 'Sahte statik',
        rewriteMode: 'Şema',
        current: 'Mevcut',
        rewriteHelper:
            'Sahte statik ayarlarının yapılması web sitesinin erişilemez hale gelmesine neden oluyorsa, varsayılan ayarlara geri dönmeyi deneyin.',
        runDir: 'Çalışma Dizini',
        runUserHelper:
            'PHP konteyner çalışma zamanı ortamı üzerinden dağıtılan web siteleri için, index ve alt dizinler altındaki tüm dosya ve klasörlerin sahibi ve kullanıcı grubu 1000 olarak ayarlanmalıdır. Yerel PHP ortamı için, yerel PHP-FPM kullanıcı ve kullanıcı grubu ayarlarına bakın',
        userGroup: 'Kullanıcı/Grup',
        uGroup: 'Grup',
        proxyPath: 'Vekil yolu',
        proxyPass: 'Hedef URL',
        cache: 'Önbellek',
        cacheTime: 'Önbellek süresi',
        enableCache: 'Önbellek',
        proxyHost: 'Vekil ana bilgisayarı',
        disabled: 'Durduruldu',
        startProxy: 'Bu, ters vekili başlatacak. Devam etmek istiyor musunuz?',
        stopProxy: 'Bu, ters vekili durduracak. Devam etmek istiyor musunuz?',
        sourceFile: 'Kaynağı görüntüle',
        proxyHelper1: 'Bu dizine erişildiğinde, hedef URL’nin içeriği döndürülecek ve görüntülenecektir.',
        proxyPassHelper: 'Hedef URL geçerli ve erişilebilir olmalıdır.',
        proxyHostHelper: 'İstek başlığındaki alan adını vekil sunucusuna ilet.',
        modifier: 'Eşleştirme kuralları',
        modifierHelper: 'Örnek: "=" tam eşleşme, "~" düzenli eşleşme, "^~" yolun başlangıcıyla eşleşme vb.',
        replace: 'Metin değiştirmeleri',
        addReplace: 'Ekle',
        replaced: 'Arama Dizisi (boş olamaz)',
        replaceText: 'Şununla değiştir',
        replacedErr: 'Arama Dizisi boş olamaz',
        replacedErr2: 'Arama Dizisi tekrarlanamaz',
        basicAuth: 'Temel kimlik doğrulama',
        editBasicAuthHelper:
            'Şifre asimetrik olarak şifrelenir ve geri alınamaz. Düzenleme, şifrenin sıfırlanmasını gerektirir',
        antiLeech: 'Sömürü karşıtı',
        extends: 'Uzantı',
        browserCache: 'Önbellek',
        leechLog: 'Sömürü karşıtı günlüğü kaydet',
        accessDomain: 'İzin verilen alan adları',
        leechReturn: 'Yanıt kaynağı',
        noneRef: 'Boş yönlendirme izni ver',
        disable: 'etkin değil',
        disableLeechHelper: 'Sömürü karşıtını devre dışı bırakıp bırakmamak',
        disableLeech: 'Sömürü karşıtını devre dışı bırak',
        ipv6: 'IPv6’yı dinle',
        leechReturnError: 'Lütfen HTTP durum kodunu doldurun',
        selectAcme: 'Acme hesabını seç',
        imported: 'Manuel olarak oluşturuldu',
        importType: 'İçe aktarma türü',
        pasteSSL: 'Kodu yapıştır',
        localSSL: 'Sunucu dosyasını seç',
        privateKeyPath: 'Özel anahtar dosyası',
        certificatePath: 'Sertifika dosyası',
        ipWhiteListHelper: 'IP izin listesinin rolü: IP izin listesindeki tüm kurallar geçersizdir',
        redirect: 'Yönlendirme',
        sourceDomain: 'Kaynak alan adı',
        targetURL: 'Hedef URL adresi',
        keepPath: 'URI parametreleri',
        path: 'yol',
        redirectType: 'yönlendirme türü',
        redirectWay: 'Yol',
        keep: 'tut',
        notKeep: 'Tutma',
        redirectRoot: 'Ana sayfaya yönlendir',
        redirectHelper: '301 kalıcı yönlendirme, 302 geçici yönlendirme',
        changePHPVersionWarn: 'Bu işlem geri alınamaz, devam etmek istiyor musunuz?',
        changeVersion: 'Sürümü değiştir',
        retainConfig: 'php-fpm.conf ve php.ini dosyalarını saklayıp saklamamak',
        runDirHelper2: 'Lütfen ikincil çalışma dizininin index dizini altında olduğundan emin olun',
        openrestyHelper:
            'OpenResty varsayılan HTTP bağlantı noktası: {0} HTTPS bağlantı noktası: {1}, bu web sitesi alan adı erişimini ve HTTPS zorunlu yönlendirmesini etkileyebilir',
        primaryDomainHelper: 'Örnek: example.com veya example.com:8080',
        acmeAccountType: 'Hesap türü',
        keyType: 'Anahtar algoritması',
        tencentCloud: 'Tencent Cloud',
        containWarn: 'Alan adı ana alanı içeriyor, lütfen tekrar girin',
        rewriteHelper2:
            'Uygulama mağazasından yüklenen WordPress gibi uygulamalar genellikle önceden ayarlanmış sahte statik yapılandırmayla gelir. Bunları yeniden yapılandırmak hatalara yol açabilir.',
        websiteBackupWarn:
            'Yalnızca yerel yedeklemelerin içe aktarılması desteklenir, diğer makinelerden yedeklerin içe aktarılması kurtarma hatasına neden olabilir',
        ipWebsiteWarn:
            'IP alan adlarına sahip web sitelerinin normal şekilde erişilebilmesi için varsayılan site olarak ayarlanması gerekir.',
        hstsHelper: 'HSTS’nin etkinleştirilmesi web sitesi güvenliğini artırabilir',
        includeSubDomains: 'Alt Alan Adları',
        hstsIncludeSubDomainsHelper:
            'Etkinleştirildiğinde, HSTS politikası geçerli etki alanının tüm alt alan adlarına uygulanacaktır.',
        defaultHtml: 'Varsayılan sayfayı ayarla',
        website404: 'Web sitesi 404 hata sayfası',
        domain404: 'Web sitesi sayfası mevcut değil',
        indexHtml: 'Statik web sitesi varsayılan sayfası',
        stopHtml: 'Web sitesi durdurma sayfası',
        indexPHP: 'PHP web sitesi varsayılan sayfası',
        sslExpireDate: 'SSL Son Kullanma Tarihi',
        website404Helper:
            'Web sitesi 404 hata sayfası yalnızca PHP çalışma zamanı ortamı web sitelerini ve statik web sitelerini destekler',
        sni: 'Kaynak SNI',
        sniHelper:
            'Ters vekil arka ucu HTTPS olduğunda, kaynak SNI’yi ayarlamanız gerekebilir. Ayrıntılar için CDN hizmet sağlayıcısının belgelerine bakın.',
        huaweicloud: 'Huawei Cloud',
        createDb: 'Veritabanı Oluştur',
        enableSSLHelper: 'Etkinleştirme başarısızlığı web sitesinin oluşturulmasını etkilemez',
        batchAdd: 'Toplu Alan Adı Ekle',
        generateDomain: 'Oluştur',
        global: 'Küresel',
        subsite: 'Alt site',
        subsiteHelper: 'Bir alt site, mevcut bir PHP veya statik web sitesi dizinini ana dizin olarak seçebilir.',
        parentWbeiste: 'Ana Web Sitesi',
        deleteSubsite: 'Geçerli web sitesini silmek için önce alt site(ler) {0} silinmelidir',
        loadBalance: 'Yük Dengeleme',
        server: 'Sunucu',
        algorithm: 'Algoritma',
        ipHash: 'IP Hash',
        ipHashHelper:
            'İstemci IP adresine dayalı olarak istekleri belirli bir sunucuya dağıtır, belirli bir istemcinin her zaman aynı sunucuya yönlendirilmesini sağlar.',
        leastConn: 'En Az Bağlantı',
        leastConnHelper: 'En az aktif bağlantıya sahip sunucuya istek gönderir.',
        leastTime: 'En Az Süre',
        leastTimeHelper: 'En kısa aktif bağlantı süresine sahip sunucuya istek gönderir.',
        defaultHelper:
            'Varsayılan yöntem, istekleri her sunucuya eşit şekilde dağıtır. Sunucuların ağırlıkları yapılandırılmışsa, istekler belirtilen ağırlıklara göre dağıtılır, daha yüksek ağırlıklı sunucular daha fazla istek alır.',
        weight: 'Ağırlık',
        maxFails: 'Maksimum Başarısızlık',
        maxConns: 'Maksimum Bağlantılar',
        strategy: 'Strateji',
        strategyDown: 'Kapalı',
        strategyBackup: 'Yedek',
        staticChangePHPHelper: 'Şu anda statik bir web sitesi, PHP web sitesine geçiş yapabilirsiniz',
        proxyCache: 'Ters Vekil Önbelleği',
        cacheLimit: 'Önbellek Alanı Sınırı',
        shareCahe: 'Önbellek Sayısı Bellek Boyutu',
        cacheExpire: 'Önbellek Son Kullanma Süresi',
        shareCaheHelper: 'Yaklaşık olarak 1M bellek başına 8000 önbellek nesnesi saklanabilir',
        cacheLimitHelper: 'Sınır aşıldığında eski önbellek otomatik olarak silinir',
        cacheExpireJHelper: 'Önbellek, son kullanma süresinden sonra kaçırılırsa silinir',
        realIP: 'Gerçek IP',
        ipFrom: 'IP Kaynağı',
        ipFromHelper:
            'Güvenilir IP kaynaklarını yapılandırarak, OpenResty HTTP başlıklarında IP bilgilerini analiz eder, ziyaretçilerin gerçek IP adreslerini doğru bir şekilde tanımlar ve kaydeder, erişim günlükleri dahil',
        ipFromExample1: "Ön uç bir Frp gibi bir araçsa, Frp'nin IP adresini girebilirsiniz, örneğin 127.0.0.1",
        ipFromExample2: 'Ön uç bir CDN ise, CDN’nin IP adres aralığını girebilirsiniz',
        ipFromExample3:
            'Emin değilseniz, 0.0.0.0/0 (ipv4) ::/0 (ipv6) girebilirsiniz [Not: Herhangi bir kaynak IP’ye izin vermek güvenli değildir]',
        http3Helper:
            'HTTP/3, HTTP/2’nin bir yükseltmesidir, daha hızlı bağlantı hızları ve daha iyi performans sunar, ancak tüm tarayıcılar HTTP/3’ü desteklemez. Etkinleştirilmesi bazı tarayıcıların siteye erişememesine neden olabilir.',
        changeDatabase: 'Veritabanını Değiştir',
        changeDatabaseHelper1:
            'Veritabanı ilişkilendirmesi, web sitesinin yedeklenmesi ve geri yüklenmesi için kullanılır.',
        changeDatabaseHelper2: 'Başka bir veritabanına geçiş, önceki yedeklerin kurtarılamamasına neden olur.',
        saveCustom: 'Şablon Olarak Kaydet',
        rainyun: 'Rain Yun',
        volcengine: 'Volcengine',
        runtimePortHelper:
            'Geçerli çalışma zamanı ortamı birden fazla bağlantı noktasına sahip. Lütfen bir vekil bağlantı noktası seçin.',
        runtimePortWarn: 'Geçerli çalışma zamanı ortamında bağlantı noktası yok, vekil yapılamıyor',
        cacheWarn: 'Lütfen önce ters vekildeki önbellek anahtarını kapatın',
        loadBalanceHelper:
            'Bu bölüm yalnızca yük dengeleme kuralları oluşturur, kuralları kullanmak için lütfen http(s)://<yük dengeleme adı> adresine ters vekil yapın',
        favorite: 'Favori',
        cancelFavorite: 'Favoriyi İptal Et',
        useProxy: 'Vekil Kullan',
        useProxyHelper: 'Panel ayarlarındaki vekil sunucu adresini kullan',
        westCN: 'Batı Dijital',
        openBaseDir: 'Siteler Arası Saldırıları Önle',
        openBaseDirHelper:
            'open_basedir, PHP dosya erişim yolunu kısıtlamak için kullanılır, bu siteler arası erişimi önlemeye ve güvenliği artırmaya yardımcı olur',
        serverCacheTime: 'Sunucu Önbellek Süresi',
        serverCacheTimeHelper:
            'Bir isteğin sunucuda önbelleğe alındığı süre. Bu süre boyunca, aynı istekler önbelleğe alınmış sonucu doğrudan döndürür ve kaynak sunucuya istekte bulunmaz.',
        browserCacheTime: 'Tarayıcı Önbellek Süresi',
        browserCacheTimeHelper:
            'Statik kaynakların tarayıcıda yerel olarak önbelleğe alındığı süre, tekrarlayan istekleri azaltır. Kullanıcılar süre dolmadan önce sayfayı yenilediğinde yerel önbelleği doğrudan kullanır.',
        donotLinkeDB: 'Veritabanına Bağlanma',
        toWebsiteDir: 'Web Sitesi Dizinine Gir',
    },
    php: {
        short_open_tag: 'Kısa etiket desteği',
        max_execution_time: 'Maksimum betik yürütme süresi',
        max_input_time: 'Maksimum giriş süresi',
        memory_limit: 'Betik bellek sınırı',
        post_max_size: 'POST veri maksimum boyutu',
        file_uploads: 'Dosya yüklemeye izin verilip verilmeyeceği',
        upload_max_filesize: 'Yüklenebilecek maksimum dosya boyutu',
        max_file_uploads: 'Aynı anda yüklenebilecek maksimum dosya sayısı',
        default_socket_timeout: 'Soket zaman aşımı',
        error_reporting: 'Hata seviyesi',
        display_errors: 'Ayrıntılı hata bilgilerinin 출력lenip 출력lenmeyeceği',
        cgi_fix_pathinfo: 'Pathinfo’nun açılıp açılmayacağı',
        date_timezone: 'Zaman dilimi',
        disableFunction: 'Devre dışı bırakma fonksiyonu',
        disableFunctionHelper:
            'Devre dışı bırakılacak fonksiyonu girin, örneğin exec, birden fazla için virgülle ayırın',
        uploadMaxSize: 'Yükleme sınırı',
        indexHelper:
            'PHP web sitesinin normal çalışmasını sağlamak için, lütfen kodu index dizinine yerleştirin ve yeniden adlandırmaktan kaçının',
        extensions: 'Uzantı şablonlarını yönet',
        extension: 'Uzantı',
        extensionHelper: 'Lütfen birden fazla uzantı için virgülle ayırın',
        toExtensionsList: 'Uzantı listesini görüntüle',
        containerConfig: 'Konteyner Yapılandırması',
        containerConfigHelper:
            'Oluşturulduktan sonra Yapılandırma - Konteyner Yapılandırması’nda ortam değişkenleri ve diğer bilgiler değiştirilebilir',
        dateTimezoneHelper: 'Örnek: TZ=Asia/Shanghai (Gerektiğinde ekleyin)',
    },
    nginx: {
        serverNamesHashBucketSizeHelper: 'Sunucu adının karma tablosu boyutu',
        clientHeaderBufferSizeHelper: 'İstemci tarafından istenen başlık arabelleği boyutu',
        clientMaxBodySizeHelper: 'Maksimum Yükleme Dosyası',
        keepaliveTimeoutHelper: 'Bağlantı Zaman Aşımı',
        gzipMinLengthHelper: 'Minimum Sıkıştırılmış Dosya',
        gzipCompLevelHelper: 'Sıkıştırma Oranı',
        gzipHelper: 'İletim için sıkıştırmayı etkinleştir',
        connections: 'Aktif bağlantılar',
        accepts: 'Kabul edilenler',
        handled: 'İşlenenler',
        requests: 'İstekler',
        reading: 'Okuma',
        writing: 'Yazma',
        waiting: 'Bekleme',
        status: 'Geçerli Durum',
        configResource: 'Yapılandırma',
        saveAndReload: 'Kaydet ve yeniden yükle',
        clearProxyCache: 'Ters vekil önbelleğini temizle',
        clearProxyCacheWarn: 'Bu işlem önbellek dizinindeki tüm dosyaları silecektir. Devam etmek istiyor musunuz?',
        create: 'Yeni bir modül ekle',
        update: 'Bir modülü düzenle',
        params: 'Parametreler',
        packages: 'Paketler',
        script: 'Betikler',
        module: 'Modüller',
        build: 'Oluştur',
        buildWarn:
            'OpenResty’nin oluşturulması belirli miktarda CPU ve bellek ayırmayı gerektirir, bu uzun sürebilir, lütfen sabırlı olun',
        mirrorUrl: 'Yazılım Kaynağı',
        paramsHelper: 'Örnek: --add-module=/tmp/ngx_brotli',
        packagesHelper: 'Örnek: git, curl (virgülle ayrılmış)',
        scriptHelper:
            'Derlemeden önce çalıştırılacak betikler, genellikle modül kaynak kodunu indirmek, bağımlılıkları kurmak vb. için',
        buildHelper:
            'Modül ekledikten/düzenledikten sonra oluştur’a tıklayın. OpenResty, başarılı oluşturma üzerine otomatik olarak yeniden başlatılacaktır.',
        defaultHttps: 'HTTPS Anti-sızdırma',
        defaultHttpsHelper1: 'Bu özelliği etkinleştirerek HTTPS sızdırma sorunlarını çözebilirsiniz.',
    },
    ssl: {
        create: 'İstek',
        provider: 'Tür',
        manualCreate: 'Manuel olarak oluşturuldu',
        acmeAccount: 'ACME hesabı',
        resolveDomain: 'Alan adını çözümle',
        err: 'Hata',
        value: 'kayıt değeri',
        dnsResolveHelper:
            'Lütfen aşağıdaki çözümleme kayıtlarını eklemek için DNS çözümleme servis sağlayıcısına gidin:',
        detail: 'Detayları görüntüle',
        msg: 'Bilgi',
        ssl: 'Sertifika',
        key: 'Özel anahtar',
        startDate: 'Geçerlilik zamanı',
        organization: 'veren organizasyon',
        renewConfirm: '{0} alan adı için yeni bir sertifika yenilenecek. Devam etmek istiyor musunuz?',
        autoRenew: 'Otomatik yenileme',
        autoRenewHelper: 'Son kullanma tarihinden 30 gün önce otomatik olarak yenilenir',
        renewSuccess: 'Yenileme başarılı',
        renewWebsite:
            'Bu sertifika aşağıdaki web siteleriyle ilişkilendirilmiştir ve başvuru aynı anda bu web sitelerine uygulanacaktır',
        createAcme: 'Hesap Oluştur',
        acmeHelper: 'Acme Hesabı ücretsiz sertifikalar başvurusu için kullanılır',
        upload: 'İçe aktar',
        applyType: 'Başvuru yöntemi',
        apply: 'Yenile',
        applyStart: 'Sertifika başvurusu başlıyor',
        getDnsResolve: 'DNS çözümleme değeri alınıyor, lütfen bekleyin...',
        selfSigned: 'Kendi kendine imzalı CA’yı yönet',
        ca: 'Sertifika yetkilisi',
        commonName: 'Ortak ad',
        caName: 'Sertifika yetkilisi adı',
        company: 'Organizasyon adı',
        department: 'Organizasyon birimi adı',
        city: 'Yer adı',
        province: 'Eyalet veya il adı',
        country: 'Ülke adı (2 harfli kod)',
        commonNameHelper: 'Örnek: ',
        selfSign: 'Sertifika ver',
        days: 'geçerlilik süresi',
        domainHelper: 'Her satırda bir alan adı, * ve IP adresini destekler',
        pushDir: 'Sertifikayı yerel dizine aktar',
        dir: 'Dizin',
        pushDirHelper:
            'Sertifika dosyası "fullchain.pem" ve anahtar dosyası "privkey.pem" bu dizinde oluşturulacaktır.',
        organizationDetail: 'Organizasyon detayları',
        fromWebsite: 'Web sitesinden',
        dnsMauanlHelper:
            'Manuel çözümleme modunda, oluşturduktan sonra DNS çözümleme değerini almak için başvuru düğmesine tıklamanız gerekir',
        httpHelper:
            'HTTP modunu kullanmak, OpenResty’nin kurulmasını gerektirir ve joker karakter alan adı sertifikaları için başvuruyu desteklemez.',
        buypassHelper: 'Buypass, Çin anakarasında erişilebilir değil',
        googleHelper: 'EAB HmacKey ve EAB kid nasıl alınır',
        googleCloudHelper: 'Google Cloud API, Çin anakarasının çoğu yerinde erişilebilir değil',
        skipDNSCheck: 'DNS kontrolünü atla',
        skipDNSCheckHelper: 'Sertifika isteği sırasında zaman aşımı sorunuyla karşılaşırsanız burayı işaretleyin.',
        cfHelper: 'Küresel API Anahtarını kullanmayın',
        deprecated: 'kaldırılacak',
        deprecatedHelper:
            'Bakım durduruldu ve gelecek bir sürümde terk edilebilir. Lütfen analiz için Tencent Cloud yöntemini kullanın',
        disableCNAME: 'CNAME’yi devre dışı bırak',
        disableCNAMEHelper: 'Alan adında bir CNAME kaydı varsa ve istek başarısız olursa burayı işaretleyin.',
        nameserver: 'DNS sunucusu',
        nameserverHelper: 'Alan adlarını doğrulamak için özel bir DNS sunucusu kullanın.',
        edit: 'Sertifikayı düzenle',
        execShell: 'Sertifika isteğinden sonra betiği çalıştır.',
        shell: 'Betik içeriği',
        shellHelper:
            'Betiğin varsayılan yürütme dizini, 1Panel kurulum dizinidir. Sertifika yerel dizine aktarılırsa, yürütme dizini sertifika aktarma dizini olacaktır. Varsayılan yürütme zaman aşımı 30 dakikadır.',
        customAcme: 'Özel ACME Servisi',
        customAcmeURL: 'ACME Servis URL’si',
        baiduCloud: 'Baidu Cloud',
    },
    firewall: {
        create: 'Kural oluştur',
        edit: 'Kuralı düzenle',
        ccDeny: 'CC Koruması',
        ipWhiteList: 'IP izin listesi',
        ipBlockList: 'IP engelleme listesi',
        fileExtBlockList: 'Dosya uzantısı engelleme listesi',
        urlWhiteList: 'URL izin listesi',
        urlBlockList: 'URL engelleme listesi',
        argsCheck: 'GET parametre kontrolü',
        postCheck: 'POST parametre doğrulaması',
        cookieBlockList: 'Çerez engelleme listesi',

        dockerHelper:
            'Linux güvenlik duvarı "{0}", Docker bağlantı noktası eşlemesini devre dışı bırakamaz. Uygulama, bağlantı noktasının serbest bırakılıp bırakılmayacağını kontrol etmek için "Uygulama Mağazası -> Kurulu" sayfasında parametreleri düzenleyebilir.',
        quickJump: 'Hızlı erişim',
        used: 'Kullanıldı',
        unUsed: 'Kullanılmadı',
        firewallHelper: '{0} sistem güvenlik duvarı',
        firewallNotStart: 'Sistem güvenlik duvarı şu anda etkin değil. Önce etkinleştirin.',
        restartFirewallHelper: 'Bu işlem mevcut güvenlik duvarını yeniden başlatacak. Devam etmek istiyor musunuz?',
        stopFirewallHelper: 'Bu, sunucunun güvenlik korumasını kaybetmesine neden olacak. Devam etmek istiyor musunuz?',
        startFirewallHelper:
            'Güvenlik duvarı etkinleştirildiğinde, sunucu güvenliği daha iyi korunabilir. Devam etmek istiyor musunuz?',
        noPing: 'Ping’i devre dışı bırak',
        noPingTitle: 'Ping’i devre dışı bırak',
        noPingHelper:
            'Bu, ping’i devre dışı bırakacak ve sunucu ICMP yanıtını geri göndermeyecek. Devam etmek istiyor musunuz?',
        onPingHelper: 'Bu, ping’i etkinleştirecek ve hackerlar sunucunuzu keşfedebilir. Devam etmek istiyor musunuz?',
        changeStrategy: '{0} stratejisini değiştir',
        changeStrategyIPHelper1:
            'IP adresi stratejisini [reddet] olarak değiştirin. IP adresi ayarlandıktan sonra sunucuya erişim yasaklanır. Devam etmek istiyor musunuz?',
        changeStrategyIPHelper2:
            'IP adresi stratejisini [izin ver] olarak değiştirin. IP adresi ayarlandıktan sonra normal erişim geri yüklenir. Devam etmek istiyor musunuz?',
        changeStrategyPortHelper1:
            'Bağlantı noktası politikasını [bırak] olarak değiştirin. Bağlantı noktası politikası ayarlandıktan sonra dış erişim reddedilir. Devam etmek istiyor musunuz?',
        changeStrategyPortHelper2:
            'Port politikasını [accept] olarak değiştirin. Port politikası ayarlandıktan sonra normal port erişimi geri yüklenecek. Devam etmek istiyor musunuz?',
        stop: 'Durdur',
        portFormatError: 'Bu alan geçerli bir port olmalıdır.',
        portHelper1: 'Birden fazla port, ör. 8080 ve 8081',
        portHelper2: 'Aralık portu, ör. 8080-8089',
        changeStrategyHelper:
            '[{1}] {0} stratejisini [{2}] olarak değiştirin. Ayar yapıldıktan sonra {0}, dışarıdan {2} erişimi sağlayacak. Devam etmek istiyor musunuz?',
        portHelper: 'Birden fazla port girilebilir, ör. 80,81 veya aralık portları, ör. 80-88',
        strategy: 'Strateji',
        accept: 'Kabul Et',
        drop: 'Reddet',
        anyWhere: 'Herhangi',
        address: 'Belirtilen IP’ler',
        addressHelper: 'IP adresi veya IP segmentini destekler',
        allow: 'İzin Ver',
        deny: 'Reddet',
        addressFormatError: 'Bu alan geçerli bir IP adresi olmalıdır.',
        addressHelper1: 'IP adresi veya IP aralığını destekler. Örneğin, "************" veya "***********/24".',
        addressHelper2: 'Birden fazla IP adresi için virgülle ayırın. Örneğin, "************, ***********/24".',
        allIP: 'Tüm IP’ler',
        portRule: 'Kural | Kurallar',
        createPortRule: '@:commons.button.create @.lower:firewall.portRule',
        forwardRule: 'Port-Yönlendirme kuralı | Port-Yönlendirme kuralları',
        createForwardRule: '@:commons.button.create @:firewall.forwardRule',
        ipRule: 'IP kuralı | IP kuralları',
        createIpRule: '@:commons.button.create @:firewall.ipRule',
        userAgent: 'Kullanıcı-Aracısı filtresi',
        sourcePort: 'Kaynak port',
        targetIP: 'Hedef IP',
        targetPort: 'Hedef port',
        forwardHelper1: 'Yerel porta yönlendirmek istiyorsanız, hedef IP "127.0.0.1" olarak ayarlanmalıdır.',
        forwardHelper2: 'Yerel porta yönlendirmek için hedef IP’yi boş bırakın.',
        forwardHelper3: 'Yalnızca IPv4 port yönlendirmesini destekler.',
    },
    runtime: {
        runtime: 'Çalışma Zamanı',
        workDir: 'Çalışma dizini',
        create: 'Oluştur',
        localHelper: 'Yerel çalışma ortamı kendi kendine kurulmalıdır',
        versionHelper: 'PHP sürümü, ör. v8.0',
        buildHelper:
            'Daha fazla eklenti seçilirse, görüntü oluşturma sürecinde CPU kullanımı artar. Tüm eklentileri seçmekten kaçının.',
        openrestyWarn: 'PHP’nin OpenResty’ye yükseltilmesi için ******** veya daha yeni bir sürüm gereklidir',
        toupgrade: 'Yükseltmek İçin',
        edit: 'Çalışma zamanını düzenle',
        extendHelper:
            'İhtiyacınız olan eklentiler listede yoksa, eklenti adını manuel olarak girebilirsiniz. Örneğin, "sockets" girin ve ardından ilkini seçin.',
        rebuildHelper: 'Eklenti düzenlendikten sonra PHP uygulamasının yeniden oluşturulması gerekir',
        rebuild: 'PHP Uygulamasını Yeniden Oluştur',
        source: 'PHP eklenti kaynağı',
        ustc: 'Çin Bilim ve Teknoloji Üniversitesi',
        netease: 'Netease',
        aliyun: 'Alibaba Cloud',
        tsinghua: 'Tsinghua Üniversitesi',
        xtomhk: 'XTOM Ayna İstasyonu (Hong Kong)',
        xtom: 'XTOM Ayna İstasyonu (Global)',
        phpsourceHelper: 'Ağ ortamınıza göre uygun bir kaynak seçin.',
        appPort: 'Uygulama portu',
        externalPort: 'Dış port',
        packageManager: 'Paket yöneticisi',
        codeDir: 'Kod dizini',
        appPortHelper: 'Uygulama tarafından kullanılan port.',
        externalPortHelper: 'Dış dünyaya açılan port.',
        runScript: 'Çalıştırma betiği',
        runScriptHelper: 'Kaynak dizindeki package.json dosyasından ayrıştırılan başlatma komutları listesi.',
        open: 'Aç',
        operatorHelper: 'Seçilen çalışma ortamında {0} işlemi gerçekleştirilecek. Devam etmek istiyor musunuz?',
        taobao: 'Taobao',
        tencent: 'Tencent',
        imageSource: 'Görüntü kaynağı',
        moduleManager: 'Modül Yönetimi',
        module: 'Modül',
        nodeOperatorHelper:
            '{0} {1} modülü mü? İşlem, çalışma ortamında anormalliklere neden olabilir, lütfen devam etmeden önce onaylayın',
        customScript: 'Özel başlatma komutu',
        customScriptHelper: 'Tam bir başlatma komutu sağlayın. Örneğin, "npm run start".',
        portError: 'Aynı portu tekrarlamayın.',
        systemRestartHelper:
            'Durum açıklaması: Kesinti - sistem yeniden başlatılması nedeniyle durum alımı başarısız oldu',
        javaScriptHelper: 'Tam bir başlatma komutu sağlayın. Örneğin, "java -jar halo.jar -Xmx1024M -Xms256M".',
        javaDirHelper: 'Dizin, jar dosyalarını içermeli, alt dizinler de kabul edilir',
        goHelper: 'Tam bir başlatma komutu sağlayın. Örneğin, "go run main.go" veya "./main".',
        goDirHelper: 'Dizin veya alt dizin, Go veya ikili dosyaları içermelidir.',
        extension: 'Eklenti',
        installExtension: '{0} eklentisini yüklemeyi onaylıyor musunuz?',
        loadedExtension: 'Yüklü Eklenti',
        popularExtension: 'Popüler Eklenti',
        uninstallExtension: '{0} eklentisini kaldırmak istediğinizden emin misiniz?',
        phpConfigHelper:
            'Yapılandırmayı değiştirmek, çalışma ortamını yeniden başlatmayı gerektirir, devam etmek istiyor musunuz?',
        operateMode: 'çalışma modu',
        dynamic: 'dinamik',
        static: 'statik',
        ondemand: 'isteğe bağlı',
        dynamicHelper:
            'işlem sayısını dinamik olarak ayarlar, yüksek esneklik, büyük trafik dalgalanmaları olan veya düşük bellekli web siteleri için uygundur',
        staticHelper:
            'sabit işlem sayısı, yüksek eşzamanlılık ve sabit trafikli web siteleri için uygundur, yüksek kaynak tüketimi',
        ondemandHelper:
            'işlemler gerektiğinde başlatılır ve yok edilir, kaynak kullanımı optimize edilir, ancak ilk yanıt yavaş olabilir',
        max_children: 'oluşturulmasına izin verilen maksimum işlem sayısı',
        start_servers: 'başlangıçta oluşturulan işlem sayısı',
        min_spare_servers: 'boşta kalan minimum işlem sayısı',
        max_spare_servers: 'boşta kalan maksimum işlem sayısı',
        envKey: 'Ad',
        envValue: 'Değer',
        environment: 'Çevre Değişkeni',
        pythonHelper:
            'Tam bir başlatma komutu sağlayın. Örneğin, "pip install -r requirements.txt && python manage.py runserver 0.0.0.0:5000".',
        dotnetHelper: 'Tam bir başlatma komutu sağlayın. Örneğin, "dotnet MyWebApp.dll".',
        dirHelper: 'Not: Lütfen kapsayıcı içindeki dizin yolunu doldurun',
        concurrency: 'Eşzamanlılık Şeması',
        loadStatus: 'Yük Durumu',
    },
    process: {
        pid: 'İşlem Kimliği',
        ppid: 'Üst işlem Kimliği',
        numThreads: 'Konular',
        memory: 'Bellek',
        diskRead: 'Disk okuma',
        diskWrite: 'Disk yazma',
        netSent: 'yukarı bağlantı',
        netRecv: 'aşağı bağlantı',
        numConnections: 'Bağlantılar',
        startTime: 'Başlangıç zamanı',
        running: 'Çalışıyor',
        sleep: 'uyku',
        stop: 'durdur',
        idle: 'boşta',
        zombie: 'zombi işlem',
        wait: 'bekliyor',
        lock: 'kilit',
        blocked: 'engellendi',
        cmdLine: 'Başlatma komutu',
        basic: 'Temel',
        mem: 'Bellek',
        openFiles: 'Açık dosyalar',
        env: 'Çevreler',
        noenv: 'Hiçbiri',
        net: 'Ağ bağlantıları',
        laddr: 'Yerel adres/port',
        raddr: 'Uzak adres/port',
        stopProcess: 'Sonlandır',
        viewDetails: 'Detayları görüntüle',
        stopProcessWarn: 'Bu işlemi (PID:{0}) sonlandırmak istediğinizden emin misiniz?',
        processName: 'İşlem adı',
    },
    tool: {
        supervisor: {
            loadStatusErr: 'İşlem durumu alınamadı, lütfen supervisor hizmetinin durumunu kontrol edin.',
            notSupport: 'Supervisor hizmeti algılanmadı, lütfen betik kütüphanesi sayfasından manuel olarak kurun',
            list: 'Arka plan işlemi',
            config: 'Supervisor yapılandırması',
            primaryConfig: 'Ana yapılandırma dosyası konumu',
            notSupportCtl: 'supervisorctl algılanmadı, lütfen betik kütüphanesi sayfasından manuel olarak kurun',
            user: 'Kullanıcı',
            command: 'Komut',
            dir: 'Dizin',
            numprocs: 'İşlem sayısı',
            initWarn:
                'Bu, ana yapılandırma dosyasındaki "[include]" bölümündeki "files" değerini değiştirecektir. Diğer yapılandırma dosyasının dizini şu şekilde olacaktır: "{1Panel kurulum dizini}/1panel/tools/supervisord/supervisor.d/".',
            operatorHelper: '{0} üzerinde {1} işlemi gerçekleştirilecek, devam etmek istiyor musunuz?',
            uptime: 'Çalışma süresi',
            notStartWarn: 'Supervisor başlatılmadı. Önce başlatın.',
            serviceName: 'Hizmet adı',
            initHelper:
                'Supervisor hizmeti algılandı ancak başlatılmadı. Lütfen üst durum çubuğundaki başlatma düğmesine tıklayarak yapılandırın.',
            serviceNameHelper:
                'systemctl tarafından yönetilen Supervisor hizmet adı, genellikle supervisor veya supervisord',
            restartHelper:
                'Bu, başlatma işleminden sonra hizmeti yeniden başlatacak ve mevcut tüm arka plan işlemlerinin durmasına neden olacaktır.',
            RUNNING: 'Çalışıyor',
            STOPPED: 'Durduruldu',
            STOPPING: 'Durduruluyor',
            STARTING: 'Başlatılıyor',
            FATAL: 'Başlatma başarısız',
            BACKOFF: 'Başlatma istisnası',
            ERROR: 'Hata',
            statusCode: 'Durum kodu',
            manage: 'Yönetim',
            autoRestart: 'Otomatik Yeniden Başlatma',
            EXITED: 'Çıkıldı',
            autoRestartHelper: 'Program çöktükten sonra otomatik olarak yeniden başlatılsın mı?',
            autoStart: 'Otomatik Başlat',
            autoStartHelper: 'Supervisor başlatıldıktan sonra servis otomatik olarak başlatılsın mı?',
        },
    },
    xpack: {
        expiresTrialAlert:
            'Nazik hatırlatma: Pro deneme sürümünüz {0} gün içinde sona erecek ve tüm Pro özellikleri kullanılamaz hale gelecektir. Lütfen zamanında yenileyin veya tam sürüme yükseltin.',
        expiresAlert:
            'Nazik hatırlatma: Pro lisansınız {0} gün içinde sona erecek ve tüm Pro özellikleri kullanılamaz hale gelecektir. Lütfen devam eden kullanım için zamanında yenileyin.',
        menu: 'Pro',
        upage: 'AI Web Sitesi Oluşturucu',
        app: {
            app: 'Uygulama',
            title: 'Panel Takma Adı',
            titleHelper: 'Uygulamada gösterim için kullanılan panel takma adı (varsayılan panel takma adı)',
            qrCode: 'QR Kodu',
            apiStatusHelper: 'Panel Uygulaması, API arayüz özelliğinin etkinleştirilmesini gerektirir',
            apiInterfaceHelper:
                'Panel API arayüz erişimini destekler (bu özellik, panel uygulaması için etkinleştirilmelidir)',
            apiInterfaceHelper1:
                'Panel uygulama erişimi, ziyaretçinin beyaz listeye eklenmesini gerektirir; sabit olmayan IP’ler için 0.0.0.0/0(tüm IPv4), ::/0 (tüm IPv6) eklenmesi önerilir',
            qrCodeExpired: 'Yenileme zamanı',
            apiLeakageHelper: 'QR kodunu paylaşmayın. Yalnızca güvenilir ortamlarda kullanıldığından emin olun.',
        },
        waf: {
            name: 'WAF',
            blackWhite: 'Kara ve Beyaz Liste',
            globalSetting: 'Genel Ayarlar',
            websiteSetting: 'Web Sitesi Ayarları',
            blockRecords: 'Engellenen Kayıtlar',
            world: 'Dünya',
            china: 'Çin',
            intercept: 'Engelleme',
            request: 'İstekler',
            count4xx: '4xx Miktarı',
            count5xx: '5xx Miktarı',
            todayStatus: 'Bugünün Durumu',
            reqMap: 'Saldırı Haritası (Son 30 gün)',
            resource: 'Kaynak',
            count: 'Miktar',
            hight: 'Yüksek',
            low: 'Düşük',
            reqCount: 'İstekler',
            interceptCount: 'Engelleme Sayısı',
            requestTrends: 'İstek Trendleri (Son 7 Gün)',
            interceptTrends: 'Engelleme Trendleri (Son 7 Gün)',
            whiteList: 'Beyaz Liste',
            blackList: 'Kara Liste',
            ipBlackListHelper: 'Kara listedeki IP adresleri web sitesine erişimden engellenir',
            ipWhiteListHelper: 'Beyaz listedeki IP adresleri tüm kısıtlamaları bypass eder',
            uaBlackListHelper: 'Kara listedeki Kullanıcı-Aracısı değerlerine sahip istekler engellenir',
            uaWhiteListHelper:
                'Beyaz listedeki Kullanıcı-Aracısı değerlerine sahip istekler tüm kısıtlamaları bypass eder',
            urlBlackListHelper: 'Kara listedeki URL’lere yapılan istekler engellenir',
            urlWhiteListHelper: 'Beyaz listedeki URL’lere yapılan istekler tüm kısıtlamaları bypass eder',
            ccHelper: 'Bir web sitesi, aynı IP’den {0} saniye içinde {1} istekten fazla alırsa, IP {2} için engellenir',
            blockTime: 'Engelleme Süresi',
            attackHelper: 'Toplam engellemeler {0} saniye içinde {1}’i aşarsa, IP {2} için engellenir',
            notFoundHelper: 'Toplam istekler {0} saniye içinde {1} kez 404 hatası döndürürse, IP {2} için engellenir',
            frequencyLimit: 'Sıklık Sınırı',
            regionLimit: 'Bölge Sınırı',
            defaultRule: 'Varsayılan Kurallar',
            accessFrequencyLimit: 'Erişim Sıklığı Sınırı',
            attackLimit: 'Saldırı Sıklığı Sınırı',
            notFoundLimit: '404 Sıklık Sınırı',
            urlLimit: 'URL Sıklık Sınırı',
            urlLimitHelper: 'Tek bir URL için erişim sıklığını ayarlayın',
            sqliDefense: 'SQL Enjeksiyon Koruması',
            sqliHelper: 'İsteklerde SQL enjeksiyonunu algılar ve engeller',
            xssHelper: 'İsteklerde XSS’i algılar ve engeller',
            xssDefense: 'XSS Koruması',
            uaDefense: 'Kötü Amaçlı Kullanıcı-Aracısı Kuralları',
            uaHelper: 'Yaygın kötü amaçlı botları tanımlamak için kuralları içerir',
            argsDefense: 'Kötü Amaçlı Parametre Kuralları',
            argsHelper: 'Kötü amaçlı parametreler içeren istekleri engeller',
            cookieDefense: 'Kötü Amaçlı Çerez Kuralları',
            cookieHelper: 'Kötü amaçlı çerezlerin isteklerde taşınmasını yasaklar',
            headerDefense: 'Kötü Amaçlı Başlık Kuralları',
            headerHelper: 'Kötü amaçlı başlıklar içeren istekleri yasaklar',
            httpRule: 'HTTP İstek Yöntemi Kuralları',
            httpHelper:
                'İzin verilen yöntem türlerini ayarlayın. Belirli erişim türlerini kısıtlamak istiyorsanız, bu tür düğmeyi kapatın. Örneğin: yalnızca GET türüne izin veriliyorsa, GET dışındaki diğer tür düğmeleri kapatmanız gerekir',
            geoRule: 'Bölgesel Erişim Kısıtlamaları',
            geoHelper:
                'Web sitenize belirli bölgelerden erişimi kısıtlayın, örneğin: Çin anakarasına erişime izin veriliyorsa, Çin anakarası dışındaki istekler engellenir',
            ipLocation: 'IP Konumu',
            action: 'Eylem',
            ruleType: 'Saldırı Türü',
            ipHelper: 'IP adresini girin',
            attackLog: 'Saldırı Günlüğü',
            rule: 'Kural',
            ipArr: 'IPV4 Aralığı',
            ipStart: 'Başlangıç IP',
            ipEnd: 'Bitiş IP',
            ipv4: 'IPv4',
            ipv6: 'IPv6',
            urlDefense: 'URL Kuralları',
            urlHelper: 'Yasak URL',
            dirFilter: 'Dizin Filtresi',
            sqlInject: 'SQL Enjeksiyonu',
            xss: 'XSS',
            phpExec: 'PHP Betiği Yürütme',
            oneWordTrojan: 'Tek Kelime Truva Atı',
            appFilter: 'Tehlikeli Dizin Filtreleme',
            webshell: 'Webshell',
            args: 'Kötü Amaçlı Parametreler',
            protocolFilter: 'Protokol Filtresi',
            javaFilter: 'Java Tehlikeli Dosya Filtreleme',
            scannerFilter: 'Tarayıcı Filtresi',
            escapeFilter: 'Kaçış Filtresi',
            customRule: 'Özel Kurallar',
            httpMethod: 'HTTP Yöntemi Filtresi',
            fileExt: 'Dosya Yükleme Sınırı',
            fileExtHelper: 'Yükleme için yasak dosya uzantıları',
            deny: 'Yasakla',
            allow: 'İzin Ver',
            field: 'Nesne',
            pattern: 'Koşul',
            ruleContent: 'İçerik',
            contain: 'içerir',
            equal: 'eşittir',
            regex: 'düzenli ifade',
            notEqual: 'Eşit değil',
            customRuleHelper: 'Belirtilen koşullara göre eylemler gerçekleştirin',
            actionAllow: 'İzin Ver',
            blockIP: 'IP’yi Engelle',
            code: 'Dönüş Durum Kodu',
            noRes: 'Bağlantıyı Kes (444)',
            badReq: 'Geçersiz Parametreler (400)',
            forbidden: 'Erişim Yasak (403)',
            serverErr: 'Sunucu Hatası (500)',
            resHtml: 'Yanıt Sayfası',
            allowHelper: 'Erişime izin vermek, sonraki WAF kurallarını atlar, lütfen dikkatli kullanın',
            captcha: 'insan-makine doğrulaması',
            fiveSeconds: '5 Saniye doğrulama',
            location: 'Bölge',
            redisConfig: 'Redis Yapılandırması',
            redisHelper: 'Geçici olarak engellenen IP’leri sürdürmek için Redis’i etkinleştirin',
            wafHelper: 'WAF kapatıldığında tüm web siteleri korumayı kaybeder',
            attackIP: 'Saldıran IP',
            attackParam: 'Saldırı Detayları',
            execRule: 'Vurulan Kural',
            acl: 'ACL',
            sql: 'SQL Enjeksiyonu',
            cc: 'Erişim Sıklığı Sınırı',
            isBlocking: 'Engellendi',
            isFree: 'Engel Kaldırıldı',
            unLock: 'Kilidi Aç',
            unLockHelper: 'IP: {0} kilidini açmak istiyor musunuz?',
            saveDefault: 'Varsayılanı Kaydet',
            saveToWebsite: 'Web Sitesine Uygula',
            saveToWebsiteHelper: 'Mevcut ayarları tüm web sitelerine uygulamak mı?',
            websiteHelper:
                'Web sitesi oluşturmak için varsayılan ayarlar buradadır. Değişikliklerin etkili olması için web sitesine uygulanması gerekir',
            websiteHelper2:
                'Web sitesi oluşturmak için varsayılan ayarlar buradadır. Lütfen web sitesinde özel yapılandırmayı değiştirin',
            ipGroup: 'IP Grubu',
            ipGroupHelper:
                'Her satırda bir IP veya IP segmenti, IPv4 ve IPv6 destekler, örneğin: *********** veya ***********/24',
            ipBlack: 'IP kara listesi',
            openRestyAlert: 'OpenResty sürümünün {0} üzerinde olması gerekir',
            initAlert:
                'İlk kullanım için başlatma gereklidir, web sitesi yapılandırma dosyası değiştirilecek ve mevcut WAF yapılandırması kaybolacaktır. Lütfen önceden OpenResty’yi yedeklediğinizden emin olun',
            initHelper: 'Başlatma işlemi mevcut WAF yapılandırmasını temizler. Başlatmak istediğinizden emin misiniz?',
            mainSwitch: 'Ana Anahtar',
            websiteAlert: 'Lütfen önce bir web sitesi oluşturun',
            defaultUrlBlack: 'URL Kuralları',
            htmlRes: 'Engelleme Sayfası',
            urlSearchHelper: 'Bulanık arama için URL girin',
            toCreate: 'Oluştur',
            closeWaf: 'WAF’ı Kapat',
            closeWafHelper:
                'WAF’ı kapatmak web sitesinin korumasını kaybetmesine neden olur, devam etmek istiyor musunuz?',
            addblack: 'Kara',
            addwhite: 'Beyaz Ekle',
            addblackHelper: 'IP:{0}’ı varsayılan kara listeye eklemek mi?',
            addwhiteHelper: 'IP:{0}’ı varsayılan beyaz listeye eklemek mi?',
            defaultUaBlack: 'Kullanıcı-Aracısı kuralı',
            defaultIpBlack: 'Kötü Amaçlı IP Grubu',
            cookie: 'Çerez Kuralları',
            urlBlack: 'URL Kara Listesi',
            uaBlack: 'Kullanıcı-Aracısı kara listesi',
            attackCount: 'Saldırı sıklığı sınırı',
            fileExtCheck: 'Dosya yükleme sınırı',
            geoRestrict: 'Bölgesel erişim kısıtlaması',
            attacklog: 'Engelleme Kaydı',
            unknownWebsite: 'Yetkisiz alan adı erişimi',
            geoRuleEmpty: 'Bölge boş olamaz',
            unknown: 'Web Sitesi Mevcut Değil',
            geo: 'Bölge Kısıtlaması',
            revertHtml: '{0}’ı varsayılan sayfa olarak geri yüklemek istiyor musunuz?',
            five_seconds: '5 Saniye doğrulama',
            header: 'Başlık kuralları',
            methodWhite: 'HTTP kuralları',
            expiryDate: 'Son Kullanım Tarihi',
            expiryDateHelper: 'Doğrulama geçildikten sonra, geçerlilik süresi içinde tekrar doğrulanmaz',
            defaultIpBlackHelper: 'İnternetten toplanan bazı kötü amaçlı IP’ler erişimi önlemek için',
            notFoundCount: '404 Sıklık Sınırı',
            matchValue: 'Eşleşme değeri',
            headerName: 'İngilizce, sayılar, - ile başlayan özel olmayan karakterleri destekler, uzunluk 3-30',
            cdnHelper: 'CDN kullanan web siteleri, doğru kaynak IP’yi almak için burayı açabilir',
            clearLogWarn: 'Günlük temizleme geri alınamaz, devam etmek istiyor musunuz?',
            commonRuleHelper: 'Kural bulanık eşleşmedir',
            blockIPHelper:
                'Engellenen IP’ler geçici olarak OpenResty’de saklanır ve OpenResty yeniden başlatıldığında engel kaldırılır. Engelleme fonksiyonu ile kalıcı olarak engellenebilir',
            addWhiteUrlHelper: 'URL {0}’ı beyaz listeye eklemek mi?',
            dashHelper: 'Topluluk sürümü, genel ayarlar ve web sitesi ayarlarındaki işlevleri de kullanabilir',
            wafStatusHelper: 'WAF etkin değil, lütfen genel ayarlarda etkinleştirin',
            ccMode: 'Mod',
            global: 'Genel Mod',
            uriMode: 'URL Modu',
            globalHelper:
                'Genel Mod: Birim zamanda herhangi bir URL’ye yapılan toplam istek sayısı eşiği aştığında tetiklenir',
            uriModeHelper: 'URL Modu: Birim zamanda tek bir URL’ye yapılan istek sayısı eşiği aştığında tetiklenir',
            ip: 'IP Kara Listesi',
            globalSettingHelper:
                '[Web Sitesi] etiketli ayarlar [Web Sitesi Ayarları]’nda etkinleştirilmelidir, genel ayarlar yalnızca yeni oluşturulan web siteleri için varsayılan ayarlardır',
            globalSettingHelper2:
                'Ayarların hem [Genel Ayarlar] hem de [Web Sitesi Ayarları]’nda aynı anda etkinleştirilmesi gerekir',
            urlCCHelper: 'Bu URL’ye {0} saniye içinde {1} istekten fazla olursa, bu IP {2} için engellenir',
            urlCCHelper2: 'URL parametre içeremez',
            notContain: 'İçermez',
            urlcc: 'URL sıklık sınırı',
            method: 'İstek türü',
            addIpsToBlock: 'Toplu IP engelleme',
            addUrlsToWhite: 'Toplu URL’yi beyaz listeye ekleme',
            noBlackIp: 'IP zaten engellenmiş, tekrar engellemeye gerek yok',
            noWhiteUrl: 'URL zaten beyaz listede, tekrar eklemeye gerek yok',
            spiderIpHelper:
                "Baidu, Bing, Google, 360, Shenma, Sogou, ByteDance, DuckDuckGo, Yandex'i içerir. Bunu kapatmak tüm örümcek erişimlerini engeller.",
            spiderIp: 'Örümcek IP Havuzu',
            geoIp: 'IP Adres Kütüphanesi',
            geoIpHelper: 'IP’nin coğrafi konumunu doğrulamak için kullanılır',
            stat: 'Saldırı Raporu',
            statTitle: 'Rapor',
            attackIp: 'IP',
            attackCountNum: 'Sayılar',
            percent: 'Yüzde',
            addblackUrlHelper: 'URL: {0}’ı varsayılan kara listeye eklemek mi?',
            rce: 'Uzaktan Kod Yürütme',
            software: 'Yazılım',
            cveHelper: 'Yaygın yazılım ve çerçevelerdeki güvenlik açıklarını içerir',
            vulnCheck: 'Tamamlayıcı Kurallar',
            ssrf: 'SSRF Güvenlik Açığı',
            afr: 'Keyfi Dosya Okuma',
            ua: 'Yetkisiz Erişim',
            id: 'Bilgi Sızıntısı',
            aa: 'Kimlik Doğrulama Atlatma',
            dr: 'Dizin Geçişi',
            xxe: 'XXE Güvenlik Açığı',
            suid: 'Serileştirme Güvenlik Açığı',
            dos: 'Hizmet Engelleme Güvenlik Açığı',
            afd: 'Keyfi Dosya İndirme',
            sqlInjection: 'SQL Enjeksiyonu',
            afw: 'Keyfi Dosya Yazma',
            il: 'Bilgi Sızıntısı',
            clearAllLog: 'Tüm günlükleri temizle',
            exportLog: 'Günlükleri dışa aktar',
            appRule: 'Uygulama Kuralları',
            appRuleHelper:
                'Yaygın uygulama kuralları, etkinleştirme yanlış pozitifleri azaltabilir, bir web sitesi yalnızca bir kural kullanabilir',
            logExternal: 'Kayıt Türlerini Hariç Tut',
            ipWhite: 'IP Beyaz Listesi',
            urlWhite: 'URL Beyaz Listesi',
            uaWhite: 'Kullanıcı-Aracısı Beyaz Listesi',
            logExternalHelper:
                'Hariç tutulan kayıt türleri günlüklerde kaydedilmez, kara liste/beyaz liste, bölgesel erişim kısıtlamaları ve özel kurallar çok sayıda günlük oluşturur, hariç tutulması önerilir',
            ssti: 'SSTI Saldırısı',
            crlf: 'CRLF Enjeksiyonu',
            strict: 'Katı Mod',
            strictHelper: 'İstekleri doğrulamak için daha katı kurallar kullanın',
            saveLog: 'Günlüğü Kaydet',
            remoteURLHelper: 'Uzak URL, her satırda bir IP içermeli ve başka karakter olmamalıdır',
            notFound: 'Bulunamadı (404)',
            serviceUnavailable: 'Hizmet Kullanılamıyor (503)',
            gatewayTimeout: 'Ağ Geçidi Zaman Aşımı (504)',
            belongToIpGroup: 'IP Grubuna Ait',
            notBelongToIpGroup: 'IP Grubuna Ait Değil',
            unknownWebsiteKey: 'Bilinmeyen Alan',
            special: 'Özel',
        },
        monitor: {
            name: 'Web Sitesi İzleme',
            pv: 'Sayfa Görüntülemeleri',
            uv: 'Benzersiz Ziyaretçiler',
            flow: 'Trafik Akışı',
            ip: 'IP',
            spider: 'Örümcek',
            visitors: 'Ziyaretçi Trendleri',
            today: 'Bugün',
            last7days: 'Son 7 Gün',
            last30days: 'Son 30 Gün',
            uvMap: 'Ziyaretçi Haritası (30’uncu)',
            qps: 'Gerçek Zamanlı İstekler (dakikada)',
            flowSec: 'Gerçek Zamanlı Trafik (dakikada)',
            excludeCode: 'Durum Kodlarını Hariç Tut',
            excludeUrl: 'URL’leri Hariç Tut',
            excludeExt: 'Uzantıları Hariç Tut',
            cdnHelper: 'CDN tarafından sağlanan Başlıktan gerçek IP’yi alın',
            reqRank: 'Ziyaret Sıralaması',
            refererDomain: 'Yönlendiren Alan Adı',
            os: 'Sistem',
            browser: 'Tarayıcı/İstemci',
            device: 'Cihaz',
            showMore: 'Daha Fazla',
            unknown: 'Diğer',
            pc: 'Bilgisayar',
            mobile: 'Mobil Cihaz',
            wechat: 'WeChat',
            machine: 'makine',
            tencent: 'Tencent Tarayıcı',
            ucweb: 'UC Tarayıcı',
            '2345explorer': '2345 tarayıcı',
            huaweibrowser: 'Huawei Tarayıcı',
            log: 'İstek Günlükleri',
            statusCode: 'Durum Kodu',
            requestTime: 'Yanıt Süresi',
            flowRes: 'Yanıt Trafiği',
            method: 'İstek Yöntemi',
            statusCodeHelper: 'Yukarıdaki durum kodunu girin',
            statusCodeError: 'Geçersiz durum kodu türü',
            methodHelper: 'Yukarıdaki istek yöntemini girin',
            all: 'Tümü',
            baidu: 'Baidu',
            google: 'Google',
            bing: 'Bing',
            bytes: 'Bugün manşetler',
            sogou: 'Sogou',
            failed: 'Hata',
            ipCount: 'IP Sayısı',
            spiderCount: 'Örümcek İstekleri',
            averageReqTime: 'Ortalama Yanıt Süresi',
            totalFlow: 'Toplam Trafik',
            logSize: 'Günlük Dosyası Boyutu',
            realIPType: 'Gerçek IP alma yöntemi',
            fromHeader: 'HTTP Başlığından Al',
            fromHeaders: 'Başlık listesinden Al',
            header: 'HTTP Başlığı',
            cdnConfig: 'CDN Yapılandırması',
            xff1: 'X-Forwarded-For’dan birinci seviye Proxy',
            xff2: 'X-Forwarded-For’dan ikinci seviye Proxy',
            xff3: 'X-Forwarded-For’dan üçüncü seviye Proxy',
            xffHelper:
                'Örneğin: X-Forwarded-For: <istemci>,<proxy1>,<proxy2>,<proxy3> Üst seviye proxy, son IP’yi <proxy3> alır',
            headersHelper: 'Yaygın CDN HTTP başlıklarından gerçek IP’yi alın, ilk mevcut değeri seçer',
            monitorCDNHelper:
                'Web sitesi izleme için CDN yapılandırmasını değiştirmek, WAF CDN ayarlarını da güncelleyecektir',
            wafCDNHelper: 'WAF CDN yapılandırmasını değiştirmek, web sitesi izleme CDN ayarlarını da güncelleyecektir',
            statusErr: 'Geçersiz durum kodu formatı',
            shenma: 'Shenma Arama',
            duckduckgo: 'DuckDuckGo',
            '360': '360 Arama',
            excludeUri: 'URI’leri Hariç Tut',
            top100Helper: 'İlk 100 veriyi göster',
            logSaveDay: 'Günlük Saklama Süresi (gün)',
            cros: 'Chrome OS',
            theworld: 'TheWorld Tarayıcı',
            edge: 'Microsoft Edge',
            maxthon: 'Maxthon Tarayıcı',
            monitorStatusHelper: 'İzleme etkin değil, lütfen ayarlarda etkinleştirin',
            excludeIp: 'IP Adreslerini Hariç Tut',
            excludeUa: 'Kullanıcı-Aracısını Hariç Tut',
            remotePort: 'Uzak Port',
            unknown_browser: 'Bilinmeyen',
            unknown_os: 'Bilinmeyen',
            unknown_device: 'Bilinmeyen',
            logSaveSize: 'Maksimum Günlük Saklama Boyutu',
            logSaveSizeHelper: 'Bu, tek bir web sitesi için günlük saklama boyutudur',
            '360se': '360 Güvenlik Tarayıcı',
            websites: 'Web Sitesi Listesi',
            trend: 'Trend İstatistikleri',
            reqCount: 'İstek Sayısı',
            uriHelper: 'Uri’yi hariç tutmak için /test/* veya /*/index.php kullanabilirsiniz',
        },
        tamper: {
            tamper: 'Web Sitesi Değiştirme Koruması',
            ignoreTemplate: 'Dizin Şablonunu Hariç Tut',
            protectTemplate: 'Dosya Şablonunu Koru',
            templateContent: 'Şablon İçeriği',
            template: 'Şablon',
            tamperHelper1:
                'Tek tıkla dağıtım türü web siteleri için, uygulama dizini değiştirme koruma özelliğinin etkinleştirilmesi önerilir; web sitesi düzgün çalışmıyorsa veya yedekleme ve kurtarma başarısızlıkları varsa, lütfen önce değiştirme koruma özelliğini devre dışı bırakın;',
            tamperHelper2:
                'Hariç tutulan dizinler dışındaki korunan dosyalara okuma, yazma, silme, izin değiştirme ve sahip değiştirme işlemleri kısıtlanacaktır',
            tamperPath: 'Korunan Dizin',
            tamperPathEdit: 'Yolu Düzenle',
            log: 'Engelleme Günlüğü',
            totalProtect: 'Toplam Koruma',
            todayProtect: 'Bugünün Koruması',
            addRule: 'Kural Ekle',
            ignore: 'Dizini Hariç Tut',
            ignoreHelper: 'Satır başına bir tane, ör.: \ntmp\n./tmp',
            ignoreTemplateHelper: 'Hariç tutulacak klasör adlarını ekleyin, virgülle ayrılmış, ör.: tmp,cache',
            templateRule: 'Uzunluk 1-512, isim {0} gibi semboller içeremez',
            ignoreHelper1: 'Hariç tutulacak klasör adlarını veya belirli yolları ekleyin',
            ignoreHelper2: 'Belirli bir klasörü hariç tutmak için ./ ile başlayan göreceli bir yol kullanın',
            protect: 'Dosyayı Koru',
            protectHelper: 'Satır başına bir tane, ör.: \npng\n./test.css',
            protectTemplateHelper:
                'Hariç tutulacak dosya adlarını veya uzantıları ekleyin, virgülle ayrılmış, ör.: conf,.css',
            protectHelper1: 'Korunacak dosya adlarını, uzantıları veya belirli dosyaları belirtebilirsiniz',
            protectHelper2: 'Belirli bir dosyayı korumak için ./ ile başlayan göreceli bir yol kullanın',
            enableHelper:
                'Aşağıdaki web siteleri için değiştirme koruma özelliği etkinleştirilecek ve web sitesi güvenliği artırılacak. Devam etmek istiyor musunuz?',
            disableHelper:
                'Aşağıdaki web siteleri için değiştirme koruma özelliği devre dışı bırakılacak. Devam etmek istiyor musunuz?',
        },
        setting: {
            setting: 'Panel Ayarları',
            title: 'Panel Açıklaması',
            titleHelper:
                'Kullanıcı giriş sayfasında gösterilecektir (ör. Linux sunucu operasyon ve bakım yönetim paneli, önerilen 8-15 karakter)',
            logo: 'Logo (Metinsiz)',
            logoHelper:
                'Menü daraltıldığında yönetim sayfasının sol üst köşesinde gösterilecektir (önerilen görüntü boyutu: 82px*82px)',
            logoWithText: 'Logo (Metinli)',
            logoWithTextHelper:
                'Menü genişletildiğinde yönetim sayfasının sol üst köşesinde gösterilecektir (önerilen görüntü boyutu: 185px*55px)',
            favicon: 'Web Sitesi Simgesi',
            faviconHelper: 'Web sitesi simgesi (önerilen görüntü boyutu: 16px*16px)',
            reUpload: 'Dosya Seç',
            setDefault: 'Varsayılana Geri Yükle',
            setHelper: 'Mevcut ayarlar kaydedilecek. Devam etmek istiyor musunuz?',
            setDefaultHelper: 'Tüm panel ayarları varsayılana geri yüklenecek. Devam etmek istiyor musunuz?',
            logoGroup: 'Logo',
            imageGroup: 'Görüntü',
            loginImage: 'Giriş Sayfası Görüntüsü',
            loginImageHelper: 'Giriş sayfasında gösterilir (önerilen görüntü boyutu: 500*416px)',
            loginBgType: 'Giriş Sayfası Arka Plan Türü',
            loginBgImage: 'Giriş Sayfası Arka Plan Görüntüsü',
            loginBgImageHelper:
                'Giriş sayfasında arka plan görüntüsü olarak gösterilir (önerilen görüntü boyutu: 1920*1080px)',
            loginBgColor: 'Giriş Sayfası Arka Plan Rengi',
            loginBgColorHelper: 'Giriş sayfasında arka plan rengi olarak gösterilir',
            image: 'Görüntü',
            bgColor: 'Arka Plan Rengi',
            loginGroup: 'Giriş Sayfası',
            loginBtnLinkColor: 'Buton/Bağlantı Rengi',
            loginBtnLinkColorHelper: 'Giriş sayfasındaki buton/bağlantı rengi olarak gösterilecektir',
        },
        helper: {
            wafTitle1: 'Engelleme Haritası',
            wafContent1: 'Son 30 gün içindeki engellemelerin coğrafi dağılımını gösterir',
            wafTitle2: 'Bölgesel Erişim Kısıtlamaları',
            wafContent2: 'Web sitesi erişim kaynaklarını coğrafi konumlara göre kısıtlar',
            wafTitle3: 'Özel Engelleme Sayfası',
            wafContent3: 'Bir istek engellendiğinde gösterilecek özel bir sayfa oluşturun',
            wafTitle4: 'Özel Kurallar (ACL)',
            wafContent4: 'İstekleri özel kurallara göre engelleyin',

            tamperTitle1: 'Dosya Bütünlüğü İzleme',
            tamperContent1:
                'Web sitesi dosyalarının bütünlüğünü izler, çekirdek dosyalar, betik dosyaları ve yapılandırma dosyaları dahil.',
            tamperTitle2: 'Gerçek Zamanlı Tarama ve Algılama',
            tamperContent2:
                'Web sitesi dosya sistemini gerçek zamanlı tarayarak anormal veya değiştirilmiş dosyaları algılar.',
            tamperTitle3: 'Güvenlik İzin Ayarları',
            tamperContent3:
                'Uygun izin ayarları ve erişim kontrol politikaları aracılığıyla web sitesi dosyalarına erişimi kısıtlar, potansiyel saldırı yüzeyini azaltır.',
            tamperTitle4: 'Günlük Kaydı ve Analiz',
            tamperContent4:
                'Yöneticilerin sonraki denetim ve analiz için dosya erişimi ve işlem günlüklerini kaydeder, potansiyel güvenlik tehditlerini belirler.',

            settingTitle1: 'Özel Hoş Geldiniz Mesajı',
            settingContent1: '1Panel giriş sayfasında özel bir hoş geldiniz mesajı ayarlayın.',
            settingTitle2: 'Özel Logo',
            settingContent2: 'Marka adları veya başka metinler içeren logo görüntülerinin yüklenmesine izin verir.',
            settingTitle3: 'Özel Web Sitesi Simgesi',
            settingContent3:
                'Varsayılan tarayıcı simgesini değiştirmek için özel simgelerin yüklenmesine izin verir, kullanıcı deneyimini iyileştirir.',

            monitorTitle1: 'Ziyaretçi Trendi',
            monitorContent1: 'Web sitesi ziyaretçi trendlerini istatistiksel olarak toplar ve gösterir',
            monitorTitle2: 'Ziyaretçi Haritası',
            monitorContent2:
                'Web sitesine gelen ziyaretçilerin coğrafi dağılımını istatistiksel olarak toplar ve gösterir',
            monitorTitle3: 'Erişim İstatistikleri',
            monitorContent3:
                'Web sitesi istek bilgileri hakkında istatistikler, örümcekler, erişim cihazları, istek durumu vb. içerir.',
            monitorTitle4: 'Gerçek Zamanlı İzleme',
            monitorContent4: 'Web sitesi istek bilgilerinin gerçek zamanlı izlenmesi, istek sayısı, trafik vb. içerir.',

            alertTitle1: 'SMS Uyarıları',
            alertContent1:
                'Anormal sunucu kaynak kullanımı, web sitesi ve sertifika sona ermesi, yeni sürüm güncellemesi, şifre sona ermesi vb. durumlarda kullanıcılar SMS alarmı ile bilgilendirilir, zamanında işlem yapılmasını sağlar.',
            alertTitle2: 'Uyarı Günlüğü',
            alertContent2:
                'Kullanıcılara geçmiş uyarı olaylarını takip etme ve analiz etme için uyarı günlüklerini görüntüleme işlevi sağlar.',
            alertTitle3: 'Uyarı Ayarları',
            alertContent3:
                'Kullanıcılara özel telefon numaraları, günlük gönderim sıklığı ve günlük gönderim zamanı yapılandırmaları sağlar, kullanıcıların daha makul gönderim uyarıları ayarlamasını kolaylaştırır.',

            nodeTitle1: 'Tek Tıkla Düğüm Ekleme',
            nodeContent1: 'Birden fazla sunucu düğümünü hızlıca entegre eder',
            nodeTitle2: 'Toplu Yükseltme',
            nodeContent2: 'Tüm düğümleri tek bir işlemle senkronize eder ve yükseltir',
            nodeTitle3: 'Düğüm Durumu İzleme',
            nodeContent3: 'Her düğümün çalışma durumunu gerçek zamanlı olarak izler',
            nodeTitle4: 'Hızlı Uzak Bağlantı',
            nodeContent4: 'Düğümlere tek tıkla doğrudan uzak terminal bağlantısı',

            fileExchangeTitle1: 'Anahtar Kimlik Doğrulama İletimi',
            fileExchangeContent1: 'İletim güvenliğini sağlamak için SSH anahtarları ile kimlik doğrulaması yapar.',
            fileExchangeTitle2: 'Verimli Dosya Senkronizasyonu',
            fileExchangeContent2:
                'Yalnızca değiştirilen içeriği senkronize ederek iletim hızını ve kararlılığını önemli ölçüde artırır.',
            fileExchangeTitle3: 'Çoklu Düğüm İletişimini Destekler',
            fileExchangeContent3:
                'Farklı düğümler arasında proje dosyalarını kolayca aktarır, birden fazla sunucuyu esnek bir şekilde yönetir.',

            appTitle1: 'Esnek Panel Yönetimi',
            appContent1: '1Panel sunucunuzu her zaman, her yerde kolayca yönetin.',
            appTitle2: 'Kapsamlı Hizmet Bilgisi',
            appContent2:
                'Temel uygulamaları, web sitelerini, Docker’ı, veritabanlarını vb. yönetin ve mobil uygulama üzerinden hızlıca uygulamalar ve web siteleri oluşturun.',
            appTitle3: 'Gerçek Zamanlı Anormallik İzleme',
            appContent3:
                'Mobil uygulama üzerinden gerçek zamanlı sunucu durumu, WAF güvenlik izleme, web sitesi trafik istatistikleri ve işlem sağlığı durumunu görüntüleyin.',

            clusterTitle1: 'Ana-Çalışan Dağıtımı',
            clusterContent1:
                'Farklı düğümlerde MySQL/Postgres/Redis ana-çalışan örnekleri oluşturmayı destekler, ana-çalışan ilişkisi ve başlatmayı otomatik olarak tamamlar',
            clusterTitle2: 'Ana-Çalışan Yönetimi',
            clusterContent2:
                'Birleşik bir sayfa ile birden çok ana-çalışan düğümünü merkezi olarak yönetin, rollerini, çalışma durumlarını vb. görüntüleyin',
            clusterTitle3: 'Çoğaltma Durumu',
            clusterContent3:
                'Ana-çalışan çoğaltma durumunu ve gecikme bilgilerini görüntüleyerek senkronizasyon sorunlarını gidermeye yardımcı olur',
        },
        node: {
            master: 'Ana Düğüm',
            masterBackup: 'Ana Düğüm Yedekleme',
            backupNode: 'Yedek Düğüm',
            backupFrequency: 'Yedekleme Sıklığı (saat)',
            backupCopies: 'Saklanacak yedek kopya sayısı',
            noBackupNode: 'Yedek düğüm şu anda boş. Lütfen kaydetmek için bir yedek düğüm seçin ve tekrar deneyin!',
            masterBackupAlert:
                'Ana düğüm yedeklemesi şu anda yapılandırılmamış. Veri güvenliği için, lütfen arıza durumunda yeni bir ana düğüme manuel geçiş yapabilmek amacıyla en kısa sürede bir yedek düğüm ayarlayın.',
            node: 'Düğüm',
            addr: 'Adres',
            nodeUnhealthy: 'Düğüm durumu anormal',
            deletedNode: 'Silinmiş düğüm {0} şu anda yükseltme işlemlerini desteklemiyor!',
            nodeUnhealthyHelper:
                'Anormal düğüm durumu algılandı. Lütfen [Düğüm Yönetimi]’nde kontrol edin ve tekrar deneyin!',
            nodeUnbind: 'Düğüm lisansa bağlı değil',
            nodeUnbindHelper:
                'Bu düğümün lisansa bağlı olmadığı algılandı. Lütfen [Panel Ayarları - Lisans] menüsünde bağlayın ve tekrar deneyin!',
            memTotal: 'Toplam Bellek',
            nodeManagement: 'Düğüm Yönetimi',
            addNode: 'Düğüm Ekle',
            connInfo: 'Bağlantı Bilgileri',
            nodeInfo: 'Düğüm Bilgileri',
            syncInfo: 'Senkronizasyon',
            syncHelper: 'Ana düğüm verileri değiştiğinde, bu alt düğüme gerçek zamanlı olarak senkronize edilir',
            syncBackupAccount: 'Yedekleme hesabı ayarları',
            syncWithMaster:
                'Pro’ya yükseltildikten sonra, tüm veriler varsayılan olarak senkronize edilir. Senkronizasyon politikaları düğüm yönetiminde manuel olarak ayarlanabilir.',
            syncProxy: 'Sistem proxy ayarları',
            syncProxyHelper: 'Sistem proxy ayarlarını senkronize etmek Docker yeniden başlatılmasını gerektirir',
            syncProxyHelper1: 'Docker’ın yeniden başlatılması, şu anda çalışan kapsayıcı hizmetlerini etkileyebilir.',
            syncProxyHelper2: 'Kapsayıcılar - Yapılandırma sayfasında manuel olarak yeniden başlatabilirsiniz.',
            syncProxyHelper3:
                'Sistem proxy ayarlarını senkronize etmek Docker yeniden başlatılmasını gerektirir, bu da şu anda çalışan kapsayıcı hizmetlerini etkileyebilir',
            syncProxyHelper4:
                'Sistem proxy ayarlarını senkronize etmek Docker yeniden başlatılmasını gerektirir. Kapsayıcılar - Yapılandırma sayfasında daha sonra manuel olarak yeniden başlatabilirsiniz.',
            syncCustomApp: 'Özel Uygulama Deposunu Senkronize Et',
            syncAlertSetting: 'Sistem uyarı ayarları',
            syncNodeInfo: 'Düğüm temel verileri,',
            nodeSyncHelper: 'Düğüm bilgisi senkronizasyonu aşağıdaki bilgileri senkronize eder:',
            nodeSyncHelper1: '1. Genel yedekleme hesabı bilgileri',
            nodeSyncHelper2: '2. Ana düğüm ile alt düğümler arasındaki bağlantı bilgileri',

            nodeCheck: 'Kullanılabilirlik kontrolü',
            checkSSH: 'Düğüm SSH bağlantısını kontrol et',
            checkUserPermission: 'Düğüm kullanıcı izinlerini kontrol et',
            isNotRoot: 'Bu düğümde şifresiz sudo desteklenmediği ve mevcut kullanıcının root olmadığı algılandı',
            checkLicense: 'Düğüm lisans durumunu kontrol et',
            checkService: 'Düğümdeki mevcut hizmet bilgilerini kontrol et',
            checkPort: 'Düğüm port erişilebilirliğini kontrol et',
            panelExist:
                'Bu düğümde 1Panel V1 hizmeti çalıştığı algılandı. Lütfen eklemeden önce geçiş betiğini kullanarak V2’ye yükseltin.',
            coreExist:
                'Mevcut düğüm zaten ana düğüm olarak etkinleştirilmiş durumda ve doğrudan alt düğüm olarak eklenemez. Lütfen eklemeden önce alt düğüme dönüştürün, ayrıntılar için belgelere bakın.',
            agentExist:
                'Bu düğümde 1panel-agent’ın zaten kurulu olduğu algılandı. Devam edilmesi mevcut verileri koruyacak ve yalnızca 1panel-agent hizmetini değiştirecektir.',
            oldDataExist:
                'Bu düğümde geçmiş 1Panel V2 verileri algılandı. Aşağıdaki bilgiler mevcut ayarları üzerine yazmak için kullanılacaktır:',
            errLicense: 'Bu düğüme bağlı lisans kullanılamıyor. Lütfen kontrol edin ve tekrar deneyin!',
            errNodePort:
                'Düğüm portu [ {0} ] erişilemez olarak algılandı. Lütfen güvenlik duvarı veya güvenlik grubunun bu portu izin verdiğinden emin olun.',

            reinstallHelper: '{0} düğümünü yeniden kurmak istiyor musunuz?',
            unhealthyCheck: 'Anormal Kontrol',
            fixOperation: 'Düzeltme İşlemi',
            checkName: 'Kontrol Öğesi',
            checkSSHConn: 'SSH Bağlantı Kullanılabilirliğini Kontrol Et',
            fixSSHConn: 'Bağlantı bilgilerini doğrulamak için düğümü manuel olarak düzenleyin',
            checkConnInfo: 'Agent Bağlantı Bilgilerini Kontrol Et',
            checkStatus: 'Düğüm Hizmeti Kullanılabilirliğini Kontrol Et',
            fixStatus:
                'Hizmetin çalıştığından emin olmak için "systemctl status 1panel-agent.service" komutunu çalıştırın.',
            checkAPI: 'Düğüm API Kullanılabilirliğini Kontrol Et',
            fixAPI: 'Düğüm günlüklerini kontrol edin ve güvenlik duvarı portlarının doğru şekilde açıldığını doğrulayın.',
            forceDelete: 'Zorla Sil',
            operateHelper: 'Aşağıdaki düğümler {0} işlemini geçirecek, devam etmek istiyor musunuz?',
            forceDeleteHelper: 'Zorla silme, düğüm silme hatalarını yok sayar ve veritabanı meta verilerini siler',
            uninstall: 'Düğüm verilerini sil',
            uninstallHelper: 'Bu, düğümle ilgili tüm 1Panel verilerini silecektir. Dikkatli ilerleyin!',
            baseDir: 'Kurulum Dizini',
            baseDirHelper: 'Kurulum dizini boş olduğunda, varsayılan olarak /opt dizinine kurulur',
            nodePort: 'Düğüm Portu',
            offline: 'Çevrimdışı mod',
            freeCount: 'Ücretsiz kota [{0}]',
            offlineHelper: 'Düğüm çevrimdışı bir ortamda olduğunda kullanılır',
        },
        customApp: {
            name: 'Özel Uygulama Deposu',
            appStoreType: 'Uygulama Mağazası Paket Kaynağı',
            appStoreUrl: 'Depo URL’si',
            local: 'Yerel Yol',
            remote: 'Uzak Bağlantı',
            imagePrefix: 'Görüntü Öneki',
            imagePrefixHelper:
                'İşlev: Görüntü önekini özelleştirir ve compose dosyasındaki görüntü alanını değiştirir. Örneğin, görüntü öneki 1panel/custom olarak ayarlandığında, MaxKB için görüntü alanı 1panel/custom/maxkb:v1.10.0 olarak değişir',
            closeHelper: 'Özel uygulama deposunu kullanmayı iptal et',
            appStoreUrlHelper: 'Yalnızca .tar.gz formatı desteklenir',
            postNode: 'Alt düğüme senkronize et',
            postNodeHelper:
                'Özel mağaza paketini alt düğümün kurulum dizinindeki tmp/customApp/apps.tar.gz’e senkronize eder',
            nodes: 'Düğümler',
            selectNode: 'Düğüm Seç',
            selectNodeError: 'Lütfen bir düğüm seçin',
            licenseHelper: 'Pro sürümü, özel uygulama deposu özelliğini destekler',
        },
        alert: {
            isAlert: 'Uyarı',
            alertCount: 'Uyarı Sayısı',
            clamHelper: 'Enfekte dosyalar tarandığında uyarısını tetikle',
            cronJobHelper: 'Görev yürütme başarısız olduğunda uyarısını tetikle',
            licenseHelper: 'Profesyonel sürüm SMS uyarısını destekler',
            alertCountHelper: 'Günlük maksimum uyarı sıklığı',
            alert: 'SMS Uyarısı',
            logs: 'Uyarı Günlükleri',
            list: 'Uyarı Listesi',
            addTask: 'Uyarı Oluştur',
            editTask: 'Uyarıyı Düzenle',
            alertMethod: 'Yöntem',
            alertMsg: 'Uyarı Mesajı',
            alertRule: 'Uyarı Kuralları',
            titleSearchHelper: 'Bulanık arama için uyarı başlığını girin',
            taskType: 'Tür',
            ssl: 'Sertifika (SSL) Sona Ermesi',
            siteEndTime: 'Web Sitesi Sona Ermesi',
            panelPwdEndTime: 'Panel Şifresi Sona Ermesi',
            panelUpdate: 'Yeni Panel Sürümü Mevcut',
            cpu: 'Sunucu CPU Uyarısı',
            memory: 'Sunucu Bellek Uyarısı',
            load: 'Sunucu Yük Uyarısı',
            disk: 'Sunucu Disk Uyarısı',
            website: 'Web Sitesi',
            certificate: 'SSL Sertifikası',
            remainingDays: 'Kalan Günler',
            sendCount: 'Gönderim Sayısı',
            sms: 'SMS',
            wechat: 'WeChat',
            dingTalk: 'DingTalk',
            feiShu: 'FeiShu',
            mail: 'E-posta',
            weCom: 'WeCom',
            sendCountRulesHelper: 'Sona ermeden önce gönderilen toplam uyarılar (günde bir kez)',
            panelUpdateRulesHelper:
                'Yeni panel sürümü algılandığında bir kez uyarı gönder (işlenmezse ertesi gün tekrar gönderilir)',
            oneDaySendCountRulesHelper: 'Günde gönderilen maksimum uyarılar',
            siteEndTimeRulesHelper: 'Asla sona ermeyen web siteleri uyarı tetiklemez',
            autoRenewRulesHelper:
                'Otomatik yenileme etkin olan ve kalan gün sayısı 31’den az olan sertifikalar uyarı tetiklemez',
            panelPwdEndTimeRulesHelper: 'Panel şifresi sona erme uyarıları, sona erme ayarlanmadıysa kullanılamaz',
            sslRulesHelper: 'Tüm SSL Sertifikaları',
            diskInfo: 'Disk',
            monitoringType: 'İzleme Türü',
            autoRenew: 'Otomatik Yenile',
            useDisk: 'Disk Kullanımı',
            usePercentage: 'Kullanım Yüzdesi',
            changeStatus: 'Durumu Değiştir',
            disableMsg:
                'Uyarı görevini durdurmak, bu görevin uyarı mesajları göndermesini engeller. Devam etmek istiyor musunuz?',
            enableMsg:
                'Uyarı görevini etkinleştirmek, bu görevin uyarı mesajları göndermesini sağlar. Devam etmek istiyor musunuz?',
            useExceed: 'Kullanım Aşımı',
            useExceedRulesHelper: 'Kullanım ayarlanan değeri aştığında uyarı tetiklenir',
            cpuUseExceedAvg: 'Ortalama CPU kullanımı belirtilen değeri aşar',
            memoryUseExceedAvg: 'Ortalama bellek kullanımı belirtilen değeri aşar',
            loadUseExceedAvg: 'Ortalama yük kullanımı belirtilen değeri aşar',
            cpuUseExceedAvgHelper: 'Belirtilen süre içinde ortalama CPU kullanımı belirtilen değeri aşar',
            memoryUseExceedAvgHelper: 'Belirtilen süre içinde ortalama bellek kullanımı belirtilen değeri aşar',
            loadUseExceedAvgHelper: 'Belirtilen süre içinde ortalama yük kullanımı belirtilen değeri aşar',
            resourceAlertRulesHelper: 'Not: 30 dakika içinde sürekli uyarılar yalnızca bir SMS gönderir',
            specifiedTime: 'Belirtilen Süre',
            deleteTitle: 'Uyarıyı Sil',
            deleteMsg: 'Uyarı görevini silmek istediğinizden emin misiniz?',

            allSslTitle: 'Tüm Web Sitesi SSL Sertifikası Sona Erme Uyarıları',
            sslTitle: 'Web Sitesi {0} için SSL Sertifikası Sona Erme Uyarısı',
            allSiteEndTimeTitle: 'Tüm Web Sitesi Sona Erme Uyarıları',
            siteEndTimeTitle: 'Web Sitesi {0} Sona Erme Uyarısı',
            panelPwdEndTimeTitle: 'Panel Şifresi Sona Erme Uyarısı',
            panelUpdateTitle: 'Yeni Panel Sürümü Bildirimi',
            cpuTitle: 'Yüksek CPU Kullanımı Uyarısı',
            memoryTitle: 'Yüksek Bellek Kullanımı Uyarısı',
            loadTitle: 'Yüksek Yük Uyarısı',
            diskTitle: 'Bağlama Dizini {0} için Yüksek Disk Kullanımı Uyarısı',
            allDiskTitle: 'Yüksek Disk Kullanımı Uyarısı',

            timeRule: 'Kalan süre {0} günden az olduğunda (işlenmezse ertesi gün tekrar gönderilir)',
            panelUpdateRule:
                'Yeni bir panel sürümü algılandığında bir kez uyarı gönder (işlenmezse ertesi gün tekrar gönderilir)',
            avgRule:
                '{0} dakika içinde ortalama {1} kullanımı {2}%’yi aşarsa, uyarı tetiklenir, günde {3} kez gönderilir',
            diskRule:
                '{0} bağlama dizini için disk kullanımı {1}{2}’yi aşarsa, uyarı tetiklenir, günde {3} kez gönderilir',
            allDiskRule: 'Disk kullanımı {0}{1}’yi aşarsa, uyarı tetiklenir, günde {2} kez gönderilir',

            cpuName: ' CPU ',
            memoryName: 'Bellek',
            loadName: 'Yük',
            diskName: 'Disk',

            syncAlertInfo: 'Manuel Gönderim',
            syncAlertInfoMsg: 'Uyarı görevini manuel olarak göndermek istiyor musunuz?',
            pushError: 'Gönderim Başarısız',
            pushSuccess: 'Gönderim Başarılı',
            syncError: 'Senkronizasyon Başarısız',
            success: 'Uyarı Başarılı',
            pushing: 'Gönderiliyor...',
            error: 'Uyarı Başarısız',
            cleanLog: 'Günlükleri Temizle',
            cleanAlertLogs: 'Uyarı Günlüklerini Temizle',
            daily: 'Günlük Uyarı Sayısı: {0}',
            cumulative: 'Toplam Uyarı Sayısı: {0}',
            clams: 'Virüs tarama',
            taskName: 'Görev Adı',
            cronJobType: 'Görev Türü',
            clamPath: 'Tarama Dizini',
            cronjob: 'Zamanlanmış Görev',
            app: 'Yedekleme Uygulaması',
            web: 'Yedekleme Web Sitesi',
            database: 'Yedekleme Veritabanı',
            directory: 'Yedekleme Dizini',
            log: 'Yedekleme Günlükleri',
            snapshot: 'Sistem Anlık Görüntüsü',
            clamsRulesHelper: 'Uyarısı gerektiren virüs tarama görevleri',
            cronJobRulesHelper: 'Bu tür zamanlanmış görevlerin yapılandırılması gerekir',
            clamsTitle: 'Virüs tarama görevi 「 {0} 」 enfekte dosya algıladı uyarısı',
            cronJobAppTitle: 'Zamanlanmış Görev - Yedekleme Uygulaması 「 {0} 」 Görev Başarısızlık Uyarısı',
            cronJobWebsiteTitle: 'Zamanlanmış Görev - Yedekleme Web Sitesi 「 {0} 」 Görev Başarısızlık Uyarısı',
            cronJobDatabaseTitle: 'Zamanlanmış Görev - Yedekleme Veritabanı 「 {0} 」 Görev Başarısızlık Uyarısı',
            cronJobDirectoryTitle: 'Zamanlanmış Görev - Yedekleme Dizini 「 {0} 」 Görev Başarısızlık Uyarısı',
            cronJobLogTitle: 'Zamanlanmış Görev - Yedekleme Günlükleri 「 {0} 」 Görev Başarısızlık Uyarısı',
            cronJobSnapshotTitle: 'Zamanlanmış Görev - Yedekleme Anlık Görüntüsü 「 {0} 」 Görev Başarısızlık Uyarısı',
            cronJobShellTitle: 'Zamanlanmış Görev - Kabuk betiği 「 {0} 」 Görev Başarısızlık Uyarısı',
            cronJobCurlTitle: 'Zamanlanmış Görev - URL erişimi 「 {0} 」 Görev Başarısızlık Uyarısı',
            cronJobCutWebsiteLogTitle:
                'Zamanlanmış Görev - Web sitesi günlüğünü kes 「 {0} 」 Görev Başarısızlık Uyarısı',
            cronJobCleanTitle: 'Zamanlanmış Görev - Önbellek temizleme 「 {0} 」 Görev Başarısızlık Uyarısı',
            cronJobNtpTitle: 'Zamanlanmış Görev - Sunucu zamanını senkronize etme 「 {0} 」 Görev Başarısızlık Uyarısı',
            clamsRule: 'Virüs tarama enfekte dosya algıladı uyarısı, günde {0} kez gönderilir',
            cronJobAppRule: 'Yedekleme uygulaması görevi başarısız uyarısı, günde {0} kez gönderilir',
            cronJobWebsiteRule: 'Yedekleme web sitesi görevi başarısız uyarısı, günde {0} kez gönderilir',
            cronJobDatabaseRule: 'Yedekleme veritabanı görevi başarısız uyarısı, günde {0} kez gönderilir',
            cronJobDirectoryRule: 'Yedekleme dizini görevi başarısız uyarısı, günde {0} kez gönderilir',
            cronJobLogRule: 'Yedekleme günlükleri görevi başarısız uyarısı, günde {0} kez gönderilir',
            cronJobSnapshotRule: 'Yedekleme anlık görüntüsü görevi başarısız uyarısı, günde {0} kez gönderilir',
            cronJobShellRule: 'Kabuk betiği görevi başarısız uyarısı, günde {0} kez gönderilir',
            cronJobCurlRule: 'URL erişimi görevi başarısız uyarısı, günde {0} kez gönderilir',
            cronJobCutWebsiteLogRule: 'Web sitesi günlüğünü kesme görevi başarısız uyarısı, günde {0} kez gönderilir',
            cronJobCleanRule: 'Önbellek temizleme görevi başarısız uyarısı, günde {0} kez gönderilir',
            cronJobNtpRule: 'Sunucu zamanını senkronize etme görevi başarısız uyarısı, günde {0} kez gönderilir',
            alertSmsHelper: 'SMS sınırı: toplam {0} mesaj, {1} zaten kullanıldı',
            goBuy: 'Daha Fazla Satın Al',
            phone: 'Telefon',
            phoneHelper: 'Uyarı mesajları için gerçek telefon numarası sağlayın',
            dailyAlertNum: 'Günlük Uyarı Sınırı',
            dailyAlertNumHelper: 'Günde maksimum uyarı sayısı (100’e kadar)',
            timeRange: 'Zaman Aralığı',
            sendTimeRange: 'gönderim zaman aralığı',
            sendTimeRangeHelper: '{0} zaman aralığında gönderim yapılabilir',
            to: 'kadar',
            startTime: 'Başlangıç Zamanı',
            endTime: 'Bitiş Zamanı',
            defaultPhone: 'Varsayılan olarak lisansa bağlı hesap telefon numarasına',
            noticeAlert: 'Bildirim Uyarısı',
            resourceAlert: 'Kaynak Uyarısı',
            agentOfflineAlertHelper:
                'Düğüm için çevrimdışı uyarı etkinleştirildiğinde, ana düğüm her 30 dakikada bir tarama yaparak uyarı görevlerini yürütür.',
            offline: 'Çevrimdışı Uyarı',
            offlineHelper:
                'Çevrimdışı uyarı olarak ayarlandığında, ana düğüm her 30 dakikada bir tarama yaparak uyarı görevlerini yürütür.',
            offlineOff: 'Çevrimdışı Uyarısını Etkinleştir',
            offlineOffHelper:
                'Çevrimdışı uyarının etkinleştirilmesi, ana düğümün her 30 dakikada bir tarama yaparak uyarı görevlerini yürütmesini sağlar.',
            offlineClose: 'Çevrimdışı Uyarısını Devre Dışı Bırak',
            offlineCloseHelper:
                'Çevrimdışı uyarının devre dışı bırakılması, alt düğümlerin uyarıları bağımsız olarak işlemesini gerektirir. Uyarı başarısızlığını önlemek için ağ bağlantısını sağlayın.',
            alertNotice: 'Uyarı Bildirimi',
            methodConfig: 'Bildirim Yöntemi Ayarı',
            commonConfig: 'Genel Ayar',
            smsConfig: 'SMS',
            smsConfigHelper: 'SMS bildirimi için telefon numaralarını yapılandırın',
            emailConfig: 'E-posta',
            emailConfigHelper: 'SMTP e-posta gönderme hizmetini yapılandırın',
            deleteConfigTitle: 'Uyarı Yapılandırmasını Sil',
            deleteConfigMsg: 'Uyarı yapılandırmasını silmek istediğinizden emin misiniz?',
            test: 'Test',
            alertTestOk: 'Test bildirimi başarılı',
            alertTestFailed: 'Test bildirimi başarısız',
            displayName: 'Görünen Ad',
            sender: 'Gönderen Adresi',
            password: 'Şifre',
            host: 'SMTP Sunucusu',
            port: 'Port Numarası',
            encryption: 'Şifreleme Yöntemi',
            recipient: 'Alıcı',
            licenseTime: 'Lisans Süresi Hatırlatması',
            licenseTimeTitle: 'Lisans Süresi Hatırlatması',
            displayNameHelper: 'E-posta göndericisinin görünen adı',
            senderHelper: 'E-posta göndermek için kullanılan adres',
            passwordHelper: 'E-posta servisinin yetkilendirme kodu',
            hostHelper: 'SMTP sunucu adresi, örneğin: smtp.qq.com',
            portHelper: 'SSL genellikle 465, TLS genellikle 587',
            sslHelper: 'SMTP portu 465 ise genellikle SSL gerekir',
            tlsHelper: 'SMTP portu 587 ise genellikle TLS gerekir',
        },
        theme: {
            lingXiaGold: 'Ling Xia Altın',
            classicBlue: 'Klasik Mavi',
            freshGreen: 'Taze Yeşil',
            customColor: 'Özel Renk',
            setDefault: 'Varsayılan',
            setDefaultHelper: 'Tema renk şeması başlangıç durumuna geri yüklenecek. Devam etmek istiyor musunuz?',
            setHelper: 'Seçilen tema renk şeması kaydedilecek. Devam etmek istiyor musunuz?',
        },
        exchange: {
            exchange: 'Dosya Değişimi',
            exchangeConfirm: '{0} düğümünden {1} dosya/klasörünü {2} düğümünün {3} dizinine aktarmak istiyor musunuz?',
        },
        cluster: {
            cluster: 'Высокая доступность приложений',
            name: 'Имя кластера',
            addCluster: 'Добавить кластер',
            installNode: 'Установить узел',
            master: 'Главный узел',
            slave: 'Подчиненный узел',
            replicaStatus: 'Ana-Çalışan Durumu',
            unhealthyDeleteError:
                'Yükleme düğümü durumu anormal, lütfen düğüm listesini kontrol edin ve tekrar deneyin!',
            replicaStatusError: 'Durum alımı anormal, lütfen ana düğümü kontrol edin.',
            masterHostError: "Ana düğüm IP'si 127.0.0.1 olamaz",
        },
    },
};

export default {
    ...fit2cloudEnLocale,
    ...message,
};
