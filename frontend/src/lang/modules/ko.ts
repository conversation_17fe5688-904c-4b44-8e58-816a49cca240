import fit2cloudKoLocale from 'fit2cloud-ui-plus/src/locale/lang/en';

const message = {
    commons: {
        true: '참',
        false: '거짓',
        colon: ': ',
        example: '예를 들어, ',
        fit2cloud: 'FIT2CLOUD',
        lingxia: 'Lingxia',
        button: {
            run: '실행',
            create: '생성',
            add: '추가',
            save: '저장',
            set: '설정 수정',
            sync: '동기화',
            delete: '삭제',
            edit: '편집',
            enable: '활성화',
            disable: '비활성화',
            confirm: '확인',
            cancel: '취소',
            reset: '재설정',
            restart: '재시작',
            conn: '연결',
            disConn: '연결 해제',
            clean: '정리',
            login: '로그인',
            close: '닫기',
            off: '꺼짐',
            stop: '중지',
            start: '시작',
            view: '보기',
            watch: '감시',
            handle: '트리거',
            clone: '복제',
            expand: '확장',
            collapse: '축소',
            log: '로그',
            back: '뒤로',
            backup: '백업',
            recover: '복구',
            retry: '재시도',
            upload: '업로드',
            download: '다운로드',
            init: '초기화',
            verify: '검증',
            saveAndEnable: '저장 및 활성화',
            import: '가져오기',
            export: '내보내기',
            power: '권한 부여',
            search: '검색',
            refresh: '새로고침',
            get: '가져오기',
            upgrade: '업그레이드',
            update: '업데이트',
            ignore: '업그레이드 무시',
            install: '설치',
            copy: '복사',
            random: '무작위',
            uninstall: '제거',
            fullscreen: '전체 화면',
            quitFullscreen: '전체 화면 종료',
            showAll: '모두 보기',
            hideSome: '일부 숨기기',
            agree: '동의',
            notAgree: '동의하지 않음',
            preview: '미리 보기',
            open: '열기',
            notSave: '저장하지 않음',
            createNewFolder: '새 폴더 생성',
            createNewFile: '새 파일 생성',
            helpDoc: '도움말 문서',
            unbind: '연결 해제',
            cover: '덮어쓰기',
            skip: '건너뛰기',
            fix: '수정',
            down: '중지',
            up: '시작',
            sure: '확인',
            show: '보기',
            hide: '숨기기',
        },
        operate: {
            start: '시작',
            stop: '중지',
            restart: '재시작',
            reload: '다시 로드',
            rebuild: '재구축',
            sync: '동기화',
            up: '실행',
            down: '중지',
            delete: '삭제',
        },
        search: {
            timeStart: '시작 시간',
            timeEnd: '종료 시간',
            timeRange: '부터',
            dateStart: '시작 날짜',
            dateEnd: '종료 날짜',
        },
        table: {
            all: '전체',
            total: '총 {0}',
            name: '이름',
            type: '유형',
            status: '상태',
            records: '기록',
            group: '그룹',
            createdAt: '생성 시간',
            publishedAt: '게시 시간',
            date: '날짜',
            updatedAt: '업데이트 시간',
            operate: '작업',
            message: '메시지',
            description: '설명',
            interval: '간격',
            user: '소유자',
            title: '제목',
            port: '포트',
            forward: '포워드',
            protocol: '프로토콜',
            tableSetting: '테이블 설정',
            refreshRate: '새로 고침 속도',
            selectColumn: '열 선택',
            local: '로컬',
            serialNumber: '일련 번호',
            manageGroup: '그룹 관리',
            backToList: '목록으로 돌아가기',
            keepEdit: '계속 편집',
        },
        loadingText: {
            Upgrading: '시스템 업그레이드 중입니다. 잠시만 기다려 주십시오...',
            Restarting: '시스템 재시작 중입니다. 잠시만 기다려 주십시오...',
            Recovering: '스냅샷에서 복구 중입니다. 잠시만 기다려 주십시오...',
            Rollbacking: '스냅샷에서 롤백 중입니다. 잠시만 기다려 주십시오...',
        },
        msg: {
            noneData: '데이터가 없습니다',
            delete: `이 작업은 되돌릴 수 없습니다. 계속하시겠습니까?`,
            clean: `이 작업은 되돌릴 수 없습니다. 계속하시겠습니까?`,
            closeDrawerHelper: '시스템에서 변경 사항을 저장하지 않을 수 있습니다. 계속하시겠습니까?',
            deleteSuccess: '삭제 완료',
            loginSuccess: '로그인 성공',
            operationSuccess: '작업 완료',
            copySuccess: '복사 완료',
            notSupportOperation: `이 작업은 지원되지 않습니다`,
            requestTimeout: '요청이 시간 초과되었습니다. 나중에 다시 시도해 주십시오',
            infoTitle: '안내',
            notRecords: '현재 작업에 대한 실행 기록이 생성되지 않았습니다',
            sureLogOut: '로그아웃하시겠습니까?',
            createSuccess: '생성 완료',
            updateSuccess: '업데이트 완료',
            uploadSuccess: '업로드 성공',
            operateConfirm: '작업을 확인하려면 수동으로 입력하십시오: ',
            inputOrSelect: '선택하거나 입력해 주십시오',
            copyFailed: '복사 실패',
            operatorHelper: `"{0}"에 대해 "{1}" 작업이 수행되며 이 작업은 되돌릴 수 없습니다. 계속하시겠습니까?`,
            notFound: '죄송합니다. 요청하신 페이지를 찾을 수 없습니다.',
            unSupportType: `현재 파일 형식은 지원되지 않습니다.`,
            unSupportSize: '업로드된 파일이 {0}M을 초과했습니다. 확인해 주십시오!',
            fileExist: `현재 폴더에 이미 동일한 파일이 존재합니다. 중복 업로드는 지원되지 않습니다.`,
            fileNameErr: '파일 이름은 1~256 자 사이의 영어, 중국어, 숫자, 또는 점(.-_)만 포함해야 합니다.',
            confirmNoNull: `{0} 값이 비어 있지 않은지 확인하십시오.`,
            errPort: '포트 정보가 올바르지 않습니다. 확인해 주십시오!',
            remove: '제거',
            backupHelper: '현재 작업은 {0}을(를) 백업합니다. 계속하시겠습니까?',
            recoverHelper: '{0} 파일에서 복원 중입니다. 이 작업은 되돌릴 수 없습니다. 계속하시겠습니까?',
            refreshSuccess: '새로 고침 완료',
            rootInfoErr: '이미 루트 디렉토리입니다',
            resetSuccess: '초기화 완료',
            creatingInfo: '생성 중입니다. 이 작업이 필요하지 않습니다',
        },
        login: {
            username: '사용자 이름',
            password: '비밀번호',
            welcome: '다시 오신 것을 환영합니다. 사용자 이름과 비밀번호를 입력하여 로그인하세요!',
            errorAuthInfo: '입력한 사용자 이름 또는 비밀번호가 잘못되었습니다. 다시 입력해주세요!',
            errorMfaInfo: '인증 정보가 잘못되었습니다. 다시 시도해주세요!',
            captchaHelper: '캡챠',
            errorCaptcha: '캡챠 코드 오류!',
            notSafe: '접근이 거부되었습니다',
            safeEntrance1: '현재 환경에서 보안 로그인이 활성화되었습니다',
            safeEntrance2: 'SSH 터미널에서 다음 명령어를 입력하여 패널 진입 경로를 확인하세요: 1pctl user-info',
            errIP1: '현재 환경에서 승인된 IP 주소 접근이 활성화되었습니다',
            errDomain1: '현재 환경에서 도메인 이름 바인딩이 활성화되었습니다',
            errHelper: '바인딩 정보를 재설정하려면 SSH 터미널에서 다음 명령어를 실행하세요:',
            codeInput: 'MFA 인증기의 6자리 인증 코드를 입력하세요',
            mfaTitle: 'MFA 인증',
            mfaCode: 'MFA 인증 코드',
            title: 'Linux 서버 관리 패널',
            licenseHelper: '<커뮤니티 라이선스 계약>',
            errorAgree: '커뮤니티 소프트웨어 라이선스에 동의하려면 클릭하세요',
            logout: '로그아웃',
            agreeTitle: '동의',
            agreeContent:
                '귀하의 합법적인 권리와 이익을 보다 잘 보호하기 위해, 다음 &laquo; <a href = "https://www.fit2cloud.com/legal/licenses.html" target = "_blank" >커뮤니티 라이선스 계약</a> &raquo;을 읽고 동의해주세요.',
        },
        rule: {
            username: '사용자 이름을 입력하세요',
            password: '비밀번호를 입력하세요',
            rePassword: '확인 비밀번호가 비밀번호와 일치하지 않습니다.',
            requiredInput: '이 필드는 필수 항목입니다.',
            requiredSelect: '목록에서 항목을 선택하세요',
            illegalChar: '현재 & ; $ \' ` ( ) " > < | 문자 주입은 지원되지 않습니다',
            illegalInput: '이 필드에는 유효하지 않은 문자가 포함될 수 없습니다.',
            commonName:
                '이 필드는 특수 문자로 시작할 수 없으며, 영어, 한자, 숫자, ".", "-", "_" 문자로 구성되어야 하며 길이는 1-128자여야 합니다.',
            userName: '특수 문자로 시작하지 않고, 영어, 한국어, 숫자 및 _, 길이 3-30 지원',
            simpleName:
                '이 필드는 "_"로 시작할 수 없으며, 영어, 숫자 및 "_" 문자로 구성되어야 하며 길이는 3-30 자여야 합니다.',
            simplePassword:
                '이 필드는 "_"로 시작할 수 없으며, 영어, 숫자 및 "_" 문자로 구성되어야 하며 길이는 1-30 자여야 합니다.',
            dbName: '이 필드는 "_"로 시작할 수 없으며, 영어, 숫자 및 "_" 문자로 구성되어야 하며 길이는 1-64 자여야 합니다.',
            imageName: '특수 문자로 시작하지 않고, 영어, 숫자, :@/.-_ 지원, 길이 1-256',
            composeName: '특수 문자로 시작할 수 없으며, 소문자, 숫자, "-", "_"를 지원하며 길이는 1-256 자여야 합니다.',
            volumeName: '이 필드는 영어, 숫자, ".", "-", "_" 문자로 구성되어야 하며 길이는 2-30 자여야 합니다.',
            supervisorName:
                '이 필드는 특수 문자로 시작할 수 없으며, 영어, 숫자, "-", "_" 문자로 구성되어야 하며 길이는 1-128 자여야 합니다.',
            complexityPassword:
                '이 필드는 영어와 숫자로 구성되어야 하며 길이는 8-30 자이고 최소 두 개의 특수 문자가 포함되어야 합니다.',
            commonPassword: '이 필드 길이는 6 자 이상이어야 합니다.',
            linuxName: '이 필드 길이는 1-128 자 사이여야 하며, 다음 특수 문자를 포함할 수 없습니다: "{0}".',
            email: '이 필드는 유효한 이메일 주소여야 합니다.',
            number: '이 필드는 숫자여야 합니다.',
            integer: '이 필드는 양의 정수여야 합니다.',
            ip: '이 필드는 유효한 IP 주소여야 합니다.',
            host: '이 필드는 유효한 IP 주소 또는 도메인 이름이어야 합니다.',
            hostHelper: 'IP 주소 또는 도메인 이름 입력을 지원합니다',
            port: '이 필드는 유효한 포트 번호여야 합니다.',
            selectHelper: '올바른 {0} 파일을 선택하세요',
            domain: '이 필드는 다음 형식이어야 합니다: example.com 또는 example.com:8080.',
            databaseName: '이 필드는 영어, 숫자 및 "_" 문자로 구성되어야 하며 길이는 1-30 자여야 합니다.',
            ipErr: '이 필드는 유효한 IP 주소여야 합니다.',
            numberRange: '이 필드는 {0}에서 {1} 사이의 숫자여야 합니다.',
            paramName: '이 필드는 영어, 숫자, ".", "-", "_" 문자로 구성되어야 하며 길이는 2-30 자여야 합니다.',
            paramComplexity:
                '이 필드는 특수 문자로 시작하거나 끝날 수 없으며, 영어, 숫자, "{0}" 문자로 구성되어야 하며 길이는 6-128 자여야 합니다.',
            paramUrlAndPort: '이 필드는 "http(s)://(도메인 이름/IP):(포트)" 형식이어야 합니다.',
            nginxDoc: '이 필드는 영어, 숫자 및 "." 문자로 구성되어야 합니다.',
            appName:
                '소문자, 숫자, "-", "_"를 지원하며 길이는 2-30 자이고, "-" 또는 "_"로 시작하거나 끝날 수 없습니다.',
            containerName:
                '영어, 숫자, "-", "_", "."를 지원하며, "-", "_", "."로 시작할 수 없고 길이는 2-128 자여야 합니다.',
            mirror: '미러 가속 주소는 http(s)://로 시작해야 하며, 대소문자 영어, 숫자, ".", "/", "-"를 지원하고 공백을 포함할 수 없습니다.',
            disableFunction: '영어 문자, 밑줄 및 ,만 지원합니다',
            leechExts: '영어 문자, 숫자 및 ,만 지원합니다',
            paramSimple: '소문자와 숫자를 지원하며 길이는 1-128 자여야 합니다',
            filePermission: '파일 권한 오류',
            formatErr: '형식 오류입니다. 확인 후 다시 시도하세요',
            phpExtension: '소문자 영어와 숫자, "_"만 지원합니다',
            paramHttp: 'http:// 또는 https:// 로 시작해야 합니다',
            phone: '전화번호 형식이 올바르지 않습니다',
            authBasicPassword: '알파벳, 숫자 및 일반 특수 문자 지원, 길이 1-72',
            length128Err: '길이는 128자를 초과할 수 없습니다',
            maxLength: '길이는 {0}자를 초과할 수 없습니다',
            alias: '영어, 숫자, - 및 _ 지원, 길이 1-30, -_로 시작하거나 끝날 수 없습니다.',
        },
        res: {
            paramError: '요청이 실패했습니다. 나중에 다시 시도하세요!',
            forbidden: '현재 사용자는 권한이 없습니다',
            serverError: '서비스 예외',
            notFound: '리소스가 존재하지 않습니다',
            commonError: '요청이 실패했습니다',
        },
        service: {
            serviceNotStarted: `{0} 서비스가 시작되지 않았습니다.`,
        },
        status: {
            running: '실행 중',
            done: '완료',
            scanFailed: '불완전',
            success: '성공',
            waiting: '대기 중',
            waiting1: '대기 중',
            failed: '실패',
            stopped: '중지됨',
            error: '오류',
            created: '생성됨',
            restarting: '재시작 중',
            uploading: '업로드 중',
            unhealthy: '비정상',
            removing: '제거 중',
            paused: '일시 중지',
            exited: '종료됨',
            dead: '중단됨',
            installing: '설치 중',
            enabled: '활성화됨',
            disabled: '비활성화됨',
            normal: '정상',
            building: '빌드 중',
            upgrading: '업그레이드 중',
            pending: '편집 대기',
            rebuilding: '재빌드 중',
            deny: '거부됨',
            accept: '수락됨',
            used: '사용 중',
            unUsed: '사용 안 함',
            starting: '시작 중',
            recreating: '재생성 중',
            creating: '생성 중',
            init: '애플리케이션 대기 중',
            ready: '정상',
            applying: '적용 중',
            uninstalling: '제거 중',
            lost: '연결 끊김',
            bound: '바인딩됨',
            unbind: '미바인드',
            exceptional: '예외',
            free: '여유',
            enable: '활성화됨',
            disable: '비활성화됨',
            deleted: '삭제됨',
            downloading: '다운로드 중',
            packing: '패키징 중',
            sending: '전송 중',
            healthy: '정상',
            executing: '실행 중',
            installerr: '설치 실패',
            applyerror: '적용 실패',
            systemrestart: '중단됨',
            starterr: '시작 실패',
            uperr: '실행 실패',
        },
        units: {
            second: '초 | 초 | 초',
            minute: '분 | 분 | 분',
            hour: '시간 | 시간 | 시간',
            day: '일 | 일 | 일',
            week: '주 | 주 | 주',
            month: '월 | 월 | 월',
            year: '년 | 년 | 년',
            time: '시간',
            core: '코어 | 코어 | 코어',
            secondUnit: '초',
            minuteUnit: '분',
            hourUnit: '시간',
            dayUnit: '일',
            millisecond: '밀리초',
        },
    },
    menu: {
        home: '개요',
        apps: '앱 스토어',
        website: '웹사이트 | 웹사이트들',
        project: '프로젝트 | 프로젝트들',
        config: '구성 | 구성들',
        ssh: 'SSH 설정',
        firewall: '방화벽',
        ssl: '인증서 | 인증서들',
        database: '데이터베이스 | 데이터베이스들',
        aiTools: 'AI',
        mcp: 'MCP',
        container: '컨테이너 | 컨테이너들',
        cronjob: '크론 작업 | 크론 작업들',
        system: '시스템',
        security: '보안',
        files: '파일',
        monitor: '모니터링',
        terminal: '터미널',
        settings: '설정 | 설정들',
        toolbox: '툴박스',
        logs: '로그 | 로그들',
        runtime: '런타임 | 런타임들',
        processManage: '프로세스 | 프로세스들',
        process: '프로세스 | 프로세스들',
        network: '네트워크 | 네트워크들',
        supervisor: '슈퍼바이저',
        tamper: '변조 방지',
        app: '애플리케이션',
        msgCenter: '작업 센터',
    },
    home: {
        restart_1panel: '패널 재시작',
        restart_system: '서버 재시작',
        operationSuccess:
            '작업이 성공적으로 완료되었습니다. 시스템이 재부팅 중입니다. 나중에 브라우저를 수동으로 새로 고침하세요!',
        entranceHelper: `보안 입구가 활성화되어 있지 않습니다. "설정 -> 보안"에서 활성화하여 시스템 보안을 강화할 수 있습니다.`,
        appInstalled: '설치된 애플리케이션',
        systemInfo: '시스템 정보',
        hostname: '호스트 이름',
        platformVersion: '운영 체제',
        kernelVersion: '커널',
        kernelArch: '아키텍처',
        network: '네트워크',
        io: '디스크 I/O',
        ip: '로컬 IP',
        proxy: '시스템 프록시',
        baseInfo: '기본 정보',
        totalSend: '총 송신',
        totalRecv: '총 수신',
        rwPerSecond: 'I/O 작업',
        ioDelay: 'I/O 지연 시간',
        uptime: '작동 시간',
        runningTime: '가동 시간',
        mem: '시스템',
        swapMem: '스왑 파티션',

        runSmoothly: '낮은 부하',
        runNormal: '보통 부하',
        runSlowly: '높은 부하',
        runJam: '심한 부하',

        core: '물리적 코어',
        logicCore: '논리 코어',
        loadAverage: '지난 1분의 평균 부하 | 지난 {n} 분의 평균 부하',
        load: '부하',
        mount: '마운트 지점',
        fileSystem: '파일 시스템',
        total: '전체',
        used: '사용',
        cache: '�시',
        free: '여유',
        shard: '샤딩',
        available: '사용 가능',
        percent: '사용률',
        goInstall: 'Go 설치',

        networkCard: '네트워크 카드',
        disk: '디스크',
    },
    tabs: {
        more: '더 보기',
        hide: '숨기기',
        closeLeft: '왼쪽 닫기',
        closeRight: '오른쪽 닫기',
        closeCurrent: '현재 탭 닫기',
        closeOther: '다른 탭 닫기',
        closeAll: '모두 닫기',
    },
    header: {
        logout: '로그아웃',
    },
    database: {
        manage: '관리',
        deleteBackupHelper: '데이터베이스 백업을 동시에 삭제',
        delete: '삭제 작업은 되돌릴 수 없습니다. 삭제하려면 "',
        deleteHelper: '"를 입력하세요.',
        create: '데이터베이스 생성',
        noMysql: '데이터베이스 서비스 (MySQL 또는 MariaDB)',
        noPostgresql: '데이터베이스 서비스 PostgreSQL',
        goUpgrade: '업그레이드로 이동',
        goInstall: '설치로 이동',
        isDelete: '삭제됨',
        permission: '권한',
        permissionForIP: 'IP',
        permissionAll: '모두(%)',
        localhostHelper:
            '컨테이너 배포 시 데이터베이스 권한을 "localhost"로 설정하면 컨테이너 외부에서 접근할 수 없게 됩니다. 신중하게 선택하세요!',
        databaseConnInfo: '연결 정보',
        rootPassword: '루트 비밀번호',
        serviceName: '서비스 이름',
        serviceNameHelper: '같은 네트워크 내 컨테이너 간의 접근.',
        backupList: '백업',
        loadBackup: '불러오기',
        remoteAccess: '원격 접근',
        remoteHelper: '여러 IP 를 쉼표로 구분하여 입력, 예: *************, *************',
        remoteConnHelper:
            'MySQL 의 root 사용자로 원격 접속은 보안 위험을 초래할 수 있습니다. 따라서 이 작업은 신중히 수행해야 합니다.',
        changePassword: '비밀번호',
        changeConnHelper: '이 작업은 현재 데이터베이스 {0}을(를) 수정합니다. 계속하시겠습니까?',
        changePasswordHelper:
            '데이터베이스가 애플리케이션과 연결되어 있습니다. 비밀번호를 변경하면 애플리케이션의 데이터베이스 비밀번호도 변경됩니다. 변경 사항은 애플리케이션이 재시작된 후에 적용됩니다.',
        confChange: '설정',
        confNotFound:
            '설정 파일을 찾을 수 없습니다. 앱 스토어에서 애플리케이션을 최신 버전으로 업그레이드하고 다시 시도해주세요!',
        portHelper: '이 포트는 컨테이너의 노출된 포트입니다. 수정을 별도로 저장하고 컨테이너를 재시작해야 합니다!',
        loadFromRemote: '동기화',
        userBind: '사용자 바인딩',
        pgBindHelper: `이 작업은 새 사용자를 생성하여 대상 데이터베이스에 바인딩하는 데 사용됩니다. 현재 데이터베이스에 이미 존재하는 사용자 선택은 지원되지 않습니다.`,
        pgSuperUser: '슈퍼 사용자',
        loadFromRemoteHelper: '이 작업은 서버의 데이터베이스 정보를 1Panel로 동기화합니다. 계속 진행하시겠습니까?',
        passwordHelper: '불러올 수 없습니다. 수정해주세요.',
        remote: '원격',
        remoteDB: '원격 서버 | 원격 서버들',
        createRemoteDB: '원격 DB 바인딩',
        unBindRemoteDB: '원격 DB 바인딩 해제',
        unBindForce: '강제 바인딩 해제',
        unBindForceHelper: '바인딩 해제 중 발생하는 모든 오류를 무시하고 최종 작업을 성공적으로 완료합니다.',
        unBindRemoteHelper:
            '원격 데이터베이스 바인딩 해제는 바인딩 관계만 제거하며, 원격 데이터베이스 자체는 삭제되지 않습니다.',
        editRemoteDB: '원격 서버 편집',
        localDB: '로컬 데이터베이스',
        address: '데이터베이스 주소',
        version: '데이터베이스 버전',
        userHelper: '루트 사용자 또는 루트 권한을 가진 데이터베이스 사용자가 원격 데이터베이스에 접근할 수 있습니다.',
        pgUserHelper: '슈퍼 사용자 권한을 가진 데이터베이스 사용자.',
        ssl: 'SSL 사용',
        clientKey: '클라이언트 개인 키',
        clientCert: '클라이언트 인증서',
        caCert: 'CA 인증서',
        hasCA: 'CA 인증서 있음',
        skipVerify: '인증서 유효성 검사 무시',

        formatHelper: '현재 데이터베이스 문자셋은 {0} 입니다. 문자셋 불일치로 인해 복구에 실패할 수 있습니다.',
        selectFile: '파일 선택',
        dropHelper: '여기에 업로드한 파일을 드래그 앤 드롭하거나',
        clickHelper: '클릭하여 업로드',
        supportUpType: 'sql, sql.gz, tar.gz 파일만 지원됩니다.',
        zipFormat: 'tar.gz 압축 패키지 구조: test.tar.gz 압축 패키지에는 test.sql이 포함되어야 합니다.',

        currentStatus: '현재 상태',
        baseParam: '기본 파라미터',
        performanceParam: '성능 파라미터',
        runTime: '시작 시간',
        connections: '전체 연결',
        bytesSent: '전송된 바이트',
        bytesReceived: '수신된 바이트',
        queryPerSecond: '초당 쿼리',
        txPerSecond: '초당 전송',
        connInfo: '활성/최대 연결',
        connInfoHelper: '값이 너무 크면 "max_connections" 값을 증가시켜야 합니다.',
        threadCacheHit: '스레드 캐시 적중',
        threadCacheHitHelper: '값이 너무 낮으면 "thread_cache_size" 값을 증가시켜야 합니다.',
        indexHit: '인덱스 적중',
        indexHitHelper: '값이 너무 낮으면 "key_buffer_size" 값을 증가시켜야 합니다.',
        innodbIndexHit: 'Innodb 인덱스 적중률',
        innodbIndexHitHelper: '값이 너무 낮으면 "innodb_buffer_pool_size" 값을 증가시켜야 합니다.',
        cacheHit: '쿼리 캐시 적중',
        cacheHitHelper: '값이 너무 낮으면 "query_cache_size" 값을 증가시켜야 합니다.',
        tmpTableToDB: '디스크로 임시 테이블',
        tmpTableToDBHelper: '값이 너무 크면 "tmp_table_size" 값을 증가시켜야 합니다.',
        openTables: '열린 테이블',
        openTablesHelper: '"table_open_cache" 설정 값이 이 값 이상이어야 합니다.',
        selectFullJoin: '전체 조인 선택',
        selectFullJoinHelper: '값이 0이 아니면 데이터 테이블의 인덱스가 올바른지 확인하십시오.',
        selectRangeCheck: '인덱스 없는 조인 수',
        selectRangeCheckHelper: '값이 0이 아니면 데이터 테이블의 인덱스가 올바른지 확인하십시오.',
        sortMergePasses: '정렬된 병합 횟수',
        sortMergePassesHelper: '값이 너무 크면 "sort_buffer_size" 값을 증가시켜야 합니다.',
        tableLocksWaited: '테이블 잠금 대기',
        tableLocksWaitedHelper: '값이 너무 크면 데이터베이스 성능을 증가시키는 것을 고려해야 합니다.',

        performanceTuning: '성능 튜닝',
        optimizationScheme: '최적화 방안',
        keyBufferSizeHelper: '인덱스용 버퍼 크기',
        queryCacheSizeHelper: '쿼리 캐시. 이 기능이 비활성화된 경우 이 값을 0으로 설정하세요.',
        tmpTableSizeHelper: '임시 테이블 캐시 크기',
        innodbBufferPoolSizeHelper: 'Innodb 버퍼 크기',
        innodbLogBufferSizeHelper: 'Innodb 로그 버퍼 크기',
        sortBufferSizeHelper: '* 연결당, 스레드 정렬 버퍼 크기',
        readBufferSizeHelper: '* 연결당, 읽기 버퍼 크기',
        readRndBufferSizeHelper: '* 연결당, 임의 읽기 버퍼 크기',
        joinBufferSizeHelper: '* 연결당, 조인 테이블 캐시 크기',
        threadStackelper: '* 연결당, 스레드별 스택 크기',
        binlogCacheSizeHelper: '* 연결당, 이진 로그 캐시 크기 (4096의 배수)',
        threadCacheSizeHelper: '스레드 풀 크기',
        tableOpenCacheHelper: '테이블 캐시',
        maxConnectionsHelper: '최대 연결 수',
        restart: '재시작',

        slowLog: '느린 로그',
        noData: '아직 느린 로그가 없습니다.',

        isOn: '켜짐',
        longQueryTime: '임계값(s)',
        thresholdRangeHelper: '올바른 임계값을 입력하십시오 (1 - 600).',

        timeout: '타임아웃(s)',
        timeoutHelper: '유휴 연결의 타임아웃 기간. 0은 연결이 지속적으로 유지됨을 의미합니다.',
        maxclients: '최대 클라이언트',
        requirepassHelper:
            '비밀번호가 설정되지 않은 경우 이 필드를 비워 두세요. 변경 사항은 별도로 저장하고 컨테이너를 재시작해야 합니다!',
        databases: '데이터베이스 수',
        maxmemory: '최대 메모리 사용량',
        maxmemoryHelper: '0은 제한이 없음을 의미합니다.',
        tcpPort: '현재 수신 포트.',
        uptimeInDays: '운영 일수.',
        connectedClients: '연결된 클라이언트 수.',
        usedMemory: '현재 Redis 의 메모리 사용량.',
        usedMemoryRss: '운영 체제에서 요청한 메모리 크기.',
        usedMemoryPeak: 'Redis 의 최대 메모리 소비량.',
        memFragmentationRatio: '메모리 단편화 비율.',
        totalConnectionsReceived: '시작 이후 총 연결된 클라이언트 수.',
        totalCommandsProcessed: '실행된 총 명령 수.',
        instantaneousOpsPerSec: '초당 서버에서 실행된 명령 수.',
        keyspaceHits: '데이터베이스 키가 성공적으로 발견된 횟수.',
        keyspaceMisses: '데이터베이스 키를 찾지 못한 횟수.',
        hit: '데이터베이스 키 발견 비율.',
        latestForkUsec: '마지막 fork() 작업에 소요된 마이크로초 수.',
        redisCliHelper: `"redis-cli" 서비스가 감지되지 않았습니다. 서비스를 먼저 활성화하십시오.`,
        redisQuickCmd: 'Redis 빠른 명령',
        recoverHelper: '이 작업은 데이터를 [{0}]으로 덮어씁니다. 계속하시겠습니까?',
        submitIt: '데이터 덮어쓰기',

        baseConf: '기본 설정',
        allConf: '모든 설정',
        restartNow: '지금 재시작',
        restartNowHelper1:
            '구성 변경 사항이 적용되려면 시스템을 재시작해야 합니다. 데이터가 지속되어야 하는 경우 먼저 저장 작업을 수행하십시오.',
        restartNowHelper: '이 작업은 시스템이 재시작된 후에만 적용됩니다.',

        persistence: '지속성',
        rdbHelper1: '초 단위, 삽입',
        rdbHelper2: '데이터 항목 수',
        rdbHelper3: '조건을 충족하면 RDB 지속성이 트리거됩니다.',
        rdbInfo: '규칙 목록의 값이 1에서 100000 사이여야 합니다.',

        containerConn: '컨테이너 연결',
        connAddress: '주소',
        containerConnHelper:
            '이 연결 주소는 웹사이트 런타임(PHP 등) 또는 컨테이너에서 실행 중인 애플리케이션에서 사용할 수 있습니다.',
        remoteConn: '외부 연결',
        remoteConnHelper2: '컨테이너 환경이 아닌 경우 또는 외부 연결에는 이 주소를 사용하십시오.',
        remoteConnHelper3:
            '기본 접근 주소는 호스트 IP입니다. 수정하려면 패널 설정 페이지의 "기본 접근 주소" 구성 항목으로 이동하세요.',
        localIP: '로컬 IP',
    },
    aiTools: {
        model: {
            model: '모델',
            create: '모델 추가',
            create_helper: '가져오기 "{0}"',
            ollama_doc: 'Ollama 공식 웹사이트를 방문하여 더 많은 모델을 검색하고 찾을 수 있습니다.',
            container_conn_helper: '컨테이너 간 접근 또는 연결에 이 주소를 사용',
            ollama_sync: 'Ollama 모델 동기화 중 다음 모델이 존재하지 않음을 발견했습니다. 삭제하시겠습니까?',
            from_remote: '이 모델은 1Panel을 통해 다운로드되지 않았으며 관련 풀 로그가 없습니다.',
            no_logs: '이 모델의 풀 로그가 삭제되어 관련 로그를 볼 수 없습니다.',
        },
        proxy: {
            proxy: 'AI 프록시 강화',
            proxyHelper1: '도메인을 바인딩하고 HTTPS를 활성화하여 전송 보안을 강화',
            proxyHelper2: 'IP 접근을 제한하여 공용 인터넷에서의 노출을 방지',
            proxyHelper3: '스트리밍을 활성화',
            proxyHelper4: '생성 후, 웹사이트 목록에서 이를 보고 관리할 수 있습니다',
            proxyHelper5:
                '활성화한 후, 앱 스토어 - 설치됨 - Ollama - 매개변수에서 포트 외부 접근을 비활성화하여 보안을 강화할 수 있습니다.',
            proxyHelper6: '프록시 구성을 비활성화하려면 웹사이트 목록에서 삭제할 수 있습니다.',
            whiteListHelper: '화이트리스트에 있는 IP만 접근 허용',
        },
        gpu: {
            gpu: 'GPU 모니터',
            base: '기본 정보',
            gpuHelper: '현재 시스템에서 NVIDIA-SMI 또는 XPU-SMI 명령이 감지되지 않았습니다. 확인 후 다시 시도하세요!',
            driverVersion: '드라이버 버전',
            cudaVersion: 'CUDA 버전',
            process: '프로세스 정보',
            type: '유형',
            typeG: '그래픽',
            typeC: '연산',
            typeCG: '연산 + 그래픽',
            processName: '프로세스 이름',
            processMemoryUsage: '메모리 사용량',
            temperatureHelper: 'GPU 온도가 높으면 GPU 주파수 제한이 발생할 수 있습니다.',
            performanceStateHelper: 'P0(최대 성능)부터 P12(최소 성능)까지',
            busID: '버스 ID',
            persistenceMode: '지속 모드',
            enabled: '활성화됨',
            disabled: '비활성화됨',
            persistenceModeHelper: '지속 모드는 작업 응답 속도를 빠르게 하지만 대기 전력 소비를 증가시킵니다.',
            displayActive: '그래픽 카드 초기화됨',
            displayActiveT: '예',
            displayActiveF: '아니요',
            ecc: '오류 감지 및 수정 기술',
            computeMode: '연산 모드',
            default: '기본값',
            exclusiveProcess: '단독 프로세스',
            exclusiveThread: '단독 스레드',
            prohibited: '금지됨',
            defaultHelper: '기본값: 프로세스가 동시에 실행될 수 있음',
            exclusiveProcessHelper:
                '단독 프로세스: 하나의 CUDA 컨텍스트만 GPU 를 사용할 수 있지만, 여러 스레드에서 공유 가능',
            exclusiveThreadHelper: '단독 스레드: CUDA 컨텍스트의 하나의 스레드만 GPU 를 사용할 수 있음',
            prohibitedHelper: '금지됨: 프로세스가 동시에 실행되는 것이 허용되지 않음',
            migModeHelper: '사용자 수준에서 GPU 를 물리적으로 분리하는 MIG 인스턴스를 생성하는 데 사용됩니다.',
            migModeNA: '지원되지 않음',
        },
        mcp: {
            server: 'MCP サーバー',
            create: 'サーバーを追加',
            edit: 'サーバーを編集',
            commandHelper: '例: npx -y {0}',
            baseUrl: '外部アクセスパス',
            baseUrlHelper: '例: http://192.168.1.2:8000',
            ssePath: 'SSE パス',
            ssePathHelper: '例: /sse, 他のサーバーと重複しないように注意してください',
            environment: '環境変数',
            envKey: '変数名',
            envValue: '変数値',
            externalUrl: '外部接続アドレス',
            operatorHelper: '{0} に {1} 操作を実行します、続行しますか？',
            domain: 'デフォルトアクセスアドレス',
            domainHelper: '例: *********** または example.com',
            bindDomain: 'ウェブサイトをバインド',
            commandPlaceHolder: '현재 npx 및 바이너리 시작 명령만 지원합니다',
            importMcpJson: 'MCP サーバー設定をインポート',
            importMcpJsonError: 'mcpServers 構造が正しくありません',
            bindDomainHelper:
                '웹사이트를 바인딩한 후, 설치된 모든 MCP 서버의 접근 주소를 수정하고 포트의 외부 접근을 닫습니다',
            outputTransport: '출력 유형',
            streamableHttpPath: '스트리밍 경로',
            streamableHttpPathHelper: '예: /mcp, 다른 서버와 중복되지 않도록 주의하세요',
        },
    },
    container: {
        create: '컨테이너 만들기',
        edit: '컨테이너 편집',
        updateHelper1: '이 컨테이너가 앱 스토어에서 왔음을 감지했습니다. 다음 두 가지 사항을 유의하십시오:',
        updateHelper2: '1. 현재 수정 사항은 앱 스토어에 설치된 애플리케이션에 동기화되지 않습니다.',
        updateHelper3: '2. 설치된 페이지에서 애플리케이션을 수정하면 현재 편집된 내용이 무효화됩니다.',
        updateHelper4: '컨테이너 편집에는 재빌드가 필요하며, 비지속적인 데이터는 손실됩니다. 계속하시겠습니까?',
        containerList: '컨테이너 목록',
        operatorHelper: '{0} 작업이 다음 컨테이너에서 수행됩니다. 계속하시겠습니까?',
        operatorAppHelper:
            '"{0}" 작업이 다음 컨테이너에서 수행되며, 실행 중인 서비스에 영향을 미칠 수 있습니다. 계속하시겠습니까?',
        start: '시작',
        stop: '중지',
        restart: '재시작',
        kill: '강제 종료',
        pause: '일시 정지',
        unpause: '재개',
        rename: '이름 변경',
        remove: '제거',
        removeAll: '모두 제거',
        containerPrune: '정리',
        containerPruneHelper1: '이 작업은 중지된 모든 컨테이너를 삭제합니다.',
        containerPruneHelper2:
            "앱 스토어에서 가져온 컨테이너는 정리 후 '앱 스토어 -> 설치됨' 페이지로 이동하여 '재빌드' 버튼을 클릭하여 재설치해야 합니다.",
        containerPruneHelper3: '이 작업은 취소할 수 없습니다. 계속하시겠습니까?',
        imagePrune: '정리',
        imagePruneSome: '라벨 없는 이미지 정리',
        imagePruneSomeEmpty: "라벨이 'none'인 이미지가 정리되지 않았습니다.",
        imagePruneSomeHelper: "컨테이너에서 사용되지 않는 'none' 태그가 붙은 이미지를 정리합니다.",
        imagePruneAll: '사용되지 않는 이미지 정리',
        imagePruneAllEmpty: '사용되지 않는 이미지가 정리되지 않았습니다.',
        imagePruneAllHelper: '사용되지 않는 이미지를 정리합니다.',
        networkPrune: '정리',
        networkPruneHelper: '사용되지 않는 네트워크를 모두 제거합니다. 계속하시겠습니까?',
        volumePrune: '정리',
        volumePruneHelper: '사용되지 않는 로컬 볼륨을 모두 제거합니다. 계속하시겠습니까?',
        cleanSuccess: '작업이 성공적으로 완료되었습니다. 이번 정리에서 {0}개의 항목이 정리되었습니다!',
        cleanSuccessWithSpace:
            '작업이 성공적으로 완료되었습니다. 이번 정리에서 {0}개의 디스크가 정리되었으며, 확보된 디스크 공간은 {1}입니다!',
        unExposedPort: '현재 포트 매핑 주소는 127.0.0.1로 외부 액세스를 활성화할 수 없습니다.',
        upTime: '업타임',
        fetch: '가져오기',
        lines: '라인',
        linesHelper: '올바른 로그 수를 입력하세요!',
        lastDay: '지난 하루',
        last4Hour: '지난 4시간',
        lastHour: '지난 1시간',
        last10Min: '지난 10분',
        cleanLog: '로그 정리',
        downLogHelper1: '이 작업은 컨테이너 {0}의 모든 로그를 다운로드합니다. 계속하시겠습니까?',
        downLogHelper2: '이 작업은 컨테이너 {0}의 최근 {0}개의 로그를 다운로드합니다. 계속하시겠습니까?',
        cleanLogHelper: '이 작업은 컨테이너를 재시작해야 하며 취소할 수 없습니다. 계속하시겠습니까?',
        newName: '새 이름',
        source: '리소스 사용',
        cpuUsage: 'CPU 사용',
        cpuTotal: '전체 CPU',
        core: '코어',
        memUsage: '메모리 사용',
        memTotal: '메모리 한도',
        memCache: '메모리 캐시',
        ip: 'IP 주소',
        cpuShare: 'CPU 공유',
        cpuShareHelper:
            '컨테이너 엔진은 기본값으로 1024를 사용합니다. 이를 늘리면 컨테이너에 더 많은 CPU 시간을 할당할 수 있습니다.',
        inputIpv4: '예시: ***********',
        inputIpv6: '예시: 2001:0db8:85a3:0000:0000:8a2e:0370:7334',
        containerFromAppHelper:
            '이 컨테이너가 앱 스토어에서 왔음을 감지했습니다. 앱 작업으로 현재 편집이 무효화될 수 있습니다.',
        containerFromAppHelper1:
            '설치된 애플리케이션 목록에서 [매개변수] 버튼을 클릭하여 편집 페이지로 이동하고 컨테이너 이름을 수정하세요.',
        command: '명령어',
        console: '컨테이너 상호작용',
        tty: '가상 TTY 할당 (-t)',
        openStdin: 'STDIN 을 열어둡니다. 연결되지 않더라도 계속 열려있습니다 (-i)',
        custom: '사용자 정의',
        emptyUser: '비워두면 기본값으로 로그인합니다.',
        privileged: '특권 모드',
        privilegedHelper:
            '컨테이너가 호스트에서 특정 특권 작업을 수행할 수 있도록 허용합니다. 이는 보안 위험을 초래할 수 있으므로 주의해서 사용하십시오.',
        editComposeHelper:
            '참고: 설정된 환경 변수는 기본적으로 1panel.env 파일에 작성됩니다. 컨테이너에서 이러한 매개변수를 사용하려면 compose 파일에 env_file 참조를 수동으로 추가해야 합니다.',
        upgradeHelper: '레포지토리 이름/이미지 이름: 이미지 버전',
        upgradeWarning2:
            '업그레이드 작업은 컨테이너를 재빌드해야 하며, 비지속적인 데이터가 손실됩니다. 계속하시겠습니까?',
        oldImage: '현재 이미지',
        sameImageContainer: '동일 이미지 컨테이너',
        sameImageHelper: '동일한 이미지를 사용하는 컨테이너는 선택 후 일괄 업그레이드 가능',
        targetImage: '대상 이미지',
        imageLoadErr: '컨테이너에 대한 이미지 이름이 감지되지 않았습니다.',
        appHelper: '이 컨테이너는 앱 스토어에서 왔으며 업그레이드 시 서비스가 중단될 수 있습니다.',
        input: '수동 입력',
        forcePull: '이미지 강제 풀',
        forcePullHelper: '이 작업은 서버에 있는 기존 이미지를 무시하고 레지스트리에서 최신 이미지를 강제로 가져옵니다.',
        server: '호스트',
        serverExample: '80, 80-88, ip:80 또는 ip:80-88',
        containerExample: '80 또는 80-88',
        exposePort: '포트 노출',
        exposeAll: '모든 포트 노출',
        cmdHelper: '예시: nginx -g "daemon off;"',
        entrypointHelper: '예시: docker-entrypoint.sh',
        autoRemove: '자동 제거',
        cpuQuota: 'CPU 코어 수',
        memoryLimit: '메모리',
        limitHelper: '0으로 설정하면 제한이 없으며, 최대값은 {0}입니다.',
        mount: '마운트',
        volumeOption: '볼륨',
        hostOption: '호스트',
        serverPath: '서버 경로',
        containerDir: '컨테이너 경로',
        volumeHelper: '저장소 볼륨의 내용이 올바른지 확인하십시오.',
        modeRW: '읽기/쓰기',
        modeR: '읽기 전용',
        mode: '모드',
        env: '환경',
        restartPolicy: '재시작 정책',
        always: '항상',
        unlessStopped: '중지되지 않는 한',
        onFailure: '실패 시 (기본 5회)',
        no: '절대',
        refreshTime: '새로 고침 간격',
        cache: '캐시',
        image: '이미지 | 이미지들',
        imagePull: '풀',
        imagePush: '푸시',
        imageDelete: '이미지 삭제',
        imageTagDeleteHelper: '이 이미지 ID와 관련된 다른 태그를 제거합니다.',
        repoName: '컨테이너 저장소 이름',
        imageName: '이미지 이름',
        pull: '풀',
        path: '경로',
        importImage: '가져오기',
        build: '빌드',
        imageBuild: '이미지 빌드',
        pathSelect: '경로',
        label: '레이블',
        imageTag: '이미지 태그',
        push: '푸시',
        fileName: '파일 이름',
        export: '내보내기',
        exportImage: '이미지 내보내기',
        size: '크기',
        tag: '태그',
        tagHelper: '한 줄에 하나씩. 예시:\nkey1=value1\nkey2=value2',
        imageNameHelper: '이미지 이름과 태그, 예시: nginx:latest',
        cleanBuildCache: '빌드 캐시 정리',
        delBuildCacheHelper: `이 작업은 빌드 중 생성된 모든 캐시된 아티팩트를 삭제하며 되돌릴 수 없습니다. 계속 하시겠습니까?`,
        urlWarning: 'URL 접두어에 http:// 또는 https://를 포함할 필요는 없습니다. 수정해 주세요.',

        network: '네트워크 | 네트워크들',
        networkHelper: '이로 인해 일부 애플리케이션과 실행 환경이 제대로 작동하지 않을 수 있습니다. 계속 하시겠습니까?',
        createNetwork: '생성',
        networkName: '이름',
        driver: '드라이버',
        option: '옵션',
        attachable: '연결 가능',
        subnet: '서브넷',
        scope: 'IP 범위',
        gateway: '게이트웨이',
        auxAddress: '제외 IP',

        volume: '볼륨 | 볼륨들',
        volumeDir: '볼륨 디렉터리',
        nfsEnable: 'NFS 스토리지 사용',
        nfsAddress: '주소',
        mountpoint: '마운트 지점',
        mountpointNFSHelper: '예: /nfs, /nfs-share',
        options: '옵션',
        createVolume: '생성',

        repo: '레지스트리',
        createRepo: '추가',
        httpRepoHelper: 'HTTP 타입 저장소 작업 시 Docker 서비스 재시작이 필요합니다.',
        httpRepo: 'HTTP 프로토콜을 선택하면 Docker 서비스를 재시작하여 불안정한 레지스트리에 추가해야 합니다.',
        delInsecure: '신뢰할 수 없는 항목 삭제',
        delInsecureHelper:
            '이 작업은 Docker 서비스를 재시작하여 불안정한 레지스트리에서 제거합니다. 계속 하시겠습니까?',
        downloadUrl: '서버',
        imageRepo: '이미지 레포지토리',
        repoHelper: '거울 레포지토리/조직/프로젝트가 포함되어 있습니까?',
        auth: '인증 필요',
        mirrorHelper:
            '거울이 여러 개 있을 경우 각 줄에 하나씩 표시해야 합니다. 예시:\nhttp://xxxxxx.m.daocloud.io \nhttps://xxxxxx.mirror.aliyuncs.com',
        registrieHelper:
            '개인 레지스트리가 여러 개 있을 경우 각 줄에 하나씩 표시해야 합니다. 예시:\n*************:8081 \n*************:8081',

        compose: '컴포즈 | 컴포즈들',
        fromChangeHelper: '소스를 변경하면 현재 편집한 내용이 삭제됩니다. 계속 하시겠습니까?',
        composePathHelper: '구성 파일 저장 경로: {0}',
        composeHelper: '1Panel 에디터나 템플릿을 통해 생성된 컴포지션은 {0}/docker/compose 디렉토리에 저장됩니다.',
        deleteFile: '파일 삭제',
        deleteComposeHelper:
            '이 작업은 컴포즈와 관련된 모든 파일을 삭제합니다. 구성을 포함한 지속적인 파일도 포함됩니다. 신중히 진행해 주세요!',
        deleteCompose: '" 이 컴포즈를 삭제하시겠습니까?',
        createCompose: '생성',
        composeDirectory: '디렉토리',
        template: '템플릿',
        composeTemplate: '컴포즈 템플릿 | 컴포즈 템플릿들',
        createComposeTemplate: '생성',
        content: '내용',
        contentEmpty: '컴포즈 내용이 비어 있습니다. 입력 후 다시 시도해 주세요!',
        containerNumber: '컨테이너 수',
        containerStatus: '컨테이너 상태',
        exited: '종료됨',
        running: '실행 중 ( {0} / {1} )',
        composeDetailHelper: '이 컴포즈는 1Panel 외부에서 생성되었습니다. 시작 및 중지 작업은 지원되지 않습니다.',
        composeOperatorHelper: '{1} 작업이 {0}에서 수행됩니다. 계속 하시겠습니까?',
        composeDownHelper:
            '이 작업은 {0} 컴포즈 아래의 모든 컨테이너와 네트워크를 중지하고 제거합니다. 계속 하시겠습니까?',

        setting: '설정 | 설정들',
        operatorStatusHelper: '이 작업은 Docker 서비스를 "{0}" 합니다. 계속 하시겠습니까?',
        dockerStatus: 'Docker 서비스',
        daemonJsonPathHelper: '구성 경로가 docker.service 에 지정된 경로와 동일한지 확인하십시오.',
        mirrors: '레지스트리 미러들',
        mirrorsHelper: '',
        mirrorsHelper2: '자세한 내용은 공식 문서를 참조하십시오.',
        registries: '불안정한 레지스트리들',
        ipv6Helper:
            'IPv6를 활성화하려면 IPv6 컨테이너 네트워크를 추가해야 합니다. 구체적인 구성 단계를 공식 문서에서 참조하십시오.',
        ipv6CidrHelper: '컨테이너를 위한 IPv6 주소 풀 범위',
        ipv6TablesHelper: 'ip6tables 규칙에 대해 Docker IPv6 을 자동 구성합니다.',
        experimentalHelper: 'ip6tables 를 활성화하려면 이 구성을 켜야 합니다. 그렇지 않으면 ip6tables가 무시됩니다.',
        cutLog: '로그 옵션',
        cutLogHelper1: '현재 구성은 새로 생성된 컨테이너에만 영향을 미칩니다.',
        cutLogHelper2: '기존 컨테이너는 재생성해야 구성이 적용됩니다.',
        cutLogHelper3:
            '컨테이너를 재생성하면 데이터 손실이 발생할 수 있습니다. 중요한 데이터가 포함된 컨테이너는 재구성 전 백업을 꼭 해주세요.',
        maxSize: '최대 크기',
        maxFile: '최대 파일',
        liveHelper:
            '기본적으로 Docker 데몬이 종료되면 실행 중인 컨테이너도 종료됩니다. 데몬이 비활성화된 상태에서 컨테이너를 계속 실행하려면 데몬을 설정할 수 있습니다. 이 기능은 라이브 복구라고 불리며, 데몬 충돌, 예정된 중단 또는 업그레이드로 인한 컨테이너 다운타임을 줄이는 데 도움을 줍니다.',
        liveWithSwarmHelper: 'live-restore 데몬 구성은 스웜 모드와 호환되지 않습니다.',
        iptablesDisable: 'iptables 비활성화',
        iptablesHelper1: 'Docker 에 대한 iptables 규칙을 자동으로 구성합니다.',
        iptablesHelper2: 'iptables 를 비활성화하면 컨테이너가 외부 네트워크와 통신할 수 없습니다.',
        daemonJsonPath: '구성 경로',
        serviceUnavailable: `현재 Docker 서비스가 시작되지 않았습니다.`,
        startIn: '시작하려면',
        sockPath: '유닉스 도메인 소켓',
        sockPathHelper: 'Docker 데몬과 클라이언트 간의 통신 채널입니다.',
        sockPathHelper1: '기본 경로: /var/run/docker-x.sock',
        sockPathMsg: '소켓 경로 설정을 저장하면 Docker 서비스가 사용 불가능할 수 있습니다. 계속 하시겠습니까?',
        sockPathErr: '올바른 Docker 소켓 파일 경로를 선택하거나 입력해 주세요.',
        related: '관련',
        includeAppstore: '앱 스토어에서 컨테이너 표시',
        excludeAppstore: '앱스토어 컨테이너 숨기기',

        cleanDockerDiskZone: 'Docker 에서 사용하는 디스크 공간 정리',
        cleanImagesHelper: '(사용되지 않는 모든 이미지를 정리합니다.)',
        cleanContainersHelper: '(정지된 모든 컨테이너를 정리합니다.)',
        cleanVolumesHelper: '(사용되지 않는 모든 로컬 볼륨을 정리합니다.)',

        makeImage: '이미지 생성',
        newImageName: '새 이미지 이름',
        commitMessage: '커밋 메시지',
        author: '작성자',
        ifPause: '생성 중 컨테이너 일시 정지',
        ifMakeImageWithContainer: '이 컨테이너에서 새 이미지를 생성하시겠습니까?',
    },
    cronjob: {
        create: '크론 작업 생성',
        edit: '크론 작업 수정',
        errImport: '파일 내용 이상:',
        errImportFormat: '가져온 예약 작업 데이터 또는 형식이 이상합니다. 확인 후 다시 시도하십시오!',
        importHelper:
            '가져오기 시 동일한 이름의 예약 작업은 자동으로 건너뜁니다. 작업은 기본적으로 【비활성화】 상태로 설정되며, 데이터 연동 이상 시 【편집 대기】 상태로 설정됩니다.',
        changeStatus: '상태 변경',
        disableMsg: '이 작업은 예약된 작업이 자동으로 실행되지 않도록 멈춥니다. 계속하시겠습니까?',
        enableMsg: '이 작업은 예약된 작업이 자동으로 실행되도록 허용합니다. 계속하시겠습니까?',
        taskType: '작업 유형',
        record: '레코드',
        viewRecords: '레코드 보기',
        shell: '셸',
        log: '백업 로그',
        logHelper: '시스템 백업 로그',
        ogHelper1: '1. 1Panel 시스템 로그',
        logHelper2: '2. 서버 SSH 로그인 로그',
        logHelper3: '3. 모든 사이트 로그',
        containerCheckBox: '컨테이너 내 (컨테이너 명령어 입력 불필요)',
        containerName: '컨테이너 이름',
        ntp: '시간 동기화',
        ntp_helper: 'Toolbox 의 빠른 설정 페이지에서 NTP 서버를 구성할 수 있습니다.',
        app: '백업 앱',
        website: '백업 웹사이트',
        rulesHelper: '여러 개의 제외 규칙 지원, 영어 쉼표 , 로 구분. 예: *.log,*.sql',
        lastRecordTime: '마지막 실행 시간',
        all: '전체',
        failedRecord: '실패한 레코드',
        successRecord: '성공한 레코드',
        database: '백업 데이터베이스',
        missBackupAccount: '백업 계정을 찾을 수 없습니다',
        syncDate: '동기화 시간',
        clean: '캐시 정리',
        curl: '접속 URL',
        taskName: '작업 이름',
        cronSpec: '트리거 주기',
        cronSpecDoc:
            '사용자 정의 실행 주기는 [분 시 일 월 요일] 형식만 지원합니다 (예: 0 0 * * *). 자세한 내용은 공식 문서를 참조하세요.',
        cronSpecHelper: '올바른 실행 주기를 입력해 주세요',
        cleanHelper: '이 작업은 모든 작업 실행 레코드, 백업 파일, 로그 파일을 기록합니다. 계속하시겠습니까?',
        directory: '백업 디렉토리',
        sourceDir: '백업 디렉토리',
        snapshot: '시스템 스냅샷',
        allOptionHelper: `현재 작업 계획은 모든 [{0}]을 백업하는 것입니다. 현재 직접 다운로드는 지원되지 않습니다. [{0}] 메뉴에서 백업 목록을 확인하실 수 있습니다.`,
        exclusionRules: '배제 규칙',
        exclusionRulesHelper: '배제 규칙은 이 백업의 모든 압축 작업에 적용됩니다.',
        default_download_path: '기본 다운로드 링크',
        saveLocal: '로컬 백업 보관 (클라우드 저장소 복사본 수와 동일)',
        url: 'URL 주소',
        targetHelper: '백업 계정은 패널 설정에서 관리됩니다.',
        withImageHelper: '앱 스토어 이미지를 백업하지만 스냅샷 파일 크기가 증가합니다.',
        ignoreApp: '앱 제외',
        withImage: '모든 앱 이미지 백업',
        retainCopies: '기록 보관',
        retryTimes: '재시도 횟수',
        timeout: '타임아웃',
        ignoreErr: '오류 무시',
        ignoreErrHelper: '백업 과정에서 발생하는 오류를 무시하여 모든 백업 작업이 실행되도록 합니다',
        retryTimesHelper: '0은 실패 후 재시도 안 함을 의미합니다',
        retainCopiesHelper: '실행 기록과 로그에 대해 보관할 복사본 수',
        retainCopiesHelper1: '백업 파일에 대해 보관할 복사본 수',
        retainCopiesUnit: '개 (보기)',
        cronSpecRule: '라인 {0}의 실행 주기 형식이 잘못되었습니다. 확인 후 다시 시도해 주세요!',
        perMonthHelper: '매월 {0}일 {1}:{2}에 실행',
        perWeekHelper: '매주 {0}일 {1}:{2}에 실행',
        perDayHelper: '매일 {0}:{1}에 실행',
        perHourHelper: '매시간 {0}분에 실행',
        perNDayHelper: '매 {0}일마다 {1}:{2}에 실행',
        perNHourHelper: '매 {0}시간마다 {1}에 실행',
        perNMinuteHelper: '매 {0}분마다 실행',
        perNSecondHelper: '매 {0}초마다 실행',
        perMonth: '매월',
        perWeek: '매주',
        perHour: '매시간',
        perNDay: '매 N일',
        perDay: '매일',
        perNHour: '매 N시간',
        perNMinute: '매 N분',
        perNSecond: '매 N초',
        day: '일',
        dayUnit: 'd',
        monday: '월요일',
        tuesday: '화요일',
        wednesday: '수요일',
        thursday: '목요일',
        friday: '금요일',
        saturday: '토요일',
        sunday: '일요일',
        shellContent: '스크립트',
        errRecord: '잘못된 로깅',
        errHandle: '크론 작업 실행 실패',
        noRecord: '크론 작업을 트리거하고 나면 여기에 레코드가 표시됩니다.',
        cleanData: '데이터 정리',
        cleanRemoteData: '원격 데이터 삭제',
        cleanDataHelper: '이 작업에서 생성된 백업 파일을 삭제합니다.',
        noLogs: '작업 출력이 아직 없습니다...',
        errPath: '백업 경로 [{0}] 오류, 다운로드할 수 없습니다!',
        cutWebsiteLog: '웹사이트 로그 회전',
        cutWebsiteLogHelper: '회전된 로그 파일은 1Panel 의 백업 디렉토리로 백업됩니다.',
        requestExpirationTime: '업로드 요청 만료 시간(시간)',
        unitHours: '단위: 시간',
        alertTitle: '예정된 작업 - {0} 「{1}」 작업 실패 경고',
        library: {
            script: '스크립트',
            isInteractive: '대화형',
            interactive: '대화형 스크립트',
            interactiveHelper: '실행 중 사용자 입력이 필요하며 예약 작업에서는 사용할 수 없습니다.',
            library: '스크립트 라이브러리',
            create: '스크립트 추가',
            edit: '스크립트 수정',
            groupHelper:
                '스크립트 특성에 따라 다양한 그룹을 설정하여 스크립트 필터링 작업을 더 빠르게 수행할 수 있습니다.',
            handleHelper: '{0} 에서 {1} 스크립트를 실행합니다. 계속하시겠습니까?',
            noSuchApp: '{0} 서비스가 감지되지 않았습니다. 스크립트 라이브러리를 사용하여 먼저 빠르게 설치하세요!',
            syncHelper:
                '시스템 스크립트 라이브러리를 동기화합니다. 이 작업은 시스템 스크립트에만 적용됩니다. 계속하시겠습니까?',
        },
    },
    monitor: {
        globalFilter: '전역 필터',
        enableMonitor: '활성화',
        storeDays: '만료일',
        cleanMonitor: '모니터링 기록 정리',

        avgLoad: '평균 부하',
        loadDetail: '부하 세부사항',
        resourceUsage: '자원 사용률',
        networkCard: '네트워크 인터페이스',
        read: '읽기',
        write: '쓰기',
        readWriteCount: 'I/O 작업',
        readWriteTime: 'I/O 지연 시간',
        today: '오늘',
        yesterday: '어제',
        lastNDay: '최근 {0}일',
        memory: '메모리',
        cache: '캐시',
        disk: '디스크',
        network: '네트워크',
        up: '업',
        down: '다운',
        interval: '간격(분)',

        gpuUtil: 'GPU 사용률',
        temperature: '온도',
        performanceState: '성능 상태',
        powerUsage: '전력 사용량',
        memoryUsage: '메모리 사용량',
        fanSpeed: '팬 속도',
    },
    terminal: {
        local: '로컬',
        localHelper: '로컬 이름은 시스템 로컬 식별에만 사용됩니다.',
        connLocalErr: '자동 인증에 실패했습니다. 로컬 서버 로그인 정보를 입력해주세요.',
        testConn: '연결 테스트',
        saveAndConn: '저장 후 연결',
        connTestOk: '연결 정보가 유효합니다.',
        connTestFailed: '연결할 수 없습니다. 연결 정보를 확인해주세요.',
        host: '호스트 | 호스트들',
        createConn: '새 연결',
        manageGroup: '그룹 관리',
        noHost: '호스트 없음',
        groupChange: '그룹 변경',
        expand: '모두 확장',
        fold: '모두 축소',
        batchInput: '배치 처리',
        quickCommand: '빠른 명령 | 빠른 명령들',
        quickCommandHelper: '"터미널 -> 터미널" 하단에서 빠른 명령을 사용할 수 있습니다.',
        groupDeleteHelper: '그룹을 제거하면 해당 그룹의 모든 연결이 기본 그룹으로 이동됩니다. 계속하시겠습니까?',
        command: '명령',
        quickCmd: '빠른 명령',
        addHost: '추가',
        localhost: '로컬호스트',
        ip: '주소',
        authMode: '인증 방식',
        passwordMode: '비밀번호',
        rememberPassword: '인증 정보 기억하기',
        keyMode: '개인 키',
        key: '개인 키',
        keyPassword: '개인 키 비밀번호',
        emptyTerminal: '현재 연결된 터미널이 없습니다.',
    },
    toolbox: {
        common: {
            toolboxHelper: '일부 설치 및 사용 문제는 다음을 참고하세요',
        },
        swap: {
            swap: '스왑 파티션',
            swapHelper1: '스왑 크기는 물리적 메모리의 1~2배로 설정해야 하며, 특정 요구 사항에 따라 조정 가능합니다.',
            swapHelper2:
                '스왑 파일을 생성하기 전에 시스템 디스크에 충분한 가용 공간이 있는지 확인하세요. 스왑 파일 크기만큼 디스크 공간이 점유됩니다.',
            swapHelper3:
                '스왑은 메모리 압력을 완화하는 데 도움이 될 수 있지만, 단지 대안일 뿐입니다. 스왑에 과도하게 의존하면 시스템 성능이 저하될 수 있으므로 메모리를 추가하거나 애플리케이션 메모리 사용을 최적화하는 것을 우선적으로 고려해야 합니다.',
            swapHelper4: '스왑 사용량을 정기적으로 모니터링하여 시스템이 정상적으로 작동하는지 확인하는 것이 좋습니다.',
            swapDeleteHelper:
                '이 작업은 스왑 파티션 {0}을 제거합니다. 시스템 보안상의 이유로 해당 파일은 자동으로 삭제되지 않습니다. 삭제가 필요한 경우 수동으로 진행하세요!',
            saveHelper: '현재 설정을 먼저 저장해주세요!',
            saveSwap: '현재 구성을 저장하면 스왑 파티션 {0}의 크기가 {1}(으)로 조정됩니다. 계속하시겠습니까?',
            swapMin: '최소 파티션 크기는 40 KB입니다. 수정 후 다시 시도해주세요!',
            swapMax: '파티션 크기의 최대값은 {0}입니다. 수정 후 다시 시도해주세요!',
            swapOff: '최소 파티션 크기는 40 KB입니다. 0으로 설정하면 스왑 파티션이 비활성화됩니다.',
        },
        device: {
            dnsHelper: 'DNS 서버',
            dnsAlert: '/etc/resolv.conf 파일의 구성을 수정하면 시스템 재부팅 후 파일이 기본값으로 복원됩니다.',
            dnsHelper1: 'DNS 항목이 여러 개인 경우 각 항목을 새 줄에 표시해야 합니다. 예:\n114.114.114.114\n8.8.8.8',
            hostsHelper: '호스트 이름 해석',
            hosts: '도메인',
            hostAlert: '주석 처리된 기록은 숨겨져 있습니다. 모든 구성 버튼을 클릭하여 보거나 설정하세요.',
            toolbox: '빠른 설정',
            hostname: '호스트 이름',
            passwd: '시스템 비밀번호',
            passwdHelper: '입력 문자는 $ 및 &를 포함할 수 없습니다.',
            timeZone: '시간대',
            localTime: '서버 시간',
            timeZoneChangeHelper: '시스템 시간대를 변경하려면 서비스를 재시작해야 합니다. 계속하시겠습니까?',
            timeZoneHelper: `"timedatectl" 명령이 설치되지 않은 경우 시간대를 변경할 수 없습니다. 시스템은 시간대 변경에 이 명령을 사용합니다.`,
            timeZoneCN: '베이징',
            timeZoneAM: '로스앤젤레스',
            timeZoneNY: '뉴욕',
            ntpALi: '알리바바',
            ntpGoogle: '구글',
            syncSite: 'NTP 서버',
            hostnameHelper: `호스트 이름 수정은 "hostnamectl" 명령에 따라 달라집니다. 명령이 설치되지 않은 경우 수정이 실패할 수 있습니다.`,
            userHelper: `사용자 이름은 "whoami" 명령을 사용하여 검색됩니다. 명령이 설치되지 않은 경우 검색이 실패할 수 있습니다.`,
            passwordHelper: `비밀번호 수정은 "chpasswd" 명령에 따라 달라집니다. 명령이 설치되지 않은 경우 수정이 실패할 수 있습니다.`,
            hostHelper: '제공된 내용에 빈 값이 포함되어 있습니다. 확인 후 수정하여 다시 시도해주세요!',
            dnsCheck: '가용성 테스트',
            dnsOK: 'DNS 구성 정보가 유효합니다!',
            dnsTestFailed: 'DNS 구성 정보가 유효하지 않습니다.',
        },
        fail2ban: {
            sshPort: 'SSH 포트 청취',
            sshPortHelper: '현재 Fail2ban 은 호스트의 SSH 연결 포트를 청취합니다.',
            unActive: '현재 Fail2ban 서비스가 활성화되어 있지 않습니다.',
            operation: 'Fail2ban 서비스에서 "{0}" 작업을 수행합니다. 계속하시겠습니까?',
            fail2banChange: 'Fail2ban 구성 수정',
            ignoreHelper: '허용 목록에 있는 IP는 차단되지 않습니다. 계속하시겠습니까?',
            bannedHelper: '차단 목록에 있는 IP는 서버에 의해 차단됩니다. 계속하시겠습니까?',
            maxRetry: '최대 재시도 횟수',
            banTime: '차단 시간',
            banTimeHelper: '기본 차단 시간은 10분이며, -1은 영구 차단을 나타냅니다.',
            banTimeRule: '유효한 차단 시간 또는 -1을 입력하세요.',
            banAllTime: '영구 차단',
            findTime: '탐지 기간',
            banAction: '차단 작업',
            banActionOption: '{0}을(를) 사용하여 지정된 IP 주소 차단',
            allPorts: ' (모든 포트)',
            ignoreIP: 'IP 허용 목록',
            bannedIP: 'IP 차단 목록',
            logPath: '로그 경로',
            logPathHelper: '기본값은 /var/log/secure 또는 /var/log/auth.log입니다.',
        },
        ftp: {
            ftp: 'FTP 계정 | FTP 계정들',
            notStart: 'FTP 서비스가 현재 실행 중이 아닙니다. 먼저 시작하세요!',
            operation: 'FTP 서비스에서 "{0}" 작업을 수행합니다. 계속하시겠습니까?',
            noPasswdMsg: '현재 FTP 계정의 비밀번호를 가져올 수 없습니다. 비밀번호를 설정한 후 다시 시도하세요!',
            enableHelper: '선택한 FTP 계정을 활성화하면 접근 권한이 복원됩니다. 계속하시겠습니까?',
            disableHelper: '선택한 FTP 계정을 비활성화하면 접근 권한이 취소됩니다. 계속하시겠습니까?',
            syncHelper: '서버와 데이터베이스 간의 FTP 계정 데이터를 동기화합니다. 계속하시겠습니까?',
            dirSystem:
                '이 디렉터리는 시스템 예약 디렉터리입니다. 수정 시 시스템 충돌이 발생할 수 있으니 수정 후 다시 시도하세요!',
            dirHelper: 'FTP 활성화를 위해 디렉터리 권한 변경이 필요합니다. 신중하게 선택하세요',
            dirMsg: 'FTP 활성화 시 {0} 디렉터리 전체의 권한이 변경됩니다. 계속하시겠습니까?',
        },
        clam: {
            clam: '바이러스 검사',
            cron: '예약 스캔',
            cronHelper: '전문 버전에서 예약 스캔 기능을 지원합니다.',
            specErr: '실행 일정 형식 오류입니다. 확인 후 다시 시도하세요!',
            disableMsg: '예약 실행을 중지하면 이 스캔 작업이 자동으로 실행되지 않습니다. 계속하시겠습니까?',
            enableMsg: '예약 실행을 활성화하면 이 스캔 작업이 정기적으로 자동 실행됩니다. 계속하시겠습니까?',
            showFresh: '서명 업데이트 서비스 표시',
            hideFresh: '서명 업데이트 서비스 숨기기',
            clamHelper:
                'ClamAV의 최소 권장 구성은 다음과 같습니다: RAM 3 GiB 이상, 2.0 GHz 이상의 단일 코어 CPU, 최소 5 GiB의 사용 가능한 하드 디스크 공간.',
            notStart: 'ClamAV 서비스가 현재 실행 중이 아닙니다. 먼저 시작하세요!',
            removeRecord: '보고서 파일 삭제',
            noRecords: '"Trigger" 버튼을 클릭하여 스캔을 시작하면 이곳에서 기록을 확인할 수 있습니다.',
            removeResultHelper: '작업 실행 중 생성된 보고서 파일을 삭제하여 저장 공간을 확보합니다.',
            removeInfected: '바이러스 파일 삭제',
            removeInfectedHelper: '작업 중 감지된 바이러스 파일을 삭제하여 서버 보안 및 정상 작동을 보장합니다.',
            clamCreate: '스캔 규칙 생성',
            infectedStrategy: '감염 파일 처리 전략',
            removeHelper: '바이러스 파일을 삭제합니다. 신중히 선택하세요!',
            move: '이동',
            moveHelper: '바이러스 파일을 지정된 디렉토리로 이동합니다.',
            copyHelper: '바이러스 파일을 지정된 디렉토리로 복사합니다.',
            none: '조치 안 함',
            noneHelper: '바이러스 파일에 대해 아무 조치도 취하지 않습니다.',
            scanDir: '스캔 디렉토리',
            infectedDir: '감염 파일 디렉토리',
            scanDate: '스캔 날짜',
            scanResult: '스캔 로그 출력',
            tail: '라인',
            infectedFiles: '감염된 파일',
            log: '상세 내용',
            clamConf: 'Clam AV 데몬',
            clamLog: '@:toolbox.clam.clamConf 로그',
            freshClam: 'FreshClam',
            freshClamLog: '@:toolbox.clam.freshClam 로그',
            alertHelper: '전문 버전에서 예약 스캔 및 SMS 알림을 지원합니다.',
            alertTitle: '바이러스 스캔 작업 「{0}」에서 감염된 파일을 발견했습니다.',
        },
    },
    logs: {
        core: '패널 서비스',
        agent: '노드 모니터링',
        panelLog: '패널 로그',
        operation: '작업 로그',
        login: '로그인 로그',
        loginIP: '로그인 IP',
        loginAddress: '로그인 주소',
        loginAgent: '로그인 에이전트',
        loginStatus: '상태',
        system: '시스템 로그',
        deleteLogs: '로그 정리',
        resource: '자원',
        detail: {
            ai: 'AI',
            groups: '그룹',
            hosts: '호스트',
            apps: '애플리케이션',
            websites: '웹사이트',
            containers: '컨테이너',
            files: '파일 관리',
            runtimes: '실행 환경',
            process: '프로세스 관리',
            toolbox: '도구 상자',
            backups: '백업 / 복원',
            tampers: '변조 방지',
            xsetting: '인터페이스 설정',
            logs: '로그 감사',
            settings: '패널 설정',
            cronjobs: '예약 작업',
            waf: 'WAF',
            databases: '데이터베이스',
            licenses: '라이선스',
            nodes: '노드',
            commands: '빠른 명령',
        },
        websiteLog: '웹사이트 로그',
        runLog: '실행 로그',
        errLog: '에러 로그',
    },
    file: {
        fileDirNum: '총 {0}개 디렉터리, {1}개 파일,',
        currentDir: '현재 디렉터리',
        dir: '폴더',
        upload: '업로드',
        uploadFile: '@:file.upload @.lower:file.file',
        uploadDirectory: '@:file.upload @.lower:file.dir',
        download: '다운로드',
        fileName: '파일 이름',
        search: '검색',
        mode: '권한',
        editPermissions: '@:file.mode',
        owner: '소유자',
        file: '파일',
        remoteFile: '원격에서 다운로드',
        share: '공유',
        sync: '데이터 동기화',
        size: '크기',
        updateTime: '수정됨',
        rename: '이름 바꾸기',
        role: '권한',
        info: '속성',
        linkFile: '소프트 링크',
        batchoperation: '일괄 작업',
        shareList: '공유 목록',
        zip: '압축됨',
        group: '그룹',
        path: '경로',
        public: '기타',
        setRole: '권한 설정',
        link: '파일 링크',
        rRole: '읽기',
        wRole: '쓰기',
        xRole: '실행 가능',
        name: '이름',
        compress: '압축',
        deCompress: '압축 해제',
        compressType: '압축 형식',
        compressDst: '압축 경로',
        replace: '기존 파일 덮어쓰기',
        compressSuccess: '압축 성공',
        deCompressSuccess: '압축 해제 성공',
        deCompressDst: '압축 해제 경로',
        linkType: '링크 유형',
        softLink: '소프트 링크',
        hardLink: '하드 링크',
        linkPath: '링크 경로',
        selectFile: '파일 선택',
        downloadUrl: '원격 URL',
        downloadStart: '다운로드 시작됨',
        moveSuccess: '이동 성공',
        copySuccess: '복사 성공',
        move: '이동',
        calculate: '계산',
        canNotDeCompress: '이 파일은 압축 해제할 수 없습니다',
        uploadSuccess: '업로드 성공',
        downloadProcess: '다운로드 진행률',
        downloading: '다운로드 중...',
        infoDetail: '파일 속성',
        root: '루트 디렉터리',
        list: '파일 목록',
        sub: '하위 폴더',
        downloadSuccess: '다운로드 성공',
        theme: '테마',
        language: '언어',
        eol: '줄 끝',
        copyDir: '복사',
        paste: '붙여넣기',
        changeOwner: '사용자 및 그룹 수정',
        containSub: '권한 변경을 하위 폴더에 적용',
        ownerHelper:
            'PHP 운영 환경의 기본 사용자: 사용자 그룹은 1000:1000 입니다. 컨테이너 내부 및 외부에서 표시되는 불일치는 정상입니다.',
        searchHelper: '* 등의 와일드카드를 지원합니다',
        uploadFailed: '[{0}] 파일 업로드 실패',
        fileUploadStart: '[{0}] 업로드 중....',
        currentSelect: '현재 선택: ',
        unsupportedType: '지원되지 않는 파일 유형',
        deleteHelper: '다음 파일을 삭제하시겠습니까? 기본적으로 삭제 후 휴지통으로 이동합니다.',
        fileHelper: `참고:\n1. 검색 결과는 정렬할 수 없습니다.\n2. 폴더는 크기로 정렬할 수 없습니다.`,
        forceDeleteHelper: '파일을 영구적으로 삭제합니다(휴지통으로 이동하지 않고 바로 삭제).',
        recycleBin: '휴지통',
        sourcePath: '원래 경로',
        deleteTime: '삭제 시간',
        confirmReduce: '다음 파일을 복원하시겠습니까?',
        reduceSuccess: '복원 성공',
        reduce: '복원',
        reduceHelper: '원래 경로에 동일한 이름의 파일이나 디렉터리가 있으면 덮어씁니다. 계속하시겠습니까?',
        clearRecycleBin: '정리',
        clearRecycleBinHelper: '휴지통을 정리하시겠습니까?',
        favorite: '즐겨찾기',
        removeFavorite: '즐겨찾기에서 제거하시겠습니까?',
        addFavorite: '즐겨찾기 추가 / 제거',
        clearList: '목록 정리',
        deleteRecycleHelper: '다음 파일을 영구적으로 삭제하시겠습니까?',
        typeErrOrEmpty: '[{0}] 파일 유형이 잘못되었거나 빈 폴더입니다.',
        dropHelper: '업로드하려는 파일을 여기에 드래그하세요',
        fileRecycleBin: '휴지통 활성화',
        fileRecycleBinMsg: '{0} 휴지통',
        wordWrap: '자동 줄바꿈',
        deleteHelper2: '선택한 파일을 삭제하시겠습니까? 삭제 작업은 되돌릴 수 없습니다.',
        ignoreCertificate: '안전하지 않은 서버 연결 허용',
        ignoreCertificateHelper:
            '안전하지 않은 서버 연결을 허용하면 데이터 유출 또는 변조가 발생할 수 있습니다. 이 옵션은 다운로드 소스를 신뢰할 때만 사용하세요.',
        uploadOverLimit: '파일 수가 1000 개를 초과했습니다! 압축하여 업로드하세요.',
        clashDitNotSupport: '파일 이름에 .1panel_clash 를 포함할 수 없습니다.',
        clashDeleteAlert: `"휴지통" 폴더는 삭제할 수 없습니다.`,
        clashOpenAlert: '휴지통 디렉터리를 열려면 "휴지통" 버튼을 클릭하세요.',
        right: '앞으로',
        back: '뒤로',
        top: '처음으로 돌아가기',
        up: '뒤로가기',
        openWithVscode: 'VS Code 로 열기',
        vscodeHelper: '로컬에 VS Code 가 설치되어 있고 SSH Remote 플러그인이 구성되어 있는지 확인하세요.',
        saveContentAndClose: '파일이 수정되었습니다. 저장 후 닫으시겠습니까?',
        saveAndOpenNewFile: '파일이 수정되었습니다. 저장 후 새 파일을 열겠습니까?',
        noEdit: '파일이 수정되지 않았습니다. 이 작업은 필요하지 않습니다!',
        noNameFolder: '제목 없는 폴더',
        noNameFile: '제목 없는 파일',
        minimap: '코드 미니맵',
        fileCanNotRead: '파일을 읽을 수 없습니다.',
        panelInstallDir: `1Panel 설치 디렉터리는 삭제할 수 없습니다.`,
        wgetTask: '다운로드 작업',
        existFileTitle: '동일한 이름의 파일 경고',
        existFileHelper: '업로드한 파일에 동일한 이름의 파일이 포함되어 있습니다. 덮어쓰시겠습니까?',
        existFileSize: '파일 크기 (새로운 -> 오래된)',
        existFileDirHelper: '선택한 파일/폴더에 동일한 이름이 이미 존재합니다. 신중하게 작업하세요!',
        coverDirHelper: '덮어쓸 폴더를 선택하면 대상 경로로 복사됩니다!',
        noSuchFile: '파일 또는 디렉터리를 찾을 수 없습니다. 확인 후 다시 시도하세요.',
        setting: '설정',
        showHide: '숨김 파일 표시',
        noShowHide: '숨김 파일 숨기기',
        cancelUpload: '업로드 취소',
        cancelUploadHelper: '업로드를 취소할지 여부, 취소 후 업로드 목록이 비워집니다.',
    },
    ssh: {
        autoStart: '자동 시작',
        enable: '자동 시작 활성화',
        disable: '자동 시작 비활성화',
        sshAlert:
            '목록 데이터는 로그인 날짜를 기준으로 정렬됩니다. 시간대 변경이나 다른 작업이 수행되면 로그인 로그의 날짜에 차이가 발생할 수 있습니다.',
        sshAlert2:
            '"Fail2ban"을 "도구 상자"에서 사용하여 무차별 대입 공격을 시도하는 IP 주소를 차단할 수 있으며, 이는 호스트의 보안을 강화하는 데 도움이 됩니다.',
        sshOperate: 'SSH 서비스에서 "{0}" 작업을 수행합니다. 계속하시겠습니까?',
        sshChange: 'SSH 설정',
        sshChangeHelper: '"{0}"을(를) "{1}"로 변경하였습니다. 계속하시겠습니까?',
        sshFileChangeHelper:
            '구성 파일을 수정하면 서비스 가용성에 영향을 미칠 수 있습니다. 이 작업을 수행할 때는 주의하십시오. 계속하시겠습니까?',
        port: '포트',
        portHelper: 'SSH 서비스가 수신하는 포트를 지정하십시오.',
        listenAddress: '수신 주소',
        allV4V6: '0.0.0.0:{0}(IPv4) 및 :::{0}(IPv6)',
        listenHelper: 'IPv4와 IPv6 설정을 모두 비워두면 "0.0.0.0:{0}(IPv4)"와 ":::{0}(IPv6)"에서 수신합니다.',
        addressHelper: 'SSH 서비스가 수신할 주소를 지정하십시오.',
        permitRootLogin: '루트 사용자 로그인 허용',
        rootSettingHelper: '루트 사용자의 기본 로그인 방법은 "SSH 로그인 허용"입니다.',
        rootHelper1: 'SSH 로그인 허용',
        rootHelper2: 'SSH 로그인 비허용',
        rootHelper3: '키 로그인만 허용',
        rootHelper4: '미리 정의된 명령만 실행할 수 있습니다. 다른 작업은 수행할 수 없습니다.',
        passwordAuthentication: '비밀번호 인증',
        pwdAuthHelper: '비밀번호 인증을 활성화할지 여부입니다. 기본적으로 이 매개변수는 활성화되어 있습니다.',
        pubkeyAuthentication: '키 인증',
        privateKey: '개인 키',
        publicKey: '공개 키',
        password: '비밀번호',
        createMode: '생성 방식',
        generate: '자동 생성',
        unSyncPass: '키 비밀번호 동기화 불가',
        syncHelper: '동기화 작업으로 유효하지 않은 키를 정리하고 새로운 완전한 키 쌍을 동기화합니다. 계속하시겠습니까?',
        input: '수동 입력',
        import: '파일 업로드',
        pubkey: '키 정보',
        encryptionMode: '암호화 모드',
        pubKeyHelper: '현재 키 정보는 사용자 {0}에게만 적용됩니다',
        passwordHelper: '6~10자리 숫자 및 영어 대소문자를 포함할 수 있습니다.',
        reGenerate: '키 재생성',
        keyAuthHelper: '키 인증을 활성화할지 여부입니다.',
        useDNS: 'useDNS',
        dnsHelper: 'SSH 서버에서 DNS 확인 기능을 활성화하여 연결의 신원을 확인할지 여부를 제어합니다.',
        analysis: '통계 정보',
        denyHelper:
            "'거부' 작업을 다음 주소에서 수행합니다. 설정 후 해당 IP는 서버에 접근할 수 없습니다. 계속하시겠습니까?",
        acceptHelper:
            "'수락' 작업을 다음 주소에서 수행합니다. 설정 후 해당 IP는 정상적으로 접근할 수 있습니다. 계속하시겠습니까?",
        noAddrWarning: '현재 [{0}] 주소가 선택되지 않았습니다. 확인 후 다시 시도하십시오!',
        loginLogs: '로그인 로그',
        loginMode: '모드',
        authenticating: '키',
        publickey: '키',
        belong: '소속',
        local: '로컬',
        session: '세션 | 세션들',
        loginTime: '로그인 시간',
        loginIP: '로그인 IP',
        stopSSHWarn: '이 SSH 연결을 끊으시겠습니까?',
    },
    setting: {
        panel: '패널',
        user: '패널 사용자',
        userChange: '패널 사용자 변경',
        userChangeHelper: '패널 사용자를 변경하면 로그아웃됩니다. 계속하시겠습니까?',
        passwd: '패널 비밀번호',
        emailHelper: '비밀번호 복구용',
        title: '패널 별칭',
        panelPort: '패널 포트',
        titleHelper: '영문자, 한자, 숫자, 공백, 일반 특수 문자를 포함하여 3~30자의 길이를 지원합니다.',
        portHelper:
            '권장 포트 범위는 8888에서 65535 사이입니다. 참고: 서버에 보안 그룹이 있는 경우, 보안 그룹에서 새로운 포트를 사전에 허용해야 합니다.',
        portChange: '포트 변경',
        portChangeHelper: '서비스 포트를 수정하고 서비스를 다시 시작합니다. 계속하시겠습니까?',
        theme: '테마',
        menuTabs: '메뉴 탭',
        dark: '다크',
        darkGold: '다크 골드',
        light: '라이트',
        auto: '시스템 따라가기',
        language: '언어',
        languageHelper: '기본적으로 브라우저 언어를 따릅니다. 이 설정은 현재 브라우저에서만 적용됩니다.',
        sessionTimeout: '세션 타임아웃',
        sessionTimeoutError: '최소 세션 타임아웃은 300초입니다.',
        sessionTimeoutHelper: '패널에서 {0}초 이상 조작이 없을 경우 자동으로 로그아웃됩니다.',
        systemIP: '기본 접근 주소',
        systemIPHelper:
            '애플리케이션 리다이렉트, 컨테이너 접근 등의 기능은 이 주소를 사용하여 라우팅됩니다. 각 노드마다 다른 주소를 설정할 수 있습니다.',
        proxy: '서버 프록시',
        proxyHelper: '프록시 서버를 설정한 후 다음 시나리오에서 적용됩니다:',
        proxyHelper1: '설치 패키지 다운로드 및 앱 스토어 동기화 (전문 버전에서만 제공)',
        proxyHelper2: '시스템 업데이트 및 업데이트 정보 가져오기 (전문 버전에서만 제공)',
        proxyHelper4: 'Docker 네트워크가 프록시 서버를 통해 액세스됩니다 (전문 버전에서만 제공)',
        proxyHelper3: '시스템 라이선스 인증 및 동기화',
        proxyHelper5: '시스템 유형 스크립트 라이브러리의 통합 다운로드 및 동기화 (프로페셔널 에디션 기능)',
        proxyHelper6: '인증서 신청 (프로 버전 기능)',
        proxyType: '프록시 유형',
        proxyUrl: '프록시 주소',
        proxyPort: '프록시 포트',
        proxyPasswdKeep: '비밀번호 기억',
        proxyDocker: 'Docker 프록시',
        proxyDockerHelper:
            '프록시 서버 구성을 Docker 에 동기화하여 오프라인 서버 이미지 가져오기 등의 작업을 지원합니다.',
        syncToNode: '자식 노드로 동기화',
        syncToNodeHelper: '다른 노드로 설정 동기화',
        nodes: '노드',
        selectNode: '노드 선택',
        selectNodeError: '노드를 선택해 주세요',
        apiInterface: 'API 활성화',
        apiInterfaceClose: '비활성화하면 API 인터페이스에 접근할 수 없습니다. 계속하시겠습니까?',
        apiInterfaceHelper: '서드파티 애플리케이션이 API 에 접근할 수 있도록 허용합니다.',
        apiInterfaceAlert1: '운영 환경에서는 활성화하지 마십시오. 서버 보안 위험이 증가할 수 있습니다.',
        apiInterfaceAlert2: '잠재적인 보안 위협을 방지하기 위해 서드파티 애플리케이션으로 API를 호출하지 마십시오.',
        apiInterfaceAlert3: 'API 문서',
        apiInterfaceAlert4: '사용 설명서',
        apiKey: 'API 키',
        apiKeyHelper: 'API 키는 서드파티 애플리케이션이 API 에 접근하는 데 사용됩니다.',
        ipWhiteList: 'IP 허용 목록',
        ipWhiteListEgs: '한 줄에 하나씩 입력하십시오. 예:\n172.161.10.111\n172.161.10.0/24',
        ipWhiteListHelper: '허용 목록에 있는 IP만 API 에 접근할 수 있습니다. 0.0.0.0/0(모든 IPv4), ::/0(모든 IPv6)',
        apiKeyValidityTime: '인터페이스 키 유효 기간',
        apiKeyValidityTimeEgs: '인터페이스 키 유효 기간 (분 단위)',
        apiKeyValidityTimeHelper:
            '인터페이스 타임스탬프가 현재 타임스탬프와의 차이가 허용 범위 내에 있을 경우 유효합니다. 값이 0이면 검증이 비활성화됩니다.',
        apiKeyReset: '인터페이스 키 재설정',
        apiKeyResetHelper: '연관된 키 서비스가 무효화됩니다. 서비스에 새 키를 추가하십시오.',
        confDockerProxy: 'Docker 프록시 구성',
        restartNowHelper: 'Docker 프록시 구성을 위해 Docker 서비스를 재시작해야 합니다.',
        restartNow: '즉시 재시작',
        restartLater: '나중에 수동으로 재시작',
        systemIPWarning: '현재 노드에 기본 접근 주소가 설정되지 않았습니다. 패널 설정에서 설정해 주세요!',
        systemIPWarning1: '현재 서버 주소는 {0}으로 설정되어 있어 빠른 리디렉션이 불가능합니다!',
        defaultNetwork: '네트워크 카드',
        syncTime: '서버 시간',
        timeZone: '시간대',
        timeZoneChangeHelper: '시간대를 변경하면 서비스를 재시작해야 합니다. 계속하시겠습니까?',
        timeZoneHelper: '시간대 변경은 시스템 timedatectl 서비스에 따라 작동하며, 1Panel 서비스 재시작 후 적용됩니다.',
        timeZoneCN: '베이징',
        timeZoneAM: '로스앤젤레스',
        timeZoneNY: '뉴욕',
        ntpALi: '알리바바',
        ntpGoogle: '구글',
        syncSite: 'NTP 서버',
        syncSiteHelper: '이 작업은 {0}을(를) 시스템 시간 동기화의 소스로 사용합니다. 계속하시겠습니까?',
        changePassword: '비밀번호 변경',
        oldPassword: '기존 비밀번호',
        newPassword: '새 비밀번호',
        retryPassword: '비밀번호 확인',
        noSpace: '입력 정보에 공백 문자를 포함할 수 없습니다.',
        duplicatePassword: '새 비밀번호는 기존 비밀번호와 동일할 수 없습니다. 다시 입력하십시오!',
        diskClean: '캐시 정리',
        developerMode: '미리보기 프로그램',
        developerModeHelper: '새로운 기능과 수정 사항을 정식 출시 전에 체험하고 초기 피드백을 제공할 수 있습니다.',
        thirdParty: '서드파티 계정',
        noTypeForCreate: '현재 생성된 백업 유형이 없습니다.',
        LOCAL: '서버 디스크',
        OSS: 'Ali OSS',
        S3: 'Amazon S3',
        mode: '모드',
        MINIO: 'MinIO',
        SFTP: 'SFTP',
        WebDAV: 'WebDAV',
        WebDAVAlist: 'Alist 에 WebDAV 를 연결하는 방법은 공식 문서를 참조하십시오.',
        OneDrive: 'Microsoft OneDrive',
        isCN: 'Century Internet',
        isNotCN: '국제 버전',
        client_id: '클라이언트 ID',
        client_secret: '클라이언트 시크릿',
        redirect_uri: '리디렉션 URL',
        onedrive_helper: '사용자 정의 구성은 공식 문서를 참조하십시오.',
        refreshTime: '토큰 갱신 시간',
        refreshStatus: '토큰 갱신 상태',
        backupDir: '백업 디렉터리',
        codeWarning: '현재 인증 코드 형식이 올바르지 않습니다. 다시 확인하십시오!',
        code: '인증 코드',
        codeHelper:
            '"획득" 버튼을 클릭한 다음 OneDrive 에 로그인하여 리디렉션된 링크에서 "code" 이후의 내용을 복사하십시오. 이 입력 상자에 붙여넣으십시오. 자세한 지침은 공식 문서를 참조하십시오.',
        loadCode: '획득',
        COS: 'Tencent COS',
        ap_beijing_1: '베이징 지역 1',
        ap_beijing: '베이징',
        ap_nanjing: '난징',
        ap_shanghai: '상하이',
        ap_guangzhou: '광저우',
        ap_chengdu: '청두',
        ap_chongqing: '충칭',
        ap_shenzhen_fsi: '선전 금융',
        ap_shanghai_fsi: '상하이 금융',
        ap_beijing_fsi: '베이징 금융',
        ap_hongkong: '홍콩, 중국',
        ap_singapore: '싱가포르',
        ap_mumbai: '뭄바이',
        ap_jakarta: '자카르타',
        ap_seoul: '서울',
        ap_bangkok: '방콕',
        ap_tokyo: '도쿄',
        na_siliconvalley: '실리콘밸리 (미국 서부)',
        na_ashburn: '애쉬번 (미국 동부)',
        na_toronto: '토론토',
        sa_saopaulo: '상파울루',
        eu_frankfurt: '프랑크푸르트',
        KODO: 'Qiniu Kodo',
        scType: '스토리지 유형',
        typeStandard: '표준',
        typeStandard_IA: '저빈도 표준',
        typeArchive: '아카이브',
        typeDeep_Archive: '심층 아카이브',
        scLighthouse: '기본값: 경량 오브젝트 스토리지는 이 스토리지 유형만 지원합니다',
        scStandard:
            '표준 스토리지는 실시간 접근, 빈번한 데이터 상호작용이 필요한 핫 파일이 많은 비즈니스 시나리오에 적합합니다.',
        scStandard_IA:
            '저빈도 스토리지는 접근 빈도가 비교적 낮고 최소 30일 동안 데이터를 저장하는 비즈니스 시나리오에 적합합니다.',
        scArchive: '아카이브 스토리지는 접근 빈도가 극히 낮은 비즈니스 시나리오에 적합합니다.',
        scDeep_Archive: '내구성이 뛰어난 콜드 스토리지는 접근 빈도가 극히 낮은 비즈니스 시나리오에 적합합니다.',
        archiveHelper:
            '아카이브 스토리지 파일은 직접 다운로드할 수 없으며, 해당 클라우드 서비스 제공자의 웹사이트를 통해 복원해야 합니다. 신중히 사용하십시오!',
        backupAlert: 'S3 프로토콜을 지원하는 클라우드 공급자는 Amazon S3 를 사용하여 백업할 수 있습니다.',
        domain: '가속 도메인',
        backupAccount: '백업 계정 | 백업 계정',
        loadBucket: '버킷 가져오기',
        accountName: '계정 이름',
        accountKey: '계정 키',
        address: '주소',
        path: '경로',

        safe: '보안',
        bindInfo: '바인딩 정보',
        bindAll: '모두 수신',
        bindInfoHelper: '서비스 수신 주소나 프로토콜 변경은 서비스 불가 상태를 초래할 수 있습니다. 계속하시겠습니까?',
        ipv6: 'IPv6 수신',
        bindAddress: '수신 주소',
        entrance: '진입점',
        showEntrance: '"개요" 페이지에서 비활성화 경고 표시',
        entranceHelper: '보안 진입점을 활성화하면 지정된 보안 진입점을 통해서만 패널에 로그인할 수 있습니다.',
        entranceError: '5-116자의 안전한 로그인 진입점을 입력하십시오. 숫자나 문자만 지원됩니다.',
        entranceInputHelper: '공백으로 남기면 보안 진입점이 비활성화됩니다.',
        randomGenerate: '랜덤',
        expirationTime: '만료 날짜',
        unSetting: '미설정',
        noneSetting: '패널 비밀번호의 만료 시간을 설정합니다. 만료 후 비밀번호를 재설정해야 합니다.',
        expirationHelper: '비밀번호 만료 시간이 [0]일인 경우 비밀번호 만료 기능이 비활성화됩니다.',
        days: '만료 일수',
        expiredHelper: '현재 비밀번호가 만료되었습니다. 비밀번호를 다시 변경하십시오.',
        timeoutHelper: '[ {0}일 ] 패널 비밀번호가 곧 만료됩니다. 만료 후 비밀번호를 재설정해야 합니다.',
        complexity: '복잡성 검증',
        complexityHelper:
            '활성화하면 비밀번호 검증 규칙이 8-30 자, 영어, 숫자 및 최소 두 개의 특수 문자 포함으로 설정됩니다.',
        bindDomain: '도메인 바인딩',
        unBindDomain: '도메인 바인딩 해제',
        panelSSL: '패널 SSL',
        unBindDomainHelper: '도메인 이름 바인딩 해제 작업은 시스템 보안에 영향을 미칠 수 있습니다. 계속하시겠습니까?',
        bindDomainHelper: '도메인을 바인딩한 후에는 해당 도메인을 통해서만 1Panel 서비스에 접근할 수 있습니다.',
        bindDomainHelper1: '공백으로 남기면 도메인 이름 바인딩이 비활성화됩니다.',
        bindDomainWarning:
            '도메인 바인딩 후 로그아웃되며 설정에 지정된 도메인 이름을 통해서만 1Panel 서비스에 접근할 수 있습니다. 계속하시겠습니까?',
        allowIPs: '허가된 IP',
        unAllowIPs: '허가되지 않은 IP',
        unAllowIPsWarning:
            '허가되지 않은 빈 IP를 설정하면 모든 IP가 시스템에 접근할 수 있어 시스템 보안에 영향을 미칠 수 있습니다. 계속하시겠습니까?',
        allowIPsHelper: '허가된 IP 주소 목록을 설정하면 목록에 있는 IP 주소만 패널 서비스에 접근할 수 있습니다.',
        allowIPsWarning:
            '허가된 IP 주소 목록을 설정하면 목록에 있는 IP 주소만 패널 서비스에 접근할 수 있습니다. 계속하시겠습니까?',
        allowIPsHelper1: '공백으로 남기면 IP 주소 제한이 비활성화됩니다.',
        allowIPEgs: '한 줄에 하나씩 입력하십시오. 예:\n*************\n***********/24',
        mfa: '2단계 인증 (2FA)',
        mfaClose: 'MFA를 비활성화하면 서비스 보안이 낮아집니다. 계속하시겠습니까?',
        secret: '비밀키',
        mfaInterval: '갱신 간격(초)',
        mfaTitleHelper:
            '제목은 다른 1Panel 호스트를 구별하는 데 사용됩니다. 제목을 수정한 후 다시 스캔하거나 비밀키를 수동으로 추가하세요.',
        mfaIntervalHelper: '갱신 시간을 수정한 후 다시 스캔하거나 비밀키를 수동으로 추가하세요.',
        mfaAlert:
            '일회용 토큰은 현재 시간을 기반으로 생성된 동적 6자리 숫자입니다. 서버 시간이 동기화되어 있는지 확인하세요.',
        mfaHelper: '활성화 후 일회용 토큰 검증이 필요합니다.',
        mfaHelper1: '인증 앱을 다운로드하세요. 예를 들어,',
        mfaHelper2:
            '다음 QR 코드를 인증 앱으로 스캔하거나 비밀키를 복사하여 인증 앱에 입력하여 일회용 토큰을 획득하세요.',
        mfaHelper3: '앱에서 생성된 6자리 숫자를 입력하세요.',
        mfaCode: '일회용 토큰',
        sslChangeHelper: 'HTTPS 설정을 수정하고 서비스를 재시작합니다. 계속하시겠습니까?',
        sslDisable: '비활성화',
        sslDisableHelper: 'HTTPS 서비스를 비활성화하면 패널을 재시작해야 적용됩니다. 계속하시겠습니까?',
        noAuthSetting: '비인가 설정',
        noAuthSettingHelper:
            '지정된 보안 진입점을 사용하지 않거나, 지정된 IP 나 도메인에서 패널에 접근하지 않는 경우 이 응답은 패널 특성을 숨길 수 있습니다.',
        responseSetting: '응답 설정',
        help200: '도움말 페이지',
        error400: '잘못된 요청',
        error401: '권한 없음',
        error403: '접근 금지',
        error404: '페이지를 찾을 수 없음',
        error408: '요청 시간 초과',
        error416: '범위 불만족',
        error444: '연결 닫힘',
        error500: '서버 오류',

        https: '패널의 HTTPS 프로토콜 접근 설정은 패널 접근 보안을 강화할 수 있습니다.',
        certType: '인증서 유형',
        selfSigned: '자가 서명',
        selfSignedHelper: '자가 서명 인증서는 브라우저에서 신뢰하지 않을 수 있으며 보안 경고가 표시될 수 있습니다.',
        select: '선택',
        domainOrIP: '도메인 또는 IP:',
        timeOut: '시간 초과',
        rootCrtDownload: '루트 인증서 다운로드',
        primaryKey: '개인 키',
        certificate: '인증서',
        backupJump: '현재 백업 목록에 없는 파일입니다. 파일 디렉토리에서 다운로드하여 백업에 가져오기를 시도하세요.',

        snapshot: '스냅샷 | 스냅샷들',
        noAppData: '선택할 수 있는 시스템 앱이 없습니다',
        noBackupData: '선택할 수 있는 백업 데이터가 없습니다',
        stepBaseData: '기본 데이터',
        stepAppData: '시스템 앱',
        stepPanelData: '시스템 데이터',
        stepBackupData: '백업 데이터',
        stepOtherData: '기타 데이터',
        operationLog: '작업 로그 유지',
        loginLog: '접속 로그 유지',
        systemLog: '시스템 로그 유지',
        taskLog: '작업 로그 유지',
        monitorData: '모니터링 데이터 유지',
        dockerConf: 'Docker 설정',
        selectAllImage: '모든 앱 이미지를 백업',
        logLabel: '로그',
        agentLabel: '노드 설정',
        appDataLabel: '앱 데이터',
        appImage: '앱 이미지',
        appBackup: '앱 백업',
        backupLabel: '백업 디렉토리',
        confLabel: '설정 파일',
        dockerLabel: '컨테이너',
        taskLabel: '예약 작업',
        resourceLabel: '앱 리소스 디렉토리',
        runtimeLabel: '실행 환경',
        appLabel: '앱',
        databaseLabel: '데이터베이스',
        snapshotLabel: '스냅샷 파일',
        websiteLabel: '웹사이트',
        directoryLabel: '디렉토리',
        appStoreLabel: '앱 스토어',
        shellLabel: '스크립트',
        tmpLabel: '임시 디렉토리',
        sslLabel: '인증서 디렉토리',
        reCreate: '스냅샷 생성 실패',
        reRollback: '스냅샷 롤백 실패',
        deleteHelper: '타사 백업 계정에 포함된 스냅샷 파일을 포함하여 모든 스냅샷 파일이 삭제됩니다. 계속하시겠습니까?',
        status: '스냅샷 상태',
        ignoreRule: '무시 규칙',
        editIgnoreRule: '@:commons.button.edit @.lower:setting.ignoreRule',
        ignoreHelper:
            '이 규칙은 스냅샷을 생성할 때 1Panel 데이터 디렉토리를 압축하고 백업하는 데 사용됩니다. 기본적으로 소켓 파일은 무시됩니다.',
        ignoreHelper1: '한 줄에 하나씩 입력하세요. 예:\n*.log\n/opt/1panel/cache',
        panelInfo: '1Panel 기본 정보 쓰기',
        panelBin: '1Panel 시스템 파일 백업',
        daemonJson: 'Docker 구성 파일 백업',
        appData: '1Panel 에서 설치된 앱 백업',
        panelData: '1Panel 데이터 디렉토리 백업',
        backupData: '1Panel 의 로컬 백업 디렉토리 백업',
        compress: '스냅샷 파일 생성',
        upload: '스냅샷 파일 업로드',
        recoverDetail: '복구 세부정보',
        createSnapshot: '스냅샷 생성',
        importSnapshot: '스냅샷 동기화',
        importHelper: '스냅샷 디렉토리: ',
        lastRecoverAt: '마지막 복구 시간',
        lastRollbackAt: '마지막 롤백 시간',
        reDownload: '백업 파일 다시 다운로드',
        recoverErrArch: '서버 아키텍처가 다른 스냅샷 복구는 지원되지 않습니다!',
        recoverErrSize: '디스크 공간이 부족합니다. 확인하거나 정리 후 다시 시도하세요!',
        recoverHelper: '스냅샷 {0} 에서 복구를 시작합니다. 계속하기 전에 다음 정보를 확인하세요:',
        recoverHelper1: '복구에는 Docker 및 1Panel 서비스를 재시작해야 합니다.',
        recoverHelper2: '서버에 충분한 디스크 공간이 있는지 확인하세요. (스냅샷 파일 크기: {0}, 사용 가능한 공간: {1})',
        recoverHelper3:
            '서버 아키텍처가 스냅샷이 생성된 서버의 아키텍처와 일치하는지 확인하세요. (현재 서버 아키텍처: {0})',
        rollback: '롤백',
        rollbackHelper:
            '이 복구를 롤백하면 해당 복구의 모든 파일이 대체되며 Docker 및 1Panel 서비스를 재시작해야 할 수 있습니다. 계속하시겠습니까?',

        upgradeHelper: '업그레이드에는 1Panel 서비스를 재시작해야 합니다. 계속하시겠습니까?',
        rollbackLocalHelper:
            '마스터 노드는 직접 롤백을 지원하지 않습니다. 수동으로 [1pctl restore] 명령어를 실행하여 롤백하세요!',
        noUpgrade: '현재 최신 버전입니다',
        upgradeNotes: '릴리스 노트',
        upgradeNow: '지금 업그레이드',
        source: '다운로드 소스',
        versionNotSame: '노드 버전이 메인 노드와 일치하지 않습니다. 노드 관리에서 업그레이드한 후 다시 시도해 주세요.',
        versionCompare:
            '노드 {0}이(가) 이미 업그레이드 가능한 최신 버전입니다. 마스터 노드 버전을 확인 후 다시 시도하세요!',

        about: '정보',
        project: 'GitHub',
        issue: '이슈',
        doc: '공식 문서',
        star: '별',
        description: '리눅스 서버 패널',
        forum: '토론',
        doc2: '문서',
        currentVersion: '버전',

        license: '라이선스',
        bindNode: '노드 바인딩',
        menuSetting: '메뉴 설정',
        menuSettingHelper: '하위 메뉴가 1개만 존재할 경우, 메뉴 바에는 해당 하위 메뉴만 표시됩니다',
        showAll: '모두 표시',
        hideALL: '모두 숨기기',
        ifShow: '표시 여부',
        menu: '메뉴',
        confirmMessage: '고급 메뉴 목록을 업데이트하려면 페이지가 새로 고쳐집니다. 계속하시겠습니까?',
        compressPassword: '압축 비밀번호',
        backupRecoverMessage: '압축 또는 압축 해제 비밀번호를 입력하세요 (설정하지 않으려면 비워 두세요)',
    },
    license: {
        community: 'OSS',
        oss: '오픈 소스 소프트웨어',
        pro: 'Pro',
        trial: '체험판',
        add: '커뮤니티 에디션 추가',
        licenseAlert:
            '라이선스가 노드에 정상적으로 바인딩된 경우에만 커뮤니티 에디션 노드를 추가할 수 있습니다. 라이선스에 정상적으로 바인딩된 노드만 전환이 지원됩니다.',
        licenseUnbindHelper: '이 라이선스에 커뮤니티 에디션 노드가 존재합니다. 바인딩 해제 후 다시 시도하세요!',
        subscription: '구독',
        perpetual: '영구 라이선스',
        versionConstraint: '{0} 버전 일시불 구매',
        forceUnbind: '강제 바인딩 해제',
        forceUnbindHelper:
            '강제 바인딩 해제를 수행하면 해제 과정에서 발생하는 오류를 무시하고 궁극적으로 라이센스 바인딩을 해제합니다.',
        updateForce: '강제 업데이트 (바인딩 해제 과정의 모든 오류를 무시하고 최종 작업 성공을 보장합니다)',
        trialInfo: '버전',
        authorizationId: '구독 인증 ID',
        authorizedUser: '인증된 사용자',
        lostHelper:
            '라이센스가 최대 재시도 횟수를 초과했습니다. 전문가 버전 기능이 제대로 작동하는지 확인하려면 동기화 버튼을 수동으로 클릭하세요.',
        disableHelper:
            '라이센스 동기화 검증에 실패했습니다. 전문가 버전 기능이 제대로 작동하는지 확인하려면 동기화 버튼을 수동으로 클릭하세요.',
        quickUpdate: '빠른 업데이트',
        power: '권한 부여',
        unbindHelper: '연결 해제 후 모든 Pro 관련 설정이 초기화됩니다. 계속하시겠습니까?',
        importLicense: '라이센스',
        importHelper: '라이센스 파일을 여기에 클릭하거나 드래그하세요',
        technicalAdvice: '기술 상담',
        advice: '상담',
        levelUpPro: 'Pro 로 업그레이드',
        licenseSync: '라이센스 동기화',
        knowMorePro: '더 알아보기',
        closeAlert: '현재 페이지는 패널 설정에서 닫을 수 있습니다.',
        introduce: '기능 소개',
        waf: '전문 버전으로 업그레이드하면 차단 맵, 로그, 차단 기록, 지리적 위치 차단, 사용자 정의 규칙, 사용자 정의 차단 페이지 등의 기능을 제공받을 수 있습니다.',
        tamper: '전문 버전으로 업그레이드하면 웹사이트를 무단 수정이나 변조로부터 보호할 수 있습니다.',
        setting: '전문 버전으로 업그레이드하면 패널 로고, 환영 메시지 등 정보를 사용자 정의할 수 있습니다.',
        monitor:
            '전문 버전으로 업그레이드하면 웹사이트의 실시간 상태, 방문자 트렌드, 방문자 출처, 요청 로그 등 정보를 확인할 수 있습니다.',
        alert: '전문 버전으로 업그레이드하면 SMS 를 통해 알림 정보를 받고 알림 로그를 볼 수 있으며, 다양한 주요 이벤트를 완벽하게 제어하여 시스템 운영을 걱정 없이 유지할 수 있습니다.',
        fileExchange: '프로페셔널 에디션으로 업그레이드하여 여러 서버 간에 파일을 빠르게 전송할 수 있습니다.',
        app: '프로페셔널 버전으로 업그레이드하면 모바일 APP을 통해 서비스 정보, 이상 모니터링 등을 확인할 수 있습니다.',
        cluster:
            '프로페셔널 에디션으로 업그레이드하면 MySQL/Postgres/Reids 마스터-슬레이브 클러스터를 관리할 수 있습니다.',
    },
    clean: {
        scan: '스캔 시작',
        scanHelper: '1Panel 실행 중에 생성된 불필요한 파일을 쉽게 정리합니다.',
        clean: '지금 정리',
        reScan: '다시 스캔',
        cleanHelper: '선택한 시스템 불필요 파일을 정리합니다. 이 작업은 되돌릴 수 없습니다. 계속 하시겠습니까?',
        statusSuggest: '(권장 정리)',
        statusClean: '(매우 깨끗함)',
        statusEmpty: '매우 깨끗합니다. 정리가 필요하지 않습니다!',
        statusWarning: '(주의하여 진행)',
        lastCleanTime: '마지막 정리 시간: {0}',
        lastCleanHelper: '정리된 파일 및 디렉터리: {0}, 총 정리된 크기: {1}',
        cleanSuccessful: '정리 성공',
        currentCleanHelper: '이번 세션에서 정리된 파일 및 디렉터리: {0}, 총 정리된 크기: {1}',
        suggest: '(권장)',
        totalScan: '정리해야 할 총 불필요 파일: ',
        selectScan: '선택된 불필요 파일 총합: ',

        system: '시스템 불필요 파일',
        systemHelper: '스냅샷, 업그레이드 중에 생성된 임시 파일과 버전 업데이트 중에 불필요해진 파일 내용',
        panelOriginal: '시스템 스냅샷 복구 백업 파일',
        backup: '임시 백업 디렉터리',
        upgrade: '시스템 업그레이드 백업 파일',
        upgradeHelper: '(시스템 롤백을 위해 최신 업그레이드 백업을 유지하는 것이 좋습니다)',
        cache: '시스템 캐시 파일',
        cacheHelper: '(주의하여 진행, 정리 후 서비스 재시작 필요)',
        rollback: '복구 전 백업 파일',

        upload: '임시 업로드 파일',
        uploadHelper: '시스템 백업 리스트에서 업로드된 임시 파일',
        download: '임시 다운로드 파일',
        downloadHelper: '시스템에서 제3자 백업 계정으로 다운로드된 임시 파일',
        directory: '디렉터리',

        systemLog: '시스템 로그 파일',
        systemLogHelper: '시스템 로그 정보, 컨테이너 빌드 또는 이미지 풀 로그 정보, 예약된 작업에서 생성된 로그 파일',
        dockerLog: '컨테이너 운영 로그 파일',
        taskLog: '예약된 작업 실행 로그 파일',
        containerShell: '컨테이너 내 쉘 스크립트 예약 작업',

        containerTrash: '컨테이너 휴지통',
        volumes: '볼륨',
        buildCache: '컨테이너 빌드 캐시',
    },
    app: {
        app: '애플리케이션 | 애플리케이션들',
        installName: '이름',
        installed: '설치됨',
        all: '모두',
        version: '버전',
        detail: '세부사항',
        params: '편집',
        author: '저자',
        source: '출처',
        appName: '애플리케이션 이름',
        deleteWarn:
            '삭제 작업은 모든 데이터와 백업을 함께 삭제합니다. 이 작업은 되돌릴 수 없습니다. 계속 하시겠습니까?',
        syncSuccess: '동기화 성공',
        canUpgrade: '업데이트',
        backupName: '파일 이름',
        backupPath: '파일 경로',
        backupdate: '백업 시간',
        versionSelect: '버전을 선택하세요',
        operatorHelper: '선택한 애플리케이션에 대해 {0} 작업이 수행됩니다. 계속 하시겠습니까?',
        startOperatorHelper: '애플리케이션이 시작됩니다. 계속 하시겠습니까?',
        stopOperatorHelper: '애플리케이션이 중지됩니다. 계속 하시겠습니까?',
        restartOperatorHelper: '애플리케이션이 재시작됩니다. 계속 하시겠습니까?',
        reloadOperatorHelper: '애플리케이션이 다시 로드됩니다. 계속 하시겠습니까?',
        checkInstalledWarn: '"{0}"이(가) 감지되지 않았습니다. "앱 스토어"로 가서 설치하세요.',
        gotoInstalled: '설치하러 가기',
        limitHelper: '애플리케이션은 이미 설치되었습니다.',
        deleteHelper: '"{0}"은(는) 다음 리소스와 연결되어 있습니다. 확인 후 다시 시도하세요!',
        checkTitle: '힌트',
        defaultConfig: '기본 설정',
        defaultConfigHelper: '기본 설정으로 복원되었습니다. 저장 후 적용됩니다.',
        forceDelete: '강제 삭제',
        forceDeleteHelper: '강제 삭제는 삭제 과정 중 오류를 무시하고 메타데이터를 삭제합니다.',
        deleteBackup: '백업 삭제',
        deleteBackupHelper: '애플리케이션 백업도 삭제됩니다.',
        deleteDB: '데이터베이스 삭제',
        deleteDBHelper: '데이터베이스도 삭제됩니다.',
        noService: '{0} 없음',
        toInstall: '설치하러 가기',
        param: '파라미터',
        alreadyRun: '나이',
        syncAppList: '동기화',
        less1Minute: '1분 미만',
        appOfficeWebsite: '오피스 웹사이트',
        github: 'GitHub',
        document: '문서',
        updatePrompt: '업데이트가 없습니다.',
        installPrompt: '아직 설치된 앱이 없습니다.',
        updateHelper: '파라미터를 수정하면 애플리케이션이 시작되지 않을 수 있습니다. 주의하세요.',
        updateWarn: '업데이트 파라미터는 애플리케이션을 재빌드해야 합니다. 계속 하시겠습니까?',
        busPort: '포트',
        syncStart: '동기화 시작! 나중에 앱 스토어를 새로고침 해주세요.',
        advanced: '고급 설정',
        cpuCore: '코어',
        containerName: '컨테이너 이름',
        containerNameHelper: '설정되지 않으면 컨테이너 이름이 자동으로 생성됩니다.',
        allowPort: '외부 접근 허용',
        allowPortHelper: '외부 포트 접근을 허용하면 방화벽 포트가 열립니다.',
        appInstallWarn:
            '애플리케이션은 기본적으로 외부 접근 포트를 노출하지 않습니다. "고급 설정"을 클릭하여 노출할 수 있습니다.',
        upgradeStart: '업그레이드 시작! 나중에 페이지를 새로고침 해주세요.',
        toFolder: '설치 디렉터리 열기',
        editCompose: 'Compose 파일 편집',
        editComposeHelper: 'Compose 파일을 편집하면 소프트웨어 설치가 실패할 수 있습니다.',
        composeNullErr: 'Compose는 비어 있을 수 없습니다.',
        takeDown: '내리기',
        allReadyInstalled: '설치됨',
        installHelper: '이미지 풀 문제 시 이미지 가속을 구성하세요.',
        installWarn:
            '외부 접근이 활성화되지 않아 애플리케이션이 외부 네트워크에서 접근할 수 없습니다. 계속 하시겠습니까?',
        showIgnore: '무시된 애플리케이션 보기',
        cancelIgnore: '무시 취소',
        ignoreList: '무시된 애플리케이션',
        appHelper: '특수 애플리케이션 설치 지침을 보려면 애플리케이션 상세 페이지로 이동하세요.',
        backupApp: '업그레이드 전 애플리케이션 백업',
        backupAppHelper:
            '업그레이드 실패 시 백업이 자동으로 롤백됩니다. 로그 감사 시스템 로그에서 실패 원인을 확인하세요. 백업은 기본적으로 최신 3개 복사본을 유지합니다.',
        openrestyDeleteHelper: 'OpenResty 강제 삭제는 모든 웹사이트를 삭제합니다. 계속 하시겠습니까?',
        downloadLogHelper1: '{0} 애플리케이션의 모든 로그가 다운로드됩니다. 계속 하시겠습니까?',
        downloadLogHelper2: '{0} 애플리케이션의 최신 {1} 로그가 다운로드됩니다. 계속 하시겠습니까?',
        syncAllAppHelper: '모든 애플리케이션이 동기화됩니다. 계속 하시겠습니까?',
        hostModeHelper:
            '현재 애플리케이션 네트워크 모드는 호스트 모드입니다. 포트를 열어야 할 경우 방화벽 페이지에서 수동으로 열어주세요.',
        showLocal: '로컬 애플리케이션 보기',
        reload: '새로고침',
        upgradeWarn:
            '애플리케이션 업그레이드는 docker-compose.yml 파일을 교체합니다. 변경 사항이 있으면 파일 비교를 클릭하여 확인할 수 있습니다.',
        newVersion: '새 버전',
        oldVersion: '현재 버전',
        composeDiff: '파일 비교',
        showDiff: '비교 보기',
        useNew: '사용자 정의 버전 사용',
        useDefault: '기본 버전 사용',
        useCustom: '사용자 정의 docker-compose.yml 사용',
        useCustomHelper:
            '사용자 정의 docker-compose.yml 파일을 사용하면 애플리케이션 업그레이드가 실패할 수 있습니다. 필요하지 않으면 체크하지 마세요.',
        diffHelper: '왼쪽은 이전 버전, 오른쪽은 새 버전입니다. 편집 후 사용자 정의 버전을 저장하려면 클릭하세요.',
        pullImage: '이미지 풀',
        pullImageHelper: '애플리케이션 시작 전에 docker pull 을 실행하여 이미지를 다운로드하세요.',
        deleteImage: '이미지 삭제',
        deleteImageHelper: '애플리케이션 관련 이미지를 삭제합니다. 삭제에 실패하더라도 작업은 종료되지 않습니다.',
        requireMemory: '메모리 요구사항',
        supportedArchitectures: '지원 아키텍처',
        link: '링크',
        showCurrentArch: '현재 서버 아키텍처 애플리케이션',
        syncLocalApp: '로컬 애플리케이션 동기화',
        memoryRequiredHelper: '현재 애플리케이션은 {0} 메모리가 필요합니다',
        gpuConfig: 'GPU 지원 활성화',
        gpuConfigHelper:
            'NVIDIA GPU가 장착되어 있으며 NVIDIA 드라이버와 NVIDIA Docker Container Toolkit이 설치되어 있는지 확인하세요',
        webUI: '웹 접속 주소',
        webUIPlaceholder: '예: example.com:8080/login',
        defaultWebDomain: '기본 접속 주소',
        defaultWebDomainHepler: '애플리케이션 포트가 8080인 경우 접속 주소는 http(s)://기본 접속 주소:8080입니다',
        webUIConfig:
            '현재 노드에 기본 접근 주소가 설정되지 않았습니다. 애플리케이션 매개변수에서 설정하거나 패널 설정으로 이동하여 설정하세요!',
        toLink: '이동',
        customAppHelper: '사용자 정의 앱 스토어 패키지를 설치하기 전에 설치된 앱이 없는지 확인하십시오.',
        forceUninstall: '강제 제거',
        syncCustomApp: 'カスタムアプリを同期',
        ignoreAll: '후속 모든 버전 무시',
        ignoreVersion: '지정된 버전 무시',
        specifyIP: '호스트 IP 바인딩',
        specifyIPHelper:
            '포트 바인딩을 위한 호스트 주소/네트워크 인터페이스를 설정합니다 (이 기능을 잘 모를 경우, 입력하지 마십시오)',
        uninstallDeleteBackup: '앱 제거 - 백업 삭제',
        uninstallDeleteImage: '앱 제거 - 이미지 삭제',
        upgradeBackup: '앱 업그레이드 전 앱 백업',
    },
    website: {
        primaryDomain: '기본 도메인',
        otherDomains: '기타 도메인',
        static: '정적',
        deployment: '배포',
        supportUpType: '지원되는 파일 형식: .tar.gz',
        zipFormat: '.tar.gz 압축 패키지 구조: test.tar.gz 패키지에는 반드시 {0} 파일이 포함되어야 합니다.',
        proxy: '리버스 프록시',
        alias: '별칭',
        ftpUser: 'FTP 계정',
        ftpPassword: 'FTP 비밀번호',
        ftpHelper: '웹사이트를 생성하면 해당 FTP 계정이 생성되고 FTP 디렉터리는 웹사이트 디렉터리와 연결됩니다.',
        remark: '비고',
        manageGroup: '그룹 관리',
        groupSetting: '그룹 설정',
        createGroup: '그룹 생성',
        appNew: '새로운 애플리케이션',
        appInstalled: '설치된 애플리케이션',
        create: '생성',
        delete: '웹사이트 삭제',
        deleteApp: '애플리케이션 삭제',
        deleteBackup: '백업 삭제',
        domain: '도메인',
        domainHelper: "한 줄에 하나의 도메인.\n와일드카드 '*'와 IP 주소를 지원합니다.\n포트 추가를 지원합니다.",
        addDomain: '추가',
        domainConfig: '도메인 설정',
        defaultDoc: '기본 문서',
        perserver: '동시 연결',
        perserverHelper: '현재 사이트의 최대 동시 연결 수를 제한합니다.',
        perip: '단일 IP',
        peripHelper: '단일 IP의 최대 동시 접속 수를 제한합니다.',
        rate: '트래픽 제한',
        rateHelper: '요청당 트래픽을 제한합니다 (단위: KB)',
        limitHelper: '트래픽 제어 활성화',
        other: '기타',
        currentSSL: '현재 인증서',
        dnsAccount: 'DNS 계정',
        applySSL: '인증서 신청',
        SSLList: '인증서 목록',
        createDnsAccount: 'DNS 계정 생성',
        aliyun: '알리윤',
        manual: '수동 설정',
        key: '키',
        check: '보기',
        acmeAccountManage: 'ACME 계정 관리',
        email: '이메일',
        acmeAccount: 'ACME 계정',
        provider: '검증 방법',
        dnsManual: '수동 설정',
        expireDate: '만료일',
        brand: '기관',
        deploySSL: '배포',
        deploySSLHelper: '인증서를 배포하시겠습니까?',
        ssl: '인증서 | 인증서들',
        dnsAccountManage: 'DNS 제공자 관리',
        renewSSL: '갱신',
        renewHelper: '인증서를 갱신하시겠습니까?',
        renewSuccess: '인증서 갱신 성공',
        enableHTTPS: 'HTTPS 활성화',
        aliasHelper: '별칭은 웹사이트의 디렉터리 이름입니다.',
        lastBackupAt: '마지막 백업 시간',
        null: '없음',
        nginxConfig: 'Nginx 설정',
        websiteConfig: '웹사이트 설정',
        basic: '기본',
        source: '구성',
        security: '보안',
        nginxPer: '성능 튜닝',
        neverExpire: '만료 없음',
        setDefault: '기본값으로 설정',
        default: '기본값',
        deleteHelper: '관련 애플리케이션 상태가 비정상입니다. 확인해 주세요.',
        toApp: '설치된 목록으로 이동',
        cycle: '주기',
        frequency: '빈도',
        ccHelper: '{0}초 내에 동일한 URL을 {1}회 이상 누적 요청하면 CC 방어가 발동되며, 해당 IP가 차단됩니다.',
        mustSave: '변경 사항은 저장해야 적용됩니다.',
        fileExt: '파일 확장자',
        fileExtBlock: '파일 확장자 차단 목록',
        value: '값',
        enable: '활성화',
        proxyAddress: '프록시 주소',
        proxyHelper: '예: 127.0.0.1:8080',
        forceDelete: '강제 삭제',
        forceDeleteHelper: '강제 삭제는 삭제 과정에서 발생하는 오류를 무시하고 최종적으로 메타데이터를 삭제합니다.',
        deleteAppHelper: '관련 애플리케이션 및 애플리케이션 백업을 동시에 삭제합니다.',
        deleteBackupHelper: '웹사이트 백업도 삭제합니다.',
        deleteConfirmHelper:
            '삭제 작업은 되돌릴 수 없습니다. 확인하려면 <span style=\'color:red\'>"{0}"</span>을(를) 입력하세요.',
        staticPath: '해당 주요 디렉터리는',
        limit: '제한',
        blog: '포럼/블로그',
        imageSite: '이미지 사이트',
        downloadSite: '다운로드 사이트',
        shopSite: '쇼핑몰',
        doorSite: '포털',
        qiteSite: '기업',
        videoSite: '비디오',
        errLog: '오류 로그',
        accessLog: '웹사이트 로그',
        stopHelper:
            '사이트를 중지하면 정상적으로 액세스할 수 없으며, 사용자가 해당 사이트의 중지 페이지를 보게 됩니다. 계속하시겠습니까?',
        startHelper: '사이트를 활성화하면 사용자가 정상적으로 사이트 내용을 액세스할 수 있습니다. 계속하시겠습니까?',
        sitePath: '디렉터리',
        siteAlias: '사이트 별칭',
        primaryPath: '루트 디렉터리',
        folderTitle: '웹사이트는 다음과 같은 폴더를 포함합니다.',
        wafFolder: '방화벽 규칙',
        indexFolder: '웹사이트 루트 디렉터리',
        logFolder: '웹사이트 로그',
        sslFolder: '웹사이트 인증서',
        enableOrNot: '활성화 여부',
        oldSSL: '기존 인증서',
        manualSSL: '인증서 가져오기',
        select: '선택',
        selectSSL: '인증서 선택',
        privateKey: '키(KEY)',
        certificate: '인증서(PEM 형식)',
        HTTPConfig: 'HTTP 옵션',
        HTTPSOnly: 'HTTP 요청 차단',
        HTTPToHTTPS: 'HTTPS로 리디렉션',
        HTTPAlso: 'HTTP 요청 허용',
        sslConfig: 'SSL 옵션',
        disableHTTPS: 'HTTPS 비활성화',
        disableHTTPSHelper: 'HTTPS 비활성화는 인증서 관련 구성을 삭제합니다. 계속하시겠습니까?',
        SSLHelper:
            '주의: 불법 웹사이트에는 SSL 인증서를 사용하지 마세요.\nHTTPS 액세스가 안 되는 경우 보안 그룹에서 443 포트를 올바르게 해제했는지 확인하세요.',
        SSLConfig: '인증서 설정',
        SSLProConfig: '프로토콜 설정',
        supportProtocol: '프로토콜 버전',
        encryptionAlgorithm: '암호화 알고리즘',
        notSecurity: '(안전하지 않음)',
        encryptHelper:
            "Let's Encrypt 는 인증서 발급에 빈도 제한이 있지만 일반적인 요구 사항을 충족하기에 충분합니다. 너무 자주 작업하면 발급 실패가 발생할 수 있습니다. 자세한 제한 사항은 <a target='_blank' href='https://letsencrypt.org/zh-cn/docs /rate-limits/'>공식 문서</a>를 참조하세요.",
        ipValue: '값',
        ext: '파일 확장자',
        wafInputHelper: '줄 단위로 데이터 입력, 한 줄에 하나씩',
        data: '데이터',
        ever: '영구',
        nextYear: '1년 후',
        noLog: '로그를 찾을 수 없습니다.',
        defaultServer: '기본 사이트',
        noDefaultServer: '설정되지 않음',
        defaultServerHelper:
            '기본 사이트를 설정한 후, 바인딩되지 않은 모든 도메인 이름과 IP는 기본 사이트로 리디렉션됩니다\n이는 악의적인 해석을 효과적으로 방지할 수 있습니다\n하지만 WAF의 무단 도메인 차단이 실패할 수도 있습니다',
        restoreHelper: '이 백업을 사용하여 복원하시겠습니까?',
        websiteDeploymentHelper: '설치된 애플리케이션을 사용하거나 새 애플리케이션을 생성하여 웹사이트를 만드세요.',
        websiteStatictHelper: '호스트에 웹사이트 디렉터리를 생성합니다.',
        websiteProxyHelper:
            '리버스 프록시를 사용하여 기존 서비스를 프록시합니다. 예를 들어, 포트 8080 에서 실행 중인 서비스를 프록시하려면 프록시 주소는 "http://127.0.0.1:8080"이 됩니다.',
        runtimeProxyHelper: '웹사이트 런타임을 사용하여 웹사이트를 만드세요.',
        runtime: '런타임',
        deleteRuntimeHelper: '런타임 애플리케이션은 웹사이트와 함께 삭제해야 하므로 신중하게 처리하세요.',
        proxyType: '네트워크 유형',
        unix: '유닉스 네트워크',
        tcp: 'TCP/IP 네트워크',
        phpFPM: 'FPM 구성',
        phpConfig: 'PHP 구성',
        updateConfig: '구성 업데이트',
        isOn: '켜짐',
        isOff: '꺼짐',
        rewrite: '의사 정적',
        rewriteMode: '방식',
        current: '현재',
        rewriteHelper: '의사 정적 설정으로 인해 웹사이트에 접근할 수 없게 되면 기본 설정으로 되돌려보세요.',
        runDir: '실행 디렉터리',
        runUserHelper:
            'PHP 컨테이너 런타임 환경에서 배포된 웹사이트의 경우, 인덱스 및 하위 디렉터리 아래의 모든 파일 및 폴더의 소유자와 사용자 그룹을 1000으로 설정해야 합니다. 로컬 PHP 환경의 경우, 로컬 PHP-FPM 사용자 및 사용자 그룹 설정을 참조하세요.',
        userGroup: '사용자/그룹',
        uGroup: '그룹',
        proxyPath: '프록시 경로',
        proxyPass: '대상 URL',
        cache: '캐시',
        cacheTime: '캐시 지속 시간',
        enableCache: '캐시 활성화',
        proxyHost: '프록시 호스트',
        disabled: '중지됨',
        startProxy: '리버스 프록시를 시작합니다. 계속하시겠습니까?',
        stopProxy: '리버스 프록시를 중지합니다. 계속하시겠습니까?',
        sourceFile: '소스',
        proxyHelper1: '이 디렉터리에 접근할 때 대상 URL 의 내용이 반환되고 표시됩니다.',
        proxyPassHelper: '대상 URL 은 유효하고 접근 가능해야 합니다.',
        proxyHostHelper: '요청 헤더에 있는 도메인 이름을 프록시 서버로 전달합니다.',
        replacementHelper: '최대 5개의 교체를 추가할 수 있으며, 교체가 필요하지 않은 경우 비워두세요.',
        modifier: '매칭 규칙',
        modifierHelper: '예: = 는 정확히 일치, ~ 는 정규식 일치, ^~ 는 경로 시작 부분 일치 등을 나타냅니다.',
        replace: '텍스트 교체',
        addReplace: '추가',
        replaced: '검색 문자열 (비울 수 없음)',
        replaceText: '교체할 문자열',
        replacedErr: '검색 문자열은 비워둘 수 없습니다',
        replacedErr2: '검색 문자열은 중복될 수 없습니다',
        basicAuth: '기본 인증',
        editBasicAuthHelper:
            '비밀번호는 비대칭으로 암호화되어 표시할 수 없습니다. 수정하려면 비밀번호를 재설정해야 합니다.',
        antiLeech: '링크 차단',
        extends: '확장',
        browserCache: '캐시',
        leechLog: '링크 차단 로그 기록',
        accessDomain: '허용된 도메인',
        leechReturn: '응답 리소스',
        noneRef: '빈 참조 허용',
        disable: '비활성화',
        disableLeechHelper: '링크 차단을 비활성화할지 여부',
        disableLeech: '링크 차단 비활성화',
        ipv6: 'IPv6 수신 대기',
        leechReturnError: 'HTTP 상태 코드를 입력하세요',
        selectAcme: 'Acme 계정 선택',
        imported: '수동으로 생성됨',
        importType: '가져오기 유형',
        pasteSSL: '코드 붙여넣기',
        localSSL: '서버 파일 선택',
        privateKeyPath: '개인 키 파일',
        certificatePath: '인증서 파일',
        ipWhiteListHelper: 'IP 허용 목록의 역할: 모든 규칙이 IP 허용 목록에 대해 무효화됩니다.',
        redirect: '리디렉션',
        sourceDomain: '소스 도메인',
        targetURL: '대상 URL 주소',
        keepPath: 'URI 매개변수',
        path: '경로',
        redirectType: '리디렉션 유형',
        redirectWay: '방식',
        keep: '유지',
        notKeep: '유지하지 않음',
        redirectRoot: '홈페이지로 리디렉션',
        redirectHelper: '301 영구 리디렉션, 302 임시 리디렉션',
        changePHPVersionWarn:
            'PHP 버전을 변경하면 기존 PHP 컨테이너가 삭제됩니다 (마운트된 웹사이트 코드는 손실되지 않습니다). 계속하시겠습니까?',
        changeVersion: '버전 전환',
        retainConfig: 'php-fpm.conf 및 php.ini 파일을 유지할지 여부',
        runDirHelper2: '보조 실행 디렉터리가 인덱스 디렉터리 아래에 있는지 확인하세요.',
        openrestyHelper:
            'OpenResty 기본 HTTP 포트: {0}, HTTPS 포트: {1}, 이는 웹사이트 도메인 접속 및 HTTPS 강제 리디렉션에 영향을 미칠 수 있습니다.',
        primaryDomainHelper: '예: example.com 또는 example.com:8080',
        acmeAccountType: '계정 유형',
        keyType: '키 알고리즘',
        tencentCloud: '텐센트 클라우드',
        containWarn: '도메인 이름에 메인 도메인이 포함되어 있습니다. 다시 입력하세요.',
        rewriteHelper2:
            '앱 스토어에서 설치된 WordPress 와 같은 응용 프로그램은 일반적으로 사전 설정된 가상 정적 구성이 포함됩니다. 이를 재구성하면 오류가 발생할 수 있습니다.',
        websiteBackupWarn:
            '로컬 백업 가져오기만 지원합니다. 다른 기기에서 가져온 백업은 복구 실패를 초래할 수 있습니다.',
        ipWebsiteWarn:
            'IP를 도메인 이름으로 사용하는 웹사이트는 정상적으로 접속되기 위해 기본 사이트로 설정해야 합니다.',
        hstsHelper: 'HSTS 를 활성화하면 웹사이트 보안을 강화할 수 있습니다.',
        includeSubDomains: '서브도메인',
        hstsIncludeSubDomainsHelper: '활성화하면 HSTS 정책이 현재 도메인의 모든 서브도메인에 적용됩니다.',
        defaultHtml: '기본 페이지',
        website404: '웹사이트 404 오류 페이지',
        domain404: '웹사이트 도메인이 존재하지 않습니다.',
        indexHtml: '정적 웹사이트 인덱스',
        stopHtml: '중지된 웹사이트',
        indexPHP: 'PHP 웹사이트 인덱스',
        sslExpireDate: '인증서 만료 날짜',
        website404Helper: '웹사이트 404 오류 페이지는 PHP 실행 환경 웹사이트 및 정적 웹사이트만 지원합니다.',
        sni: '원본 SNI',
        sniHelper:
            '역방향 프록시 백엔드가 HTTPS 인 경우 원본 SNI 를 설정해야 할 수 있습니다. 자세한 내용은 CDN 서비스 제공자의 문서를 참조하세요.',
        huaweicloud: '화웨이 클라우드',
        createDb: '데이터베이스 생성',
        enableSSLHelper: 'SSL 활성화 실패는 웹사이트 생성에 영향을 미치지 않습니다.',
        batchAdd: '도메인 일괄 추가',
        generateDomain: '생성',
        global: '글로벌',
        subsite: '하위 사이트',
        subsiteHelper: '하위 사이트는 기존 PHP 또는 정적 웹사이트의 디렉토리를 루트 디렉토리로 선택할 수 있습니다.',
        parentWebsite: '상위 웹사이트',
        deleteSubsite: '현재 웹사이트를 삭제하려면 먼저 하위 사이트 {0}를 삭제해야 합니다.',
        loadBalance: '로드 밸런싱',
        server: '노드',
        algorithm: '알고리즘',
        ipHash: 'IP 해시',
        ipHashHelper:
            '클라이언트 IP 주소를 기반으로 요청을 특정 서버에 분배하여 특정 클라이언트가 항상 동일한 서버로 라우팅되도록 합니다.',
        leastConn: '최소 연결',
        leastConnHelper: '활성 연결 수가 가장 적은 서버로 요청을 보냅니다.',
        leastTime: '최소 시간',
        leastTimeHelper: '활성 연결 시간이 가장 짧은 서버로 요청을 보냅니다.',
        defaultHelper:
            '기본 방법으로, 요청은 각 서버에 균등하게 분배됩니다. 서버에 가중치 설정이 있는 경우 지정된 가중치에 따라 요청이 분배됩니다. 가중치가 높은 서버는 더 많은 요청을 받습니다.',
        weight: '가중치',
        maxFails: '최대 실패 횟수',
        maxConns: '최대 연결 수',
        strategy: '전략',
        strategyDown: '비활성화',
        strategyBackup: '백업',
        staticChangePHPHelper: '현재 정적 웹사이트이며 PHP 웹사이트로 전환할 수 있습니다.',
        proxyCache: '리버스 프록시 캐시',
        cacheLimit: '캐시 공간 제한',
        shareCache: '캐시 카운트 메모리 크기',
        cacheExpire: '캐시 만료 시간',
        shareCacheHelper: '1M 메모리로 약 8000개의 캐시 객체를 저장할 수 있습니다.',
        cacheLimitHelper: '제한을 초과하면 이전 캐시가 자동으로 삭제됩니다.',
        cacheExpireHelper: '만료 시간 내에 히트되지 않은 캐시는 삭제됩니다.',
        realIP: '실제 IP',
        ipFrom: 'IP 소스',
        ipFromHelper:
            '신뢰할 수 있는 IP 소스를 구성함으로써 OpenResty는 HTTP 헤더의 IP 정보를 분석하여 방문자의 실제 IP 주소를 정확하게 식별하고 기록합니다(액세스 로그 포함).',
        ipFromExample1: '프론트엔드가 Frp와 같은 도구인 경우 Frp의 IP 주소(예: 127.0.0.1)를 입력할 수 있습니다.',
        ipFromExample2: '프론트엔드가 CDN인 경우 CDN의 IP 범위를 입력할 수 있습니다.',
        ipFromExample3:
            '확실하지 않은 경우 0.0.0.0/0 (IPv4) 또는 ::/0 (IPv6)를 입력할 수 있습니다. [주의: 모든 소스 IP를 허용하는 것은 안전하지 않습니다.]',
        http3Helper:
            'HTTP/3는 HTTP/2의 업그레이드 버전으로, 더 빠른 연결 속도와 더 나은 성능을 제공합니다. 그러나 모든 브라우저가 HTTP/3를 지원하는 것은 아니며, 활성화하면 일부 브라우저가 사이트에 접근하지 못할 수 있습니다.',
        changeDatabase: '데이터베이스 전환',
        changeDatabaseHelper1: '데이터베이스 연관은 웹사이트 백업 및 복원에 사용됩니다.',
        changeDatabaseHelper2: '다른 데이터베이스로 전환하면 이전 백업을 복원할 수 없게 될 수 있습니다.',
        saveCustom: '템플릿으로 저장',
        rainyun: 'Rainyun',
        volcengine: 'volcengine',
        runtimePortHelper: '현재 실행 환경에 여러 포트가 있습니다. 프록시 포트를 선택하세요.',
        runtimePortWarn: '현재 실행 환경에 포트가 없습니다. 프록시할 수 없습니다',
        cacheWarn: '먼저 리버스 프록시의 캐시 스위치를 끄십시오',
        loadBalanceHelper:
            '로드 밸런싱을 생성한 후, "리버스 프록시"로 이동하여 프록시를 추가하고 백엔드 주소를 다음으로 설정하세요: http://<로드 밸런싱 이름>.',
        favorite: '즐겨찾기',
        cancelFavorite: '즐겨찾기 취소',
        useProxy: '프록시 사용',
        useProxyHelper: '패널 설정의 프록시 서버 주소 사용',
        westCN: '서부 디지털',
        openBaseDir: '사이트 간 공격 방지',
        openBaseDirHelper:
            'open_basedir는 PHP 파일 액세스 경로를 제한하여 사이트 간 액세스를 방지하고 보안을 향상시키는 데 사용됩니다',
        serverCacheTime: '서버 캐시 시간',
        serverCacheTimeHelper:
            '요청이 서버에서 캐시되는 시간. 이 기간 동안 동일한 요청은 원본 서버에 요청하지 않고 캐시된 결과를 직접 반환합니다.',
        browserCacheTime: '브라우저 캐시 시간',
        browserCacheTimeHelper:
            '정적 리소스가 브라우저 로컬에 캐시되는 시간, 중복 요청을 줄입니다. 유효기간 전에 사용자가 페이지를 새로 고치면 로컬 캐시가 직접 사용됩니다.',
        donotLinkeDB: '데이터베이스 연결하지 않기',
        toWebsiteDir: '웹사이트 디렉토리로 이동',
    },
    php: {
        short_open_tag: '짧은 태그 지원',
        max_execution_time: '최대 스크립트 실행 시간',
        max_input_time: '최대 입력 시간',
        memory_limit: '스크립트 메모리 제한',
        post_max_size: 'POST 데이터 최대 크기',
        file_uploads: '파일 업로드 허용 여부',
        upload_max_filesize: '업로드 가능한 파일의 최대 크기',
        max_file_uploads: '한 번에 업로드 가능한 파일의 최대 개수',
        default_socket_timeout: '소켓 타임아웃',
        error_reporting: '에러 수준',
        display_errors: '상세한 에러 정보 출력 여부',
        cgi_fix_pathinfo: 'pathinfo 활성화 여부',
        date_timezone: '시간대',
        disableFunction: '비활성화 함수',
        disableFunctionHelper: '비활성화할 함수를 입력하세요. 예: exec, 여러 항목은 쉼표로 구분',
        uploadMaxSize: '업로드 제한',
        indexHelper: 'PHP 웹사이트의 정상 작동을 위해 코드를 인덱스 디렉터리에 배치하고 이름 변경을 피하세요.',
        extensions: '확장 템플릿',
        extension: '확장',
        extensionHelper: '여러 확장은 쉼표로 구분하여 입력하세요.',
        toExtensionsList: '확장 목록 보기',
        containerConfig: '컨테이너 구성',
        containerConfigHelper: '환경 변수 및 기타 정보는 생성 후 구성 - 컨테이너 구성에서 수정할 수 있습니다',
        dateTimezoneHelper: '예: TZ=Asia/Shanghai（필요에 따라 추가하세요）',
    },
    nginx: {
        serverNamesHashBucketSizeHelper: '서버 이름의 해시 테이블 크기',
        clientHeaderBufferSizeHelper: '클라이언트가 요청한 헤더 버퍼 크기',
        clientMaxBodySizeHelper: '최대 업로드 파일 크기',
        keepaliveTimeoutHelper: '연결 시간 초과',
        gzipMinLengthHelper: '최소 압축 파일 크기',
        gzipCompLevelHelper: '압축률',
        gzipHelper: '전송을 위한 압축 활성화',
        connections: '활성 연결',
        accepts: '수락',
        handled: '처리됨',
        requests: '요청',
        reading: '읽기 중',
        writing: '쓰기 중',
        waiting: '대기 중',
        status: '현재 상태',
        configResource: '구성',
        saveAndReload: '저장 및 다시 로드',
        clearProxyCache: '리버스 프록시 캐시 삭제',
        clearProxyCacheWarn:
            '캐시가 구성된 모든 웹사이트에 영향을 미치며 OpenResty 가 다시 시작됩니다. 계속하시겠습니까?',
        create: '모듈 추가',
        update: '모듈 편집',
        params: '매개변수',
        packages: '패키지',
        script: '스크립트',
        module: '모듈',
        build: '빌드',
        buildWarn: 'OpenResty 빌드는 CPU와 메모리의 일정량을 예약해야 하며, 시간이 오래 걸릴 수 있으니 기다려 주세요.',
        mirrorUrl: '소프트웨어 소스',
        paramsHelper: '예: --add-module=/tmp/ngx_brotli',
        packagesHelper: '예: git,curl 쉼표로 구분',
        scriptHelper: '컴파일 전에 실행할 스크립트, 일반적으로 모듈 소스 코드 다운로드, 종속성 설치 등',
        buildHelper: '모듈 추가/수정 후 빌드를 클릭하세요. 빌드가 성공하면 OpenResty가 자동으로 재시작됩니다.',
        defaultHttps: 'HTTPS 변조 방지',
        defaultHttpsHelper1: '이를 활성화하면 HTTPS 변조 문제를 해결할 수 있습니다.',
    },
    ssl: {
        create: '요청',
        provider: '유형',
        manualCreate: '수동 생성됨',
        acmeAccount: 'ACME 계정',
        resolveDomain: '도메인 이름 확인',
        err: '오류',
        value: '레코드 값',
        dnsResolveHelper: 'DNS 해석 서비스 제공업체에서 다음 해석 레코드를 추가하세요:',
        detail: '세부 정보',
        msg: '정보',
        ssl: '인증서',
        key: '개인 키',
        startDate: '유효 시작 시간',
        organization: '발급 기관',
        renewConfirm: '도메인 이름 {0}에 대해 새 인증서를 갱신합니다. 계속하시겠습니까?',
        autoRenew: '자동 갱신',
        autoRenewHelper: '만료 30일 전에 자동으로 갱신',
        renewSuccess: '갱신 성공',
        renewWebsite: '이 인증서는 다음 웹사이트에 연결되었으며, 적용은 이러한 웹사이트에 동시에 적용됩니다.',
        createAcme: '계정 생성',
        acmeHelper: 'Acme 계정은 무료 인증서를 신청하는 데 사용됩니다.',
        upload: '가져오기',
        applyType: '유형',
        apply: '갱신',
        applyStart: '인증서 신청 시작',
        getDnsResolve: 'DNS 해석 값을 가져오는 중입니다. 잠시 기다려주세요...',
        selfSigned: '자체 서명된 CA',
        ca: '인증 기관',
        commonName: '공통 이름',
        caName: '인증 기관 이름',
        company: '기관 이름',
        department: '조직 단위 이름',
        city: '지역 이름',
        province: '주 또는 지방 이름',
        country: '국가 이름 (2자리 코드)',
        commonNameHelper: '예를 들어, ',
        selfSign: '인증서 발급',
        days: '유효 기간',
        domainHelper: '줄당 하나의 도메인 이름, * 및 IP 주소 지원',
        pushDir: '로컬 디렉토리로 인증서 푸시',
        dir: '디렉토리',
        pushDirHelper: '인증서 파일 "fullchain.pem" 및 키 파일 "privkey.pem"이 이 디렉토리에 생성됩니다.',
        organizationDetail: '조직 세부 정보',
        fromWebsite: '웹사이트에서 가져오기',
        dnsMauanlHelper: '수동 해석 모드에서는 생성 후 신청 버튼을 클릭하여 DNS 해석 값을 얻어야 합니다.',
        httpHelper:
            'HTTP 모드를 사용하려면 OpenResty를 설치해야 하며, 와일드카드 도메인 인증서 신청을 지원하지 않습니다.',
        buypassHelper: 'Buypass 는 중국 본토에서 접근할 수 없습니다.',
        googleHelper: 'EAB HmacKey 및 EAB kid 를 얻는 방법',
        googleCloudHelper: 'Google Cloud API 는 중국 본토 대부분에서 접근할 수 없습니다.',
        skipDNSCheck: 'DNS 확인 건너뛰기',
        skipDNSCheckHelper: '인증 요청 중 타임아웃 문제가 발생할 경우에만 선택하세요.',
        cfHelper: 'Global API Key 를 사용하지 마세요.',
        deprecated: '더 이상 지원되지 않습니다.',
        deprecatedHelper:
            '유지 관리가 중단되었으며 향후 버전에서 제외될 수 있습니다. Tencent Cloud 방법을 사용하여 분석하세요.',
        disableCNAME: 'CNAME 비활성화',
        disableCNAMEHelper: '도메인 이름에 CNAME 레코드가 있고 요청이 실패할 경우 선택하세요.',
        nameserver: 'DNS 서버',
        nameserverHelper: '사용자 지정 DNS 서버를 사용하여 도메인 이름을 확인합니다.',
        edit: '인증서 편집',
        execShell: '인증 요청 후 스크립트 실행',
        shell: '스크립트 내용',
        shellHelper:
            '스크립트의 기본 실행 디렉토리는 1Panel 설치 디렉토리입니다. 인증서가 로컬 디렉토리에 푸시되는 경우 실행 디렉토리는 인증서 푸시 디렉토리가 됩니다. 기본 실행 제한 시간은 30분입니다.',
        customAcme: '사용자 정의 ACME 서비스',
        customAcmeURL: 'ACME 서비스 URL',
        baiduCloud: '바이두 클라우드',
    },
    firewall: {
        create: '규칙 만들기',
        edit: '규칙 수정',
        ccDeny: 'CC 보호',
        ipWhiteList: 'IP 허용 목록',
        ipBlockList: 'IP 차단 목록',
        fileExtBlockList: '파일 확장자 차단 목록',
        urlWhiteList: 'URL 허용 목록',
        urlBlockList: 'URL 차단 목록',
        argsCheck: 'GET 파라미터 검사',
        postCheck: 'POST 파라미터 검사',
        cookieBlockList: '쿠키 차단 목록',
        dockerHelper:
            '리눅스 방화벽 "{0}"은(는) Docker 포트 매핑을 비활성화할 수 없습니다. 애플리케이션은 "앱 스토어 -> 설치됨" 페이지에서 매핑 포트를 해제할 수 있는 파라미터를 수정해야 합니다.',
        quickJump: '빠른 접근',
        used: '사용됨',
        unUsed: '사용 안 함',
        firewallHelper: '{0} 시스템 방화벽',
        firewallNotStart: '현재 시스템 방화벽이 활성화되지 않았습니다. 먼저 활성화하세요.',
        restartFirewallHelper: '이 작업은 현재 방화벽을 재시작합니다. 계속하시겠습니까?',
        stopFirewallHelper: '이 작업은 서버 보안을 잃게 만듭니다. 계속하시겠습니까?',
        startFirewallHelper: '방화벽이 활성화되면 서버 보안이 강화됩니다. 계속하시겠습니까?',
        noPing: 'Ping 비활성화',
        noPingTitle: 'Ping 비활성화',
        noPingHelper: '이 작업은 Ping 을 비활성화하며 서버는 ICMP 응답을 보내지 않게 됩니다. 계속하시겠습니까?',
        onPingHelper: '이 작업은 Ping 을 활성화하여 해커가 서버를 발견할 수 있습니다. 계속하시겠습니까?',
        changeStrategy: '{0} 전략 변경',
        changeStrategyIPHelper1:
            'IP 주소 전략을 [거부]로 변경합니다. 설정 후 해당 IP 주소는 서버 접근이 차단됩니다. 계속하시겠습니까?',
        changeStrategyIPHelper2:
            'IP 주소 전략을 [허용]으로 변경합니다. 설정 후 해당 IP 주소는 정상적으로 접근할 수 있습니다. 계속하시겠습니까?',
        changeStrategyPortHelper1:
            '포트 정책을 [차단]으로 변경합니다. 설정 후 외부 접근이 차단됩니다. 계속하시겠습니까?',
        changeStrategyPortHelper2:
            '포트 정책을 [허용]으로 변경합니다. 설정 후 정상적으로 포트 접근이 복원됩니다. 계속하시겠습니까?',
        stop: '정지',
        portFormatError: '이 필드는 유효한 포트이어야 합니다.',
        portHelper1: '여러 포트, 예: 8080, 8081',
        portHelper2: '포트 범위, 예: 8080-8089',
        changeStrategyHelper:
            '[{1}] {0} 전략을 [{2}]로 변경합니다. 설정 후 {0}은(는) {2}로 외부 접근을 허용합니다. 계속하시겠습니까?',
        portHelper: '여러 포트를 입력할 수 있습니다. 예: 80, 81 또는 포트 범위, 예: 80-88',
        strategy: '전략',
        accept: '허용',
        drop: '차단',
        anyWhere: '어디든지',
        address: '지정된 IP',
        addressHelper: 'IP 주소 또는 IP 범위를 지원합니다.',
        allow: '허용',
        deny: '거부',
        addressFormatError: '이 필드는 유효한 IP 주소여야 합니다.',
        addressHelper1: "IP 주소 또는 IP 범위가 필요합니다. 예: '************' 또는 '***********/24'.",
        addressHelper2: "여러 IP 주소는 쉼표로 구분합니다. 예: '************, **********/24'.",
        allIP: '모든 IP',
        portRule: '규칙 | 규칙들',
        createPortRule: '@:commons.button.create @.lower:firewall.portRule',
        forwardRule: '포트 전달 규칙 | 포트 전달 규칙들',
        createForwardRule: '@:commons.button.create @:firewall.forwardRule',
        ipRule: 'IP 규칙 | IP 규칙들',
        createIpRule: '@:commons.button.create @:firewall.ipRule',
        userAgent: 'User-Agent 필터',
        sourcePort: '소스 포트',
        targetIP: '대상 IP',
        targetPort: '대상 포트',
        forwardHelper1: "로컬 포트로 전달하려면, 대상 IP 를 '127.0.0.1'로 설정해야 합니다.",
        forwardHelper2: '대상 IP 를 비워두면 로컬 포트로 전달됩니다.',
        forwardHelper3: 'IPv4 포트 전달만 지원됩니다.',
    },
    runtime: {
        runtime: '실행 환경',
        workDir: '작업 디렉토리',
        create: '실행 환경 생성',
        localHelper: '로컬 운영 환경은 직접 설치해야 합니다.',
        versionHelper: 'PHP 버전, 예: v8.0',
        buildHelper:
            '확장 기능이 많을수록 이미지 생성 시 CPU 사용량이 증가합니다. 환경 생성 후 확장 기능을 설치하는 것도 가능합니다.',
        openrestyWarn: 'PHP는 OpenResty 버전 ******** 이상으로 업그레이드해야 사용 가능합니다.',
        toupgrade: '업그레이드하기',
        edit: '실행 환경 수정',
        extendHelper:
            '목록에 없는 확장 프로그램은 수동으로 입력하고 선택할 수 있습니다. 예를 들어 "sockets"를 입력한 후 드롭다운 목록에서 첫 번째 옵션을 선택하여 확장 목록을 확인하세요.',
        rebuildHelper: '확장 기능을 수정한 후에는 PHP 애플리케이션을 재빌드해야 적용됩니다.',
        rebuild: 'PHP 애플리케이션 재빌드',
        source: 'PHP 확장 소스',
        ustc: '중국과학기술대학',
        netease: '네티이즈',
        aliyun: '알리바바 클라우드',
        default: '기본',
        tsinghua: '칭화대학교',
        xtomhk: 'XTOM 미러 사이트 (홍콩)',
        xtom: 'XTOM 미러 사이트 (전 세계)',
        phpsourceHelper: '네트워크 환경에 맞는 적절한 소스를 선택하세요.',
        appPort: '앱 포트',
        externalPort: '외부 포트',
        packageManager: '패키지 관리자',
        codeDir: '코드 디렉터리',
        appPortHelper: '애플리케이션이 사용하는 포트.',
        externalPortHelper: '외부에 노출된 포트.',
        runScript: '실행 스크립트',
        runScriptHelper: '시작 명령 목록은 소스 디렉터리의 package.json 파일에서 분석됩니다.',
        open: '열기',
        operatorHelper: '{0} 작업이 선택된 운영 환경에서 수행됩니다. 계속하시겠습니까?',
        taobao: '타오바오',
        tencent: '텐센트',
        imageSource: '이미지 소스',
        moduleManager: '모듈 관리',
        module: '모듈',
        nodeOperatorHelper:
            '{0} {1} 모듈인가요? 이 작업은 운영 환경에 비정상을 일으킬 수 있으므로 진행 전에 확인해 주세요.',
        customScript: '사용자 정의 시작 명령',
        customScriptHelper: '전체 시작 명령을 제공하세요. 예: "npm run start".',
        portError: '포트를 중복 사용하지 마세요.',
        systemRestartHelper: '상태 설명: 중단 - 시스템 재시작으로 인해 상태 가져오기가 실패했습니다.',
        javaScriptHelper: '전체 시작 명령을 제공하세요. 예: "java -jar halo.jar -Xmx1024M -Xms256M".',
        javaDirHelper: '디렉터리는 jar 파일을 포함해야 하며, 하위 디렉터리도 허용됩니다.',
        goHelper: '전체 시작 명령을 제공하세요. 예: "go run main.go" 또는 "./main".',
        goDirHelper: '디렉터리 또는 하위 디렉터리는 Go 또는 바이너리 파일을 포함해야 합니다.',
        pythonHelper:
            '전체 시작 명령을 제공하세요. 예: "pip install -r requirements.txt && python manage.py runserver 0.0.0.0:5000".',
        dotnetHelper: '완전한 시작 명령을 입력하세요. 예: dotnet MyWebApp.dll',
        dirHelper: '주의: 컨테이너 내의 디렉토리 경로를 입력하세요',
        concurrency: '동시성 체계',
        loadStatus: '부하 상태',
    },
    process: {
        pid: '프로세스 ID',
        ppid: '부모 PID',
        numThreads: '스레드',
        memory: '메모리',
        diskRead: '디스크 읽기',
        diskWrite: '디스크 쓰기',
        netSent: '업링크',
        netRecv: '다운스트림',
        numConnections: '연결',
        startTime: '시작 시간',
        state: '상태',
        running: '실행 중',
        sleep: '대기 중',
        stop: '중지',
        idle: '유휴',
        zombie: '좀비 프로세스',
        wait: '대기',
        lock: '잠금',
        blocked: '차단됨',
        cmdLine: '시작 명령',
        basic: '기본',
        mem: '메모리',
        openFiles: '열린 파일',
        env: '환경 변수',
        noenv: '없음',
        net: '네트워크 연결',
        laddr: '출발지 주소/포트',
        raddr: '목적지 주소/포트',
        stopProcess: '종료',
        viewDetails: '세부 사항',
        stopProcessWarn: '이 프로세스(PID:{0})를 종료하시겠습니까?',
        processName: '프로세스 이름',
    },
    tool: {
        supervisor: {
            loadStatusErr: '프로세스 상태를 가져오지 못했습니다. supervisor 서비스의 상태를 확인하세요.',
            notSupport: 'Supervisor 서비스를 감지하지 못했습니다. 스크립트 라이브러리 페이지에서 수동으로 설치하세요',
            list: '데몬 프로세스 | 데몬 프로세스들',
            config: 'Supervisor 설정',
            primaryConfig: '주 설정 파일 위치',
            notSupportCtl: `supervisorctl 이 감지되지 않았습니다.  스크립트 라이브러리 페이지에서 수동으로 설치하세요.`,
            user: '사용자',
            command: '명령어',
            dir: '디렉토리',
            numprocs: '프로세스 수',
            initWarn:
                '이 작업은 주 설정 파일의 "[include]" 섹션에 있는 "files" 값을 수정합니다. 다른 설정 파일의 디렉토리는 "{1Panel 설치 디렉토리}/1panel/tools/supervisord/supervisor.d/"입니다.',
            operatorHelper: '{1} 작업을 {0}에서 수행합니다. 계속하시겠습니까?',
            uptime: '운영 시간',
            notStartWarn: `Supervisor 가 시작되지 않았습니다. 먼저 시작하세요.`,
            serviceName: '서비스 이름',
            initHelper:
                'Supervisor 서비스가 감지되었지만 초기화되지 않았습니다. 상단 상태 표시줄의 초기화 버튼을 클릭하여 구성하세요.',
            serviceNameHelper: 'systemctl 로 관리되는 Supervisor 서비스 이름, 보통 supervisor 또는 supervisord입니다.',
            restartHelper:
                '이 작업은 초기화 후 서비스를 재시작합니다. 이로 인해 기존의 모든 데몬 프로세스가 중지됩니다.',
            RUNNING: '실행 중',
            STOPPED: '중지됨',
            STOPPING: '중지 중',
            STARTING: '시작 중',
            FATAL: '시작 실패',
            BACKOFF: '시작 예외',
            ERROR: '오류',
            statusCode: '상태 코드',
            manage: '관리',
            autoRestart: '자동 재시작',
            EXITED: '종료됨',
            autoRestartHelper: '프로그램이 비정상적으로 종료된 후 자동으로 재시작할지 여부',
            autoStart: '자동 시작',
            autoStartHelper: 'Supervisor 시작 후 서비스를 자동으로 시작할지 여부',
        },
    },
    xpack: {
        expiresTrialAlert:
            '친절한 알림: 귀하의 Pro 체험판이 {0}일 후 만료되며, 모든 Pro 기능에 더 이상 접근할 수 없습니다. 제때 갱신하거나 전체 버전으로 업그레이드하시기 바랍니다.',
        expiresAlert:
            '친절한 알림: 귀하의 Pro 라이선스가 {0}일 후 만료되며, 모든 Pro 기능에 더 이상 접근할 수 없습니다. 지속적인 사용을 위해 신속하게 갱신하시기 바랍니다.',
        menu: 'Pro',
        upage: 'AI 웹사이트 빌더',
        app: {
            app: 'APP',
            title: '패널 별칭',
            titleHelper: '패널 별칭은 APP 에서 표시되는 데 사용됩니다(기본 패널 별칭)',
            qrCode: 'QR 코드',
            apiStatusHelper: '패널 APP 는 API 인터페이스 기능을 활성화해야 합니다',
            apiInterfaceHelper: '패널 API 인터페이스 액세스를 지원합니다(패널 앱에서 이 기능을 활성화해야 합니다)',
            apiInterfaceHelper1:
                '패널 앱의 액세스에는 방문자를 화이트리스트에 추가해야 하며, 고정 IP가 아닌 경우 0.0.0.0/0(모든 IPv4), ::/0(모든 IPv6) 을 추가하는 것이 좋습니다',
            qrCodeExpired: '새로 고침 시간',
            apiLeakageHelper: 'QR 코드를 누출하지 마십시오. 신뢰할 수 있는 환경에서만 사용하십시오.',
        },
        waf: {
            WAF: 'WAF',
            blackWhite: '블랙 및 화이트 목록',
            globalSetting: '전역 설정',
            websiteSetting: '웹사이트 설정',
            blockRecords: '차단된 기록',
            world: '전 세계',
            china: '중국',
            intercept: '차단',
            request: '요청',
            count4xx: '4xx 수량',
            count5xx: '5xx 수량',
            todayStatus: '오늘의 상태',
            reqMap: '공격 맵 (최근 30일)',
            resource: '출처',
            count: '수량',
            hight: '높음',
            low: '낮음',
            reqCount: '요청 수',
            interceptCount: '차단 수',
            requestTrends: '요청 트렌드 (최근 7일)',
            interceptTrends: '차단 트렌드 (최근 7일)',
            whiteList: '화이트 목록',
            blackList: '블랙 목록',
            ipBlackListHelper: '블랙 목록에 있는 IP 주소는 웹사이트에 접근할 수 없습니다.',
            ipWhiteListHelper: '화이트 목록에 있는 IP 주소는 모든 제한을 우회합니다.',
            uaBlackListHelper: '블랙 목록에 있는 User-Agent 값의 요청은 차단됩니다.',
            uaWhiteListHelper: '화이트 목록에 있는 User-Agent 값의 요청은 모든 제한을 우회합니다.',
            urlBlackListHelper: '블랙 목록에 있는 URL 로의 요청은 차단됩니다.',
            urlWhiteListHelper: '화이트 목록에 있는 URL 로의 요청은 모든 제한을 우회합니다.',
            ccHelper: '같은 IP 에서 {0}초 이내에 {1}회 이상 요청을 받으면 해당 IP 는 {2}동안 차단됩니다.',
            blockTime: '차단 기간',
            attackHelper: '누적 차단이 {0}초 이내에 {1}회를 초과하면 해당 IP 는 {2}동안 차단됩니다.',
            notFoundHelper: '누적된 요청이 {0}초 이내에 404 오류를 {1}번 이상 반환하면 해당 IP 는 {2}동안 차단됩니다.',
            frequencyLimit: '접속 빈도 제한',
            regionLimit: '지역 제한',
            defaultRule: '기본 규칙',
            accessFrequencyLimit: '접속 빈도 제한',
            attackLimit: '공격 빈도 제한',
            notFoundLimit: '404 빈도 제한',
            urlLimit: 'URL 빈도 제한',
            urlLimitHelper: '단일 URL 의 접속 빈도를 설정합니다.',
            sqliDefense: 'SQL 인젝션 보호',
            sqliHelper: '요청에서 SQL 인젝션을 감지하여 차단합니다.',
            xssHelper: '요청에서 XSS 를 감지하여 차단합니다.',
            xssDefense: 'XSS 보호',
            uaDefense: '악성 User-Agent 규칙',
            uaHelper: '일반적인 악성 봇을 식별하는 규칙이 포함되어 있습니다.',
            argsDefense: '악성 파라미터 규칙',
            argsHelper: '악성 파라미터를 포함한 요청을 차단합니다.',
            cookieDefense: '악성 쿠키 규칙',
            cookieHelper: '악성 쿠키가 요청에 포함되지 않도록 차단합니다.',
            headerDefense: '악성 헤더 규칙',
            headerHelper: '악성 헤더가 포함된 요청을 차단합니다.',
            httpRule: 'HTTP 요청 방법 규칙',
            httpHelper:
                '허용된 접근 방식 유형을 설정합니다. 특정 접근 방식을 제한하려면 해당 유형의 버튼을 끄십시오. 예를 들어: GET 방식만 허용하려면 GET 을 제외한 다른 방식의 버튼을 끄세요.',
            geoRule: '지역별 접근 제한',
            geoHelper:
                '특정 지역에서만 웹사이트 접근을 허용하고 다른 지역에서의 접근을 차단할 수 있습니다. 예를 들어: 중국 본토에서만 접근을 허용하고, 그 외 지역에서의 접근을 차단할 수 있습니다.',
            ipLocation: 'IP 위치',
            action: '동작',
            ruleType: '공격 유형',
            ipHelper: 'IP 주소를 입력하세요',
            attackLog: '공격 로그',
            rule: '규칙',
            ipArr: 'IPV4 범위',
            ipStart: '시작 IP',
            ipEnd: '끝 IP',
            ipv4: 'IPv4',
            ipv6: 'IPv6',
            urlDefense: 'URL 규칙',
            urlHelper: '금지된 URL',
            dirFilter: '디렉터리 필터',
            sqlInject: 'SQL 인젝션',
            xss: 'XSS',
            phpExec: 'PHP 스크립트 실행',
            oneWordTrojan: '단어 트로이',
            appFilter: '위험한 디렉터리 필터링',
            webshell: '웹쉘',
            args: '악성 파라미터',
            protocolFilter: '프로토콜 필터',
            javaFilter: '자바 위험 파일 필터링',
            scannerFilter: '스캐너 필터',
            escapeFilter: '이스케이프 필터',
            customRule: '사용자 정의 규칙',
            httpMethod: 'HTTP 메소드 필터',
            fileExt: '파일 업로드 제한',
            fileExtHelper: '업로드 제한 파일 확장자',
            deny: '금지',
            allow: '허용',
            field: '객체',
            pattern: '조건',
            ruleContent: '내용',
            contain: '포함',
            equal: '같음',
            regex: '정규 표현식',
            notEqual: '같지 않음',
            customRuleHelper: '지정된 조건에 따라 동작을 수행합니다.',
            actionAllow: '허용',
            blockIP: 'IP 차단',
            code: '상태 코드 반환',
            noRes: '연결 끊기 (444)',
            badReq: '잘못된 매개변수 (400)',
            forbidden: '접근 금지 (403)',
            serverErr: '서버 오류 (500)',
            resHtml: '응답 페이지',
            allowHelper: '허용된 접근은 후속 WAF 규칙을 건너뛰게 하므로 주의해서 사용하세요.',
            captcha: '사람-컴퓨터 검증',
            fiveSeconds: '5초 검증',
            location: '지역',
            redisConfig: 'Redis 설정',
            redisHelper: 'Redis 를 활성화하여 일시적으로 차단된 IP를 유지합니다.',
            wafHelper: 'WAF 를 종료하면 모든 웹사이트 보호가 해제됩니다.',
            attackIP: '공격 IP',
            attackParam: '공격 세부사항',
            execRule: '적용된 규칙',
            acl: 'ACL',
            sql: 'SQL 인젝션',
            cc: '접속 빈도 제한',
            isBlocking: '차단됨',
            isFree: '차단 해제됨',
            unLock: '차단 해제',
            unLockHelper: 'IP: {0}의 차단을 해제하시겠습니까?',
            saveDefault: '기본값 저장',
            saveToWebsite: '웹사이트에 적용',
            saveToWebsiteHelper: '현재 설정을 모든 웹사이트에 적용하시겠습니까?',
            websiteHelper: '웹사이트를 생성할 때의 기본 설정입니다. 수정 사항은 웹사이트에 적용해야 효력이 있습니다.',
            websiteHelper2: '웹사이트 생성 시 기본 설정이 제공됩니다. 웹사이트에서 해당 설정을 수정해 주세요.',
            ipGroup: 'IP 그룹',
            ipGroupHelper:
                '한 줄에 하나의 IP 또는 IP 범위를 입력하세요, IPv4 및 IPv6 을 지원합니다. 예: *********** 또는 ***********/24',
            ipBlack: 'IP 블랙리스트',
            openRestyAlert: 'OpenResty 버전은 {0} 이상이어야 합니다.',
            initAlert:
                '처음 사용 시 초기화가 필요하며, 웹사이트 설정 파일이 수정됩니다. 기존 WAF 설정은 손실될 수 있습니다. 미리 백업해 주세요.',
            initHelper: '초기화 작업은 기존 WAF 설정을 삭제합니다. 초기화하시겠습니까?',
            mainSwitch: '메인 스위치',
            websiteAlert: '먼저 웹사이트를 생성해 주세요.',
            defaultUrlBlack: 'URL 규칙',
            htmlRes: '차단 페이지',
            urlSearchHelper: 'URL 을 입력하여 유사 검색을 지원합니다.',
            toCreate: '생성',
            closeWaf: 'WAF 종료',
            closeWafHelper: 'WAF 를 종료하면 웹사이트 보호가 해제됩니다. 계속하시겠습니까?',
            addblack: '블랙 추가',
            addwhite: '화이트 추가',
            addblackHelper: 'IP: {0}를 기본 블랙리스트에 추가하시겠습니까?',
            addwhiteHelper: 'IP: {0}를 기본 화이트리스트에 추가하시겠습니까?',
            defaultUaBlack: 'User-Agent 규칙',
            defaultIpBlack: '악성 IP 그룹',
            cookie: '쿠키 규칙',
            urlBlack: 'URL 블랙리스트',
            uaBlack: 'User-Agent 블랙리스트',
            attackCount: '공격 빈도 제한',
            fileExtCheck: '파일 업로드 제한',
            geoRestrict: '지역 접근 제한',
            attacklog: '차단 기록',
            unknownWebsite: '허가되지 않은 도메인 접근',
            geoRuleEmpty: '지역은 비워 둘 수 없습니다.',
            unknown: '웹사이트 없음',
            geo: '지역 제한',
            revertHtml: '{0}을 기본 페이지로 복원하시겠습니까?',
            five_seconds: '5초 검증',
            header: '헤더 규칙',
            methodWhite: 'HTTP 규칙',
            expiryDate: '만료 날짜',
            expiryDateHelper: '검증을 통과한 후 유효 기간 내에는 더 이상 검증하지 않습니다.',
            defaultIpBlackHelper: '인터넷에서 수집된 일부 악성 IP로 접근을 차단합니다.',
            notFoundCount: '404 빈도 제한',
            matchValue: '매칭 값',
            headerName: '이 필드는 특수 문자로 시작할 수 없으며,영어, 숫자, -, 길이 3-30 지원',
            cdnHelper: 'CDN 을 사용하는 웹사이트는 여기서 원본 IP를 확인할 수 있습니다.',
            clearLogWarn: '로그를 삭제하면 복구할 수 없습니다. 계속하시겠습니까?',
            commonRuleHelper: '규칙은 유사 매칭 방식입니다.',
            blockIPHelper:
                '차단된 IP는 OpenResty 에 일시적으로 저장되며, OpenResty 재시작 시 차단이 해제됩니다. 차단 기능을 통해 영구적으로 차단할 수 있습니다.',
            addWhiteUrlHelper: 'URL {0}를 화이트리스트에 추가하시겠습니까?',
            dashHelper: '커뮤니티 버전은 전역 설정 및 웹사이트 설정에서 기능을 사용할 수 있습니다.',
            wafStatusHelper: 'WAF 가 활성화되지 않았습니다. 전역 설정에서 활성화해 주세요.',
            ccMode: '모드',
            global: '전역 모드',
            uriMode: 'URL 모드',
            globalHelper: '전역 모드: 특정 시간 내에 모든 URL에 대한 요청 수가 임계값을 초과하면 트리거됩니다.',
            uriModeHelper: 'URL 모드: 특정 시간 내에 개별 URL에 대한 요청 수가 임계값을 초과하면 트리거됩니다.',
            ip: 'IP 블랙리스트',
            globalSettingHelper:
                '[웹사이트] 태그가 있는 설정은 [웹사이트 설정]에서 활성화해야 하며, 전역 설정은 새로 생성된 웹사이트의 기본 설정입니다.',
            globalSettingHelper2: '[전역 설정]과 [웹사이트 설정]에서 모두 활성화해야 설정이 적용됩니다.',
            urlCCHelper: '{0} 초 이내에 이 URL에 대해 {1} 회를 초과하는 요청이 있어 이 IP를 차단합니다 {2}',
            urlCCHelper2: 'URL에 매개변수를 포함할 수 없습니다',
            notContain: '포함하지 않음',
            urlcc: 'URL 빈도 제한',
            method: '요청 유형',
            addIpsToBlock: 'IP 일괄 차단',
            addUrlsToWhite: 'URL을 일괄 허용 목록에 추가',
            noBlackIp: 'IP가 이미 차단되어 있으므로 다시 차단할 필요가 없습니다',
            noWhiteUrl: 'URL이 이미 허용 목록에 포함되어 있으므로 다시 추가할 필요가 없습니다',
            spiderIpHelper:
                '바이두, 빙, 구글, 360, 신마, 소구, 바이트댄스, DuckDuckGo, Yandex가 포함됩니다. 이를 끄면 모든 스파이더의 접근이 차단됩니다.',
            spiderIp: '스파이더 IP 풀',
            geoIp: 'IP 주소 라이브러리',
            geoIpHelper: 'IP의 지리적 위치를 확인하는 데 사용됩니다',
            stat: '공격 보고서',
            statTitle: '보고서',
            attackIp: '공격 IP',
            attackCountNum: '공격 횟수',
            percent: '비율',
            addblackUrlHelper: 'URL: {0}을(를) 기본 블랙리스트에 추가할까요?',
            rce: '원격 코드 실행',
            software: '소프트웨어',
            cveHelper: '일반적인 소프트웨어 및 프레임워크의 취약점을 포함',
            vulnCheck: '보충 규칙',
            ssrf: 'SSRF 취약점',
            afr: '임의 파일 읽기',
            ua: '무단 액세스',
            id: '정보 누출',
            aa: '인증 우회',
            dr: '디렉토리 순회',
            xxe: 'XXE 취약점',
            suid: '직렬화 취약점',
            dos: '서비스 거부 취약점',
            afd: '임의 파일 다운로드',
            sqlInjection: 'SQL 인젝션',
            afw: '임의 파일 쓰기',
            il: '정보 유출',
            clearAllLog: '모든 로그 삭제',
            exportLog: '로그 내보내기',
            appRule: '애플리케이션 규칙',
            appRuleHelper:
                '일반적인 애플리케이션 규칙. 활성화하면 오탐지를 줄일 수 있습니다. 하나의 웹사이트는 하나의 규칙만 사용할 수 있습니다',
            logExternal: '기록 유형 제외',
            ipWhite: 'IP 허용 목록',
            urlWhite: 'URL 허용 목록',
            uaWhite: '사용자 에이전트 허용 목록',
            logExternalHelper:
                '제외된 기록 유형은 로그에 기록되지 않습니다. 블랙리스트/허용 목록, 지역 액세스 제한, 사용자 정의 규칙은 많은 로그를 생성합니다. 제외를 권장합니다',
            ssti: 'SSTI 공격',
            crlf: 'CRLF 인젝션',
            strict: '엄격 모드',
            strictHelper: '더 엄격한 규칙을 사용하여 요청을 검증합니다',
            saveLog: '로그 저장',
            remoteURLHelper: '원격 URL은 한 줄에 하나의 IP만 포함하고 다른 문자는 포함하지 않아야 합니다',
            notFound: 'Not Found (404)',
            serviceUnavailable: '서비스 불가 (503)',
            gatewayTimeout: '게이트웨이 시간 초과 (504)',
            belongToIpGroup: 'IP 그룹에 속함',
            notBelongToIpGroup: 'IP 그룹에 속하지 않음',
            unknownWebsiteKey: '알 수 없는 도메인',
            special: '특수 문자',
        },
        monitor: {
            name: '웹사이트 모니터링',
            pv: '페이지 조회수',
            uv: '고유 방문자 수',
            flow: '트래픽 흐름',
            ip: 'IP',
            spider: '스파이더',
            visitors: '방문자 추세',
            today: '오늘',
            last7days: '지난 7일',
            last30days: '지난 30일',
            uvMap: '방문자 지도 (30일)',
            qps: '실시간 요청 (분당)',
            flowSec: '실시간 트래픽 (분당)',
            excludeCode: '상태 코드 제외',
            excludeUrl: 'URL 제외',
            excludeExt: '확장자 제외',
            cdnHelper: 'CDN 제공 헤더에서 실제 IP 가져오기',
            reqRank: '방문 순위',
            refererDomain: '참조 도메인',
            os: '운영 체제',
            browser: '브라우저/클라이언트',
            device: '장치',
            showMore: '더보기',
            unknown: '기타',
            pc: '컴퓨터',
            mobile: '모바일 장치',
            wechat: '위챗',
            machine: '머신',
            tencent: '텐센트 브라우저',
            ucweb: 'UC 브라우저',
            '2345explorer': '2345 브라우저',
            huaweibrowser: '화웨이 브라우저',
            log: '요청 로그',
            statusCode: '상태 코드',
            requestTime: '응답 시간',
            flowRes: '응답 트래픽',
            method: '요청 메서드',
            statusCodeHelper: '상태 코드를 입력하세요',
            statusCodeError: '유효하지 않은 상태 코드 유형',
            methodHelper: '요청 메서드를 입력하세요',
            all: '전체',
            baidu: '바이두',
            google: '구글',
            bing: '빙',
            bytes: '오늘의 헤드라인',
            sogou: '소구',
            failed: '오류',
            ipCount: 'IP 수',
            spiderCount: '스파이더 요청',
            averageReqTime: '평균 응답 시간',
            totalFlow: '총 트래픽',
            logSize: '로그 파일 크기',
            realIPType: '실제 IP 가져오기 방법',
            fromHeader: 'HTTP 헤더에서 가져오기',
            fromHeaders: '헤더 목록에서 가져오기',
            header: 'HTTP 헤더',
            cdnConfig: 'CDN 구성',
            xff1: 'X-Forwarded-For 의 1단계 프록시',
            xff2: 'X-Forwarded-For 의 2단계 프록시',
            xff3: 'X-Forwarded-For 의 3단계 프록시',
            xffHelper:
                '예: X-Forwarded-For: <client>,<proxy1>,<proxy2>,<proxy3>. 최상위 프록시는 마지막 IP <proxy3>를 가져옵니다',
            headersHelper:
                '일반적으로 사용되는 CDN HTTP 헤더에서 실제 IP를 가져오며, 사용 가능한 첫 번째 값을 선택합니다',
            monitorCDNHelper: '웹사이트 모니터링을 위한 CDN 구성을 수정하면 WAF CDN 설정도 업데이트됩니다',
            wafCDNHelper: 'WAF CDN 구성을 수정하면 웹사이트 모니터링 CDN 설정도 업데이트됩니다',
            statusErr: '잘못된 상태 코드 형식',
            shenma: '션마 검색',
            duckduckgo: '덕덕고',
            '360': '360 검색',
            excludeUri: 'URI 제외',
            top100Helper: '상위 100 개의 데이터를 표시합니다',
            logSaveDay: '로그 보관 기간 (일)',
            cros: '크롬 OS',
            theworld: '더월드 브라우저',
            edge: '마이크로소프트 엣지',
            maxthon: '맥스톤 브라우저',
            monitorStatusHelper: '모니터링이 활성화되지 않았습니다. 설정에서 활성화하세요',
            excludeIp: 'IP 주소 제외',
            excludeUa: '사용자 에이전트 제외',
            remotePort: '원격 포트',
            unknown_browser: '알 수 없음',
            unknown_os: '알 수 없음',
            unknown_device: '알 수 없음',
            logSaveSize: '최대 로그 저장 크기',
            logSaveSizeHelper: '이것은 단일 웹사이트의 로그 저장 크기입니다',
            '360se': '360 보안 브라우저',
            websites: '웹사이트 목록',
            trend: '추세 통계',
            reqCount: '요청 수',
            uriHelper: '/test/* 또는 /*/index.php를 사용하여 Uri를 제외할 수 있습니다',
        },
        tamper: {
            tamper: '웹사이트 변조 방지',
            ignoreTemplate: '디렉토리 템플릿 제외',
            protectTemplate: '파일 템플릿 보호',
            templateContent: '템플릿 내용',
            template: '템플릿',
            tamperHelper1:
                '원클릭 배포 유형의 웹사이트는 애플리케이션 디렉토리 변조 방지 기능을 활성화하는 것이 좋습니다; 웹사이트가 정상적으로 작동하지 않거나 백업 및 복구에 실패하는 경우, 먼저 변조 방지 기능을 비활성화하십시오;',
            tamperHelper2:
                '제외된 디렉토리 외부의 보호된 파일에 대한 읽기, 쓰기, 삭제, 권한 및 소유자 변경 작업이 제한됩니다',
            tamperPath: '보호 디렉토리',
            tamperPathEdit: '경로 수정',
            log: '차단 로그',
            totalProtect: '총 보호',
            todayProtect: '오늘의 보호',
            addRule: '규칙 추가',
            ignore: '디렉토리 제외',
            ignoreHelper: '한 줄에 하나씩, 예: \ntmp\n./tmp',
            ignoreTemplateHelper: '무시할 폴더 이름을 추가하고, 쉼표로 구분하십시오. 예: tmp,cache',
            templateRule: '길이 1-512, 이름에 {0}와 같은 기호를 포함할 수 없습니다',
            ignoreHelper1: '무시할 폴더 이름이나 특정 경로를 추가하십시오',
            ignoreHelper2: '특정 폴더를 무시하려면 ./로 시작하는 상대 경로를 사용하십시오',
            protect: '파일 보호',
            protectHelper: '한 줄에 하나씩, 예: \npng\n./test.css',
            protectTemplateHelper: '무시할 파일 이름이나 확장자를 추가하고, 쉼표로 구분하십시오. 예: conf,.css',
            protectHelper1: '파일 이름, 확장자 또는 특정 파일을 보호할 수 있습니다',
            protectHelper2: '특정 파일을 보호하려면 ./로 시작하는 상대 경로를 사용하십시오',
            enableHelper:
                '다음 웹사이트의 변조 방지 기능이 활성화됩니다. 웹사이트 보안을 강화하기 위해 계속하시겠습니까?',
            disableHelper: '다음 웹사이트의 변조 방지 기능이 비활성화됩니다. 계속하시겠습니까?',
        },
        setting: {
            setting: '패널 설정',
            title: '패널 설명',
            titleHelper: '사용자 로그인 페이지에 표시됩니다 (예: Linux 서버 운영 및 유지 관리 패널, 권장 길이: 8-15자)',
            logo: '로고 (텍스트 없음)',
            logoHelper: '메뉴가 축소되었을 때 관리 페이지의 왼쪽 상단에 표시됩니다 (권장 이미지 크기: 82px*82px)',
            logoWithText: '로고 (텍스트 포함)',
            logoWithTextHelper:
                '메뉴가 확장되었을 때 관리 페이지의 왼쪽 상단에 표시됩니다 (권장 이미지 크기: 185px*55px)',
            favicon: '웹사이트 아이콘',
            faviconHelper: '웹사이트 아이콘 (권장 이미지 크기: 16px*16px)',
            reUpload: '파일 선택',
            setDefault: '기본값 복원',
            setHelper: '현재 설정이 저장됩니다. 계속하시겠습니까?',
            setDefaultHelper: '모든 패널 설정이 기본값으로 복원됩니다. 계속하시겠습니까?',
            logoGroup: '로고',
            imageGroup: '이미지',
            loginImage: '로그인 페이지 이미지',
            loginImageHelper: '로그인 페이지에 표시됩니다 (권장 크기: 500*416px)',
            loginBgType: '로그인 배경 유형',
            loginBgImage: '로그인 배경 이미지',
            loginBgImageHelper: '로그인 페이지의 배경 이미지로 표시됩니다 (권장 크기: 1920*1080px)',
            loginBgColor: '로그인 배경 색상',
            loginBgColorHelper: '로그인 페이지의 배경 색상으로 표시됩니다',
            image: '이미지',
            bgColor: '배경 색상',
            loginGroup: '로그인 페이지',
            loginBtnLinkColor: '버튼/링크 색상',
            loginBtnLinkColorHelper: '로그인 페이지의 버튼/링크 색상으로 표시됩니다',
        },
        helper: {
            wafTitle1: '차단 지도',
            wafContent1: '지난 30일 동안의 차단 지역 분포를 표시합니다.',
            wafTitle2: '지역별 접근 제한',
            wafContent2: '지리적 위치에 따라 웹사이트 접근 소스를 제한합니다.',
            wafTitle3: '사용자 지정 차단 페이지',
            wafContent3: '요청이 차단된 후 표시할 사용자 지정 페이지를 생성합니다.',
            wafTitle4: '사용자 지정 규칙 (ACL)',
            wafContent4: '사용자 지정 규칙에 따라 요청을 차단합니다.',

            tamperTitle1: '파일 무결성 모니터링',
            tamperContent1: '핵심 파일, 스크립트 파일, 구성 파일을 포함한 웹사이트 파일의 무결성을 모니터링합니다.',
            tamperTitle2: '실시간 스캔 및 감지',
            tamperContent2: '웹사이트 파일 시스템을 실시간으로 스캔하여 비정상적이거나 변조된 파일을 감지합니다.',
            tamperTitle3: '보안 권한 설정',
            tamperContent3:
                '적절한 권한 설정과 접근 제어 정책을 통해 웹사이트 파일 접근을 제한하여 잠재적 공격 면적을 줄입니다.',
            tamperTitle4: '로그 기록 및 분석',
            tamperContent4:
                '파일 접근 및 작업 로그를 기록하여 관리자가 감사 및 분석을 수행할 수 있도록 하고, 잠재적 보안 위협을 식별합니다.',

            settingTitle1: '사용자 정의 환영 메시지',
            settingContent1: '1Panel 로그인 페이지에 사용자 정의 환영 메시지를 설정합니다.',
            settingTitle2: '사용자 정의 로고',
            settingContent2: '브랜드명이나 텍스트가 포함된 로고 이미지를 업로드할 수 있습니다.',
            settingTitle3: '사용자 정의 웹사이트 아이콘',
            settingContent3: '브라우저 기본 아이콘을 대체할 사용자 정의 아이콘을 업로드하여 사용자 경험을 개선합니다.',

            monitorTitle1: '방문자 추세',
            monitorContent1: '웹사이트 방문자 추세를 통계적으로 표시합니다.',
            monitorTitle2: '방문자 지도',
            monitorContent2: '웹사이트 방문자의 지리적 분포를 통계적으로 표시합니다.',
            monitorTitle3: '접속 통계',
            monitorContent3: '웹사이트 요청 정보(스파이더, 접속 장치, 요청 상태 등)에 대한 통계를 제공합니다.',
            monitorTitle4: '실시간 모니터링',
            monitorContent4: '웹사이트 요청 정보(요청 수, 트래픽 등)를 실시간으로 모니터링합니다.',

            alertTitle1: 'SMS 알림',
            alertContent1:
                '서버 리소스 사용량 이상, 웹사이트 및 인증서 만료, 새로운 버전 업데이트, 비밀번호 만료 등의 문제가 발생하면 SMS 알림을 통해 사용자가 신속히 처리할 수 있도록 합니다.',
            alertTitle2: '알림 로그',
            alertContent2:
                '사용자에게 알림 로그를 조회할 수 있는 기능을 제공하여 과거 알림 이벤트를 추적하고 분석할 수 있도록 합니다.',
            alertTitle3: '알림 설정',
            alertContent3:
                '사용자에게 전화번호, 일일 푸시 빈도, 일일 푸시 시간 등을 사용자 정의할 수 있는 설정을 제공하여 보다 합리적인 푸시 알림을 설정할 수 있도록 합니다.',

            nodeTitle1: '원클릭 노드 추가',
            nodeContent1: '여러 서버 노드를 빠르게 통합',
            nodeTitle2: '일괄 업그레이드',
            nodeContent2: '한 번의 작업으로 모든 노드를 동기화 및 업그레이드',
            nodeTitle3: '노드 상태 모니터링',
            nodeContent3: '각 노드의 운영 상태를 실시간으로 파악',
            nodeTitle4: '빠른 원격 연결',
            nodeContent4: '원클릭으로 노드 원격 터미널에 직접 연결',

            fileExchangeTitle1: '키 인증 전송',
            fileExchangeContent1: 'SSH 키를 통해 인증하여 전송 보안을 보장합니다.',
            fileExchangeTitle2: '효율적인 파일 동기화',
            fileExchangeContent2: '변경된 내용만 동기화하여 전송 속도와 안정성을 크게 향상시킵니다.',
            fileExchangeTitle3: '다중 노드 상호 통신 지원',
            fileExchangeContent3: '다른 노드 간에 프로젝트 파일을 쉽게 전송하고, 여러 서버를 유연하게 관리합니다.',

            appTitle1: '유연한 패널 관리',
            appContent1: '언제 어디서나 1Panel 서버를 쉽게 관리하세요.',
            appTitle2: '종합적인 서비스 정보',
            appContent2:
                '모바일 앱에서 애플리케이션, 웹사이트, Docker, 데이터베이스 등의 기본 관리를 하고, 애플리케이션과 웹사이트의 빠른 생성을 지원합니다.',
            appTitle3: '실시간 이상 모니터링',
            appContent3:
                '모바일 앱에서 서버 상태, WAF 보안 모니터링, 웹사이트 방문 통계 및 프로세스 건강 상태를 실시간으로 확인하세요.',

            clusterTitle1: '마스터-슬레이브 배포',
            clusterContent1:
                '다른 노드에서 MySQL/Postgres/Redis 마스터-슬레이브 인스턴스를 생성하는 것을 지원하며, 자동으로 마스터-슬레이브 연결 및 초기화를 완료합니다',
            clusterTitle2: '마스터-슬레이브 관리',
            clusterContent2:
                '통합 페이지에서 여러 마스터-슬레이브 노드를 중앙에서 관리하고, 역할, 실행 상태 등을 확인합니다',
            clusterTitle3: '복제 상태',
            clusterContent3:
                '마스터-슬레이브 복제 상태 및 지연 정보를 표시하여 동기화 문제를 해결하는 데 도움을 줍니다',
        },
        node: {
            master: '주 노드',
            masterBackup: '마스터 노드 백업',
            backupNode: '백업 노드',
            backupFrequency: '백업 주기(시간)',
            backupCopies: '백업 기록 보관 수',
            noBackupNode: '현재 백업 노드가 비어 있습니다. 저장할 백업 노드를 선택한 후 다시 시도하십시오!',
            masterBackupAlert:
                '현재 마스터 노드 백업이 구성되지 않았습니다. 데이터 보안을 위해 장애 시 새로운 마스터 노드로 수동 전환이 가능하도록 가능한 빨리 백업 노드를 설정하십시오.',
            node: '노드',
            addr: '주소',
            nodeUnhealthy: '노드 상태 이상',
            deletedNode: '삭제된 노드 {0}은(는) 현재 업그레이드 작업을 지원하지 않습니다!',
            nodeUnhealthyHelper: '노드 상태 이상이 감지되었습니다. [노드 관리]에서 확인 후 다시 시도하세요!',
            nodeUnbind: '노드가 라이선스에 바인딩되지 않음',
            nodeUnbindHelper:
                '이 노드가 라이선스에 바인딩되지 않은 것으로 감지되었습니다. [패널 설정 - 라이선스] 메뉴에서 바인딩 후 다시 시도하세요!',
            memTotal: '총 메모리',
            nodeManagement: '노드 관리',
            addNode: '노드 추가',
            connInfo: '연결 정보',
            nodeInfo: '노드 정보',
            syncInfo: '데이터 동기화,',
            syncHelper: '마스터 노드 데이터가 변경되면, 이 자식 노드에 실시간으로 동기화됩니다,',
            syncBackupAccount: '백업 계정 설정',
            syncWithMaster:
                '프로 버전으로 업그레이드 후 모든 데이터가 기본적으로 동기화됩니다. 노드 관리에서 동기화 정책을 수동으로 조정할 수 있습니다.',
            syncProxy: '시스템 프록시 설정',
            syncProxyHelper: '시스템 프록시 설정 동기화에는 Docker 재시작이 필요합니다',
            syncProxyHelper1: 'Docker 재시작은 현재 실행 중인 컨테이너 서비스에 영향을 줄 수 있습니다.',
            syncProxyHelper2: '컨테이너 - 설정 페이지에서 수동으로 재시작할 수 있습니다.',
            syncProxyHelper3:
                '시스템 프록시 설정 동기화에는 Docker 재시작이 필요하며, 현재 실행 중인 컨테이너 서비스에 영향을 줄 수 있습니다',
            syncProxyHelper4:
                '시스템 프록시 설정 동기화에는 Docker 재시작이 필요합니다. 나중에 컨테이너 - 설정 페이지에서 수동으로 재시작할 수 있습니다.',
            syncCustomApp: '사용자 정의 앱 저장소 동기화',
            syncAlertSetting: '시스템 경고 설정',
            syncNodeInfo: '노드 기본 데이터,',
            nodeSyncHelper: '노드 정보 동기화는 다음 정보를 동기화합니다:',
            nodeSyncHelper1: '1. 공용 백업 계정 정보',
            nodeSyncHelper2: '2. 주 노드와 하위 노드 간의 연결 정보',

            nodeCheck: '가용성 확인',
            checkSSH: '노드 SSH 연결 확인',
            checkUserPermission: '노드 사용자 권한 확인',
            isNotRoot:
                '이 노드에서 비밀번호 없이 sudo를 사용할 수 없으며 현재 사용자가 root가 아닌 것으로 감지되었습니다',
            checkLicense: '노드 라이선스 상태 확인',
            checkService: '노드의 기존 서비스 정보 확인',
            checkPort: '노드 포트 접근 가능 여부 확인',
            panelExist:
                '이 노드에서 1Panel V1 서비스가 실행 중인 것으로 감지되었습니다. 추가 전에 마이그레이션 스크립트로 V2로 업그레이드하십시오.',
            coreExist:
                '현재 노드가 마스터 노드로 활성화되어 있어 슬레이브 노드로 직접 추가할 수 없습니다. 추가하기 전에 먼저 슬레이브 노드로 다운그레이드하십시오. 자세한 내용은 문서를 참조하십시오.',
            agentExist:
                '이 노드에 1panel-agent가 이미 설치되어 있는 것으로 감지되었습니다. 계속하면 기존 데이터를 유지하고 1panel-agent 서비스만 교체됩니다.',
            oldDataExist:
                '이 노드에서 1Panel V2 기록 데이터가 감지되었습니다. 다음 정보를 사용하여 현재 설정을 덮어씁니다:',
            errLicense: '이 노드에 바인딩된 라이선스를 사용할 수 없습니다. 확인 후 다시 시도하십시오!',
            errNodePort:
                '노드 포트 [ {0} ]에 접근할 수 없는 것으로 감지되었습니다. 방화벽 또는 보안 그룹에서 해당 포트가 허용되었는지 확인하십시오.',

            reinstallHelper: '노드 {0}를 재설치합니다. 계속하시겠습니까?',
            unhealthyCheck: '비정상 체크',
            fixOperation: '수정 작업',
            checkName: '체크 항목',
            checkSSHConn: 'SSH 연결 가능성 확인',
            fixSSHConn: '노드를 수동으로 편집하여 연결 정보를 확인합니다',
            checkConnInfo: '에이전트 연결 정보 확인',
            checkStatus: '노드 서비스 가용성 확인',
            fixStatus: '"systemctl status 1panel-agent.service"를 실행하여 서비스가 실행 중인지 확인합니다.',
            checkAPI: '노드 API 가용성 확인',
            fixAPI: '노드 로그를 확인하고 방화벽 포트가 정상적으로 열려 있는지 확인합니다.',
            forceDelete: '강제 삭제',
            operateHelper: '다음 노드에 대해 {0} 작업을 수행합니다. 계속하시겠습니까?',
            forceDeleteHelper: '강제 삭제는 노드 삭제 오류를 무시하고 데이터베이스 메타데이터를 삭제합니다',
            uninstall: '노드 데이터 삭제',
            uninstallHelper: '이 작업은 노드의 모든 1Panel 관련 데이터를 삭제합니다. 신중하게 선택하세요!',
            baseDir: '설치 디렉토리',
            baseDirHelper: '설치 디렉토리가 비어 있으면 기본적으로 /opt 디렉토리에 설치됩니다',
            nodePort: '노드 포트',
            offline: '오프라인 모드',
            freeCount: '무료 할당량 [{0}]',
            offlineHelper: '노드가 오프라인 환경일 때 사용',
        },
        customApp: {
            name: '사용자 정의 앱 저장소',
            appStoreType: '앱 스토어 패키지 소스',
            appStoreUrl: '저장소 URL',
            local: '로컬 경로',
            remote: '원격 링크',
            imagePrefix: '이미지 접두사',
            imagePrefixHelper:
                '기능: 이미지 접두사를 사용자 정의하고 compose 파일의 이미지 필드를 수정합니다. 예를 들어 이미지 접두사를 1panel/custom으로 설정하면 MaxKB의 이미지 필드는 1panel/custom/maxkb:v1.10.0으로 변경됩니다',
            closeHelper: '사용자 정의 앱 저장소 사용 취소',
            appStoreUrlHelper: '.tar.gz 형식만 지원합니다',
            postNode: '서브 노드로 동기화',
            postNodeHelper:
                '사용자 정의 스토어 패키지를 하위 노드의 설치 디렉토리에 있는 tmp/customApp/apps.tar.gz로 동기화합니다',
            nodes: '노드 선택',
            selectNode: '노드 선택',
            selectNodeError: '노드를 선택하세요',
            licenseHelper: '프로 버전은 사용자 정의 애플리케이션 저장소 기능을 지원합니다',
        },
        alert: {
            isAlert: '알림',
            alertCount: '알림 횟수',
            clamHelper: '감염된 파일을 스캔할 때 알림 트리거',
            cronJobHelper: '작업 실행 실패 시 알림 트리거',
            licenseHelper: '전문 버전에서는 SMS 알림을 지원합니다.',
            alertCountHelper: '최대 일일 알림 빈도',
            alert: 'SMS 알림',
            logs: '알림 로그',
            list: '알림 목록',
            addTask: '알림 생성',
            editTask: '알림 수정',
            alertMethod: '알림 방법',
            alertMsg: '알림 메시지',
            alertRule: '알림 규칙',
            titleSearchHelper: '알림 제목을 입력하여 검색하세요',
            taskType: '유형',
            ssl: 'SSL 인증서 만료',
            siteEndTime: '웹사이트 만료',
            panelPwdEndTime: '패널 비밀번호 만료',
            panelUpdate: '새 패널 버전 사용 가능',
            cpu: '서버 CPU 알림',
            memory: '서버 메모리 알림',
            load: '서버 부하 알림',
            disk: '서버 디스크 알림',
            website: '웹사이트',
            certificate: 'SSL 인증서',
            remainingDays: '남은 일수',
            sendCount: '발송 횟수',
            sms: 'SMS',
            wechat: '위챗',
            dingTalk: '딩톡',
            feiShu: '페이슈',
            mail: '이메일',
            weCom: 'WeCom',
            sendCountRulesHelper: '만료 전 발송된 총 알림 수 (하루 1회)',
            panelUpdateRulesHelper: '새 패널 버전에 대한 총 알림 수 (하루 1회)',
            oneDaySendCountRulesHelper: '하루 최대 발송 가능한 알림 수',
            siteEndTimeRulesHelper: '만료되지 않는 웹사이트는 알림이 발생하지 않습니다',
            autoRenewRulesHelper: '자동 갱신이 설정된 인증서의 남은 일수가 31일 미만이면 알림이 발생하지 않습니다',
            panelPwdEndTimeRulesHelper: '만료가 설정되지 않은 경우 패널 비밀번호 만료 알림이 비활성화됩니다',
            sslRulesHelper: '모든 SSL 인증서',
            diskInfo: '디스크',
            monitoringType: '모니터링 유형',
            autoRenew: '자동 갱신',
            useDisk: '디스크 사용량',
            usePercentage: '사용 비율',
            changeStatus: '상태 변경',
            disableMsg: '알림 작업을 중지하면 알림 메시지가 전송되지 않습니다. 계속하시겠습니까?',
            enableMsg: '알림 작업을 활성화하면 알림 메시지가 전송됩니다. 계속하시겠습니까?',
            useExceed: '사용량 초과',
            useExceedRulesHelper: '사용량이 설정 값을 초과하면 알림을 트리거합니다',
            cpuUseExceedAvg: '평균 CPU 사용량이 지정된 값을 초과함',
            memoryUseExceedAvg: '평균 메모리 사용량이 지정된 값을 초과함',
            loadUseExceedAvg: '평균 부하 사용량이 지정된 값을 초과함',
            cpuUseExceedAvgHelper: '지정된 시간 내의 평균 CPU 사용량이 지정된 값을 초과함',
            memoryUseExceedAvgHelper: '지정된 시간 내의 평균 메모리 사용량이 지정된 값을 초과함',
            loadUseExceedAvgHelper: '지정된 시간 내의 평균 부하 사용량이 지정된 값을 초과함',
            resourceAlertRulesHelper: '참고: 30분 내에 연속적인 알림은 SMS 한 번만 발송됩니다',
            specifiedTime: '지정된 시간',
            deleteTitle: '알림 삭제',
            deleteMsg: '알림 작업을 삭제하시겠습니까?',
            allSslTitle: '모든 웹사이트 SSL 인증서 만료 알림',
            sslTitle: '웹사이트 {0}의 SSL 인증서 만료 알림',
            allSiteEndTimeTitle: '모든 웹사이트 만료 알림',
            siteEndTimeTitle: '웹사이트 {0} 만료 알림',
            panelPwdEndTimeTitle: '패널 비밀번호 만료 알림',
            panelUpdateTitle: '새 패널 버전 알림',
            cpuTitle: '고 CPU 사용량 알림',
            memoryTitle: '고 메모리 사용량 알림',
            loadTitle: '고 부하 알림',
            diskTitle: '디스크 사용량 초과 알림 {0}',
            allDiskTitle: '고 디스크 사용량 알림',
            timeRule: '{0}일 이하 남은 시간 (처리되지 않으면 다음 날 다시 전송)',
            panelUpdateRule: '새 패널 버전이 감지되면 한 번 알림을 전송 (처리되지 않으면 다음 날 다시 전송)',
            avgRule: '{0}분 내 평균 {1} 사용량이 {2}% 초과 시 알림을 트리거하며 하루에 {3}번 발송',
            diskRule: '디스크 사용량이 마운트 디렉토리 {0}에서 {1}{2}를 초과하면 알림을 트리거하고 하루에 {3}번 발송',
            allDiskRule: '디스크 사용량이 {0}{1}을 초과하면 알림을 트리거하고 하루에 {2}번 발송',
            cpuName: ' CPU ',
            memoryName: '메모리',
            loadName: '부하',
            diskName: '디스크',
            syncAlertInfo: '수동 푸시',
            syncAlertInfoMsg: '알림 작업을 수동으로 푸시하시겠습니까?',
            pushError: '푸시 실패',
            pushSuccess: '푸시 성공',
            syncError: '동기화 실패',
            success: '알림 성공',
            pushing: '푸시 중...',
            error: '알림 실패',
            cleanLog: '로그 정리',
            cleanAlertLogs: '알림 로그 정리',
            daily: '일일 알림 수: {0}',
            cumulative: '누적 알림 수: {0}',
            clams: '바이러스 검사',
            taskName: '작업 이름',
            cronJobType: '작업 유형',
            clamPath: '검사 디렉토리',
            cronjob: '크론 작업',
            app: '백업 애플리케이션',
            web: '백업 웹사이트',
            database: '백업 데이터베이스',
            directory: '백업 디렉토리',
            log: '백업 로그',
            snapshot: '시스템 스냅샷',
            clamsRulesHelper: '알림이 필요한 바이러스 검사 작업',
            cronJobRulesHelper: '이 유형의 예약된 작업은 구성해야 합니다',
            clamsTitle: '바이러스 검사 작업 「 {0} 」 감염된 파일 알림',
            cronJobAppTitle: '크론 작업 - 백업 애플리케이션 「 {0} 」 작업 실패 알림',
            cronJobWebsiteTitle: '크론 작업 - 백업 웹사이트「 {0} 」작업 실패 알림',
            cronJobDatabaseTitle: '크론 작업 - 백업 데이터베이스「 {0} 」작업 실패 알림',
            cronJobDirectoryTitle: '크론 작업 - 백업 디렉토리「 {0} 」작업 실패 알림',
            cronJobLogTitle: '크론 작업 - 백업 로그「 {0} 」작업 실패 알림',
            cronJobSnapshotTitle: '크론 작업 - 백업 스냅샷「 {0} 」작업 실패 알림',
            cronJobShellTitle: '크론 작업 - 셸 스크립트 「 {0} 」작업 실패 알림',
            cronJobCurlTitle: '크론 작업 - URL 접근「 {0} 」작업 실패 알림',
            cronJobCutWebsiteLogTitle: '크론 작업 - 웹사이트 로그 자르기「 {0} 」작업 실패 알림',
            cronJobCleanTitle: '크론 작업 - 캐시 정리「 {0} 」작업 실패 알림',
            cronJobNtpTitle: '크론 작업 - 서버 시간 동기화「 {0} 」작업 실패 알림',
            clamsRule: '바이러스 검사로 감염된 파일 알림, 하루에 {0}번 발송',
            cronJobAppRule: '백업 애플리케이션 작업 실패 알림, 하루에 {0}번 발송',
            cronJobWebsiteRule: '백업 웹사이트 작업 실패 알림, 하루에 {0}번 발송',
            cronJobDatabaseRule: '백업 데이터베이스 작업 실패 알림, 하루에 {0}번 발송',
            cronJobDirectoryRule: '백업 디렉토리 작업 실패 알림, 하루에 {0}번 발송',
            cronJobLogRule: '백업 로그 작업 실패 알림, 하루에 {0}번 발송',
            cronJobSnapshotRule: '백업 스냅샷 작업 실패 알림, 하루에 {0}번 발송',
            cronJobShellRule: '셸 스크립트 작업 실패 알림, 하루에 {0}번 발송',
            cronJobCurlRule: 'URL 접근 작업 실패 알림, 하루에 {0}번 발송',
            cronJobCutWebsiteLogRule: '웹사이트 로그 자르기 작업 실패 알림, 하루에 {0}번 발송',
            cronJobCleanRule: '캐시 정리 작업 실패 알림, 하루에 {0}번 발송',
            cronJobNtpRule: '서버 시간 동기화 작업 실패 알림, 하루에 {0}번 발송',
            alertSmsHelper: 'SMS 한도: 총 {0}개의 메시지, {1}개 사용됨',
            goBuy: '추가 구매',
            phone: '전화',
            phoneHelper: '알림 메시지를 위한 실제 전화번호를 제공하세요',
            dailyAlertNum: '일일 알림 한도',
            dailyAlertNumHelper: '하루에 보낼 수 있는 최대 알림 수 (최대 100개)',
            timeRange: '시간 범위',
            sendTimeRange: '발송 시간 범위',
            sendTimeRangeHelper: '{0} 시간 범위를 푸시할 수 있습니다',
            to: '부터',
            startTime: '시작 시간',
            endTime: '종료 시간',
            defaultPhone: '기본적으로 라이선스에 묶인 계정의 전화번호 사용',
            noticeAlert: '공지 알림',
            resourceAlert: '리소스 알림',
            agentOfflineAlertHelper:
                '노드에서 오프라인 알림이 활성화되면, 메인 노드가 30분마다 스캔하여 알림 작업을 수행합니다.',
            offline: '오프라인 알림',
            offlineHelper: '오프라인 알림으로 설정하면, 메인 노드가 30분마다 스캔하여 알림 작업을 수행합니다.',
            offlineOff: '오프라인 알림 활성화',
            offlineOffHelper: '오프라인 알림을 활성화하면, 메인 노드가 30분마다 스캔하여 알림 작업을 수행합니다.',
            offlineClose: '오프라인 알림 비활성화',
            offlineCloseHelper:
                '오프라인 알림을 비활성화하면, 하위 노드가 알림을 직접 처리해야 합니다. 알림 실패를 방지하려면 네트워크 연결이 원활한지 확인하세요.',
            alertNotice: '알림 통지',
            methodConfig: '알림 방법 설정',
            commonConfig: '전역 설정',
            smsConfig: 'SMS',
            smsConfigHelper: 'SMS 알림을 받을 전화번호를 설정합니다',
            emailConfig: '이메일',
            emailConfigHelper: 'SMTP 이메일 발송 서비스를 설정합니다',
            deleteConfigTitle: '알림 설정 삭제',
            deleteConfigMsg: '알림 설정을 삭제하시겠습니까?',
            test: '테스트',
            alertTestOk: '테스트 알림 성공',
            alertTestFailed: '테스트 알림 실패',
            displayName: '표시 이름',
            sender: '발신 주소',
            password: '비밀번호',
            host: 'SMTP 서버',
            port: '포트 번호',
            encryption: '암호화 방식',
            recipient: '수신자',
            licenseTime: '라이선스 만료 알림',
            licenseTimeTitle: '라이선스 만료 알림',
            displayNameHelper: '이메일 발신자 표시 이름',
            senderHelper: '이메일 발송에 사용되는 주소',
            passwordHelper: '메일 서비스의 인증 코드',
            hostHelper: 'SMTP 서버 주소, 예: smtp.qq.com',
            portHelper: 'SSL 은 일반적으로 465, TLS 는 587',
            sslHelper: 'SMTP 포트가 465 이면 일반적으로 SSL 이 필요합니다',
            tlsHelper: 'SMTP 포트가 587 이면 일반적으로 TLS 가 필요합니다',
        },
        theme: {
            lingXiaGold: '링샤 골드',
            classicBlue: '클래식 블루',
            freshGreen: '프레시 그린',
            customColor: '사용자 정의 색상',
            setDefault: '기본값',
            setDefaultHelper: '테마 색상 스킴이 초기 상태로 복원됩니다. 계속하시겠습니까?',
            setHelper: '현재 선택한 테마 색상 스킴이 저장됩니다. 계속하시겠습니까?',
        },
        exchange: {
            exchange: '파일 교환',
            exchangeConfirm: '{0} 노드의 파일/폴더 {1}을(를) {2} 노드의 {3} 디렉토리로 전송하시겠습니까?',
        },
        cluster: {
            cluster: '애플리케이션 고가용성',
            name: '클러스터 이름',
            addCluster: '클러스터 추가',
            installNode: '노드 설치',
            master: '마스터 노드',
            slave: '슬레이브 노드',
            replicaStatus: '마스터-슬레이브 상태',
            unhealthyDeleteError: '설치 노드 상태가 비정상입니다. 노드 목록을 확인한 후 다시 시도하세요!',
            replicaStatusError: '상태 획득이 비정상입니다. 마스터 노드를 확인하세요.',
            masterHostError: '마스터 노드의 IP는 127.0.0.1이 될 수 없습니다',
        },
    },
};

export default {
    ...fit2cloudKoLocale,
    ...message,
};
