import fit2cloudEnLocale from 'fit2cloud-ui-plus/src/locale/lang/ja';

const message = {
    commons: {
        true: '真実',
        false: '間違い',
        colon: ':',
        example: '例えば、',
        fit2cloud: 'FIT2CLOUD',
        lingxia: 'Lingxia',
        button: {
            run: '実行',
            create: '作成する',
            add: '追加',
            save: '保存',
            set: '構成を編集します',
            sync: '同期',
            delete: '消去',
            edit: '編集',
            enable: '有効にする',
            disable: '無効にします',
            confirm: '確認する',
            cancel: 'キャンセル',
            reset: 'リセット',
            restart: '再起動',
            conn: '接続する',
            disConn: '切断します',
            clean: 'クリーン',
            login: 'ログイン',
            close: '近い',
            off: '近い',
            stop: '停止',
            start: '始める',
            view: 'ビュー',
            watch: '時計',
            handle: 'トリガー',
            clone: 'クローン',
            expand: '拡大する',
            collapse: '崩壊',
            log: 'ログ',
            back: '戻る',
            backup: 'バックアップ',
            recover: '回復する',
            retry: 'リトライ',
            upload: 'アップロード',
            download: 'ダウンロード',
            init: '初期化',
            verify: '確認する',
            saveAndEnable: '保存して有効にします',
            import: '輸入',
            export: 'エクスポート',
            power: '認可',
            search: '検索',
            refresh: 'リロード',
            get: '得る',
            upgrade: 'アップグレード',
            update: '編集',
            ignore: '更新を無視する',
            copy: 'コピー',
            random: 'ランダム',
            install: 'インストール',
            uninstall: 'アンインストール',
            fullscreen: 'フルスクリーン',
            quitFullscreen: 'フルスクリーンを終了',
            showAll: 'すべてを表示します',
            hideSome: 'いくつかを隠します',
            agree: '同意する',
            notAgree: '同意しません',
            preview: 'プレビュー',
            open: '開ける',
            notSave: '保存しないでください',
            createNewFolder: '新しいフォルダーを作成します',
            createNewFile: '新しいファイルを作成します',
            helpDoc: '文書をヘルプします',
            unbind: 'バインド',
            cover: 'に覆いを',
            skip: 'スキップ',
            fix: '修正',
            down: '停止',
            up: '起動',
            sure: '確認',
            show: '表示する',
            hide: '隠す',
        },
        operate: {
            start: '開始',
            stop: '停止',
            restart: '再起動',
            reload: '再読み込み',
            rebuild: '再構築',
            sync: '同期',
            up: '起動',
            down: '停止',
            delete: '削除',
        },
        search: {
            timeStart: '時間開始',
            timeEnd: 'タイムエンド',
            timeRange: 'に',
            dateStart: '日付開始',
            dateEnd: '日付の終わり',
        },
        table: {
            all: '全て',
            total: '合計{0}',
            name: '名前',
            type: 'タイプ',
            status: '状態',
            records: '記録',
            group: 'グループ',
            createdAt: '作成時間',
            publishedAt: '公開時間',
            date: '日付',
            updatedAt: '時間を更新します',
            operate: '操作',
            message: 'メッセージ',
            description: '説明',
            interval: '間隔',
            user: '所有者',
            title: 'タイトル',
            port: 'ポート',
            forward: 'フォワード',
            protocol: 'プロトコル',
            tableSetting: 'テーブル設定',
            refreshRate: 'リフレッシュレート',
            selectColumn: '列を選択します',
            local: 'ローカル',
            serialNumber: 'シリアル番号',
            manageGroup: 'グループ管理',
            backToList: 'リストに戻る',
            keepEdit: '編集を続ける',
        },
        loadingText: {
            Upgrading: 'システムのアップグレード、待ってください...',
            Restarting: 'システムの再起動、待ってください...',
            Recovering: 'スナップショットから回復して、待ってください...',
            Rollbacking: 'スナップショットからのロールバック、お待ちください...',
        },
        msg: {
            noneData: '利用可能なデータはありません',
            delete: `この操作削除は元に戻すことはできません。続けたいですか？`,
            clean: `この操作は取り消すことはできません。続けたいですか？`,
            closeDrawerHelper: 'システムは変更を保存しない可能性があります。続行しますか？',
            deleteSuccess: '正常に削除されました',
            loginSuccess: '正常にログインしました',
            operationSuccess: '正常に完了',
            copySuccess: '正常にコピーされました',
            notSupportOperation: `この操作はサポートされていません`,
            requestTimeout: 'リクエストがタイムアウトしました。後でもう一度やり直してください',
            infoTitle: 'ヒント',
            notRecords: '現在のタスクの実行レコードは生成されません',
            sureLogOut: 'ログアウトしたいですか？',
            createSuccess: '正常に作成されました',
            updateSuccess: '正常に更新されました',
            uploadSuccess: '正常にアップロードされました',
            operateConfirm: '操作について確信している場合は、手動で入力してください。',
            inputOrSelect: '選択または入力してください',
            copyFailed: 'コピーに失敗しました',
            operatorHelper: `操作「{1}」は「{0}」で実行され、元に戻すことはできません。続けたいですか？`,
            notFound: '申し訳ありませんが、要求したページは存在しません。',
            unSupportType: `現在のファイルタイプはサポートされていません。`,
            unSupportSize: 'アップロードされたファイルは{0} mを超えています、確認してください！',
            fileExist: `ファイルはすでに現在のフォルダーに存在しています。リピートアップロードはサポートされていません。`,
            fileNameErr:
                '名前には、英語、中国語、数字、または期間を含む1〜256文字が含まれるファイルのみをアップロードできます（。-_）',
            confirmNoNull: `値{0}が空でないことを確認してください。`,
            errPort: 'ポート情報が正しくありません、確認してください！',
            remove: '取り除く',
            backupHelper: '現在の操作は{0}をバックアップします。先に進みたいですか？',
            recoverHelper: '{0}ファイルから復元。この操作は不可逆的です。続けたいですか？',
            refreshSuccess: 'リフレッシュして成功します',
            rootInfoErr: 'すでにルートディレクトリです',
            resetSuccess: 'リセット成功',
            creatingInfo: '作成、この操作は必要ありません',
        },
        login: {
            username: 'ユーザー名',
            password: 'パスワード',
            welcome: 'ようこそ、ユーザー名とパスワードを入力してログインしてください！',
            errorAuthInfo: '入力したユーザー名またはパスワードは間違っています。再入力してください！',
            errorMfaInfo: '認証情報が誤っていない、もう一度やり直してください！',
            captchaHelper: 'キャプチャ',
            errorCaptcha: 'Captchaコードエラー！',
            notSafe: 'アクセスが拒否されました',
            safeEntrance1: '安全なログインは現在の環境で有効になっています',
            safeEntrance2: 'SSH端末に次のコマンドを入力して、パネルエントリを表示します:1PCTLユーザー-INFO',
            errIP1: '現在の環境で認定されたIPアドレスアクセスが有効になっています',
            errDomain1: 'アクセスドメイン名のバインディングが現在の環境で有効になっています',
            errHelper: 'バインディング情報をリセットするには、SSH端末で次のコマンドを実行します。',
            codeInput: 'MFAバリデーターの6桁の検証コードを入力してください',
            mfaTitle: 'MFA認定',
            mfaCode: 'MFA検証コード',
            title: 'Linuxサーバー管理パネル',
            licenseHelper: '<コミュニティライセンス契約>',
            errorAgree: 'クリックして、コミュニティソフトウェアライセンスに同意します',
            logout: 'ログアウト',
            agreeTitle: '合意',
            agreeContent:
                'あなたの正当な権利と利益をよりよく保護するために、次の契約とラコを読んで同意してください。<a href = "https://www.fit2cloud.com/legal/licenses.html" target="_blank">コミュニティライセンス契約</a>＆raquo;',
        },
        rule: {
            username: 'ユーザー名を入力します',
            password: 'パスワードを入力します',
            rePassword: 'パスワードがパスワードと矛盾することを確認してください。',
            requiredInput: 'この項目は必須です。',
            requiredSelect: 'リスト内のアイテムを選択します',
            illegalChar: '現在、文字 & ; $ \' ` ( ) " > < | の注入はサポートされていません',
            illegalInput: `このフィールドには違法なキャラクターが含まれてはなりません。`,
            commonName:
                'このフィールドは、特別なキャラクターではなく、英語、中国語、数字で構成されている必要があります。「。」、「」、および「_」文字が1〜128の文字で構成されている必要があります。',
            userName: '特殊文字で始まらない、英字、漢字、数字、および_をサポート、長さ3-30',
            simpleName: `このフィールドは、アンダースコアキャラクターから始めてはなりません。長さ3〜30の英語、数字、「_」文字で構成されている必要があります。`,
            simplePassword: `このフィールドは、アンダースコアキャラクターから始めてはなりません。長さ1〜30の英語、数字、「_」文字で構成されている必要があります。`,
            dbName: `このフィールドは、アンダースコアキャラクターから始めてはなりません。長さ1〜64の英語、数字、「_」文字で構成されている必要があります。`,
            imageName: '特殊文字で始まらない、英字、数字、:@/.-_をサポート、長さ1-256',
            composeName: '最初の特別な文字、小文字、数字、_、長さ1-256をサポートします',
            volumeName:
                'このフィールドは、英語、数字、「。」、「 - 」、および「_」文字で構成されている必要があります。',
            supervisorName:
                'このフィールドは、特別な文字以外の文字から開始する必要があり、英語、数字、「 - 」、および「_」文字が1〜128の文字で構成されている必要があります。',
            complexityPassword:
                'このフィールドは、英語で構成され、長さは8〜30で、少なくとも2つの特殊文字が含まれている必要があります。',
            commonPassword: 'このフィールドの長さは6を超える必要があります。',
            linuxName: `このフィールドの長さは1〜128でなければなりません。フィールドには、これらの特殊文字を含めてはなりません。「{0}」。`,
            email: 'このフィールドは有効な電子メールアドレスでなければなりません。',
            number: 'このフィールドは数字でなければなりません。',
            integer: 'このフィールドは正の整数でなければなりません。',
            ip: 'このフィールドは有効なIPアドレスでなければなりません。',
            host: 'このフィールドは、有効なIPアドレスまたはドメイン名でなければなりません。',
            hostHelper: 'IPアドレスまたはドメイン名の入力をサポートします',
            port: 'このフィールドは有効なポート番号でなければなりません。',
            selectHelper: '正しい{0}ファイルを選択してください',
            domain: 'このフィールドは、example.comまたはexample.com:8080のようなものでなければなりません。',
            databaseName: 'このフィールドは、長さ1〜30の英語、数字、「_」文字で構成されている必要があります。',
            ipErr: 'このフィールドは有効なIPアドレスでなければなりません。',
            numberRange: 'このフィールドは、{0}と{1}の間の数字でなければなりません。',
            paramName: 'このフィールドは、英語、数字、「。」、「 - 」、および「_」文字で構成されている必要があります。',
            paramComplexity: `このフィールドは、特殊文字で開始および終了する必要はなく、英語、数字「{0}」文字で構成されている必要があります。`,
            paramUrlAndPort:
                'このフィールドは、「http（s）://（domain name/ip）:(ポート）」の形式でなければなりません。',
            nginxDoc: 'このフィールドは、英語、数字、「」で構成されている必要があります。文字。',
            appName: `このフィールドは、「 - 」と「_」文字で開始および終了してはなりません。英語、数字、 "、および「_」文字で2〜30の文字で構成されている必要があります。`,
            containerName: '文字、数字、 - 、_および。;-  _または。で始めることはできません。長さ:2-128',
            mirror: 'ミラーアクセラレーションアドレスは、http（s）://、英語の文字（大文字と小文字の両方）、数字をサポートする必要があります。/および - 、そして空白の行を含めてはなりません。',
            disableFunction: 'サポートレター、アンダースコア、および',
            leechExts: 'サポートレター、数字、および',
            paramSimple: '小文字と数字をサポート、長さ1〜128',
            filePermission: 'ファイル許可エラー',
            formatErr: 'フォーマットエラー、チェックして再試行してください',
            phpExtension: '_小文字の英語と数字のみをサポートします',
            paramHttp: 'http://またはhttps://で始める必要があります',
            phone: '電話番号の形式は正しくありません',
            authBasicPassword: '英字、数字、一般的な特殊文字をサポート、長さ1-72',
            length128Err: '長さは128文字を超えることはできません',
            maxLength: '長さは {0} 文字を超えることはできません',
            alias: '英字、数字、-と_をサポート、長さ1-30、-_で始まるまたは終わることはできません。',
        },
        res: {
            paramError: 'リクエストが失敗しました。後でもう一度やり直してください！',
            forbidden: '現在のユーザーには許可がありません',
            serverError: 'サービスの例外',
            notFound: 'リソースは存在しません',
            commonError: 'リクエストに失敗しました',
        },
        service: {
            serviceNotStarted: `{0}サービスは開始されません。`,
        },
        status: {
            running: 'ランニング',
            done: '終わり',
            scanFailed: '不完全',
            success: '成功',
            waiting: '待っている',
            waiting1: '待っている',
            failed: '失敗した',
            stopped: '停止',
            error: 'エラー',
            created: '作成されました',
            restarting: '再起動',
            uploading: 'アップロード',
            unhealthy: '不健康',
            removing: '削除',
            paused: '一時停止',
            exited: '終了',
            dead: '死んだ',
            installing: 'インストール',
            enabled: '有効になっています',
            disabled: '無効',
            normal: '普通',
            building: '建物',
            upgrading: 'アップグレード',
            pending: '編集待ち',
            rebuilding: '再構築',
            deny: '拒否されました',
            accept: '受け入れられました',
            used: '使用済み',
            unUsed: '未使用',
            starting: '起動',
            recreating: '再作成',
            creating: '作成',
            init: 'アプリケーションを待っています',
            ready: '普通',
            applying: '適用',
            uninstalling: 'アンインストール中',
            lost: '接続失効',
            bound: 'バインド済み',
            unbind: '未バインド',
            exceptional: '異常',
            free: '空き',
            enable: '有効',
            disable: '無効',
            deleted: '削除済み',
            downloading: 'ダウンロード中',
            packing: 'パッキング中',
            sending: '送信中',
            healthy: '正常',
            executing: '実行中',
            installerr: 'インストールに失敗しました',
            applyerror: '適用に失敗しました',
            systemrestart: '中断',
            starterr: '起動に失敗しました',
            uperr: '起動に失敗しました',
        },
        units: {
            second: '2番目|2番目|秒',
            minute: '分|分|分',
            hour: '時間|時間|時間',
            day: '日|日|日',
            week: '週|週|週',
            month: '月|月|数ヶ月',
            year: '年|年|年',
            time: 'RPM',
            core: 'コア|コア|コア',
            secondUnit: 's',
            minuteUnit: 'min',
            hourUnit: 'h',
            dayUnit: 'd',
            millisecond: 'ミリ秒',
        },
    },
    menu: {
        home: '概要',
        apps: 'アプリストア',
        website: 'ウェブサイト|ウェブサイト',
        project: 'プロジェクト|プロジェクト',
        config: '構成|構成',
        ssh: 'SSH設定',
        firewall: 'ファイアウォール',
        ssl: '証明書|証明書',
        database: 'データベース|データベース',
        aiTools: 'AI',
        mcp: 'MCP',
        container: 'コンテナ|コンテナ',
        cronjob: 'クロンジョブ|クロンの仕事',
        system: 'システム',
        security: '安全',
        files: 'ファイル',
        monitor: '監視',
        terminal: '端子',
        settings: '設定|設定',
        toolbox: 'ツールボックス',
        logs: 'ログ |ログ',
        runtime: 'ランタイム|ランタイム',
        processManage: 'プロセス|プロセス',
        process: 'プロセス|プロセス',
        network: 'ネットワーク|ネットワーク',
        supervisor: '監督者',
        tamper: '改ざん防止',
        app: 'アプリケーション',
        msgCenter: 'タスクセンター',
    },
    home: {
        restart_1panel: 'パネルを再起動します',
        restart_system: 'サーバーを再起動します',
        operationSuccess: '操作が成功し、再起動します。後で手動でブラウザを更新してください！',
        entranceHelper: `セキュリティの入り口は有効になりません。「設定 - >セキュリティ」でそれを有効にして、システムセキュリティを改善できます。`,
        appInstalled: 'アプリケーション',
        systemInfo: 'システム情報',
        hostname: 'ホスト名',
        platformVersion: 'オペレーティング·システム',
        kernelVersion: 'カーネル',
        kernelArch: '建築',
        network: 'ネットワーク',
        io: 'ディスクI/O',
        ip: 'ローカルIP',
        proxy: 'システムプロキシ',
        baseInfo: '基本情報',
        totalSend: '合計送信',
        totalRecv: '総受領',
        rwPerSecond: 'I/O操作',
        ioDelay: 'I/Oレイテンシ',
        uptime: 'それ以来',
        runningTime: '稼働時間',
        mem: 'システム',
        swapMem: 'パーティションを交換します',

        runSmoothly: '低負荷',
        runNormal: '中程度の負荷',
        runSlowly: '高負荷',
        runJam: '重い負荷',

        core: '物理コア',
        logicCore: '論理コア',
        loadAverage: '最後の1分で平均を積み込みます|最後の{n}分で平均を読み込みます',
        load: '負荷',
        mount: 'マウントポイント',
        fileSystem: 'ファイルシステム',
        total: '合計',
        used: '使用済み',
        cache: 'キャッシュ',
        free: '空き',
        shard: 'シャーディング',
        available: '利用可能',
        percent: '利用',
        goInstall: 'インストールします',

        networkCard: 'ネットワークカード',
        disk: 'ディスク',
    },
    tabs: {
        more: 'もっと',
        hide: '隠れる',
        closeLeft: '左を閉じます',
        closeRight: '右に閉じます',
        closeCurrent: '電流を閉じます',
        closeOther: '他を閉じます',
        closeAll: 'すべてを閉じます',
    },
    header: {
        logout: 'ログアウト',
    },
    database: {
        manage: '管理',
        deleteBackupHelper: 'データベースのバックアップを同時に削除します',
        delete: '削除操作はロールバックできません、入力してください」',
        deleteHelper: '「このデータベースを削除します',
        create: 'データベースを作成します',
        noMysql: 'データベースサービス（mysqlまたはmariadb）',
        noPostgresql: 'データベースサービスpostgreSql',
        goUpgrade: 'アップグレードに移動します',
        goInstall: 'インストールに移動します',
        isDelete: '削除されました',
        permission: '権限',
        permissionForIP: 'ip',
        permissionAll: 'それらすべて（％）',
        localhostHelper:
            'コンテナ展開でデータベース権限を"localhost"に設定すると、コンテナ外部からのアクセスができなくなります。慎重に選択してください！',
        databaseConnInfo: '接続情報',
        rootPassword: 'ルートパスワード',
        serviceName: 'サービス名',
        serviceNameHelper: '同じネットワーク内のコンテナ間のアクセス。',
        backupList: 'バックアップ',
        loadBackup: '輸入',
        remoteAccess: 'リモートアクセス',
        remoteHelper: '複数のIP Comma delimited、例:*************、*************',
        remoteConnHelper:
            'ユーザールートとしてのMySQLへのリモート接続には、セキュリティリスクがある場合があります。したがって、この操作を慎重に実行します。',
        changePassword: 'パスワード',
        changeConnHelper: 'この操作は現在のデータベース {0} を変更します。続行しますか？',
        changePasswordHelper:
            'データベースはアプリケーションに関連付けられています。パスワードを変更すると、アプリケーションのデータベースパスワードが同時に変更されます。アプリケーションが再起動した後、変更は有効になります。',

        confChange: '構成',
        confNotFound:
            '設定ファイルが見つかりませんでした。アプリストアでアプリケーションを最新バージョンにアップグレードして、再度お試しください！',

        portHelper:
            'このポートは、コンテナの露出したポートです。変更を個別に保存して、コンテナを再起動する必要があります！',

        loadFromRemote: '同期',
        userBind: 'バインドユーザー',
        pgBindHelper: `この操作は、新しいユーザーを作成し、ターゲットデータベースにバインドするために使用されます。現在、データベースに既存のユーザーを選択することはサポートされていません。`,
        pgSuperUser: 'スーパーユーザー',
        loadFromRemoteHelper: 'これにより、サーバー上のデータベース情報が1パネルに同期します。続けたいですか？',
        passwordHelper: '取得できない場合は、変更してください',
        remote: 'リモート',
        remoteDB: 'リモートサーバー|リモートサーバー',
        createRemoteDB: 'リモートサーバーを追加',
        unBindRemoteDB: 'リモートサーバーのバインドを解除',
        unBindForce: '強制バインド',
        unBindForceHelper: '結合プロセス中にすべてのエラーを無視して、最終操作が成功するようにします',
        unBindRemoteHelper:
            'リモートデータベースのバインディングを解除すると、バインディング関係が削除されるだけで、リモートデータベースは直接削除されません。',
        editRemoteDB: 'リモートサーバーを編集します',
        localDB: 'ローカルデータベース',
        address: 'データベースアドレス',
        version: 'データベースバージョン',
        userHelper:
            'ルートユーザーまたはルート特権を持つデータベースユーザーは、リモートデータベースにアクセスできます。',
        pgUserHelper: 'スーパーユーザーの特権を持つデータベースユーザー。',
        ssl: 'SSLを使用します',
        clientKey: 'クライアントの秘密鍵',
        clientCert: 'クライアント証明書',
        caCert: '証明書として',
        hasCA: 'CA証明書があります',
        skipVerify: '証明書の有効性チェックを無視します',

        formatHelper: '現在のデータベース文字セットは{0}です。文字セットの矛盾は回復の故障を引き起こす可能性があります',
        selectFile: '[ファイル]を選択します',
        dropHelper: 'ここでアップロードされたファイルをドラッグアンドドロップするか、',
        clickHelper: 'クリックしてアップロードします',
        supportUpType: 'SQL、SQL.GZ、およびTAR.GZファイルのみがサポートされています',
        zipFormat: 'tar.gz圧縮パッケージ構造:test.tar.gz圧縮パッケージにはtest.sqlが含まれている必要があります',

        currentStatus: '現在の状態',
        baseParam: '基本パラメーター',
        performanceParam: 'パフォーマンスパラメーター',
        runTime: '起動時間',
        connections: '合計接続',
        bytesSent: 'バイトを送信します',
        bytesReceived: '受信バイト',
        queryPerSecond: 'クエリあたりのクエリ',
        txPerSecond: '1秒あたりのTX',
        connInfo: 'アクティブ/ピーク接続',
        connInfoHelper: '値が大きすぎる場合は、「max_connections」を増やします。',
        threadCacheHit: 'スレッドキャッシュがヒットします',
        threadCacheHitHelper: '低すぎる場合は、「thread_cache_size」を増やします。',
        indexHit: 'インデックスヒット',
        indexHitHelper: '低すぎる場合は、「key_buffer_size」を増やします。',
        innodbIndexHit: 'INNODBインデックスヒット率',
        innodbIndexHitHelper: '低すぎる場合は、「innodb_buffer_pool_size」を増やします。',
        cacheHit: 'キャッシュヒットのクエリ',
        cacheHitHelper: '低すぎる場合は、「query_cache_size」を増やします。',
        tmpTableToDB: 'ディスクへの一時テーブル',
        tmpTableToDBHelper: '大きすぎる場合は、「tmp_table_size」を増やしてみてください。',
        openTables: 'テーブルを開きます',
        openTablesHelper: '「table_open_cache」の構成値は、この値以上に等しくなければなりません。',
        selectFullJoin: '完全な結合を選択します',
        selectFullJoinHelper: `値が0でない場合は、データテーブルのインデックスが正しいかどうかを確認します。`,
        selectRangeCheck: 'インデックスなしの結合の数',
        selectRangeCheckHelper: `値が0でない場合は、データテーブルのインデックスが正しいかどうかを確認します。`,
        sortMergePasses: 'ソートされたマージの数',
        sortMergePassesHelper: '値が大きすぎる場合は、「sort_buffer_size」を増やします。',
        tableLocksWaited: 'テーブル番号をロックします',
        tableLocksWaitedHelper:
            '値が大きすぎる場合は、データベースのパフォーマンスを向上させることを検討してください。',

        performanceTuning: 'パフォーマンスチューニング',
        optimizationScheme: '最適化スキーム',
        keyBufferSizeHelper: 'インデックスのバッファサイズ',
        queryCacheSizeHelper: 'クエリキャッシュ。この関数が無効になっている場合は、このパラメーターを0に設定します。',
        tmpTableSizeHelper: '一時的なテーブルキャッシュサイズ',
        innodbBufferPoolSizeHelper: 'INNODBバッファサイズ',
        innodbLogBufferSizeHelper: 'innodbログバッファサイズ',
        sortBufferSizeHelper: '*接続、スレッドソートごとのバッファサイズ',
        readBufferSizeHelper: '*接続、バッファサイズの読み取り',
        readRndBufferSizeHelper: '*接続、ランダム読み取りバッファサイズ',
        joinBufferSizeHelper: '*接続、アソシエーションテーブルキャッシュサイズ',
        threadStackelper: '*接続、スレッドあたりのスタックサイズ',
        binlogCacheSizeHelper: '* onnections、バイナリログキャッシュサイズ（4096の倍数）',
        threadCacheSizeHelper: 'スレッドプールサイズ',
        tableOpenCacheHelper: 'テーブルキャッシュ',
        maxConnectionsHelper: 'マックス接続',
        restart: '再起動',

        slowLog: '遅いログ',
        noData: 'まだ遅いログはありません。',

        isOn: 'の上',
        longQueryTime: 'しきい値',
        thresholdRangeHelper: '正しいしきい値（1-600）を入力してください。',

        timeout: 'タイムアウト(s)',
        timeoutHelper: 'アイドル接続タイムアウト期間。0は、接続が継続的にオンになっていることを示します。',
        maxclients: 'マックスクライアント',
        requirepassHelper:
            'この空白のままにして、パスワードが設定されていないことを示します。変更を個別に保存し、コンテナを再起動する必要があります！',
        databases: 'データベースの数',
        maxmemory: '最大メモリ使用量',
        maxmemoryHelper: '0は制限がないことを示します。',
        tcpPort: '現在のリスニングポート。',
        uptimeInDays: '稼働している日。',
        connectedClients: '接続されたクライアントの数。',
        usedMemory: 'Redisの現在のメモリ使用。',
        usedMemoryRss: 'オペレーティングシステムから要求されたメモリサイズ。',
        usedMemoryPeak: 'Redisのピークメモリ消費。',
        memFragmentationRatio: 'メモリフラグメンテーション比。',
        totalConnectionsReceived: '実行以来、接続されているクライアントの総数。',
        totalCommandsProcessed: '実行以降に実行されたコマンドの総数。',
        instantaneousOpsPerSec: 'サーバーによって実行されるコマンドの数。',
        keyspaceHits: 'データベースキーの回数が正常に見つかりました。',
        keyspaceMisses: 'データベースキーを見つけようとする試みの失敗の数。',
        hit: 'データベースキーヒット率を見つけます。',
        latestForkUsec: '最後のfork（）操作に費やされたマイクロ秒数。',
        redisCliHelper: `「Redis-Cli」サービスは検出されません。最初にサービスを有効にします。`,
        redisQuickCmd: 'Redis Quickコマンド',
        recoverHelper: 'これにより、[{0}]でデータが上書きされます。続けたいですか？',
        submitIt: 'データを上書きします',

        baseConf: '基本',
        allConf: '全て',
        restartNow: '今すぐ再起動します',
        restartNowHelper1:
            '構成の変更が有効になった後、システムを再起動する必要があります。データを持続する必要がある場合は、最初に保存操作を実行します。',
        restartNowHelper: 'これは、システムが再起動した後にのみ有効になります。',

        persistence: '持続性',
        rdbHelper1: '2番目、挿入',
        rdbHelper2: 'データの部分',
        rdbHelper3: '条件のいずれかを満たすと、RDBの持続性がトリガーされます。',
        rdbInfo: 'ルールリストの値が1〜100000の範囲であることを確認してください',

        containerConn: 'コンテナ接続',
        connAddress: '住所',
        containerConnHelper:
            'この接続アドレスは、Webサイトのランタイム（PHPなど）またはコンテナで実行されているアプリケーションで使用できます。',
        remoteConn: '外部接続',
        remoteConnHelper2: 'コンテナ環境以外または外部接続にはこのアドレスを使用してください。',
        remoteConnHelper3:
            'デフォルトアクセスアドレスはホストIPです。変更するには、パネル設定ページの「デフォルトアクセスアドレス」設定項目へ移動してください。',
        localIP: 'ローカルIP',
    },
    aiTools: {
        model: {
            model: 'モデル',
            create: 'モデルを追加',
            create_helper: 'を取得 "{0}"',
            ollama_doc: 'Ollama の公式ウェブサイトを訪れて、さらに多くのモデルを検索して見つけることができます。',
            container_conn_helper: 'コンテナ間のアクセスまたは接続にこのアドレスを使用',
            ollama_sync: 'Ollamaモデルの同期中に、以下のモデルが存在しないことが判明しました。削除しますか？',
            from_remote: 'このモデルは1Panelを介してダウンロードされておらず、関連するプルログはありません。',
            no_logs: 'このモデルのプルログは削除されており、関連するログを表示できません。',
        },
        proxy: {
            proxy: 'AI プロキシ強化',
            proxyHelper1: 'ドメインをバインドし、HTTPS を有効にして通信のセキュリティを強化',
            proxyHelper2: 'IP アクセスを制限し、パブリックインターネットでの露出を防止',
            proxyHelper3: 'ストリーミングを有効にする',
            proxyHelper4: '作成後、ウェブサイトリストで確認および管理できます',
            proxyHelper5:
                '有効にすると、アプリストア - インストール済み - Ollama - パラメータでポートの外部アクセスを無効にし、セキュリティを向上させることができます。',
            proxyHelper6: 'プロキシ設定を無効にするには、ウェブサイトリストから削除できます。',
            whiteListHelper: 'ホワイトリスト内のIPのみアクセスを許可する',
        },
        gpu: {
            gpu: 'GPUモニター',
            base: '基本情報',
            gpuHelper:
                '現在のシステムでNVIDIA-SMIまたはXPU-SMIコマンドが検出されませんでした。確認して再試行してください！',
            driverVersion: 'ドライバーバージョン',
            cudaVersion: 'CUDAバージョン',
            process: 'プロセス情報',
            type: 'タイプ',
            typeG: 'グラフィックス',
            typeC: 'コンピュート',
            typeCG: 'コンピュート + グラフィックス',
            processName: 'プロセス名',
            processMemoryUsage: 'メモリ使用量',
            temperatureHelper: '高いGPU温度はGPUの周波数制限を引き起こす可能性があります',
            performanceStateHelper: 'P0（最大性能）からP12（最小性能）まで',
            busID: 'バスID',
            persistenceMode: '永続モード',
            enabled: '有効',
            disabled: '無効',
            persistenceModeHelper: '永続モードはタスクの応答速度を速くしますが、待機時の消費電力が増加します。',
            displayActive: 'グラフィックカード初期化済み',
            displayActiveT: 'はい',
            displayActiveF: 'いいえ',
            ecc: 'エラー訂正およびチェック技術',
            computeMode: 'コンピュートモード',
            default: 'デフォルト',
            exclusiveProcess: '専用プロセス',
            exclusiveThread: '専用スレッド',
            prohibited: '禁止',
            defaultHelper: 'デフォルト：プロセスは並行して実行できます',
            exclusiveProcessHelper:
                '専用プロセス：1つのCUDAコンテキストのみがGPUを使用できますが、複数のスレッドで共有できます',
            exclusiveThreadHelper: '専用スレッド：CUDAコンテキスト内の1つのスレッドのみがGPUを使用できます',
            prohibitedHelper: '禁止：プロセスは同時に実行できません',
            migModeHelper: 'ユーザーレベルでGPUの物理的分離を行うためのMIGインスタンスを作成するために使用されます。',
            migModeNA: 'サポートされていません',
        },
        mcp: {
            server: 'MCP サーバー',
            create: 'サーバーを追加',
            edit: 'サーバーを編集',
            commandHelper: '例: npx -y {0}',
            baseUrl: '外部アクセスパス',
            baseUrlHelper: '例: http://192.168.1.2:8000',
            ssePath: 'SSE パス',
            ssePathHelper: '例: /sse, 他のサーバーと重複しないように注意してください',
            environment: '環境変数',
            envKey: '変数名',
            envValue: '変数値',
            externalUrl: '外部接続アドレス',
            operatorHelper: '{0} に {1} 操作を実行します、続行しますか？',
            domain: 'デフォルトアクセスアドレス',
            domainHelper: '例: *********** または example.com',
            bindDomain: 'ウェブサイトをバインド',
            commandPlaceHolder: '現在、npx およびバイナリスタートアップコマンドのみをサポートしています',
            importMcpJson: 'MCP サーバー設定をインポート',
            importMcpJsonError: 'mcpServers 構造が正しくありません',
            bindDomainHelper:
                'ウェブサイトをバインドした後、インストールされたすべての MCP サーバーのアクセスアドレスを変更し、ポートへの外部アクセスを閉じます',
            outputTransport: '出力タイプ',
            streamableHttpPath: 'ストリーミングパス',
            streamableHttpPathHelper: '例：/mcp、他のサーバーと重複しないように注意してください',
        },
    },
    container: {
        create: 'コンテナを作成します',
        edit: 'コンテナを編集します',
        updateHelper1: 'このコンテナがアプリストアから取得されたことを検出しました。以下の2点にご注意ください:',
        updateHelper2: '1.現在の変更内容は、アプリストアにインストールされているアプリケーションには同期されません。',
        updateHelper3:
            '2.インストールされているページでアプリケーションを変更すると、現在編集されているコンテンツが無効になります。',
        updateHelper4: 'コンテナを編集するには再構築が必要であり、非存在データが失われます。続けたいですか？',
        containerList: 'コンテナリスト',
        operatorHelper: '{0}は次のコンテナで実行されます、続行しますか？',
        operatorAppHelper:
            '「{0}」操作は次のコンテナで実行され、実行中のサービスに影響を与える可能性があります。続けたいですか？',
        start: '始める',
        stop: '停止',
        restart: '再起動',
        kill: '殺す',
        pause: '一時停止',
        unpause: '再開する',
        rename: '名前を変更します',
        remove: '取り除く',
        removeAll: 'すべてを削除します',
        containerPrune: 'プルーン',
        containerPruneHelper1: 'これにより、停止状態にあるすべてのコンテナが削除されます。',
        containerPruneHelper2:
            'コンテナがアプリストアから取得された場合、クリーンアップを実行した後に、「アプリストア -> インストール済み」セクションに移動し、「再構築」ボタンをクリックして再インストールする必要があります。',
        containerPruneHelper3: 'この操作は元に戻すことはできません。続けたいですか？',
        imagePrune: 'プルーン',
        imagePruneSome: 'クリーンラベル付けされています',
        imagePruneSomeEmpty: '「none」タグのある画像はクリーニングできません。',
        imagePruneSomeHelper: 'コンテナでは使用されていないタグ「None」で画像をクリーニングします。',
        imagePruneAll: '未使用の清掃',
        imagePruneAllEmpty: '未使用の画像をクリーニングすることはできません。',
        imagePruneAllHelper: 'コンテナで使用されていない画像を清掃します。',
        networkPrune: 'プルーン',
        networkPruneHelper: 'これにより、すべての未使用ネットワークが削除されます。続けたいですか？',
        volumePrune: 'プルーン',
        volumePruneHelper: 'これにより、未使用のすべてのローカルボリュームが削除されます。続けたいですか？',
        cleanSuccess: '操作は成功しました。このクリーンアップの数:{0}！',
        cleanSuccessWithSpace:
            '操作は成功しています。今回クリーニングされたディスクの数は{0}です。解放されたディスクスペースは{1}です！',
        unExposedPort: '現在のポートマッピングアドレスは127.0.0.1であり、外部アクセスを有効にできません。',
        upTime: '稼働時間',
        fetch: 'フェッチ',
        lines: '線',
        linesHelper: '取得するには、正しい数のログを入力してください！',
        lastDay: '最終日',
        last4Hour: '最後の4時間',
        lastHour: '最後の時間',
        last10Min: '最後の10分',
        cleanLog: 'クリーンログ',
        downLogHelper1: 'これにより、Container {0}からすべてのログをダウンロードします。続けたいですか？',
        downLogHelper2: 'これにより、コンテナ{0}から最近の{0}ログをダウンロードします。続けたいですか？',
        cleanLogHelper: 'これには、コンテナを再起動する必要があり、元に戻すことはできません。続けたいですか？',
        newName: '新しい名前',
        source: 'リソースの使用',
        cpuUsage: 'CPUの使用',
        cpuTotal: 'CPU合計',
        core: 'コア',
        memUsage: 'メモリの使用',
        memTotal: 'メモリ制限',
        memCache: 'メモリキャッシュ',
        ip: 'IPアドレス',
        cpuShare: 'CPU共有',
        cpuShareHelper:
            'コンテナエンジンは、CPU株に1024の基本値を使用します。それを増やして、コンテナにCPU時間を増やすことができます。',
        inputIpv4: '例:***********',
        inputIpv6: '例:2001:0DB8:85A3:0000:0000:8A2E:0370:7334',

        containerFromAppHelper:
            'このコンテナがアプリストアから取得されたことが検出されました。アプリの操作により、現在の編集が無効になる可能性があります。',
        containerFromAppHelper1:
            'インストールされているアプリケーションリストの[PARAM]ボタンをクリックして、編集ページを入力し、コンテナ名を変更します。',
        command: '指示',
        console: 'コンテナインタラクション',
        tty: 'pseudo-tty（-t）を割り当てる',
        openStdin: '添付されていなくてもstdinを開いたままにしてください（-i）',
        custom: 'カスタム',
        emptyUser: '空の場合、デフォルトとしてログインします',
        privileged: '特権',
        privilegedHelper:
            'コンテナがホストに特定の特権操作を実行できるようにします。これにより、コンテナのリスクが増加する可能性があります。注意して使用してください！',
        editComposeHelper: '注:環境変数セットは、デフォルトで1Panel.ENVファイルに書き込まれます。',

        upgradeHelper: 'リポジトリ名/画像名:画像バージョン',
        upgradeWarning2: 'アップグレード操作では、コンテナを再構築する必要があります。続けたいですか？',
        oldImage: '現在の画像',
        targetImage: 'ターゲット画像',
        sameImageContainer: '同一イメージコンテナ',
        sameImageHelper: '同一イメージを使用するコンテナは選択後一括アップグレード可能',
        imageLoadErr: 'コンテナの画像名は検出されません',
        appHelper:
            'このコンテナはアプリストアから取得されたものであり、アップグレードによってサービスが利用不可になる可能性があります。',
        input: '手動入力',
        forcePull: '常に画像を引っ張ってください',
        forcePullHelper: 'これにより、サーバー上の既存の画像が無視され、レジストリから最新の画像が引き出されます。',
        server: 'ホスト',
        serverExample: '80、80-88、IP:80またはIP:80-88',
        containerExample: '80または80-88',
        exposePort: 'ポートを公開します',
        exposeAll: 'すべてを公開します',
        cmdHelper: '例:nginx -g "daemon off;"',
        entrypointHelper: '例:docker-entrypoint.sh',
        autoRemove: '自動削除',
        cpuQuota: 'CPUコアの数',
        memoryLimit: 'メモリ',
        limitHelper: `0に設定すると、制限がないことを意味します。最大値は{0}です`,
        mount: 'マウント',
        volumeOption: '音量',
        hostOption: 'ホスト',
        serverPath: 'サーバーパス',
        containerDir: 'コンテナパス',
        volumeHelper: 'ストレージボリュームのコンテンツが正しいことを確認してください',
        modeRW: 'rw',
        modeR: 'r',
        mode: 'モード',
        env: '環境',
        restartPolicy: 'ポリシーを再起動します',
        always: 'いつも',
        unlessStopped: '止まらない限り',
        onFailure: 'オンフェイル（デフォルトで5回）',
        no: '一度もない',

        refreshTime: '間隔を更新します',
        cache: 'キャッシュ',

        image: '画像|画像',
        imagePull: '引く',
        imagePush: '押す',
        imageDelete: '画像削除',
        imageTagDeleteHelper: 'この画像IDに関連付けられた他のタグを削除します',
        repoName: 'コンテナレジストリ',
        imageName: '画像名',
        pull: '引く',
        path: 'パス',
        importImage: '輸入',
        build: '建てる',
        imageBuild: '建てる',
        pathSelect: 'パス',
        label: 'ラベル',
        imageTag: '画像タグ',
        push: '押す',
        fileName: 'ファイル名',
        export: '輸出',
        exportImage: '画像エクスポート',
        size: 'サイズ',
        tag: 'タグ',
        tagHelper: '1行に1つ。たとえば、 nkey1 = value1  nkey2 = value2',
        imageNameHelper: '画像名とタグ、例:nginx:最新',
        cleanBuildCache: 'クリーンビルドキャッシュ',
        delBuildCacheHelper: `これにより、ビルド中に生成され、元に戻すことができないすべてのキャッシュされたアーティファクトが削除されます。続けたいですか？`,
        urlWarning: 'URLプレフィックスには、http://またはhttps://を含める必要はありません。変更してください。',

        network: 'ネットワーク|ネットワーク',
        networkHelper:
            'これにより、一部のアプリケーションとランタイム環境が適切に機能しない場合があります。続けたいですか？',
        createNetwork: '作成する',
        networkName: '名前',
        driver: 'ドライバ',
        option: 'オプション',
        attachable: '取り付け可能',
        subnet: 'サブネット',
        scope: 'IPスコープ',
        gateway: 'ゲートウェイ',
        auxAddress: 'IPを除外します',

        volume: 'ボリューム|ボリューム',
        volumeDir: 'ボリュームディレクトリ',
        nfsEnable: 'NFSストレージを有効にします',
        nfsAddress: '住所',
        mountpoint: 'マウントポイント',
        mountpointNFSHelper: '例えば/nfs、 /nfs-sh',
        options: 'オプション',
        createVolume: '作成する',

        repo: 'レジストリ',
        createRepo: '追加',
        httpRepoHelper: 'HTTPタイプのリポジトリを操作するにはDockerサービスの再起動が必要です。',
        httpRepo:
            'HTTPプロトコルを選択するには、Dockerサービスを再起動して不安定なレジストリに追加する必要があります。',
        delInsecure: 'クレジットの削除',
        delInsecureHelper: 'これにより、Dockerサービスを再起動して、不安定なレジストリから削除します。続けたいですか？',
        downloadUrl: 'サーバ',
        imageRepo: '画像リポジトリ',
        repoHelper: 'ミラーリポジトリ/組織/プロジェクトが含まれていますか？',
        auth: '認証が必要です',
        mirrorHelper:
            '複数のミラーがある場合、たとえばnewlinesを表示する必要があります:Nhttp://xxxxxxMDaocloudIo Nhttps://xxxxxxMirrorAliyuncsCom',
        registrieHelper: '複数のプライベートリポジトリが存在する場合、たとえばnewlinesを表示する必要があります。',

        compose: '構成|作曲',
        fromChangeHelper: 'ソースを切り替えると、現在の編集されたコンテンツがきれいになります。続けたいですか？',
        composePathHelper: '構成ファイル保存パス:{0}',
        composeHelper:
            '1パネルの編集者またはテンプレートを介して作成された構成は、{0}/docker/composeディレクトリに保存されます。',
        deleteFile: 'ファイルを削除します',
        deleteComposeHelper:
            '構成ファイルや永続的なファイルを含む、コンテナに関連するすべてのファイルを削除します。注意して進めてください！',
        deleteCompose: 'この構成を削除します。',
        createCompose: '作成する',
        composeDirectory: 'ディレクトリ',
        template: 'テンプレート',
        composeTemplate: 'テンプレートを作成|テンプレートを作成します',
        createComposeTemplate: '作成する',
        content: 'コンテンツ',
        contentEmpty: 'コンテンツを空にすることはできません。入力して再試行してください！',
        containerNumber: 'コンテナ番号',
        containerStatus: 'コンテナステータス',
        exited: '終了',
        running: 'ランニング ( {0} / {1} )',
        composeDetailHelper: '構成は1パネルの外部に作成されます。開始および停止操作はサポートされていません。',
        composeOperatorHelper: '{1}操作は{0}で実行されます。続けたいですか？',
        composeDownHelper:
            'これにより、{0}構成の下のすべてのコンテナとネットワークが停止して削除されます。続けたいですか？',

        setting: '設定|設定',
        operatorStatusHelper: 'これは「{0}」Dockerサービスになります。続けたいですか？',
        dockerStatus: 'Dockerサービス',
        daemonJsonPathHelper: '構成パスがdocker.serviceで指定されているものと同じであることを確認してください。',
        mirrors: 'レジストリミラー',
        mirrorsHelper2: '詳細については、公式文書を参照してください。',
        registries: '不安定なレジストリ',
        ipv6Helper:
            'IPv6を有効にするときは、IPv6コンテナネットワークを追加する必要があります。特定の構成手順については、公式ドキュメントを参照してください。',
        ipv6CidrHelper: 'IPv6はコンテナのプール範囲をアドレスします',
        ipv6TablesHelper: 'iptablesルール用のDocker IPv6の自動構成。',
        experimentalHelper:
            'IP6Tablesを有効にするには、この構成をオンにする必要があります。それ以外の場合、IP6テーブルは無視されます',
        cutLog: 'ログオプション',
        cutLogHelper1: '現在の構成は、新しく作成されたコンテナのみに影響します。',
        cutLogHelper2: '構成を有効にするために、既存のコンテナを再作成する必要があります。',
        cutLogHelper3:
            'コンテナを再現すると、データの損失が発生する可能性があることに注意してください。コンテナに重要なデータが含まれている場合は、再構築操作を実行する前に必ずバックアップしてください。',
        maxSize: '最大サイズ',
        maxFile: '最大ファイル',
        liveHelper:
            'デフォルトでは、Docker Daemonが終了すると、実行中のコンテナをシャットダウンします。デーモンを設定して、デーモンが利用できなくなった場合にコンテナが実行され続けるように設定できます。この機能は、Live Restoreと呼ばれます。Live Restoreオプションは、デーモンのクラッシュ、計画された停止、またはアップグレードにより、コンテナのダウンタイムを短縮するのに役立ちます。',
        liveWithSwarmHelper: 'ライブレストアデーモン構成は、群れモードと互換性がありません。',
        iptablesDisable: 'Iptablesを閉じます',
        iptablesHelper1: 'DockerのiPtablesルールの自動構成。',
        iptablesHelper2: 'IPTABLEを無効にすると、コンテナが外部ネットワークと通信できなくなります。',
        daemonJsonPath: 'conf path',
        serviceUnavailable: `現在、Dockerサービスは開始されていません。`,
        startIn: '開始する',
        sockPath: 'UNIXドメインソケット',
        sockPathHelper: 'Dockerデーモンとクライアントの間のコミュニケーションチャネル。',
        sockPathHelper1: 'デフォルトパス:/var/run/docker-x.sock',
        sockPathMsg:
            'ソケットパスの設定を保存すると、Dockerサービスが利用できなくなる可能性があります。続けたいですか？',
        sockPathErr: '正しいDockerソックファイルパスを選択または入力してください',
        related: '関連している',
        includeAppstore: 'アプリストアから取得したコンテナを表示',
        excludeAppstore: 'アプリストアコンテナを隠す',

        cleanDockerDiskZone: 'Dockerが使用するディスクスペースをクリーンアップします',
        cleanImagesHelper: '（コンテナで使用されていないすべての画像をクリーンアップ）',
        cleanContainersHelper: '（停止したすべての容器をクリーンアップ）',
        cleanVolumesHelper: '（未使用のすべてのローカルボリュームをクリーンアップ）',

        makeImage: '画像を作成します',
        newImageName: '新しい画像名',
        commitMessage: 'メッセージをコミットします',
        author: '著者',
        ifPause: '作成中にコンテナを一時停止します',
        ifMakeImageWithContainer: 'このコンテナから新しい画像を作成しますか？',
    },
    cronjob: {
        create: 'Cronジョブを作成します',
        edit: 'Cronジョブを編集します',
        errImport: 'ファイル内容異常:',
        errImportFormat: 'インポートしたスケジュールタスクのデータまたは形式が異常です。確認して再試行してください！',
        importHelper:
            'インポート時に同名のスケジュールタスクは自動的にスキップされます。タスクはデフォルトで【無効】状態に設定され、データ関連付け異常時には【編集待ち】状態に設定されます。',
        changeStatus: 'ステータスを変更します',
        disableMsg: 'これにより、スケジュールされたタスクが自動的に実行されなくなります。続けたいですか？',
        enableMsg: 'これにより、スケジュールされたタスクが自動的に実行されます。続けたいですか？',
        taskType: 'タイプ',
        record: '記録',
        viewRecords: '記録',
        shell: 'シェル',
        log: 'バックアップログ',
        logHelper: 'バックアップシステムログ',
        ogHelper1: '1.1パネルシステムログ',
        logHelper2: '2。サーバーのSSHログインログ',
        logHelper3: '3.すべてのサイトログ',
        containerCheckBox: 'コンテナ内（コンテナコマンドを入力する必要はありません）',
        containerName: 'コンテナ名',
        ntp: '時間同期',
        ntp_helper: 'ツールボックスのクイックセットアップページでNTPサーバーを構成できます。',
        app: 'バックアップアプリ',
        website: 'バックアップウェブサイト',
        rulesHelper: '複数の除外ルールをサポート、英語のカンマ , で区切ります。例：*.log,*.sql',
        lastRecordTime: '最後の実行時間',
        all: '全て',
        failedRecord: '失敗記録',
        successRecord: '成功した記録',
        database: 'バックアップデータベース',
        missBackupAccount: 'バックアップアカウントは見つかりませんでした',
        syncDate: '同期時間',
        clean: 'キャッシュクリーン',
        curl: 'アクセスURL',
        taskName: '名前',
        cronSpec: 'トリガーサイクル',
        cronSpecDoc:
            'カスタム実行周期は【分 時 日 月 曜日】形式のみサポートしています（例: 0 0 * * *）。詳細は公式ドキュメントをご参照ください。',
        cronSpecHelper: '正しい実行期間を入力します',
        cleanHelper:
            'この操作は、すべてのジョブ実行レコード、バックアップファイル、ログファイルを記録します。続けたいですか？',
        directory: 'バックアップディレクトリ',
        sourceDir: 'バックアップディレクトリ',
        snapshot: 'システムスナップショット',
        allOptionHelper:
            '現在のタスク計画は、すべての[{0}]をバックアップすることです。直接ダウンロードは現時点ではサポートされていません。[{{0}]メニューのバックアップリストを確認できます。',
        exclusionRules: '排他的ルール',
        exclusionRulesHelper: '除外ルールは、このバックアップのすべての圧縮操作に適用されます。',
        default_download_path: 'デフォルトのダウンロードリンク',
        saveLocal: 'ローカルバックアップを保持します（クラウドストレージコピーの数と同じ）',
        url: 'URLアドレス',
        targetHelper: 'バックアップアカウントは、パネル設定で維持されます。',
        withImageHelper:
            'アプリストアのイメージをバックアップしますが、スナップショットファイルのサイズが大きくなります。',
        ignoreApp: 'アプリを除外',
        withImage: 'すべてのアプリイメージをバックアップ',
        retainCopies: '記録を保持します',
        retryTimes: 'リトライ回数',
        timeout: 'タイムアウト',
        ignoreErr: 'エラーを無視',
        ignoreErrHelper: 'バックアップ中のエラーを無視し、全てのバックアップタスクを確実に実行します',
        retryTimesHelper: '0は失敗後リトライしないことを意味します',
        retainCopiesHelper: '実行記録とログのために保持するコピーの数',
        retainCopiesHelper1: 'バックアップファイル用に保持するコピーの数',
        retainCopiesUnit: 'コピー（表示）',
        cronSpecRule: 'ライン{0}の実行期間形式は正しくありません。チェックしてもう一度やり直してください！',
        perMonth: '毎月',
        perWeek: '毎週',
        perHour: '毎時間',
        perNDay: '毎日毎日）',
        perDay: '毎日',
        perNHour: 'N時間ごと）',
        perNMinute: '毎分（s）',
        perNSecond: '毎秒毎回',
        per: '毎',
        day: '日',
        dayUnit: 'd',
        monday: '月曜日',
        tuesday: '火曜日',
        wednesday: '水曜日',
        thursday: '木曜日',
        friday: '金曜日',
        saturday: '土曜日',
        sunday: '日曜日',
        shellContent: 'スクリプト',
        errRecord: '誤ったロギング',
        errHandle: 'cronjob実行障害',
        noRecord: 'Cronジョブをトリガーすると、ここにレコードが表示されます。',
        cleanData: 'クリーンデータ',
        cleanRemoteData: 'リモートデータを削除',
        cleanDataHelper: 'このタスク中に生成されたバックアップファイルを削除します。',
        noLogs: 'タスク出力はまだありません...',
        errPath: 'バックアップパス[{0}]エラー、ダウンロードできません！',
        cutWebsiteLog: 'ウェブサイトのログローテーション',
        cutWebsiteLogHelper: '回転したログファイルは、1パネルのバックアップディレクトリにバックアップされます。',

        requestExpirationTime: 'リクエストの有効期限（時間）のアップロード',
        unitHours: 'ユニット:時間',
        alertTitle: '計画タスク -  {0}「{1}」タスク障害アラート',
        library: {
            script: 'スクリプト',
            isInteractive: '対話型',
            interactive: '対話型スクリプト',
            interactiveHelper: '実行中にユーザー入力が必要で、スケジュールタスクでは使用できません。',
            library: 'スクリプトライブラリ',
            create: 'スクリプトを追加',
            edit: 'スクリプトを編集',
            groupHelper:
                'スクリプトの特徴に基づいて異なるグループを設定することで、スクリプトのフィルタリング操作をより迅速に行うことができます。',
            handleHelper: '{0} で {1} スクリプトを実行します。続行しますか？',
            noSuchApp:
                '{0} サービスが検出されませんでした。スクリプトライブラリを使って素早くインストールしてください！',
            syncHelper:
                'システムスクリプトライブラリを同期します。この操作はシステムスクリプトのみ影響します。続行しますか？',
        },
    },
    monitor: {
        globalFilter: 'グローバルフィルター',
        enableMonitor: '有効にする',
        storeDays: '有効期限',
        cleanMonitor: '監視記録をきれいにします',

        avgLoad: 'ロード平均',
        loadDetail: '詳細を読み込みます',
        resourceUsage: '利用',
        networkCard: 'ネットワークインターフェイス',
        read: '読む',
        write: '書く',
        readWriteCount: 'I/O操作',
        readWriteTime: 'I/Oレイテンシ',
        today: '今日',
        yesterday: '昨日',
        lastNDay: '最後の{0}日',
        memory: 'メモリ',
        cache: 'キャッシュ',
        disk: 'ディスク',
        network: 'ネットワーク',
        up: '上',
        down: '下',
        interval: '間隔（分）',

        gpuUtil: 'GPU利用',
        temperature: '温度',
        performanceState: 'パフォーマンス状態',
        powerUsage: '電力使用量',
        memoryUsage: 'メモリの使用',
        fanSpeed: 'ファンの速度',
    },
    terminal: {
        local: 'ローカル',
        localHelper: 'ローカル名はシステムのローカル識別にのみ使用されます。',
        connLocalErr: '自動的に認証できない場合は、ローカルサーバーのログイン情報を入力してください。',
        testConn: 'テスト接続',
        saveAndConn: '保存して接続します',
        connTestOk: '利用可能な接続情報',
        connTestFailed: '接続は利用できません。接続情報を確認してください。',
        host: 'ホスト|ホスト',
        createConn: '新しい接続',
        manageGroup: 'グループを管理します',
        noHost: 'ホストはありません',
        groupChange: 'グループを変更します',
        expand: 'すべて拡張します',
        fold: 'すべての契約',
        batchInput: 'バッチ処理',
        quickCommand: 'クイックコマンド|クイックコマンド',
        quickCommandHelper: '「端末 - >端子」の下部にあるクイックコマンドを使用できます。',
        groupDeleteHelper:
            'グループが削除された後、グループ内のすべての接続がデフォルトグループに移行されます。続けたいですか？',
        command: '指示',
        quickCmd: 'クイックコマンド',
        addHost: '追加',
        localhost: 'localhost',
        ip: '住所',
        authMode: '認証',
        passwordMode: 'パスワード',
        rememberPassword: '認証情報を忘れないでください',
        keyMode: 'privatekey',
        key: '秘密鍵',
        keyPassword: '秘密キーパスワード',
        emptyTerminal: '現在接続されている端子はありません。',
    },
    toolbox: {
        common: {
            toolboxHelper: '一部のインストールおよび使用に関する問題については、以下を参照してください',
        },
        swap: {
            swap: 'パーティションを交換します',
            swapHelper1: 'スワップのサイズは、特定の要件に基づいて調整可能な物理メモリの1〜2倍である必要があります。',
            swapHelper2:
                'スワップファイルを作成する前に、スワップファイルサイズが対応するディスクスペースを占有するため、システムディスクに十分な利用可能なスペースがあることを確認してください。',
            swapHelper3:
                'スワップはメモリの圧力を軽減するのに役立ちますが、それは代替案にすぎません。スワップに過度に依存すると、システムのパフォーマンスが低下する可能性があります。メモリの増加を優先したり、アプリケーションメモリの使用量を最適化することをお勧めします。',
            swapHelper4: '通常のシステム操作を確保するために、スワップの使用を定期的に監視することをお勧めします。',
            swapDeleteHelper:
                'この操作は、スワップパーティション{0}を削除します。システムセキュリティ上の理由から、対応するファイルは自動的に削除されません。削除が必要な場合は、手動で続行してください！',
            saveHelper: '最初に現在の設定を保存してください！',
            saveSwap: '現在の構成を保存すると、スワップパーティション{0}サイズを{1}に調整します。続けたいですか？',
            swapMin: '最小パーティションサイズは40 kbです。変更してもう一度やり直してください！',
            swapMax: 'パーティションサイズの最大値は{0}です。変更してもう一度やり直してください！',
            swapOff: '最小パーティションサイズは40 kbです。0に設定すると、スワップパーティションが無効になります。',
        },
        device: {
            dnsHelper: 'DNSサーバー',
            dnsAlert:
                '注意！/etc/resolv.confファイルの構成を変更すると、システムが再起動した後、ファイルがデフォルト値に復元されます。',
            dnsHelper1:
                '複数のDNSエントリがある場合は、新しい行に表示する必要があります。例えば、 n114.114.114.114  n8.8.8.8',
            hostsHelper: 'ホスト名解像度',
            hosts: 'ドメイン',
            hostAlert: '隠されたコメントレコード、すべての構成ボタンをクリックして表示または設定してください',
            toolbox: 'クイック設定',
            hostname: 'ホスト名',
            passwd: 'システムパスワード',
            passwdHelper: '入力文字は$ and＆＆を含めることはできません',
            timeZone: 'タイムゾーン',
            localTime: 'サーバー時間',
            timeZoneChangeHelper: 'システムタイムゾーンを変更するには、サービスを再起動する必要があります。続く？',
            timeZoneHelper: `「TimeDatectl」コマンドをインストールしない場合、タイムゾーンを変更することはできません。システムはそのコマンドを使用してタイムゾーンを変更するためです。`,
            timeZoneCN: '北京',
            timeZoneAM: 'ロサンゼルス',
            timeZoneNY: 'ニューヨーク',
            ntpALi: 'アリババ',
            ntpGoogle: 'グーグル',
            syncSite: 'NTPサーバー',
            hostnameHelper: `ホスト名の変更は、「hostnamectl」コマンドに依存します。コマンドがインストールされていない場合、変更が失敗する可能性があります。`,
            userHelper: `ユーザー名は、取得の「whoami」コマンドに依存します。コマンドがインストールされていない場合、検索が失敗する可能性があります。`,
            passwordHelper: `パスワードの変更は、「chpasswd」コマンドに依存します。コマンドがインストールされていない場合、変更が失敗する可能性があります。`,
            hostHelper: '提供されたコンテンツには空の値があります。変更後に確認して再試行してください！',
            dnsCheck: 'テストの可用性',
            dnsOK: 'DNS構成情報が利用可能です！',
            dnsTestFailed: `DNS構成情報は利用できません。`,
        },
        fail2ban: {
            sshPort: 'SSHポートを聞いてください',
            sshPortHelper: '現在のFAL2BANは、ホストのSSH接続ポートに耳を傾けます',
            unActive: `現在、Fail2Banサービスは有効になっていません。`,
            operation: 'fail2banサービスで操作「{0}」を実行します。続けたいですか？',
            fail2banChange: 'fail2ban構成の変更',
            ignoreHelper: 'AllowListのIPリストは、ブロックについて無視されます。続けたいですか？',
            bannedHelper: 'ブロックリストのIPリストは、サーバーによってブロックされます。続けたいですか？',
            maxRetry: '最大再試行',
            banTime: '禁止時間',
            banTimeHelper: 'デフォルトの禁止時間は10分、-1は永続的な禁止を示します',
            banTimeRule: '有効な禁止時間または-1を入力してください',
            banAllTime: '恒久的な禁止',
            findTime: '発見期間',
            banAction: '禁止措置',
            banActionOption: '{0}を使用して指定されたIPアドレスを禁止',
            allPorts: '（すべてのポート）',
            ignoreIP: 'IP AllowList',
            bannedIP: 'IPブロックリスト',
            logPath: 'ログパス',
            logPathHelper: 'デフォルトは/var/log/secureまたは/var/log/auth.logです',
        },
        ftp: {
            ftp: 'FTPアカウント|FTPアカウント',
            notStart: 'FTP Serviceは現在実行されていません。最初に開始してください！',
            operation: 'これにより、FTPサービスで「{0}」操作が実行されます。続けたいですか？',
            noPasswdMsg: '現在のFTPアカウントパスワードを取得できません。パスワードを設定して再試行してください！',
            enableHelper: '選択したFTPアカウントを有効にすると、アクセス許可が復元されます。続けたいですか？',
            disableHelper: '選択したFTPアカウントを無効にすると、アクセス許可が取り消されます。続けたいですか？',
            syncHelper: 'サーバーとデータベースの間でFTPアカウントデータを同期します。続けたいですか？',
            dirSystem:
                'このディレクトリはシステム予約領域です。変更するとシステムがクラッシュする可能性があります。修正して再試行してください！',
            dirHelper: 'FTPを有効にするにはディレクトリ権限の変更が必要です。慎重に選択してください',
            dirMsg: 'FTPを有効にすると{0}ディレクトリ全体の権限が変更されます。続行しますか？',
        },
        clam: {
            clam: 'ウイルススキャン',
            cron: 'スケジュールされたスキャン',
            cronHelper: 'プロフェッショナルバージョンは、スケジュールされたスキャン機能をサポートしています',
            specErr: '実行スケジュールフォーマットエラー、チェックして再試行してください！',
            disableMsg:
                'スケジュールされた実行を停止すると、このスキャンタスクが自動的に実行されなくなります。続けたいですか？',
            enableMsg:
                'スケジュールされた実行を有効にすることで、このスキャンタスクは定期的に自動的に実行できます。続けたいですか？',
            showFresh: '署名のアップデーターサービスを表示します',
            hideFresh: '署名のアップデーターサービスを非表示にします',
            clamHelper:
                'Clamavの最小推奨構成は、3ギブ以上のRAM、2.0 GHz以上のシングルコアCPU、および少なくとも5 GIBの利用可能なハードディスクスペースです。',
            notStart: 'Clamav Serviceは現在実行されていません。最初に開始してください！',
            removeRecord: 'ペポートファイルを削除します',
            noRecords: '[トリガー]ボタンをクリックしてスキャンを開始すると、ここにレコードが表示されます。',
            removeResultHelper: 'タスク実行中に生成されたレポートファイルを削除して、ストレージスペースを解放します。',
            removeInfected: 'ウイルスファイルを削除します',
            removeInfectedHelper:
                'サーバーのセキュリティと通常の操作を確保するために、タスク中に検出されたウイルスファイルを削除します。',
            clamCreate: 'スキャンルールを作成します',
            infectedStrategy: '感染した戦略',
            removeHelper: 'ウイルスファイルを削除して、慎重に選択してください！',
            move: '動く',
            moveHelper: 'ウイルスファイルを指定されたディレクトリに移動します',
            copyHelper: 'ウイルスファイルを指定されたディレクトリにコピーします',
            none: '何もしません',
            noneHelper: 'ウイルスファイルにアクションを実行しません',
            scanDir: 'スキャンディレクトリ',
            infectedDir: '感染したディレクトリ',
            scanDate: 'スキャン日',
            scanResult: 'テールをスキャンします',
            tail: '線',
            infectedFiles: '感染したファイル',
            log: '詳細',
            clamConf: 'クラマブデーモン',
            clamLog: '@:toolbox.clam.clamconfログ',
            freshClam: 'フレッシュクラム',
            freshClamLog: '@:toolbox.clam.freshclamログ',
            alertHelper: 'プロフェッショナル版は、定期スキャンとSMSアラート機能をサポートしています',
            alertTitle: 'ウイルススキャンタスク（{0}」感染したファイルアラートが検出されました',
        },
    },
    logs: {
        core: 'パネルサービス',
        agent: 'ノード監視',
        panelLog: 'パネルログ',
        operation: '操作ログ',
        login: 'ログインログ',
        loginIP: 'ログインIP',
        loginAddress: 'ログインアドレス',
        loginAgent: 'ログインエージェント',
        loginStatus: '状態',
        system: 'システムログ',
        deleteLogs: 'クリーンログ',
        resource: 'リソース',
        detail: {
            ai: 'AI',
            groups: 'グループ',
            hosts: 'ホスト',
            apps: 'アプリケーション',
            websites: 'ウェブサイト',
            containers: 'コンテナ',
            files: 'ファイル管理',
            runtimes: 'ランタイム',
            process: 'プロセス管理',
            toolbox: 'ツールボックス',
            backups: 'バックアップ / 復元',
            tampers: '改ざん防止',
            xsetting: 'インターフェース設定',
            logs: 'ログ監査',
            settings: 'パネル設定',
            cronjobs: 'スケジュールされたタスク',
            databases: 'データベース',
            waf: 'WAF',
            licenses: 'ライセンス',
            nodes: 'ノード',
            commands: 'クイックコマンド',
        },
        websiteLog: 'ウェブサイトログ',
        runLog: 'ログを実行します',
        errLog: 'エラーログ',
    },
    file: {
        fileDirNum: '{0} 個のディレクトリ、{1} 個のファイル、',
        currentDir: '現在のディレクトリ',
        dir: 'フォルダ',
        upload: 'アップロード',
        uploadFile: '@:file.upload@.lower:file.file',
        uploadDirectory: '@:file.upload@.lower:file.dir',
        download: 'ダウンロード',
        fileName: 'ファイル名',
        search: '検索',
        mode: '権限',
        editPermissions: '編集@:file.mode',
        owner: '所有者',
        file: 'ファイル',
        remoteFile: 'リモートダウンロード',
        share: '共有',
        sync: 'データ同期',
        size: 'サイズ',
        updateTime: '修正',
        rename: '名前を変更します',
        role: '権限',
        info: '属性',
        linkFile: 'ソフトリンク',
        batchoperation: 'バッチ操作',
        shareList: '共有リスト',
        zip: '圧縮',
        group: 'グループ',
        path: 'パス',
        public: 'その他',
        setRole: '設定権限',
        link: 'ファイルリンク',
        rRole: '読む',
        wRole: '書く',
        xRole: '実行可能',
        name: '名前',
        compress: '圧縮',
        deCompress: '減圧',
        compressType: '圧縮形式',
        compressDst: 'パスを圧縮します',
        replace: '既存のファイルを上書きします',
        compressSuccess: '正常に圧縮されました',
        deCompressSuccess: '減圧は成功しました',
        deCompressDst: 'パスを減圧します',
        linkType: 'リンクタイプ',
        softLink: 'ソフトリンク',
        hardLink: 'ハードリンク',
        linkPath: 'リンクパス',
        selectFile: '[ファイル]を選択します',
        downloadUrl: 'リモートURL',
        downloadStart: 'ダウンロードが始まりました',
        moveSuccess: '正常に移動しました',
        copySuccess: '正常にコピーされました',
        move: '動く',
        calculate: '計算します',
        canNotDeCompress: 'このファイルを解凍できません',
        uploadSuccess: '正常にアップロードします',
        downloadProcess: '進捗状況をダウンロードします',
        downloading: 'ダウンロード...',
        infoDetail: 'ファイルプロパティ',
        root: 'ルートディレクトリ',
        list: 'ファイルリスト',
        sub: 'サブフォルダー',
        downloadSuccess: 'ダウンロードに成功しました',
        theme: 'テーマ',
        language: '言語',
        eol: '行の終わり',
        copyDir: 'コピー',
        paste: 'ペースト',
        changeOwner: 'ユーザーグループとユーザーグループを変更します',
        containSub: '許可変更を再帰的に適用します',
        ownerHelper:
            'PHP運用環境のデフォルトユーザー:ユーザーグループは1000:1000です。コンテナの内側と外側のユーザーが矛盾を示すのは普通です',
        searchHelper: '*などのワイルドカードをサポート',
        uploadFailed: '[{0}]ファイルアップロードファイル',
        fileUploadStart: 'アップロード[{0}] ....',
        currentSelect: '現在の選択:',
        unsupportedType: 'サポートされていないファイルタイプ',
        deleteHelper: '次のファイルを削除したいですか？デフォルトでは、削除後にリサイクルビンに入ります',
        fileHelper: `注意:1. 検索結果は並べ替え機能をサポートしていません 2. フォルダはサイズで並べ替えできません。`,
        forceDeleteHelper: 'ファイルを永久に削除します（リサイクルビンを入力せずに、直接削除します）',
        recycleBin: 'ビンをリサイクルします',
        sourcePath: 'オリジナルパス',
        deleteTime: '時間を削除します',
        confirmReduce: '次のファイルを復元したいですか？',
        reduceSuccess: '成功して復元します',
        reduce: '削減',
        reduceHelper: '同じ名前のファイルまたはディレクトリが元のパスに存在する場合、上書きされます。続けたいですか？',
        clearRecycleBin: 'クリーン',
        clearRecycleBinHelper: 'リサイクルビンを掃除しますか？',
        favorite: 'お気に入り',
        removeFavorite: 'お気に入りから取り外しますか？',
        addFavorite: 'お気に入りの追加/削除',
        clearList: 'クリーンリスト',
        deleteRecycleHelper: '次のファイルを永続的に削除する必要がありますか？',
        typeErrOrEmpty: '[{0}]ファイルタイプは間違っているか、空のフォルダーです',
        dropHelper: 'ここにアップロードするファイルをドラッグします',
        fileRecycleBin: 'リサイクルビンを有効にします',
        fileRecycleBinMsg: '{0}リサイクルビン',
        wordWrap: '自動的にラップします',
        deleteHelper2: '選択したファイルを削除する必要がありますか？削除操作をロールバックすることはできません',
        ignoreCertificate: '不安定なサーバー接続を許可します',
        ignoreCertificateHelper:
            '不安定なサーバー接続を可能にすると、データが漏れたり改ざんしたりする可能性があります。ダウンロードソースを信頼する場合にのみ、このオプションを使用します。',
        uploadOverLimit: 'ファイルの数は1000を超えています！圧縮してアップロードしてください',
        clashDitNotSupport: 'ファイル名は、.1panel_clashを含むことを禁止されています',
        clashDeleteAlert: `「リサイクルビン」フォルダーを削除することはできません`,
        clashOpenAlert: '「リサイクルビン」ボタンをクリックして、リサイクルビンディレクトリを開きます',
        right: 'フォワード',
        back: '戻る',
        top: '戻って行きます',
        up: '戻って行きます',
        openWithVscode: 'VSコードで開く',
        vscodeHelper:
            'VSコードがローカルにインストールされ、SSHリモートプラグインが構成されていることを確認してください',
        saveContentAndClose: 'ファイルが変更されましたが、保存して閉じたいですか？',
        saveAndOpenNewFile: 'ファイルが変更されましたが、新しいファイルを保存して開きますか？',
        noEdit: 'ファイルは変更されておらず、これを行う必要はありません！',
        noNameFolder: '無題のフォルダー',
        noNameFile: '無題のファイル',
        minimap: 'コードミニマップ',
        fileCanNotRead: 'ファイルは読み取れません',
        panelInstallDir: `1Panelインストールディレクトリは削除できません`,
        wgetTask: 'ダウンロードタスク',
        existFileTitle: '同名ファイルの警告',
        existFileHelper: 'アップロードしたファイルに同じ名前のファイルが含まれています。上書きしますか？',
        existFileSize: 'ファイルサイズ（新しい -> 古い）',
        existFileDirHelper: '選択したファイル/フォルダーには同じ名前のものが既に存在します。慎重に操作してください！',
        coverDirHelper: '上書きするフォルダを選択すると、対象パスにコピーされます！',
        noSuchFile: 'ファイルまたはディレクトリが見つかりませんでした。確認して再試行してください。',
        setting: '設定',
        showHide: '隠しファイルを表示',
        noShowHide: '隠しファイルを表示しない',
        cancelUpload: 'アップロードをキャンセル',
        cancelUploadHelper: 'アップロードをキャンセルするかどうか、キャンセル後、アップロードリストはクリアされます。',
    },
    ssh: {
        autoStart: 'オートスタート',
        enable: 'AutoStartを有効にします',
        disable: 'AutoStartを無効にします',
        sshAlert:
            'リストデータは、ログイン日に基づいてソートされます。タイムゾーンを変更したり、他の操作を実行したりすると、ログインログの日付が逸脱を引き起こす可能性があります。',
        sshAlert2:
            '「ツールボックス」で「Fail2ban」を使用して、ブルートフォース攻撃を試みるIPアドレスをブロックすることができます。これにより、ホストのセキュリティが向上します。',
        sshOperate: 'SSHサービスの操作「{0}」が実行されます。続けたいですか？',
        sshChange: 'SSH設定',
        sshChangeHelper: 'このアクションは「{0}」が「{1}」に変更されました。続けたいですか？',
        sshFileChangeHelper:
            '構成ファイルを変更すると、サービスの可用性が発生する場合があります。この操作を実行するときは注意してください。続けたいですか？',
        port: 'ポート',
        portHelper: 'SSHサービスが耳を傾けるポートを特定します。',
        listenAddress: '住所を聞いてください',
        allV4V6: '0.0.0.0: {0 }(IPv4）および::: {0}（IPv6）',
        listenHelper:
            'IPv4とIPv6の両方の設定を空白のままにしておくと、「0.0.0.0:0:0:0}」と「::: {0}（IPv6）」で聞きます。',
        addressHelper: 'SSHサービスが耳を傾けるアドレスを指定します。',
        permitRootLogin: 'ルートユーザーログインを許可します',
        rootSettingHelper: 'ルートユーザーのデフォルトのログインメソッドは「SSHログインを許可」です。',
        rootHelper1: 'SSHログインを許可します',
        rootHelper2: 'SSHログインを無効にします',
        rootHelper3: 'キーログインのみが許可されています',
        rootHelper4: '事前定義されたコマンドのみを実行できます。他の操作を実行することはできません。',
        passwordAuthentication: 'パスワード認証',
        pwdAuthHelper: 'パスワード認証を有効にするかどうか。このパラメーターはデフォルトで有効になります。',
        pubkeyAuthentication: '重要な認証',
        privateKey: '秘密鍵',
        publicKey: '公開鍵',
        password: 'パスワード',
        createMode: '作成方法',
        generate: '自動生成',
        unSyncPass: '鍵パスワードは同期できません',
        syncHelper: '同期操作は無効なキーをクリーンアップし、新しい完全なキーペアを同期します。続行しますか？',
        input: '手動入力',
        import: 'ファイルアップロード',
        pubkey: '重要な情報',
        encryptionMode: '暗号化モード',
        pubKeyHelper: '現在の鍵情報はユーザー {0} にのみ有効です',
        passwordHelper: '6〜10桁と英語のケースを含めることができます',
        reGenerate: 'キーを再生します',
        keyAuthHelper: 'キー認証を有効にするかどうか。',
        useDNS: '使用済み',
        dnsHelper: 'DNS解像度関数がSSHサーバーで有効になっているかどうかを制御して、接続のIDを確認します。',
        analysis: '統計情報',
        denyHelper:
            '次のアドレスで「拒否」操作を実行します。設定後、IPはサーバーへのアクセスが禁止されます。続けたいですか？',
        acceptHelper:
            '次のアドレスで「受け入れる」操作を実行します。設定後、IPは通常のアクセスを取り戻します。続けたいですか？',
        noAddrWarning: '[{0}]アドレスは現在選択されていません。チェックしてもう一度やり直してください！',
        loginLogs: 'ログインログ',
        loginMode: 'モード',
        authenticating: '鍵',
        publickey: '鍵',
        belong: '属する',
        local: '地元',
        session: 'セッション|セッション',
        loginTime: 'ログイン時間',
        loginIP: 'ログインIP',
        stopSSHWarn: 'このSSH接続を切断するかどうか',
    },
    setting: {
        panel: 'パネル',
        user: 'パネルユーザー',
        userChange: 'パネルユーザーを変更します',
        userChangeHelper: 'パネルユーザーを変更すると、ログアウトします。続く？',
        passwd: 'パネルパスワード',
        emailHelper: 'パスワード取得用',
        title: 'パネルエイリアス',
        panelPort: 'パネルポート',
        titleHelper: '英語、漢字、数字、スペース、および一般的な特殊文字を含む3～30文字の長さをサポートします',
        portHelper:
            '推奨されるポート範囲は8888〜65535です。注:サーバーにセキュリティグループがある場合は、事前にセキュリティグループから新しいポートを許可します',
        portChange: 'ポート変更',
        portChangeHelper: 'サービスポートを変更し、サービスを再起動します。続けたいですか？',
        theme: 'テーマ',
        menuTabs: 'タブメニュー',
        dark: '暗い',
        darkGold: 'ダークゴールド',
        light: 'ライト',
        auto: 'システムをフォローします',
        language: '言語',
        languageHelper:
            'デフォルトでは、ブラウザ言語に従います。このパラメーターは、現在のブラウザでのみ有効になります',
        sessionTimeout: 'セッションタイムアウト',
        sessionTimeoutError: '最小セッションタイムアウトは300秒です',
        sessionTimeoutHelper: '{0}秒以上操作がない場合、パネルは自動的にログアウトされます。',
        systemIP: 'デフォルトアクセスアドレス',
        systemIPHelper:
            'アプリケーションリダイレクト、コンテナアクセスなどの機能はこのアドレスを使用して転送されます。各ノードで異なるアドレスを設定できます。',
        proxy: 'サーバープロキシ',
        proxyHelper: 'プロキシサーバーを設定した後、次のシナリオで効果的になります。',
        proxyHelper1:
            'アプリストアからのインストールパッケージのダウンロードと同期（プロフェッショナルエディションのみ）',
        proxyHelper2: 'システムの更新と更新情報検索（プロフェッショナルエディションのみ）',
        proxyHelper4:
            'Dockerネットワークはプロキシサーバーを通じてアクセスされます（プロフェッショナルエディションのみ）',
        proxyHelper5: 'システムタイプスクリプトライブラリの統一下載と同期（プロフェッショナル版機能）',
        proxyHelper6: '証明書を申請する（プロ版機能）',
        proxyHelper3: 'システムライセンスの確認と同期',
        proxyType: 'プロキシタイプ',
        proxyUrl: 'プロキシアドレス',
        proxyPort: 'プロキシポート',
        proxyPasswdKeep: 'パスワードを覚えておいてください',
        proxyDocker: 'Dockerプロキシ',
        proxyDockerHelper:
            'プロキシサーバーの構成をDockerに同期し、オフラインサーバーイメージの引っ張りやその他の操作をサポートします',
        syncToNode: '子ノードに同期',
        syncToNodeHelper: '選択したノードへの同期設定',
        nodes: 'ノード',
        selectNode: 'ノードを選択',
        selectNodeError: 'ノードを選択してください',
        apiInterface: 'APIを有効にします',
        apiInterfaceClose: '閉じたら、APIインターフェイスにアクセスできません。続けたいですか？',
        apiInterfaceHelper: 'サードパーティのアプリケーションにAPIにアクセスできるようにします。',
        apiInterfaceAlert1: `サーバーのセキュリティリスクが増加する可能性があるため、生産環境で有効にしないでください。`,
        apiInterfaceAlert2: `サードパーティのアプリケーションを使用してAPIを呼び出して、潜在的なセキュリティの脅威を防止しないでください。`,
        apiInterfaceAlert3: 'APIドキュメント',
        apiInterfaceAlert4: '使用ドキュメント',
        apiKey: 'APIキー',
        apiKeyHelper: 'APIキーは、サードパーティアプリケーションに使用されてAPIにアクセスします。',
        ipWhiteList: 'IP AllowList',
        ipWhiteListEgs: '1行に1つ。たとえば、 n172.161.10.111  n172.161.10.0/24',
        ipWhiteListHelper: 'AllowList内のIPSはAPIにアクセスできます、0.0.0.0/0（すべての IPv4）、::/0（すべての IPv6）',
        apiKeyReset: 'インターフェイスキーリセット',
        apiKeyResetHelper: '関連するキーサービスは無効になります。サービスに新しいキーを追加してください',
        confDockerProxy: 'Dockerプロキシを構成します',
        restartNowHelper: 'Dockerプロキシの構成には、Dockerサービスを再起動する必要があります。',
        restartNow: 'すぐに再起動します',
        restartLater: '後で手動で再起動',
        systemIPWarning:
            '現在のノードにはデフォルトアクセスアドレスが設定されていません。パネル設定から設定してください！',
        systemIPWarning1: `現在のサーバーアドレスは{0}に設定されており、クイックリダイレクトは不可能です！`,
        defaultNetwork: 'ネットワークカード',
        syncTime: 'サーバー時間',
        timeZone: 'タイムゾーン',
        timeZoneChangeHelper: 'タイムゾーンを変更するには、サービスを再起動する必要があります。続けたいですか？',
        timeZoneHelper:
            'TimeZoneの変更は、システムのTimeDatectlサービスに依存します。1パネルサービスを再起動した後に有効になります。',
        timeZoneCN: '北京',
        timeZoneAM: 'ロサンゼルス',
        timeZoneNY: 'ニューヨーク',
        ntpALi: 'アリババ',
        ntpGoogle: 'グーグル',
        syncSite: 'NTPサーバー',
        syncSiteHelper: 'この操作は、システム時間同期のソースとして{0}を使用します。続けたいですか？',
        changePassword: 'パスワードを変更する',
        oldPassword: '元のパスワード',
        newPassword: '新しいパスワード',
        retryPassword: 'パスワードを認証する',
        noSpace: '入力情報にはスペース文字を含めることはできません',
        duplicatePassword: '新しいパスワードは元のパスワードと同じになることはできません。再入力してください！',
        diskClean: 'キャッシュクリーン',
        developerMode: 'プレビュープログラム',
        developerModeHelper: `それらが広くリリースされる前に、新しい機能と修正を体験し、早期のフィードバックを提供できます。`,
        thirdParty: 'サードパーティのアカウント',
        noTypeForCreate: '現在、バックアップタイプは作成されていません',
        LOCAL: 'サーバーディスク',
        OSS: 'アリ私たち',
        S3: 'amazonS3',
        mode: 'モード',
        MINIO: 'ミニオ',
        SFTP: 'sftp',
        WebDAV: 'webdav',
        WebDAVAlist: 'WebDav Connect Alistは、公式ドキュメントを参照できます',
        OneDrive: 'Microsoft Onedrive',
        isCN: '世紀のインターネット',
        isNotCN: '国際版',
        client_id: 'クライアントID',
        client_secret: 'クライアントの秘密',
        redirect_uri: 'URLをリダイレクトします',
        onedrive_helper: 'カスタム構成は公式ドキュメントで参照できます',
        refreshTime: 'トークン更新時間',
        refreshStatus: 'トークン更新ステータス',
        backupDir: 'バックアップディレクトリ',
        codeWarning: '現在の承認コード形式が正しくありません。もう一度確認してください！',
        code: '認証コード',
        codeHelper:
            '[取得]ボタンをクリックしてから、リダイレクトリンクの「コード」の後にコンテンツをログインしてコピーします。この入力ボックスに貼り付けます。特定の手順については、公式のドキュメントを参照してください。',
        loadCode: '取得する',
        COS: 'tencent cos',
        ap_beijing_1: '北京ゾーン1',
        ap_beijing: '北京',
        ap_nanjing: '南京',
        ap_shanghai: '上海',
        ap_guangzhou: '広州',
        ap_chengdu: '成都',
        ap_chongqing: '唐辛子',
        ap_shenzhen_fsi: '深圳金融',
        ap_shanghai_fsi: '上海金融',
        ap_beijing_fsi: '北京金融',
        ap_hongkong: '香港、中国',
        ap_singapore: 'シンガポール',
        ap_mumbai: 'ムンバイ',
        ap_jakarta: 'ジャカルタ',
        ap_seoul: 'ソウル',
        ap_bangkok: 'バンコク',
        ap_tokyo: '東京',
        na_siliconvalley: 'シリコンバレー（米国西）',
        na_ashburn: 'アッシュバーン（米国東）',
        na_toronto: 'トロント',
        sa_saopaulo: 'サンパウロ',
        eu_frankfurt: 'フランクフルト',
        KODO: 'Qiniコード',
        scType: 'ストレージタイプ',
        typeStandard: '標準ストレージ',
        typeStandard_IA: '低頻度ストレージ',
        typeArchive: 'アーカイブストレージ',
        typeDeep_Archive: '深層アーカイブストレージ',
        scLighthouse: 'デフォルト：軽量オブジェクトストレージはこのストレージタイプのみサポートしています',
        scStandard:
            '標準ストレージは、リアルタイムアクセスが多いホットファイルや頻繁なデータ交換などの業務シナリオに適しています。',
        scStandard_IA:
            '低頻度ストレージは、アクセス頻度が低い業務シナリオ（例:月に1〜2回程度のアクセス）に適しており、最低30日間の保存が必要です。',
        scArchive: 'アーカイブストレージは、極めて低いアクセス頻度（例:半年に1回程度）の業務シナリオに適しています。',
        scDeep_Archive:
            '深層アーカイブストレージは、極めて低いアクセス頻度（例:年に1〜2回程度）の業務シナリオに適しています。',
        archiveHelper:
            'アーカイブストレージのファイルは直接ダウンロードできず、対応するクラウドサービスプロバイダーのサイトで復元操作を行う必要があります。慎重に使用してください。',
        backupAlert:
            '理論的には、クラウドプロバイダーがS3プロトコルに対応していれば、現行のAmazon S3クラウドストレージを使ってバックアップできます。具体的な設定については、',
        domain: 'ドメインを加速します',
        backupAccount: 'バックアップアカウント|バックアップアカウント',
        loadBucket: 'バケツを入手してください',
        accountName: 'アカウント名',
        accountKey: 'アカウントキー',
        address: '住所',
        path: 'パス',

        safe: '安全',
        bindInfo: 'バインド情報',
        bindAll: 'すべてを聞いてください',
        bindInfoHelper:
            'サービスリスニングアドレスまたはプロトコルを変更すると、サービスが利用できない場合があります。続けたいですか？',
        ipv6: 'IPv6をリッスン',
        bindAddress: '住所を聞いてください',
        entrance: '入り口',
        showEntrance: '「概要」ページで無効なアラートを表示します',
        entranceHelper:
            'セキュリティの入り口を有効にすると、指定されたセキュリティの入り口を介してパネルにログインするだけです。',
        entranceError:
            '5〜116文字の安全なログインエントリポイントを入力してください。数字または文字のみがサポートされています。',
        entranceInputHelper: 'セキュリティの入り口を無効にするために空白のままにしてください。',
        randomGenerate: 'ランダム',
        expirationTime: '有効期限',
        unSetting: '解き放つ',
        noneSetting:
            'パネルパスワードの有効期限を設定します。有効期限が切れた後、パスワードをリセットする必要があります',
        expirationHelper: 'パスワードの有効期限が[0]日の場合、パスワードの有効期限機能が無効になっています',
        days: '有効期限',
        expiredHelper: '現在のパスワードの有効期限が切れています。もう一度パスワードを変更してください。',
        timeoutHelper:
            '[{0}日]パネルパスワードの有効期限が切れようとしています。有効期限が切れた後、パスワードをリセットする必要があります',
        complexity: '複雑さの検証',
        complexityHelper: `有効にした後、パスワード検証ルールは次のとおりです。英語、数字、少なくとも2つの特殊文字を含む8〜30文字です。`,
        bindDomain: 'バインドドメイン',
        unBindDomain: 'バインドドメイン',
        panelSSL: 'パネルSSL',
        unBindDomainHelper:
            'ドメイン名をバインドするアクションは、システムの不安を引き起こす可能性があります。続けたいですか？',
        bindDomainHelper: 'ドメインにバインドした後、そのドメインのみが1パネルサービスにアクセスできます。',
        bindDomainHelper1: 'ドメイン名のバインディングを無効にするために空白のままにします。',
        bindDomainWarning:
            'ドメインバインディングの後、ログアウトされ、設定で指定されたドメイン名を介して1パネルサービスのみにアクセスできます。続けたいですか？',
        allowIPs: '承認されたIP',
        unAllowIPs: '許可されていないIP',
        unAllowIPsWarning:
            '空のIPを許可すると、すべてのIPがシステムにアクセスできるようになり、システムの不安定性が発生する可能性があります。続けたいですか？',
        allowIPsHelper:
            '承認されたIPアドレスリストを設定した後、リスト内のIPアドレスのみがパネルサービスにアクセスできます。',
        allowIPsWarning:
            '承認されたIPアドレスリストを設定した後、リスト内のIPアドレスのみがパネルサービスにアクセスできます。続けたいですか？',
        allowIPsHelper1: `IPアドレスの制限を無効にするために空白のままにします。`,
        allowIPEgs: '1行に1つ。たとえば、 \n*************  \n***********/24',
        mfa: '二因子認証（2FA）',
        mfaClose: 'MFAを無効にすると、サービスのセキュリティが減少します。続けたいですか？',
        secret: '秘密',
        mfaInterval: '間隔を更新する',
        mfaTitleHelper:
            'タイトルは、さまざまな1パネルホストを区別するために使用されます。タイトルを変更した後、再度スキャンするか、秘密のキーを手動で追加します。',
        mfaIntervalHelper: '更新時間を変更した後、再度スキャンするか、秘密のキーを手動で追加します。',
        mfaAlert:
            '1回限りのトークンは、現在の時刻に基づいて動的に生成された6桁の数値です。サーバー時間が同期されていることを確認してください。',
        mfaHelper: '有効にした後、1回限りのトークンを検証する必要があります。',
        mfaHelper1: 'たとえば、Authenticatorアプリをダウンロードしてください。',
        mfaHelper2:
            '1回限りのトークンを取得するには、Authenticatorアプリを使用して次のQRコードをスキャンするか、Secretキーを認証アプリにコピーします。',
        mfaHelper3: 'アプリから6桁を入力します',
        mfaCode: '1回限りのトークン',
        sslChangeHelper: 'HTTPS設定を変更し、サービスを再起動します。続けたいですか？',
        sslDisable: '無効にします',
        sslDisableHelper:
            'HTTPSサービスが無効になっている場合は、有効にするためにパネルを再起動する必要があります。続けたいですか？',
        noAuthSetting: '不正な設定',
        noAuthSettingHelper:
            'ユーザーが未ログインで、セキュリティ入口、認証IP、またはバインドされたドメイン名を正しく入力していない場合、このレスポンスでパネルの特徴を非表示にできます。',
        responseSetting: '応答設定',
        help200: 'ヘルプページ',
        error400: '要求の形式が正しくありません',
        error401: '不正',
        error403: '禁断',
        error404: '見つかりません',
        error408: 'リクエストタイムアウト',
        error416: '範囲は満足できません',
        error444: '接続が閉じた',
        error500: 'サーバーエラー',

        https: 'パネル用のHTTPSプロトコルアクセスをセットアップすると、パネルアクセスのセキュリティが強化されます。',
        certType: '証明書の種類',
        selfSigned: '自己署名',
        selfSignedHelper: `ブラウザは、自己署名の証明書を信頼していない場合があり、セキュリティ警告を表示する場合があります。`,
        select: '選択します',
        domainOrIP: 'ドメインまたはIP:',
        timeOut: 'タイムアウト',
        rootCrtDownload: 'ルート証明書のダウンロード',
        primaryKey: '主キー',
        certificate: '証明書',
        backupJump:
            'バックアップファイルは現在のバックアップリストにありません。ファイルディレクトリからダウンロードして、バックアップ用にインポートしてみてください。',

        snapshot: 'スナップショット|スナップショット',
        noAppData: '選択可能なシステムアプリはありません',
        noBackupData: '選択可能なバックアップデータはありません',
        stepBaseData: '基本データ',
        stepAppData: 'システムアプリ',
        stepPanelData: 'システムデータ',
        stepBackupData: 'バックアップデータ',
        stepOtherData: 'その他のデータ',
        operationLog: '操作ログを保持',
        loginLog: 'アクセスログを保持',
        systemLog: 'システムログを保持',
        taskLog: 'タスクログを保持',
        monitorData: '監視データを保持',
        dockerConf: 'Docker設定保持',
        selectAllImage: 'すべてのアプリイメージをバックアップ',
        logLabel: 'ログ',
        agentLabel: 'ノード設定',
        appDataLabel: 'アプリデータ',
        appImage: 'アプリイメージ',
        appBackup: 'アプリバックアップ',
        backupLabel: 'バックアップディレクトリ',
        confLabel: '設定ファイル',
        dockerLabel: 'コンテナ',
        taskLabel: 'スケジュールタスク',
        resourceLabel: 'アプリリソースディレクトリ',
        runtimeLabel: '実行環境',
        appLabel: 'アプリ',
        databaseLabel: 'データベース',
        snapshotLabel: 'スナップショットファイル',
        websiteLabel: 'ウェブサイト',
        directoryLabel: 'ディレクトリ',
        appStoreLabel: 'アプリストア',
        shellLabel: 'スクリプト',
        tmpLabel: '一時ディレクトリ',
        sslLabel: '証明書ディレクトリ',
        reCreate: 'スナップショットの作成に失敗しました',
        reRollback: 'スナップショットのロールバックに失敗しました',
        deleteHelper:
            'サードパーティのバックアップアカウントにあるものを含むすべてのスナップショットファイルが削除されます。続けたいですか？',
        status: 'スナップショットステータス',
        ignoreRule: 'ルールを無視します',
        editIgnoreRule: '@:commons.button.edit @.lower:setting.ignorerule',
        ignoreHelper:
            'このルールは、スナップショットの作成中に1パネルデータディレクトリを圧縮およびバックアップするために使用されます。デフォルトでは、ソケットファイルは無視されます。',
        ignoreHelper1: '1行に1つ。たとえば、 n*.log  n/opt/1panel/cache',
        panelInfo: '1パネルの基本情報を書いてください',
        panelBin: 'バックアップ1パネルシステムファイル',
        daemonJson: 'バックアップDocker構成ファイル',
        appData: 'バックアップ1パネルからアプリをインストールしました',
        panelData: 'バックアップ1パネルデータディレクトリ',
        backupData: '1パネル用のバックアップローカルバックアップディレクトリ',
        compress: 'スナップショットファイルを作成します',
        upload: 'スナップショットファイルをアップロードします',
        recoverDetail: '詳細を回復します',
        createSnapshot: 'スナップショットを作成します',
        importSnapshot: 'スナップショットを同期します',
        lastRecoverAt: '最後の回復時間',
        lastRollbackAt: '最後のロールバック時間',
        reDownload: 'バックアップファイルをもう一度ダウンロードしてください',
        recoverErrArch: `さまざまなサーバーアーキテクチャ間のスナップショット回復はサポートされていません！`,
        recoverErrSize:
            '不十分なディスクスペースが検出されました。チェックまたはクリーンアップして、再試行してください！',
        recoverHelper: 'Snapshot {0}からの回復を開始して、先に進む前に次の情報を確認してください。',
        recoverHelper1: '回復には、Dockerサービスと1パネルサービスを再起動する必要があります',
        recoverHelper2:
            'サーバーに十分なディスクスペースがあることを確認してください（スナップショットファイルサイズ:{0}、利用可能なスペース:{1}）',
        recoverHelper3:
            'サーバーアーキテクチャが、スナップショットが作成されたサーバーのアーキテクチャと一致していることを確認してください（現在のサーバーアーキテクチャ:{0}）',
        rollback: 'ロールバック',
        rollbackHelper:
            'この回復をロールバックすると、この回復からすべてのファイルを置き換え、Dockerサービスと1パネルサービスを再起動する必要がある場合があります。続けたいですか？',

        upgradeHelper: 'アップグレードには、1パネルサービスを再起動する必要があります。続けたいですか？',
        rollbackLocalHelper:
            'マスターノードは直接ロールバックをサポートしていません。手動で「1pctl restore」コマンドを実行してロールバックしてください！',
        noUpgrade: '現在、最新バージョンです',
        upgradeNotes: 'リリースノート',
        upgradeNow: '今すぐアップグレードしてください',
        source: 'ソースをダウンロードします',
        versionNotSame:
            'ノードのバージョンがメインノードと一致していません。ノード管理でアップグレードしてから再試行してください。',
        versionCompare:
            'ノード {0} は既にアップグレード可能な最新バージョンです。マスターノードのバージョンを確認後、再試行してください！',

        about: 'について',
        project: 'GitHub',
        issue: '問題',
        doc: '公式文書',
        star: '星',
        description: 'Linuxサーバーパネル',
        forum: '議論',
        doc2: 'ドキュメント',
        currentVersion: 'バージョン',

        license: 'ライセンス',
        bindNode: 'ノードをバインド',
        menuSetting: 'メニュー設定',
        menuSettingHelper: 'サブメニューが1つしか存在しない場合、メニューバーにはそのサブメニューのみが表示されます',
        showAll: 'すべてを表示します',
        hideALL: 'すべてを隠します',
        ifShow: '表示するかどうか',
        menu: 'メニュー',
        confirmMessage: 'ページは更新されて、高度なメニューリストを更新します。続く？',
        compressPassword: '圧縮パスワード',
        backupRecoverMessage: '圧縮または減圧パスワードを入力してください（設定しないように空白のままにしてください）',
    },
    license: {
        community: '無料',
        oss: '無料',
        pro: '専門',
        trial: '体験',
        add: 'コミュニティ版を追加',
        licenseAlert:
            'ライセンスがノードに正常にバインドされている場合のみ、コミュニティ版ノードを追加できます。ライセンスに正常にバインドされているノードのみ切り替えがサポートされます。',
        licenseUnbindHelper:
            'このライセンスにコミュニティ版ノードが存在します。バインドを解除してから再試行してください！',
        subscription: 'サブスクリプション',
        perpetual: '永久ライセンス',
        versionConstraint: '{0} バージョン買い取り',
        forceUnbind: '強制バインド解除',
        forceUnbindHelper:
            '強制的にバインド解除を行うと、解除プロセス中に発生するエラーを無視し、最終的にライセンスのバインドを解除します。',
        updateForce: '強制更新（アンバインド中のすべてのエラーを無視し、最終操作の成功を保証します）',
        trialInfo: 'バージョン',
        authorizationId: 'サブスクリプション承認ID',
        authorizedUser: '認定ユーザー',
        lostHelper:
            'ライセンスは、再試行の最大数に達しました。プロのバージョン機能が適切に機能していることを確認するには、手動で同期ボタンをクリックしてください。세부사항: ',
        disableHelper:
            'ライセンスの同期の検証は失敗しました。プロのバージョン機能が適切に機能していることを確認するには、手動で同期ボタンをクリックしてください。세부사항: ',
        quickUpdate: 'クイックアップデート',
        power: '許可',
        unbindHelper: 'すべてのPro関連設定は、バインディングを解除した後にクリーニングされます。続けたいですか？',
        importLicense: 'ライセンス',
        importHelper: 'ここでライセンスファイルをクリックまたはドラッグしてください',
        technicalAdvice: '技術的な相談',
        advice: '相談',
        levelUpPro: 'Proにアップグレードします',
        licenseSync: 'ライセンス同期',
        knowMorePro: 'もっと詳しく知る',
        closeAlert: '現在のページはパネル設定で閉じることができます',
        introduce: '機能の紹介',
        waf: 'プロフェッショナルバージョンにアップグレードすると、インターセプトマップ、ログ、ブロックレコード、地理的位置ブロッキング、カスタムルール、カスタムインターセプトページなどの機能を提供できます。',
        tamper: 'プロのバージョンにアップグレードすると、不正な変更や改ざんからWebサイトを保護できます。',
        setting:
            'プロのバージョンにアップグレードすることで、パネルロゴ、ウェルカムメッセージ、その他の情報のカスタマイズが可能になります。',
        monitor:
            'プロのバージョンにアップグレードして、Webサイトのリアルタイムステータス、訪問者の傾向、訪問者ソース、リクエストログ、その他の情報を表示します。',
        alert: 'プロのバージョンにアップグレードして、SMSを介してアラーム情報を受信し、アラームログを表示し、さまざまなキーイベントを完全に制御し、心配のないシステム操作を確実にする',
        app: 'モバイルアプリでサービス情報、異常監視などを表示するには、プロフェッショナル版にアップグレードしてください。',
        fileExchange: 'プロフェッショナル版にアップグレードすると、複数のサーバー間でファイルを迅速に転送できます。',
        cluster:
            'プロフェッショナル版にアップグレードすると、MySQL/Postgres/Reidsマスタースレーブクラスタを管理できます。',
    },
    clean: {
        scan: 'スキャンを開始します',
        scanHelper: '1パネルのランタイム中に作成されたジャンクファイルを簡単にクリーンアップできます',
        clean: '今すぐきれいにします',
        reScan: 'レスカン',
        cleanHelper: `これにより、選択したシステムジャンクファイルがクリーンアップされ、元に戻すことはできません。続けたいですか？`,
        statusSuggest: '（推奨クリーニング）',
        statusClean: '（とてもきれい）',
        statusEmpty: 'とてもきれいで、掃除は必要ありません！',
        statusWarning: '（注意して進めます）',
        lastCleanTime: '最後にクリーニング:{0}',
        lastCleanHelper: 'ファイルとディレクトリのクリーニング:{0}、合計クリーニング:{1}',
        cleanSuccessful: '正常に掃除',
        currentCleanHelper: 'このセッションでクリーニングされたファイルとディレクトリ:{0}、合計クリーニング:{1}',
        suggest: '（推奨）',
        totalScan: 'クリーニングするジャンクファイルの合計:',
        selectScan: '選択したジャンクファイルの合計:',

        system: 'システムジャンクファイル',
        systemHelper:
            'スナップショット中に生成された一時ファイル、アップグレード、およびバージョンの反復中にファイルのコンテンツが廃止されました',
        panelOriginal: 'システムスナップショットリカバリバックアップファイル',
        backup: '一時バックアップディレクトリ',
        upgrade: 'システムアップグレードバックアップファイル',
        upgradeHelper: '（システムロールバックのために最新のアップグレードバックアップを保持することをお勧めします）',
        cache: 'システムキャッシュファイル',
        cacheHelper: '（注意を払って進むには、クリーニングにはサービスの再起動が必要です）',
        rollback: '回復する前にファイルをバックアップします',

        upload: '一時的なアップロードファイル',
        uploadHelper: 'システムバックアップリストからアップロードされた一時ファイル',
        download: '一時的なダウンロードファイル',
        downloadHelper: 'システムによってサードパーティのバックアップアカウントからダウンロードされた一時ファイル',
        directory: 'ディレクトリ',

        systemLog: 'システムログファイル',
        systemLogHelper:
            'システムログ情報、コンテナビルドまたは画像プルログ情報、およびスケジュールされたタスクで生成されたログファイル',
        dockerLog: 'コンテナ操作ログファイル',
        taskLog: 'スケジュールされたタスク実行ログファイル',
        containerShell: 'コンテナ内部シェルスクリプトスケジュールされたタスク',

        containerTrash: 'コンテナのゴミ',
        volumes: 'ボリューム',
        buildCache: 'コンテナビルドキャッシュ',
    },
    app: {
        app: 'アプリケーション|アプリケーション',
        installName: '名前',
        installed: 'インストール',
        all: '全て',
        version: 'バージョン',
        detail: '詳細',
        params: '編集',
        author: '著者',
        source: 'ソース',
        appName: 'アプリケーション名',
        deleteWarn:
            '削除操作は、すべてのデータとバックアップを一緒に削除します。この操作はロールバックすることはできません。続けたいですか？',
        syncSuccess: '正常に同期しました',
        canUpgrade: '更新',
        backupName: 'ファイル名',
        backupPath: 'ファイルパス',
        backupdate: 'バックアップ時間',
        versionSelect: 'バージョンを選択してください',
        operatorHelper: '操作{0}は、選択したアプリケーションで実行されます。続けたいですか？',
        startOperatorHelper: 'アプリケーションが開始されます。続けたいですか？',
        stopOperatorHelper: 'アプリケーションは停止します。続けたいですか？',
        restartOperatorHelper: 'アプリケーションが再起動されます。続けたいですか？',
        reloadOperatorHelper: 'アプリケーションはリロードされます。続けたいですか？',
        checkInstalledWarn: `「{0}」が検出されませんでした。「アプリストア」に移動してインストールしてください。`,
        gotoInstalled: 'インストールに移動します',
        limitHelper: 'アプリケーションはすでにインストールされています。',
        deleteHelper: `「{0}」は、次のリソースに関連付けられています。チェックしてもう一度やり直してください！`,
        checkTitle: 'ヒント',
        defaultConfig: 'デフォルトの構成',
        defaultConfigHelper: 'デフォルトの構成に復元されており、保存後に有効になります',
        forceDelete: 'フォース削除',
        forceDeleteHelper: 'フォース削除は、削除プロセス中のエラーを無視し、最終的にメタデータを削除します。',
        deleteBackup: 'バックアップを削除します',
        deleteBackupHelper: 'また、アプリケーションのバックアップを削除します',
        deleteDB: 'データベースを削除します',
        deleteDBHelper: 'データベースも削除します',
        noService: 'いいえ{0}',
        toInstall: 'インストールに移動します',
        param: 'パラメーター',
        alreadyRun: '年',
        syncAppList: '同期',
        less1Minute: '1分未満',
        appOfficeWebsite: 'オフィスのウェブサイト',
        github: 'Github',
        document: '書類',
        updatePrompt: '更新はありません',
        installPrompt: 'まだインストールされていません',
        updateHelper:
            'パラメーターの編集により、アプリケーションが開始されない場合があります。注意して進めてください。',
        updateWarn: '更新パラメータアプリケーションを再構築する必要がありますが、続行しますか？',
        busPort: 'ポート',
        syncStart: '同期を開始します！後でアプリストアを更新してください',
        advanced: '高度な設定',
        cpuCore: 'コア',
        containerName: 'コンテナ名',
        containerNameHelper: 'コンテナ名は設定されていないときに自動的に生成されます',
        allowPort: '外部アクセス',
        allowPortHelper: '外部ポートアクセスを許可すると、ファイアウォールポートがリリースされます',
        appInstallWarn: `アプリケーションは、デフォルトで外部アクセスポートを公開しません。[Advanced Settings]をクリックして公開します。`,
        upgradeStart: 'アップグレードを起動します！後でページを更新してください',
        toFolder: 'インストールディレクトリを開きます',
        editCompose: '編集ファイルを作成します',
        editComposeHelper: 'Composeファイルを編集すると、ソフトウェアのインストールの障害が発生する可能性があります',
        composeNullErr: '作曲は空にすることはできません',
        takeDown: '降ろす',
        allReadyInstalled: 'インストール',
        installHelper: '画像プルの問題がある場合は、画像アクセラレーションを構成します。',
        installWarn: `外部アクセスは有効になっていないため、アプリケーションが外部ネットワークを介してアクセスできるようになります。続けたいですか？`,
        showIgnore: '無視されたアプリケーションを表示します',
        cancelIgnore: 'キャンセルは無視します',
        ignoreList: '無視されたアプリケーション',
        appHelper:
            'アプリケーションの詳細ページにアクセスして、いくつかの特別なアプリケーションのインストール命令を学びます。',
        backupApp: 'アップグレード前のバックアップアプリケーション',
        backupAppHelper:
            'アップグレードが失敗した場合、バックアップは自動的にロールバックされます。ログ監査システムログの障害理由を確認してください。バックアップは、デフォルトで最新の3コピーを保持します',
        openrestyDeleteHelper: 'OpenRestyの強制削除により、すべてのWebサイトが削除されます。続けたいですか？',
        downloadLogHelper1: '{0}アプリケーションのすべてのログがダウンロードされようとしています。続けたいですか？',
        downloadLogHelper2: '{0}アプリケーションの最新{1}ログはダウンロードされようとしています。続けたいですか？',
        syncAllAppHelper: 'すべてのアプリケーションが同期されます。続けたいですか？',
        hostModeHelper:
            '現在のアプリケーションネットワークモードはホストモードです。ポートを開く必要がある場合は、ファイアウォールページで手動で開いてください。',
        showLocal: 'ローカルアプリケーションを表示します',
        reload: 'リロード',
        upgradeWarn:
            'アプリケーションのアップグレードは、docker-compose.ymlファイルを置き換えます。変更がある場合は、クリックしてファイルの比較を表示できます',
        newVersion: '新しいバージョン',
        oldVersion: '現在のバージョン',
        composeDiff: 'ファイルの比較',
        showDiff: '比較を表示します',
        useNew: 'カスタムバージョンを使用します',
        useDefault: 'デフォルトバージョンを使用します',
        useCustom: 'docker-compose.ymlをカスタマイズします',
        useCustomHelper: `カスタムdocker-compose.ymlファイルを使用すると、アプリケーションのアップグレードが失敗する場合があります。必要でない場合は、確認しないでください。`,
        diffHelper:
            '左側は古いバージョンで、右側は新しいバージョンです。編集後、クリックしてカスタムバージョンを保存します',
        pullImage: '画像を引っ張ります',
        pullImageHelper: 'アプリケーションが開始する前に、Docker Pullを実行して画像をプルします',
        deleteImage: 'イメージを削除',
        deleteImageHelper: 'アプリ関連のイメージを削除します。削除に失敗してもタスクは終了しません。',
        requireMemory: 'メモリ要件',
        supportedArchitectures: '対応アーキテクチャ',
        link: 'リンク',
        showCurrentArch: '現在のサーバーアーキテクチャのアプリケーション',
        syncLocalApp: 'ローカルアプリの同期',
        memoryRequiredHelper: '現在のアプリケーションは {0} メモリが必要です',
        gpuConfig: 'GPUサポートを有効化',
        gpuConfigHelper:
            'マシンにNVIDIA GPUが搭載され、NVIDIAドライバーとNVIDIA Docker Container Toolkitがインストールされていることを確認してください',
        webUI: 'Webアクセスアドレス',
        webUIPlaceholder: '例：example.com:8080/login',
        defaultWebDomain: 'デフォルトアクセスアドレス',
        defaultWebDomainHepler:
            'アプリケーションポートが8080の場合、アドレスはhttp(s)://デフォルトアクセスアドレス:8080にジャンプします',
        webUIConfig:
            '現在のノードにはデフォルトアクセスアドレスが設定されていません。アプリケーションパラメータで設定するか、パネル設定から設定してください！',
        toLink: 'ジャンプ',
        customAppHelper:
            'カスタムアプリストアパッケージをインストールする前に、インストールされているアプリがないことを確認してください。',
        forceUninstall: '強制アンインストール',
        syncCustomApp: 'カスタムアプリを同期',
        ignoreAll: '後続のすべてのバージョンを無視',
        ignoreVersion: '指定されたバージョンを無視',
        specifyIP: 'ホスト IP をバインド',
        specifyIPHelper:
            'ポートにバインドするホストアドレス/ネットワークインターフェースを設定します（この機能がわからない場合は、入力しないでください）',
        uninstallDeleteBackup: 'アプリをアンインストール - バックアップを削除',
        uninstallDeleteImage: 'アプリをアンインストール - イメージを削除',
        upgradeBackup: 'アプリのアップグレード前にアプリをバックアップ',
    },
    website: {
        primaryDomain: 'プライマリドメイン',
        otherDomains: '他のドメイン',
        static: '静的',
        deployment: '展開',
        supportUpType: '.tar.gzファイルのみがサポートされています',
        zipFormat: '.tar.gz圧縮パッケージ構造:test.tar.gz圧縮パッケージは{0}ファイルを含める必要があります',
        proxy: '逆プロキシ',
        alias: 'エイリアス',
        ftpUser: 'FTPアカウント',
        ftpPassword: 'FTPパスワード',
        ftpHelper:
            'Webサイトを作成すると、対応するFTPアカウントが作成され、FTPディレクトリがWebサイトディレクトリにリンクされます。',
        remark: '述べる',
        manageGroup: 'グループを管理します',
        groupSetting: 'グループ管理',
        createGroup: 'グループを作成します',
        appNew: '新しいアプリケーション',
        appInstalled: 'インストールされたアプリケーション',
        create: 'Webサイトを作成します',
        delete: 'Webサイトを削除します',
        deleteApp: 'アプリケーションを削除します',
        deleteBackup: 'バックアップを削除します',
        domain: 'ドメイン',
        domainHelper:
            '1行ごとに1つのドメイン.\nワイルドカード「*」とIPアドレスをサポートします.\nポートの追加をサポートします.',
        addDomain: '追加',
        domainConfig: 'ドメイン',
        defaultDoc: '書類',
        perserver: '並行性',
        perserverHelper: '現在のサイトの最大並行性を制限します',
        perip: '単一のIP',
        peripHelper: '単一のIPへの同時アクセスの最大数を制限する',
        rate: '交通制限',
        rateHelper: '各リクエストのフローを制限する（ユニット:KB）',
        limitHelper: 'フロー制御を有効にします',
        other: '他の',
        currentSSL: '現在の証明書',
        dnsAccount: 'DNSアカウント',
        applySSL: '証明書申請',
        SSLList: '証明書リスト',
        createDnsAccount: 'DNSアカウント',
        aliyun: 'エイリアン',
        manual: '手動解析',
        key: '鍵',
        check: 'ビュー',
        acmeAccountManage: 'ACMEアカウント',
        email: 'メール',
        acmeAccount: 'ACMEアカウント',
        provider: '検証方法',
        dnsManual: '手動解決',
        expireDate: '有効期限',
        brand: '組織',
        deploySSL: '展開',
        deploySSLHelper: '証明書を展開しますか？',
        ssl: '証明書|証明書',
        dnsAccountManage: 'DNSプロバイダー',
        renewSSL: '更新します',
        renewHelper: '必ず証明書を更新しますか？',
        renewSuccess: '更新証明書',
        enableHTTPS: '有効にする',
        aliasHelper: 'エイリアスは、ウェブサイトのディレクトリ名です',
        lastBackupAt: '最後のバックアップ時間',
        null: 'なし',
        nginxConfig: 'nginx構成',
        websiteConfig: 'ウェブサイトの設定',
        basic: '基本',
        source: '構成',
        security: '安全',
        nginxPer: 'パフォーマンスチューニング',
        neverExpire: '一度もない',
        setDefault: 'デフォルトとして設定します',
        default: 'デフォルト',
        deleteHelper: '関連するアプリケーションステータスは異常です。確認してください',
        toApp: 'インストールされているリストに移動します',
        cycle: 'サイクル',
        frequency: '頻度',
        ccHelper: '同じURLを{0}秒以内に{1}を超えて同じURLを要求し、CC防御をトリガーし、このIPをブロックする',
        mustSave: '有効にするには、変更を保存する必要があります',
        fileExt: 'ファイル拡張子',
        fileExtBlock: 'ファイル拡張ブロックリスト',
        value: '価値',
        enable: '有効にする',
        proxyAddress: 'プロキシアドレス',
        proxyHelper: '例:127.0.0.1:8080',
        forceDelete: 'フォース削除',
        forceDeleteHelper: 'フォース削除は、削除プロセス中のエラーを無視し、最終的にメタデータを削除します。',
        deleteAppHelper: '関連するアプリケーションとアプリケーションのバックアップを同時に削除する',
        deleteBackupHelper: 'また、Webサイトのバックアップを削除します。',
        deleteConfirmHelper:
            '削除操作を元に戻すことはできません。<span style = "color:red"> "{0}" </span>を入力して、削除を確認します。',
        staticPath: '対応するメインディレクトリはです',
        limit: 'スキーム',
        blog: 'フォーラム/ブログ',
        imageSite: '写真サイト',
        downloadSite: 'サイトをダウンロードします',
        shopSite: 'モール',
        doorSite: 'ポータル',
        qiteSite: '企業',
        videoSite: 'ビデオ',
        errLog: 'エラーログ',
        accessLog: 'ウェブサイトログ',
        stopHelper:
            'サイトを停止した後、正常にアクセスできなくなり、ユーザーは訪問時に現在のサイトの停止ページを表示します。続けたいですか？',
        startHelper: 'サイトを有効にした後、ユーザーは通常、サイトのコンテンツにアクセスできますが、続行しますか？',
        sitePath: 'ディレクトリ',
        siteAlias: 'サイトエイリアス',
        primaryPath: 'ルートディレクトリ',
        folderTitle: 'ウェブサイトには主に次のフォルダーが含まれています',
        wafFolder: 'ファイアウォールルール',
        indexFolder: 'ウェブサイトルートディレクトリ',
        logFolder: 'ウェブサイトログ',
        sslFolder: 'ウェブサイト証明書',
        enableOrNot: '有効にする',
        oldSSL: '既存の証明書',
        manualSSL: '輸入証明書',
        select: '選択します',
        selectSSL: '選択証明書を選択します',
        privateKey: 'キー（キー）',
        certificate: '証明書（PEM形式）',
        HTTPConfig: 'HTTPオプション',
        HTTPSOnly: 'HTTP要求をブロックします',
        HTTPToHTTPS: 'HTTPSにリダイレクトします',
        HTTPAlso: '直接HTTPリクエストを許可します',
        sslConfig: 'SSLオプション',
        disableHTTPS: 'httpsを無効にします',
        disableHTTPSHelper: 'HTTPSを無効にすると、証明書関連の構成が削除されますが、続行しますか？',
        SSLHelper:
            '注:違法なWebサイトにSSL証明書を使用しないでください。 nif httpsアクセスを開いた後に使用できません。.',
        SSLConfig: '証明書設定',
        SSLProConfig: 'プロトコル設定',
        supportProtocol: 'プロトコルバージョン',
        encryptionAlgorithm: '暗号化アルゴリズム',
        notSecurity: '（安全ではない）',
        encryptHelper:
            "暗号化しようと証明書を発行するための周波数制限がありますが、通常のニーズを満たすには十分です。頻繁に操作すると、発行の失敗が発生します。特定の制限については、参照してください<a target='_blank' href='https://letsencrypt.org/zh-cn/docs /rate-limits/'>公式文書</a>",
        ipValue: '価値',
        ext: 'ファイル拡張子',
        wafInputHelper: 'ラインごとに入力します。1つの行',
        data: 'データ',
        ever: '永続',
        nextYear: '1年後',
        noLog: 'ログは見つかりません',
        defaultServer: 'デフォルトサイト',
        noDefaultServer: '設定されていません',
        defaultServerHelper:
            'デフォルトサイトを設定した後、すべての未バインドのドメイン名とIPはデフォルトサイトにリダイレクトされます\nこれにより、悪意のある解決を効果的に防ぐことができます\nただし、WAFの未承認ドメイン名の遮断が失敗することもあります',
        restoreHelper: 'このバックアップを使用して復元することは間違いありませんか？',
        websiteDeploymentHelper:
            'インストールされたアプリケーションを使用するか、新しいアプリケーションを作成してWebサイトを作成します。',
        websiteStatictHelper: 'ホストにWebサイトディレクトリを作成します。',
        websiteProxyHelper:
            'リバースプロキシを使用して、既存のサービスをプロキシします。たとえば、サービスがインストールされ、ポート8080で実行されている場合、プロキシアドレスは「http://127.0.0.1:8080」になります。',
        runtimeProxyHelper: 'Webサイトランタイムを使用してWebサイトを作成します。',
        runtime: 'ランタイム',
        deleteRuntimeHelper:
            'ランタイムアプリケーションはWebサイトと一緒に削除する必要があります。注意して処理してください',
        proxyType: 'ネットワークタイプ',
        unix: 'UNIXネットワーク',
        tcp: 'TCP/IPネットワーク',
        phpFPM: 'fpm config',
        phpConfig: 'php config',
        updateConfig: 'configを更新します',
        isOn: 'の上',
        isOff: 'オフ',
        rewrite: '擬似静的',
        rewriteMode: 'スキーム',
        current: '現在',
        rewriteHelper:
            '擬似静的に設定すると、ウェブサイトがアクセスできない場合、デフォルトの設定に戻るようにしてください。',
        runDir: 'ディレクトリを実行します',
        runUserHelper:
            'PHPコンテナランタイム環境を介して展開されているWebサイトの場合、インデックスおよびサブディレクトリのすべてのファイルとフォルダーの所有者とユーザーグループを1000に設定する必要があります。ローカルPHP環境については、ローカルPHP-FPMユーザーとユーザーグループの設定を参照してください。',
        userGroup: 'ユーザー/グループ',
        uGroup: 'グループ',
        proxyPath: 'プロキシパス',
        proxyPass: 'ターゲットURL',
        cache: 'キャッシュ',
        cacheTime: 'キャッシュ期間',
        enableCache: 'キャッシュ',
        proxyHost: 'プロキシホスト',
        disabled: '停止',
        startProxy: 'これにより、リバースプロキシが開始されます。続けたいですか？',
        stopProxy: 'これにより、逆プロキシが停止します。続けたいですか？',
        sourceFile: 'ソース',
        proxyHelper1: 'このディレクトリにアクセスすると、ターゲットURLのコンテンツが返されて表示されます。',
        proxyPassHelper: 'ターゲットURLは有効でアクセス可能でなければなりません。',
        proxyHostHelper: 'リクエストヘッダーのドメイン名をプロキシサーバーに渡します。',
        replacementHelper: '最大5つの交換を追加できます。交換が不要な場合は、空白のままにしてください。',
        modifier: '一致するルール',
        modifierHelper: '例: "="は正確な一致、 "〜"は通常の一致、^〜」はパスの始まりなどと一致します。',
        replace: 'テキスト置換',
        addReplace: '追加',
        replaced: '検索文字列（空にすることはできません）',
        replaceText: '文字列に置き換えます',
        replacedErr: '検索文字列を空にすることはできません',
        replacedErr2: '検索文字列を繰り返すことはできません',
        basicAuth: '基本認証',
        editBasicAuthHelper:
            'パスワードは非対称的に暗号化されており、反響することはできません。編集はパスワードをリセットする必要があります',
        antiLeech: '反リーチ',
        extends: '拡大',
        browserCache: 'キャッシュ',
        leechLog: '反リーチログを記録します',
        accessDomain: '許可されたドメイン',
        leechReturn: '応答リソース',
        noneRef: '空のリファラーを許可します',
        disable: '有効になっていません',
        disableLeechHelper: '反リーチを無効にするかどうか',
        disableLeech: '反リーチを無効にします',
        ipv6: '緑',
        leechReturnError: 'HTTPステータスコードを入力してください',
        selectAcme: 'ACMEアカウントを選択します',
        imported: '手動で作成されます',
        importType: 'インポートタイプ',
        pasteSSL: 'コードを貼り付けます',
        localSSL: 'サーバーファイルを選択します',
        privateKeyPath: '秘密キーファイル',
        certificatePath: '証明書ファイル',
        ipWhiteListHelper: 'IP AllowListの役割:すべてのルールはIP AllowListに対して無効です',
        redirect: 'リダイレクト',
        sourceDomain: 'ソースドメイン',
        targetURL: 'ターゲットURLアドレス',
        keepPath: 'uri params',
        path: 'パス',
        redirectType: 'リダイレクトタイプ',
        redirectWay: '方法',
        keep: '保つ',
        notKeep: '保持しないでください',
        redirectRoot: 'ホームページにリダイレクトします',
        redirectHelper: '301永久リダイレクト、302一時的なリダイレクト',
        changePHPVersionWarn:
            'PHPバージョンを切り替えると、元のPHPコンテナが削除されます（マウントされたWebサイトコードは失われません）、続行しますか？',
        changeVersion: 'スイッチバージョン',
        retainConfig: 'php-fpm.confとphp.iniファイルを保持するかどうか',
        runDirHelper2: 'セカンダリランニングディレクトリがインデックスディレクトリの下にあることを確認してください',
        openrestyHelper: 'OpenRestyデフォルトのHTTPポート:{0} HTTPSポート:{1}。',
        primaryDomainHelper: '例:example.comまたはexample.com:8080',
        acmeAccountType: 'アカウントタイプ',
        keyType: 'キーアルゴリズム',
        tencentCloud: 'テンセントクラウド',
        containWarn: 'ドメイン名にはメインドメインが含まれています。再入力してください',
        rewriteHelper2:
            'アプリストアからインストールされたWordPressなどのアプリケーションは、通常、擬似静的設定が事前に設定されています。それらを再設定すると、エラーが発生する可能性があります。',
        websiteBackupWarn:
            'ローカルバックアップのインポートをサポートするだけで、他のマシンからバックアップをインポートすることは回復の故障を引き起こす可能性があります',
        ipWebsiteWarn:
            'ドメイン名としてIPを持つWebサイトは、正常にアクセスするデフォルトサイトとして設定する必要があります。',
        hstsHelper: 'HSTを有効にすると、Webサイトのセキュリティが向上する可能性があります',
        includeSubDomains: 'サブドメイン',
        hstsIncludeSubDomainsHelper: '有効化すると、HSTSポリシーが現在のドメインのすべてのサブドメインに適用されます。',
        defaultHtml: 'デフォルトページ',
        website404: 'ウェブサイト404エラーページ',
        domain404: 'ウェブサイトドメインは存在しません',
        indexHtml: '静的ウェブサイトのインデックス',
        stopHtml: 'ウェブサイトを停止しました',
        indexPHP: 'PHP Webサイトのインデックス',
        sslExpireDate: '証明書の有効期限',
        website404Helper:
            'ウェブサイト404エラーページは、PHPランタイム環境Webサイトと静的Webサイトのみをサポートしています',
        sni: '起源は悲しい',
        sniHelper:
            '逆プロキシバックエンドがHTTPSの場合、Origin SNIを設定する必要がある場合があります。詳細については、CDNサービスプロバイダーのドキュメントを参照してください。',
        huaweicloud: 'huaweiCloud',
        createDb: 'データベースを作成',
        enableSSLHelper: 'SSLの有効化に失敗しても、ウェブサイトの作成には影響しません。',
        batchAdd: 'ドメインを一括追加',
        generateDomain: '生成',
        global: 'グローバル',
        subsite: 'サブサイト',
        subsiteHelper:
            'サブサイトは、既存のPHPまたは静的ウェブサイトのディレクトリをルートディレクトリとして選択できます。',
        parentWebsite: '親ウェブサイト',
        deleteSubsite: '現在のウェブサイトを削除するには、まずサブサイト {0} を削除する必要があります。',
        loadBalance: 'ロードバランシング',
        server: 'ノード',
        algorithm: 'アルゴリズム',
        ipHash: 'IPハッシュ',
        ipHashHelper:
            'クライアントのIPアドレスに基づいてリクエストを特定のサーバーに分散し、特定のクライアントが常に同じサーバーにルーティングされるようにします。',
        leastConn: '最小接続',
        leastConnHelper: 'アクティブな接続数が最も少ないサーバーにリクエストを送信します。',
        leastTime: '最小時間',
        leastTimeHelper: 'アクティブな接続時間が最も短いサーバーにリクエストを送信します。',
        defaultHelper:
            'デフォルトの方法では、リクエストは各サーバーに均等に分散されます。サーバーに重み設定がある場合、指定された重みに従ってリクエストが分散されます。重みが高いサーバーほど多くのリクエストを受け取ります。',
        weight: '重み',
        maxFails: '最大失敗回数',
        maxConns: '最大接続数',
        strategy: '戦略',
        strategyDown: '無効',
        strategyBackup: 'バックアップ',
        staticChangePHPHelper: '現在は静的ウェブサイトですが、PHPウェブサイトに切り替えることができます。',
        proxyCache: 'リバースプロキシキャッシュ',
        cacheLimit: 'キャッシュスペース制限',
        shareCache: 'キャッシュカウントメモリサイズ',
        cacheExpire: 'キャッシュ有効期限',
        shareCacheHelper: '1Mのメモリで約8000個のキャッシュオブジェクトを保存できます。',
        cacheLimitHelper: '制限を超えると、古いキャッシュが自動的に削除されます。',
        cacheExpireHelper: '有効期限内にヒットしないキャッシュは削除されます。',
        realIP: 'リアルIP',
        ipFrom: 'IPソース',
        ipFromHelper:
            '信頼できるIPソースを設定することで、OpenRestyはHTTPヘッダーのIP情報を分析し、訪問者のリアルIPアドレスを正確に識別して記録します（アクセスログを含む）。',
        ipFromExample1: 'フロントエンドがFrpなどのツールの場合、FrpのIPアドレス（例：127.0.0.1）を入力できます。',
        ipFromExample2: 'フロントエンドがCDNの場合、CDNのIP範囲を入力できます。',
        ipFromExample3:
            '不明な場合は、0.0.0.0/0（IPv4）または::/0（IPv6）を入力できます。[注意：任意のソースIPを許可することは安全ではありません。]',
        http3Helper:
            'HTTP/3はHTTP/2のアップグレード版で、より高速な接続速度とパフォーマンスを提供します。ただし、すべてのブラウザがHTTP/3をサポートしているわけではなく、有効にすると一部のブラウザがサイトにアクセスできなくなる可能性があります。',
        changeDatabase: 'データベースを切り替え',
        changeDatabaseHelper1: 'データベースの関連付けは、ウェブサイトのバックアップと復元に使用されます。',
        changeDatabaseHelper2: '別のデータベースに切り替えると、以前のバックアップが復元できなくなる可能性があります。',
        saveCustom: 'テンプレートとして保存',
        rainyun: '雨雲',
        volcengine: 'volcengine',
        runtimePortHelper: '現在の実行環境には複数のポートがあります。プロキシポートを選択してください。',
        runtimePortWarn: '現在の実行環境にはポートがありません。プロキシできません',
        cacheWarn: 'まずリバースプロキシのキャッシュスイッチをオフにしてください',
        loadBalanceHelper:
            '負荷分散を作成した後、「リバースプロキシ」に移動し、プロキシを追加してバックエンドアドレスを次のように設定してください：http://<負荷分散名>。',
        favorite: 'お気に入り',
        cancelFavorite: 'お気に入りを解除',
        useProxy: 'プロキシを使用',
        useProxyHelper: 'パネル設定のプロキシサーバーアドレスを使用',
        westCN: '西部デジタル',
        openBaseDir: 'クロスサイト攻撃を防ぐ',
        openBaseDirHelper:
            'open_basedir は PHP ファイルのアクセスパスを制限し、クロスサイトアクセスを防ぎセキュリティを向上させるために使用されます',
        serverCacheTime: 'サーバーキャッシュ時間',
        serverCacheTimeHelper:
            'リクエストがサーバー上でキャッシュされる時間。この期間中、同一のリクエストはオリジンサーバーにリクエストせず、キャッシュされた結果を直接返します。',
        browserCacheTime: 'ブラウザキャッシュ時間',
        browserCacheTimeHelper:
            '静的リソースがブラウザのローカルにキャッシュされる時間、冗長なリクエストを減らします。有効期限前にユーザーがページをリフレッシュすると、ローカルキャッシュが直接使用されます。',
        donotLinkeDB: 'データベースをリンクしない',
        toWebsiteDir: 'ウェブサイトディレクトリに入る',
    },
    php: {
        short_open_tag: '短いタグサポート',
        max_execution_time: '最大スクリプト実行時間',
        max_input_time: '最大入力時間',
        memory_limit: 'スクリプトメモリ制限',
        post_max_size: 'データの最大サイズを投稿します',
        file_uploads: 'ファイルのアップロードを許可するかどうか',
        upload_max_filesize: 'ファイルのアップロードに許可されている最大サイズ',
        max_file_uploads: '同時にアップロードできるファイルの最大数',
        default_socket_timeout: 'ソケットタイムアウト',
        error_reporting: 'エラーレベル',
        display_errors: '詳細なエラー情報を出力するかどうか',
        cgi_fix_pathinfo: 'Pathinfoを開くかどうか',
        date_timezone: 'タイムゾーン',
        disableFunction: '機能を無効にします',
        disableFunctionHelper: 'execなど、無効にする関数を入力してください。複数、分割を使用してください',
        uploadMaxSize: 'アップロード制限',
        indexHelper:
            'PHP Webサイトの通常の操作を確保するために、コードをインデックスディレクトリに配置して、名前変更を避けてください',
        extensions: '拡張テンプレート',
        extension: '拡大',
        extensionHelper: '複数の拡張機能を使用して、分割してください',
        toExtensionsList: '拡張リストを表示します',
        containerConfig: 'コンテナ設定',
        containerConfigHelper: '環境変数などの情報は、作成完了後に設定 - コンテナ設定で変更できます',
        dateTimezoneHelper: '例：TZ=Asia/Shanghai（必要に応じて追加してください）',
    },
    nginx: {
        serverNamesHashBucketSizeHelper: 'サーバー名のハッシュテーブルサイズ',
        clientHeaderBufferSizeHelper: 'クライアントが要求するヘッダーバッファサイズ',
        clientMaxBodySizeHelper: '最大アップロードファイル',
        keepaliveTimeoutHelper: '接続タイムアウト',
        gzipMinLengthHelper: '最小圧縮ファイル',
        gzipCompLevelHelper: '圧縮率',
        gzipHelper: '伝送の圧縮を有効にします',
        connections: 'アクティブな接続',
        accepts: '受け入れます',
        handled: '処理',
        requests: 'リクエスト',
        reading: '読む',
        writing: '書き込み',
        waiting: '待っている',
        status: '現在のステータス',
        configResource: '構成',
        saveAndReload: '保存してリロードします',
        clearProxyCache: '逆プロキシキャッシュをきれいにします',
        clearProxyCacheWarn:
            'キャッシュで構成されたすべてのWebサイトが影響を受け、「OpenResty」が再起動されます。続けたいですか？',
        create: 'モジュールを追加',
        update: 'モジュールを編集',
        params: 'パラメータ',
        packages: 'パッケージ',
        script: 'スクリプト',
        module: 'モジュール',
        build: 'ビルド',
        buildWarn:
            'OpenRestyのビルドには一定量のCPUとメモリを確保する必要があり、時間がかかる場合がありますので、お待ちください。',
        mirrorUrl: 'ソフトウェアソース',
        paramsHelper: '例：--add-module=/tmp/ngx_brotli',
        packagesHelper: '例：git,curl カンマ区切り',
        scriptHelper:
            'コンパイル前に実行するスクリプト、通常はモジュールソースコードのダウンロード、依存関係のインストールなど',
        buildHelper:
            'モジュールの追加/変更後にビルドをクリックします。ビルドが成功すると、OpenRestyは自動的に再起動します。',
        defaultHttps: 'HTTPS 改ざん防止',
        defaultHttpsHelper1: 'これを有効にすると、HTTPS 改ざん問題を解決できます。',
    },
    ssl: {
        create: 'リクエスト',
        provider: 'タイプ',
        manualCreate: '手動で作成されます',
        acmeAccount: 'ACMEアカウント',
        resolveDomain: 'ドメイン名を解決します',
        err: 'エラー',
        value: '記録値',
        dnsResolveHelper: 'DNS Resolution Service Providerにアクセスして、次の解像度レコードを追加してください。',
        detail: '詳細',
        msg: '情報',
        ssl: '証明書',
        key: '秘密鍵',
        startDate: '有効時間',
        organization: '発行組織',
        renewConfirm: 'これにより、ドメイン名{0}の新しい証明書が更新されます。続けたいですか？',
        autoRenew: '自動更新',
        autoRenewHelper: '有効期限の30日前に自動的に更新します',
        renewSuccess: '更新成功',
        renewWebsite:
            'この証明書は次のWebサイトに関連付けられており、アプリケーションはこれらのWebサイトに同時に適用されます',
        createAcme: 'アカウントを作成する',
        acmeHelper: 'ACMEアカウントは、無料の証明書を申請するために使用されます',
        upload: '輸入',
        applyType: 'タイプ',
        apply: '更新します',
        applyStart: '証明書申請が開始されます',
        getDnsResolve: 'DNS解像度の値を取得してください、待ってください...',
        selfSigned: '自己署名CA',
        ca: '証明書当局',
        commonName: '一般名',
        caName: '証明書当局名',
        company: '組織名',
        department: '組織ユニット名',
        city: 'ローカリティ名',
        province: '州または州の名前',
        country: '国名（2文字コード）',
        commonNameHelper: '例えば、',
        selfSign: '発行証明書',
        days: '有効期間',
        domainHelper: '1行ごとに1つのドメイン名、 *およびIPアドレスをサポートします',
        pushDir: '証明書をローカルディレクトリにプッシュします',
        dir: 'ディレクトリ',
        pushDirHelper: '証明書ファイル「FullChain.PEM」とキーファイル「Privkey.Pem」がこのディレクトリで生成されます。',
        organizationDetail: '組織の詳細',
        fromWebsite: 'ウェブサイトから',
        dnsMauanlHelper: '手動解像度モードでは、作成後に適用ボタンをクリックしてDNS解像度値を取得する必要があります',
        httpHelper:
            'HTTPモードを使用するにはOpenRestyをインストールする必要があり、ワイルドカードドメイン証明書の申請はサポートされていません。',
        buypassHelper: `中国本土では、ブライパスはアクセスできません`,
        googleHelper: 'EAB HMACキーとEABキッドを取得する方法',
        googleCloudHelper: `GoogleクラウドAPIは中国本土のほとんどの地域でアクセスできません`,
        skipDNSCheck: 'DNSチェックをスキップします',
        skipDNSCheckHelper: '認定リクエスト中にタイムアウトの問題が発生した場合にのみ、こちらを確認してください。',
        cfHelper: 'グローバルAPIキーを使用しないでください',
        deprecated: '非推奨されます',
        deprecatedHelper:
            'メンテナンスは停止されており、将来のバージョンでは放棄される可能性があります。分析にはTencent Cloudメソッドを使用してください',
        disableCNAME: 'cnameを無効にします',
        disableCNAMEHelper: 'ドメイン名にCNAMEレコードがあり、リクエストが失敗するかどうかを確認してください。',
        nameserver: 'DNSサーバー',
        nameserverHelper: 'カスタムDNSサーバーを使用して、ドメイン名を確認します。',
        edit: '編集証明書',
        execShell: '認定リクエスト後にスクリプトを実行します。',
        shell: 'スクリプトコンテンツ',
        shellHelper:
            'スクリプトのデフォルトの実行ディレクトリは、1Panelインストールディレクトリです。証明書がローカルディレクトリにプッシュされた場合、実行ディレクトリは証明書プッシュディレクトリになります。デフォルトの実行タイムアウトは30分です。',
        customAcme: 'カスタム ACME サービス',
        customAcmeURL: 'ACME サービス URL',
        baiduCloud: '百度クラウド',
    },
    firewall: {
        create: 'ルールを作成します',
        edit: 'ルールを編集します',
        ccDeny: 'CC保護',
        ipWhiteList: 'IP AllowList',
        ipBlockList: 'IPブロックリスト',
        fileExtBlockList: 'ファイル拡張ブロックリスト',
        urlWhiteList: 'URL AllowList',
        urlBlockList: 'URLブロックリスト',
        argsCheck: 'パラメーターチェックを取得します',
        postCheck: 'パラメーターの検証を投稿します',
        cookieBlockList: 'クッキーブロックリスト',

        dockerHelper: `Linuxファイアウォール「{0}」ではDockerポートマッピングを無効にできません。アプリケーションは「アプリストア -> インストール済み」ページでパラメータを編集することにより、ポートの解放を制御できます。`,
        quickJump: 'クイックアクセス',
        used: '使用済み',
        unUsed: '未使用',
        firewallHelper: '{0}システムファイアウォール',
        firewallNotStart: `現在、システムファイアウォールは有効になっていません。最初に有効にします。`,
        restartFirewallHelper: 'この操作は、現在のファイアウォールを再起動します。続けたいですか？',
        stopFirewallHelper: 'これにより、サーバーはセキュリティ保護を失います。続けたいですか？',
        startFirewallHelper:
            'ファイアウォールが有効になった後、サーバーのセキュリティをよりよく保護できます。続けたいですか？',
        noPing: 'pingを無効にします',
        noPingTitle: 'pingを無効にします',
        noPingHelper: `これによりPingが無効になり、サーバーはICMP応答をエコーし​​ません。続けたいですか？`,
        onPingHelper: 'これによりPingが可能になり、ハッカーはサーバーを発見する場合があります。続けたいですか？',
        changeStrategy: '{0}戦略を変更します',
        changeStrategyIPHelper1:
            'IPアドレス戦略を[拒否]に変更します。IPアドレスが設定された後、サーバーへのアクセスは禁止されています。続けたいですか？',
        changeStrategyIPHelper2:
            'IPアドレス戦略を[許可]に変更します。IPアドレスが設定された後、通常のアクセスが復元されます。続けたいですか？',
        changeStrategyPortHelper1:
            'ポートポリシーを[ドロップ]に変更します。ポートポリシーが設定された後、外部アクセスが拒否されます。続けたいですか？',
        changeStrategyPortHelper2:
            'ポートポリシーを[受け入れる]に変更します。ポートポリシーが設定されると、通常のポートアクセスが復元されます。続けたいですか？',
        stop: '停止',
        portFormatError: 'このフィールドは有効なポートでなければなりません。',
        portHelper1: '複数のポート、例えば8080および8081',
        portHelper2: '範囲ポート、例えば8080-8089',
        changeStrategyHelper:
            '[{1}] {0}戦略を[{2}]に変更します。設定後、{0}は外部から{2}にアクセスします。続けたいですか？',
        portHelper: '複数のポートを入力できます。80,81、または範囲ポート、例えば80-88',
        strategy: '戦略',
        accept: '受け入れる',
        drop: '落とす',
        anyWhere: 'どれでも',
        address: '指定されたIPS',
        addressHelper: 'IPアドレスまたはIPセグメントをサポートします',
        allow: '許可する',
        deny: '拒否',
        addressFormatError: 'このフィールドは有効なIPアドレスでなければなりません。',
        addressHelper1: 'IPアドレスまたはIP範囲をサポートします。たとえば、「************」または「***********/24」。',
        addressHelper2: '複数のIPアドレスの場合、コンマと分離します。たとえば、「************、**********/24」。',
        allIP: 'すべてのIP',
        portRule: 'ルール|ルール',
        createPortRule: '@:commons.button.create @.lower:firewall.portrule',
        forwardRule: 'ポートフォワードルール|ポートフォワードルール',
        createForwardRule: '@:commons.button.create @:firewall.forwardrule',
        ipRule: 'IPルール|IPルール',
        createIpRule: '@:commons.button.create @:firewall.iprule',
        userAgent: 'ユーザーエージェントフィルター',
        sourcePort: 'ソースポート',
        targetIP: '宛先IP',
        targetPort: '宛先ポート',
        forwardHelper1: 'ローカルポートに転送する場合は、宛先IPを「127.0.0.1」に設定する必要があります。',
        forwardHelper2: '宛先IPを空白のままにして、ローカルポートに転送します。',
        forwardHelper3: 'IPv4ポート転送のみをサポートします。',
    },
    runtime: {
        runtime: 'ランタイム',
        workDir: '作業ディレクトリ',
        create: 'ランタイムを作成します',
        localHelper: 'ローカルオペレーティング環境は単独でインストールする必要があります',
        versionHelper: 'PHPバージョン、例えばv8.0',
        buildHelper:
            '拡張機能が多いほど、イメージ作成時にCPUの負荷が高くなります。環境作成後に拡張機能をインストールすることもできます。',
        openrestyWarn: 'PHPは、使用するためにバージョン********以降にOpenRestyにアップグレードする必要があります',
        toupgrade: 'アップグレードします',
        edit: 'ランタイムを編集します',
        extendHelper:
            'リストにない拡張機能は手動で入力して選択できます。例えば、「sockets」と入力し、ドロップダウンリストの最初の項目を選択して拡張機能リストを表示します。',
        rebuildHelper: '拡張機能を編集した後、有効にするためにPHPアプリケーションを再構築する必要があります',
        rebuild: 'PHPアプリを再構築します',
        source: 'PHP拡張ソース',
        ustc: '中国科学技術大学',
        netease: 'netease',
        aliyun: 'アリババクラウド',
        default: 'デフォルト',
        tsinghua: 'ツィンガ大学',
        xtomhk: 'Xtom Mirror Station（香港）',
        xtom: 'XTOMミラーステーション（グローバル）',
        phpsourceHelper: 'ネットワーク環境に従って適切なソースを選択してください。',
        appPort: 'アプリポート',
        externalPort: '外部ポート',
        packageManager: 'パッケージマネージャー',
        codeDir: 'コードディレクトリ',
        appPortHelper: 'アプリケーションで使用されるポート。',
        externalPortHelper: '港は外の世界にさらされました。',
        runScript: 'スクリプトを実行します',
        runScriptHelper: '起動コマンドリストは、ソースディレクトリのpackage.jsonファイルから解析されます。',
        open: '開ける',
        operatorHelper: '{0}操作は、選択した動作環境で実行されます。続けたいですか？',
        taobao: 'タオバオ',
        tencent: 'テンセント',
        imageSource: '画像ソース',
        moduleManager: 'モジュール管理',
        module: 'モジュール',
        nodeOperatorHelper:
            '{0} {1}モジュールですか？操作は、動作環境で異常を引き起こす可能性があります。進む前に確認してください',
        customScript: 'カスタムスタートアップコマンド',
        customScriptHelper: '完全な起動コマンドを提供します。たとえば、「NPM Run Start」。',
        portError: `同じポートを繰り返さないでください。`,
        systemRestartHelper: 'ステータスの説明:中断 - システムの再起動によりステータスの取得が失敗しました',
        javaScriptHelper: '完全な起動コマンドを提供します。たとえば、「Java -Jar Halo.Jar -XMX1024M -XMS256M」。',
        javaDirHelper: 'ディレクトリにはjarファイルが含まれている必要があり、サブディレクトリも受け入れられます',
        goHelper: '完全な起動コマンドを提供します。たとえば、「Go Run Main.go」または「./Main」。',
        goDirHelper: 'ディレクトリまたはサブディレクトリには、goファイルまたはバイナリファイルを含める必要があります。',
        pythonHelper:
            '完全な起動コマンドを提供します。たとえば、「PIP Install -R Repormations.txt && python manage.py runserver 0.0.0.0:5000」。',
        dotnetHelper: '完全な起動コマンドを入力してください。例えば、dotnet MyWebApp.dll',
        dirHelper: 'ノート: コンテナ内のディレクトリパスを入力してください',
        concurrency: '並行処理スキーム',
        loadStatus: '負荷状態',
    },
    process: {
        pid: 'プロセスID',
        ppid: '親pid',
        numThreads: 'スレッド',
        memory: 'メモリ',
        diskRead: 'ディスク読み取り',
        diskWrite: 'ディスク書き込み',
        netSent: 'アップリンク',
        netRecv: '下流',
        numConnections: '接続',
        startTime: '開始時間',
        state: '州',
        running: 'ランニング',
        sleep: '寝る',
        stop: '停止',
        idle: 'アイドル',
        zombie: 'ゾンビプロセス',
        wait: '待っている',
        lock: 'ロック',
        blocked: 'ブロックされています',
        cmdLine: 'コマンドを開始します',
        basic: '基本',
        mem: 'メモリ',
        openFiles: 'オープンファイル',
        env: '環境',
        noenv: 'なし',
        net: 'ネットワーク接続',
        laddr: 'ソースアドレス/ポート',
        raddr: '宛先アドレス/ポート',
        stopProcess: '終わり',
        viewDetails: '詳細',
        stopProcessWarn: 'このプロセスを終了したいですか（PID:{0}）？',
        processName: 'プロセス名',
    },
    tool: {
        supervisor: {
            loadStatusErr:
                'プロセスステータスの取得に失敗しました。スーパーバイザーサービスのステータスを確認してください。',
            notSupport:
                'Supervisor サービスが検出されませんでした。スクリプトライブラリページに移動して手動でインストールしてください',
            list: 'デーモンプロセス|デーモンプロセス',
            config: 'スーパーバイザー構成',
            primaryConfig: 'メイン構成ファイルの場所',
            notSupportCtl: `supervisorctlは検出されません。スクリプトライブラリページに移動して手動でインストールしてください。`,
            user: 'ユーザー',
            command: '指示',
            dir: 'ディレクトリ',
            numprocs: '番号。プロセスの',
            initWarn:
                'これにより、メイン構成ファイルの[[include] [include]セクションの「ファイル」値が変更されます。他の構成ファイルのディレクトリは、「{1Panel Installation Directory} /1Panel/tools/supervisord/supervisor.d/」になります。',
            operatorHelper: '操作{1}は{0}で実行されます、続行しますか？',
            uptime: '実行時間',
            notStartWarn: `スーパーバイザーは開始されません。最初に開始します。`,
            serviceName: 'サービス名',
            initHelper:
                'Supervisor サービスが検出されましたが、初期化されていません。上部のステータスバーにある初期化ボタンをクリックして設定してください。',
            serviceNameHelper: 'Systemctlが管理するスーパーバイザーサービス名、通常は監督者または監督者',
            restartHelper: 'これにより、初期化後にサービスが再起動され、既存のすべてのデーモンプロセスが停止します。',
            RUNNING: 'ランニング',
            STOPPED: '停止',
            STOPPING: '停止',
            STARTING: '起動',
            FATAL: '開始に失敗しました',
            BACKOFF: '例外を開始します',
            ERROR: 'エラー',
            statusCode: 'ステータスコード',
            manage: '管理',
            autoRestart: '自動再起動',
            EXITED: '終了しました',
            autoRestartHelper: 'プログラムが異常終了した後に自動的に再起動するかどうか',
            autoStart: '自動起動',
            autoStartHelper: 'Supervisor 起動後にサービスを自動的に起動するかどうか',
        },
    },
    xpack: {
        expiresTrialAlert:
            'ご注意: あなたのProトライアルは{0}日後に終了し、すべてのPro機能が使用できなくなります。適時に更新またはフルバージョンにアップグレードしてください。',
        expiresAlert:
            'ご注意: あなたのProライセンスは{0}日後に終了し、すべてのPro機能が使用できなくなります。継続的な使用のために速やかに更新してください。',
        menu: 'Рro',
        upage: 'AIウェブサイトビルダー',
        app: {
            app: 'APP',
            title: 'パネルの別名',
            titleHelper: 'パネルのエイリアスは、APP端末での表示に使用されます（デフォルトのパネルエイリアス）',
            qrCode: 'QRコード',
            apiStatusHelper: 'パネルAPPはAPIインターフェース機能を有効にする必要があります',
            apiInterfaceHelper:
                'パネルAPIインターフェースアクセスをサポート（この機能はパネルアプリで有効にする必要があります）',
            apiInterfaceHelper1:
                'パネルアプリのアクセスには訪問者をホワイトリストに追加する必要があります。固定IPでない場合、0.0.0.0/0（すべての IPv4）、::/0（すべての IPv6）を追加することをお勧めします',
            qrCodeExpired: 'リフレッシュ時間',
            apiLeakageHelper: 'QRコードを漏洩しないでください。信頼できる環境でのみ使用してください。',
        },
        waf: {
            name: 'WAF',
            blackWhite: 'ブラックリストとホワイトリスト',
            globalSetting: 'グローバル設定',
            websiteSetting: 'ウェブサイト設定',
            blockRecords: 'ブロック記録',
            world: '世界',
            china: '中国',
            intercept: 'インターセプト',
            request: 'リクエスト',
            count4xx: '4xxの数',
            count5xx: '5xxの数',
            todayStatus: '今日のステータス',
            reqMap: '攻撃マップ（過去30日間）',
            resource: 'リソース',
            count: '数',
            hight: '高い',
            low: '低い',
            reqCount: 'リクエスト数',
            interceptCount: 'インターセプト数',
            requestTrends: 'リクエストトレンド（過去7日間）',
            interceptTrends: 'インターセプトトレンド（過去7日間）',
            whiteList: 'ホワイトリスト',
            blackList: 'ブラックリスト',
            ipBlackListHelper: 'ブラックリストに登録されたIPアドレスはウェブサイトへのアクセスがブロックされます',
            ipWhiteListHelper: 'ホワイトリストに登録されたIPアドレスはすべての制限をバイパスします',
            uaBlackListHelper: 'ブラックリストに登録されたUser-Agentのリクエストはブロックされます',
            uaWhiteListHelper: 'ホワイトリストに登録されたUser-Agentのリクエストはすべての制限をバイパスします',
            urlBlackListHelper: 'ブラックリストに登録されたURLへのリクエストはブロックされます',
            urlWhiteListHelper: 'ホワイトリストに登録されたURLへのリクエストはすべての制限をバイパスします',
            ccHelper:
                'もしサイトが{1}回以上のリクエストを同一IPから{0}秒以内に受けた場合、そのIPは{2}間ブロックされます',
            blockTime: 'ブロック時間',
            attackHelper: 'もし累積的なインターセプトが{1}回以上{0}秒以内に発生した場合、そのIPは{2}間ブロックされます',
            notFoundHelper: 'もし404エラーが{1}回以上{0}秒以内に返された場合、そのIPは{2}間ブロックされます',
            frequencyLimit: '頻度制限',
            regionLimit: '地域制限',
            defaultRule: 'デフォルトルール',
            accessFrequencyLimit: 'アクセス頻度制限',
            attackLimit: '攻撃頻度制限',
            notFoundLimit: '404頻度制限',
            urlLimit: 'URL頻度制限',
            urlLimitHelper: '単一URLのアクセス頻度を設定します',
            sqliDefense: 'SQLインジェクション防止',
            sqliHelper: 'リクエストでSQLインジェクションを検出してブロックします',
            xssHelper: 'リクエストでXSSを検出してブロックします',
            xssDefense: 'XSS防止',
            uaDefense: '悪意のあるUser-Agentルール',
            uaHelper: '一般的な悪意のあるボットを識別するルールが含まれています',
            argsDefense: '悪意のあるパラメータルール',
            argsHelper: '悪意のあるパラメータを含むリクエストをブロックします',
            cookieDefense: '悪意のあるCookieルール',
            cookieHelper: 'リクエストに悪意のあるCookieを持ち込むことを禁止します',
            headerDefense: '悪意のあるヘッダールール',
            headerHelper: '悪意のあるヘッダーを含むリクエストを禁止します',
            httpRule: 'HTTPリクエストメソッドルール',
            httpHelper:
                'アクセスを許可するメソッドタイプを設定します。特定のタイプのアクセスを制限したい場合は、そのタイプのボタンをオフにしてください。例えば、GETタイプのみのアクセスを許可する場合は、GET以外のボタンをオフにする必要があります',
            geoRule: '地域アクセス制限',
            geoHelper:
                '特定の地域からのウェブサイトへのアクセスを制限します。例えば、中国本土からのアクセスを許可し、それ以外の地域からのリクエストをブロックすることができます',
            ipLocation: 'IP位置',
            action: 'アクション',
            ruleType: '攻撃タイプ',
            ipHelper: 'IPアドレスを入力してください',
            attackLog: '攻撃ログ',
            rule: 'ルール',
            ipArr: 'IPV4範囲',
            ipStart: '開始IP',
            ipEnd: '終了IP',
            ipv4: 'IPv4',
            ipv6: 'IPv6',
            urlDefense: 'URLルール',
            urlHelper: '禁止されたURL',
            dirFilter: 'ディレクトリフィルター',
            sqlInject: 'SQLインジェクション',
            xss: 'XSS',
            phpExec: 'PHPスクリプト実行',
            oneWordTrojan: 'ワンワードトロイの木馬',
            appFilter: '危険なディレクトリフィルタリング',
            webshell: 'Webシェル',
            args: '悪意のあるパラメータ',
            protocolFilter: 'プロトコルフィルター',
            javaFilter: 'Java危険ファイルフィルタリング',
            scannerFilter: 'スキャナーフィルター',
            escapeFilter: 'エスケープフィルター',
            customRule: 'カスタムルール',
            httpMethod: 'HTTPメソッドフィルター',
            fileExt: 'ファイルアップロード制限',
            fileExtHelper: 'アップロード禁止ファイル拡張子',
            deny: '禁止',
            allow: '許可',
            field: 'オブジェクト',
            pattern: '条件',
            ruleContent: 'コンテンツ',
            contain: '含む',
            equal: '等しい',
            regex: '正規表現',
            notEqual: '等しくない',
            customRuleHelper: '指定された条件に基づいてアクションを実行',
            actionAllow: '許可',
            blockIP: 'IPをブロック',
            code: 'ステータスコードを返す',
            noRes: '切断（444）',
            badReq: '無効なパラメータ（400）',
            forbidden: 'アクセス禁止（403）',
            serverErr: 'サーバーエラー（500）',
            resHtml: '応答ページ',
            allowHelper: 'アクセスを許可すると、後続のWAFルールをスキップします。慎重に使用してください',
            captcha: '人間と機械の検証',
            fiveSeconds: '5秒検証',
            location: '地域',
            redisConfig: 'Redis設定',
            redisHelper: 'Redisを有効にして、一時的にブロックされたIPを永続化します',
            wafHelper: 'WAFを閉じると、すべてのウェブサイトが保護を失います',
            attackIP: '攻撃IP',
            attackParam: '攻撃詳細',
            execRule: 'ヒットしたルール',
            acl: 'ACL',
            sql: 'SQLインジェクション',
            cc: 'アクセス頻度制限',
            isBlocking: 'ブロック中',
            isFree: 'ブロック解除',
            unLock: 'ロック解除',
            unLockHelper: 'IP:{0}をブロック解除しますか？',
            saveDefault: 'デフォルトを保存',
            saveToWebsite: 'ウェブサイトに適用',
            saveToWebsiteHelper: '現在の設定をすべてのウェブサイトに適用しますか？',
            websiteHelper: 'ウェブサイトを作成するためのデフォルト設定です。変更をウェブサイトに適用する必要があります',
            websiteHelper2:
                'ウェブサイトを作成するためのデフォルト設定です。ウェブサイトで特定の設定を変更してください',
            ipGroup: 'IPグループ',
            ipGroupHelper:
                '1行に1つのIPまたはIPセグメントを入力、IPv4およびIPv6をサポートします。例: ***********または***********/24',
            ipBlack: 'IPブラックリスト',
            openRestyAlert: 'OpenRestyのバージョンは{0}より高くする必要があります',
            initAlert:
                '初回使用時には初期化が必要です。ウェブサイトの設定ファイルが変更され、元のWAF設定が失われます。事前にOpenRestyのバックアップを取ってください',
            initHelper: '初期化操作により、既存のWAF設定がクリアされます。初期化してもよろしいですか？',
            mainSwitch: 'メインスイッチ',
            websiteAlert: 'まずウェブサイトを作成してください',
            defaultUrlBlack: 'URLルール',
            htmlRes: 'インターセプトページ',
            urlSearchHelper: 'URLを入力して、曖昧検索をサポートしてください',
            toCreate: '作成',
            closeWaf: 'WAFを閉じる',
            closeWafHelper: 'WAFを閉じると、ウェブサイトは保護を失います。続行しますか？',
            addblack: 'ブラック追加',
            addwhite: 'ホワイト追加',
            addblackHelper: 'IP:{0}をデフォルトのブラックリストに追加しますか？',
            addwhiteHelper: 'IP:{0}をデフォルトのホワイトリストに追加しますか？',
            defaultUaBlack: 'ユーザーエージェントルール',
            defaultIpBlack: '悪意のあるIPグループ',
            cookie: 'クッキールール',
            urlBlack: 'URLブラックリスト',
            uaBlack: 'ユーザーエージェントブラックリスト',
            attackCount: '攻撃頻度制限',
            fileExtCheck: 'ファイルアップロード制限',
            geoRestrict: '地域アクセス制限',
            attacklog: '遮断記録',
            unknownWebsite: '認証されていないドメイン名アクセス',
            geoRuleEmpty: '地域は空にできません',
            unknown: 'ウェブサイトが存在しません',
            geo: '地域制限',
            revertHtml: '{0} をデフォルトページに戻しますか？',
            five_seconds: '5秒認証',
            header: 'ヘッダールール',
            methodWhite: 'HTTPルール',
            expiryDate: '有効期限',
            expiryDateHelper: '認証後、有効期間内は再認証されません',
            defaultIpBlackHelper: 'インターネットから収集された悪意のあるIPをアクセス防止のために使用',
            notFoundCount: '404頻度制限',
            matchValue: '一致する値',
            headerName: 'このフィールドは、特別なキャラクターではなく、英語、数字、-をサポート、長さは3-30',
            cdnHelper: 'CDNを使用しているウェブサイトは、ここで正しいソースIPを取得できます',
            clearLogWarn: 'ログをクリアすると元に戻せません。続けますか？',
            commonRuleHelper: 'ルールは部分一致です',
            blockIPHelper:
                'ブロックされたIPはOpenRestyに一時的に保存され、OpenRestyを再起動すると解除されます。ブロック機能で永久的にブロックできます',
            addWhiteUrlHelper: 'URL {0} をホワイトリストに追加しますか？',
            dashHelper: 'コミュニティバージョンでもグローバル設定とウェブサイト設定の機能を使用できます',
            wafStatusHelper: 'WAFが有効ではありません。グローバル設定で有効にしてください',
            ccMode: 'モード',
            global: 'グローバルモード',
            uriMode: 'URLモード',
            globalHelper:
                'グローバルモード: 任意のURLに対するリクエストの合計数が一定時間内にしきい値を超えるとトリガーされます',
            uriModeHelper: 'URLモード: 単一のURLに対するリクエストの数が一定時間内にしきい値を超えるとトリガーされます',
            ip: 'IPブラックリスト',
            globalSettingHelper:
                '[ウェブサイト] タグの設定は [ウェブサイト設定] で有効にする必要があり、グローバル設定は新しく作成されたウェブサイトのデフォルト設定です',
            globalSettingHelper2: '設定は [グローバル設定] と [ウェブサイト設定] の両方で有効にする必要があります',
            urlCCHelper:
                '{0} 秒以内にこの URL に対して {1} 回を超えるリクエストがあったため、この IP をブロックします {2}',
            urlCCHelper2: 'URL にパラメータを含めることはできません',
            notContain: '含まない',
            urlcc: 'URL 頻度制限',
            method: 'リクエストタイプ',
            addIpsToBlock: 'IP を一括ブロック',
            addUrlsToWhite: 'URL を一括でホワイトリストに追加',
            noBlackIp: 'IP は既にブロックされているため、再度ブロックする必要はありません',
            noWhiteUrl: 'URL は既にホワイトリストに含まれているため、再度追加する必要はありません',
            spiderIpHelper:
                '百度、Bing、Google、360、神马、搜狗、字节、DuckDuckGo、Yandexを含みます。これを閉じると、すべてのクローラーのアクセスがブロックされます。',
            spiderIp: 'スパイダー IP プール',
            geoIp: 'IP アドレスライブラリ',
            geoIpHelper: 'IP の地理的位置を確認するために使用されます',
            stat: '攻撃レポート',
            statTitle: 'レポート',
            attackIp: '攻撃 IP',
            attackCountNum: '攻撃回数',
            percent: '割合',
            addblackUrlHelper: 'URL: {0} をデフォルトのブラックリストに追加しますか？',
            rce: 'リモートコード実行',
            software: 'ソフトウェア',
            cveHelper: '一般的なソフトウェアやフレームワークの脆弱性を含みます',
            vulnCheck: '補足ルール',
            ssrf: 'SSRF 脆弱性',
            afr: '任意ファイル読み取り',
            ua: '未承認アクセス',
            id: '情報漏洩',
            aa: '認証回避',
            dr: 'ディレクトリトラバーサル',
            xxe: 'XXE 脆弱性',
            suid: 'シリアライズ脆弱性',
            dos: 'サービス拒否脆弱性',
            afd: '任意ファイルダウンロード',
            sqlInjection: 'SQL インジェクション',
            afw: '任意ファイル書き込み',
            il: '情報漏洩',
            clearAllLog: 'すべてのログをクリア',
            exportLog: 'ログをエクスポート',
            appRule: 'アプリケーションルール',
            appRuleHelper:
                '一般的なアプリケーションルール。有効にすると誤検出を減らすことができます。1つのウェブサイトにつき1つのルールのみ使用可能です',
            logExternal: '記録タイプを除外',
            ipWhite: 'IP ホワイトリスト',
            urlWhite: 'URL ホワイトリスト',
            uaWhite: 'ユーザーエージェントホワイトリスト',
            logExternalHelper:
                '除外された記録タイプはログに記録されません。ブラックリスト/ホワイトリスト、地域アクセス制限、カスタムルールは大量のログを生成します。除外をお勧めします',
            ssti: 'SSTI 攻撃',
            crlf: 'CRLF インジェクション',
            strict: '厳格モード',
            strictHelper: 'より厳格なルールを使用してリクエストを検証します',
            saveLog: 'ログを保存',
            remoteURLHelper: 'リモート URL は、1行に1つのIPで、他の文字がないことを保証する必要があります',
            notFound: 'Not Found (404)',
            serviceUnavailable: 'サービスを利用できません (503)',
            gatewayTimeout: 'ゲートウェイタイムアウト (504)',
            belongToIpGroup: 'IP グループに属しています',
            notBelongToIpGroup: 'IP グループに属していません',
            unknownWebsiteKey: '未知のドメイン',
            special: '特別な文字は使用できません',
        },
        monitor: {
            name: 'ウェブサイトモニタリング',
            pv: 'ページビュー',
            uv: 'ユニークビジター',
            flow: 'トラフィックフロー',
            ip: 'IP',
            spider: 'スパイダー',
            visitors: '訪問者のトレンド',
            today: '今日',
            last7days: '過去7日間',
            last30days: '過去30日間',
            uvMap: '訪問者マップ（30日間）',
            qps: 'リアルタイムリクエスト（分単位）',
            flowSec: 'リアルタイムトラフィック（分単位）',
            excludeCode: 'ステータスコードを除外',
            excludeUrl: 'URLを除外',
            excludeExt: '拡張子を除外',
            cdnHelper: 'CDN提供のヘッダーから実際のIPを取得',
            reqRank: '訪問ランキング',
            refererDomain: 'リファラードメイン',
            os: 'システム',
            browser: 'ブラウザ/クライアント',
            device: 'デバイス',
            showMore: 'もっと見る',
            unknown: 'その他',
            pc: 'コンピュータ',
            mobile: 'モバイルデバイス',
            wechat: 'WeChat',
            machine: 'マシン',
            tencent: 'Tencentブラウザ',
            ucweb: 'UCブラウザ',
            '2345explorer': '2345ブラウザ',
            huaweibrowser: 'Huaweiブラウザ',
            log: 'リクエストログ',
            statusCode: 'ステータスコード',
            requestTime: '応答時間',
            flowRes: 'レスポンストラフィック',
            method: 'リクエストメソッド',
            statusCodeHelper: '上記のステータスコードを入力してください',
            statusCodeError: '無効なステータスコードのタイプ',
            methodHelper: '上記のリクエストメソッドを入力してください',
            all: 'すべて',
            baidu: 'Baidu',
            google: 'Google',
            bing: 'Bing',
            bytes: '今日のヘッドライン',
            sogou: 'Sogou',
            failed: 'エラー',
            ipCount: 'IP数',
            spiderCount: 'スパイダリクエスト',
            averageReqTime: '平均応答時間',
            totalFlow: '総トラフィック',
            logSize: 'ログファイルのサイズ',
            realIPType: '実際のIP取得方法',
            fromHeader: 'HTTPヘッダーから取得',
            fromHeaders: 'ヘッダーリストから取得',
            header: 'HTTPヘッダー',
            cdnConfig: 'CDN設定',
            xff1: 'X-Forwarded-Forの最初のプロキシ',
            xff2: 'X-Forwarded-Forの第二のプロキシ',
            xff3: 'X-Forwarded-Forの第三のプロキシ',
            xffHelper:
                '例: X-Forwarded-For: <client>,<proxy1>,<proxy2>,<proxy3> 上位のプロキシは最後のIP <proxy3> を取得します',
            headersHelper: '一般的に使用されるCDNのHTTPヘッダーから実際のIPを取得し、最初の利用可能な値を選択',
            monitorCDNHelper: 'ウェブサイトモニタリングのためにCDN設定を変更すると、WAF CDN設定も更新されます',
            wafCDNHelper: 'WAF CDN設定を変更すると、ウェブサイトモニタリングのCDN設定も更新されます',
            statusErr: '無効なステータスコード形式',
            shenma: '神馬検索',
            duckduckgo: 'DuckDuckGo',
            '360': '360検索',
            excludeUri: 'URIを除外',
            top100Helper: '上位100データを表示',
            logSaveDay: 'ログ保持期間（日数）',
            cros: 'Chrome OS',
            theworld: 'TheWorldブラウザ',
            edge: 'Microsoft Edge',
            maxthon: 'Maxthonブラウザ',
            monitorStatusHelper: 'モニタリングは有効ではありません。設定で有効にしてください',
            excludeIp: 'IPアドレスを除外',
            excludeUa: 'ユーザーエージェントを除外',
            remotePort: 'リモートポート',
            unknown_browser: '不明',
            unknown_os: '不明',
            unknown_device: '不明',
            logSaveSize: '最大ログ保存サイズ',
            logSaveSizeHelper: 'これは単一ウェブサイトのログ保存サイズです',
            '360se': '360 セキュリティブラウザ',
            websites: 'ウェブサイトリスト',
            trend: 'トレンド統計',
            reqCount: 'リクエスト数',
            uriHelper: '/test/* や /*/index.php を使用して Uri を除外できます',
        },
        tamper: {
            tamper: 'ウェブサイトの改ざん防止',
            ignoreTemplate: 'ディレクトリテンプレートを除外',
            protectTemplate: 'ファイルテンプレートを保護',
            templateContent: 'テンプレート内容',
            template: 'テンプレート',
            tamperHelper1:
                'ワンクリック展開タイプのウェブサイトには、アプリケーションディレクトリの改ざん防止機能を有効にすることをお勧めします。ウェブサイトが正常に動作しない場合やバックアップ、復元に失敗した場合は、まず改ざん防止機能を無効にしてください;',
            tamperHelper2:
                '除外されたディレクトリの外にある保護されたファイルの読み取り、書き込み、削除、権限および所有者の変更操作が制限されます',
            tamperPath: '保護ディレクトリ',
            tamperPathEdit: 'パスを変更',
            log: 'インターセプトログ',
            totalProtect: '総保護',
            todayProtect: '今日の保護',
            addRule: 'ルールを追加',
            ignore: 'ディレクトリを除外',
            ignoreHelper: '1行に1つ、例: \ntmp\n./tmp',
            ignoreTemplateHelper: '無視するフォルダ名を追加し、カンマで区切ってください。例: tmp,cache',
            templateRule: '長さ1-512、名前には{0}などの記号を含めることはできません',
            ignoreHelper1: '無視するフォルダ名または特定のパスを追加してください',
            ignoreHelper2: '特定のフォルダを無視するには、./で始まる相対パスを使用してください',
            protect: 'ファイルを保護',
            protectHelper: '1行に1つ、例: \npng\n./test.css',
            protectTemplateHelper: '無視するファイル名または拡張子を追加し、カンマで区切ってください。例: conf,.css',
            protectHelper1: 'ファイル名、拡張子、または特定のファイルを保護することができます',
            protectHelper2: '特定のファイルを保護するには、./で始まる相対パスを使用してください',
            enableHelper:
                '次のウェブサイトの改ざん防止機能が有効になります。ウェブサイトのセキュリティを向上させるために続行しますか？',
            disableHelper: '次のウェブサイトの改ざん防止機能が無効になります。続行しますか？',
        },
        setting: {
            setting: 'パネル設定',
            title: 'パネルの説明',
            titleHelper:
                'ユーザーログインページに表示されます（例：Linuxサーバー運用管理パネル、推奨文字数：8〜15文字）',
            logo: 'ロゴ（テキストなし）',
            logoHelper:
                'メニューが折りたたまれている場合、管理ページの左上隅に表示されます（推奨画像サイズ：82px*82px）',
            logoWithText: 'ロゴ（テキストあり）',
            logoWithTextHelper:
                'メニューが展開されている場合、管理ページの左上隅に表示されます（推奨画像サイズ：185px*55px）',
            favicon: 'ウェブサイトアイコン',
            faviconHelper: 'ウェブサイトアイコン（推奨画像サイズ：16px*16px）',
            reUpload: 'ファイルを選択',
            setDefault: 'デフォルトに戻す',
            setHelper: '現在の設定が保存されます。続けますか？',
            setDefaultHelper: 'すべてのパネル設定がデフォルトに戻されます。続けますか？',
            logoGroup: 'ロゴ',
            imageGroup: '画像',
            loginImage: 'ログインページの画像',
            loginImageHelper: 'ログインページに表示されます（推奨画像サイズ：500×416px）',
            loginBgType: 'ログインページ背景タイプ',
            loginBgImage: 'ログインページ背景画像',
            loginBgImageHelper: 'ログインページの背景画像として表示されます（推奨画像サイズ：1920×1080px）',
            loginBgColor: 'ログインページ背景色',
            loginBgColorHelper: 'ログインページの背景色として表示されます',
            image: '画像',
            bgColor: '背景色',
            loginGroup: 'ログインページ',
            loginBtnLinkColor: 'ボタン／リンクの色',
            loginBtnLinkColorHelper: 'ログインページに表示されるボタン／リンクの色になります',
        },
        helper: {
            wafTitle1: 'インターセプションマップ',
            wafContent1: '過去30日間のインターセプトの地理的分布を表示',
            wafTitle2: '地域別アクセス制限',
            wafContent2: '地理的な位置に基づいてウェブサイトのアクセス元を制限',
            wafTitle3: 'カスタムインターセプションページ',
            wafContent3: 'リクエストがインターセプトされた後に表示するカスタムページを作成',
            wafTitle4: 'カスタムルール (ACL)',
            wafContent4: 'カスタムルールに基づいてリクエストをインターセプト',

            tamperTitle1: 'ファイル整合性監視',
            tamperContent1:
                'ウェブサイトのファイルの整合性を監視、コアファイル、スクリプトファイル、設定ファイルを含む。',
            tamperTitle2: 'リアルタイムスキャンと検出',
            tamperContent2:
                'ウェブサイトのファイルシステムをリアルタイムでスキャンし、異常や改竄されたファイルを検出。',
            tamperTitle3: 'セキュリティ権限設定',
            tamperContent3:
                '適切な権限設定とアクセス制御ポリシーを通じてウェブサイトファイルへのアクセスを制限し、潜在的な攻撃対象面を減少。',
            tamperTitle4: 'ログ記録と分析',
            tamperContent4:
                'ファイルのアクセスおよび操作ログを記録し、後の監査および分析に使用、また潜在的なセキュリティ脅威を特定。',

            settingTitle1: 'カスタムウェルカムメッセージ',
            settingContent1: '1Panelのログインページにカスタムウェルカムメッセージを設定。',
            settingTitle2: 'カスタムロゴ',
            settingContent2: 'ブランド名やその他のテキストを含むロゴ画像をアップロードできる。',
            settingTitle3: 'カスタムウェブサイトアイコン',
            settingContent3:
                'カスタムアイコンをアップロードして、デフォルトのブラウザアイコンを置き換え、ユーザー体験を向上。',

            monitorTitle1: '訪問者トレンド',
            monitorContent1: 'ウェブサイト訪問者のトレンドを統計および表示',
            monitorTitle2: '訪問者マップ',
            monitorContent2: 'ウェブサイト訪問者の地理的分布を統計および表示',
            monitorTitle3: 'アクセス統計',
            monitorContent3:
                'ウェブサイトリクエスト情報を統計、スパイダー、アクセスデバイス、リクエストステータスなどを含む。',
            monitorTitle4: 'リアルタイムモニタリング',
            monitorContent4:
                'ウェブサイトリクエスト情報をリアルタイムでモニタリング、リクエスト数、トラフィックなどを含む。',

            alertTitle1: 'SMSアラート',
            alertContent1:
                'サーバーリソース使用量の異常、ウェブサイトおよび証明書の有効期限、新しいバージョンの更新、パスワードの期限切れなどが発生した場合、ユーザーにSMSアラートで通知し、タイムリーな処理を確保。',
            alertTitle2: 'アラートログ',
            alertContent2:
                'ユーザーがアラートログを表示できる機能を提供し、過去のアラートイベントを追跡および分析しやすく。',
            alertTitle3: 'アラート設定',
            alertContent3:
                'カスタム電話番号、日々のプッシュ頻度、日々のプッシュ時間設定を提供し、ユーザーがより合理的なプッシュアラートを設定しやすく。',

            nodeTitle1: 'ワンクリックノード追加',
            nodeContent1: '複数のサーバーノードを迅速に統合',
            nodeTitle2: 'バッチアップグレード',
            nodeContent2: '一度の操作ですべてのノードを同期アップグレード',
            nodeTitle3: 'ノードステータス監視',
            nodeContent3: '各ノードの運用状況をリアルタイムで把握',
            nodeTitle4: '迅速なリモート接続',
            nodeContent4: 'ワンクリックでノードリモート端末に直接接続',

            fileExchangeTitle1: 'キー認証伝送',
            fileExchangeContent1: 'SSHキーを介して認証し、伝送のセキュリティを確保します。',
            fileExchangeTitle2: '効率的なファイル同期',
            fileExchangeContent2: '変更されたコンテンツのみを同期し、伝送速度と安定性を大幅に向上させます。',
            fileExchangeTitle3: 'マルチノード相互通信のサポート',
            fileExchangeContent3:
                '異なるノード間でプロジェクトファイルを簡単に転送し、複数のサーバーを柔軟に管理します。',

            appTitle1: '柔軟なパネル管理',
            appContent1: 'いつでもどこでも1Panelサーバーを簡単に管理できます。',
            appTitle2: '包括的なサービス情報',
            appContent2:
                'モバイル端末でアプリケーション、ウェブサイト、Docker、データベースなどの基本的な管理を行い、アプリやウェブサイトの迅速な作成をサポートします。',
            appTitle3: 'リアルタイム異常監視',
            appContent3:
                'モバイル端末でサーバーステータス、WAFセキュリティ監視、ウェブサイトの訪問統計、プロセスの健康状態をリアルタイムで確認できます。',

            clusterTitle1: 'マスタースレーブ展開',
            clusterContent1:
                '異なるノードで MySQL/Postgres/Redis マスタースレーブインスタンスを作成することをサポートし、自動的にマスタースレーブ関連付けと初期化を完了します',
            clusterTitle2: 'マスタースレーブ管理',
            clusterContent2:
                '統一されたページで複数のマスタースレーブノードを一元的に管理し、それらの役割、実行状態などを表示します',
            clusterTitle3: 'レプリケーション状態',
            clusterContent3:
                'マスタースレーブレプリケーション状態と遅延情報を表示し、同期の問題を解決するのに役立ちます',
        },
        node: {
            master: '主ノード',
            masterBackup: 'マスターノードバックアップ',
            backupNode: 'バックアップノード',
            backupFrequency: 'バックアップ頻度（時間）',
            backupCopies: 'バックアップ保持数',
            noBackupNode: '現在バックアップノードが空です。保存するバックアップノードを選択して再試行してください！',
            masterBackupAlert:
                '現在マスターノードのバックアップが設定されていません。データセキュリティを確保するため、障害時に新しいマスターノードに手動で切り替えられるよう、速やかにバックアップノードを設定してください。',
            node: 'ノード',
            addr: 'アドレス',
            nodeUnhealthy: 'ノード状態異常',
            deletedNode: '削除済みノード {0} は現在アップグレード操作をサポートしていません！',
            nodeUnhealthyHelper: 'ノード状態異常を検出しました。[ノード管理]で確認してから再試行してください！',
            nodeUnbind: 'ノードがライセンスにバインドされていません',
            nodeUnbindHelper:
                'このノードがライセンスにバインドされていないことを検出しました。[パネル設定 - ライセンス]メニューでバインドしてから再試行してください！',
            memTotal: '総メモリ',
            nodeManagement: 'ノード管理',
            addNode: 'ノードを追加',
            connInfo: '接続情報',
            nodeInfo: 'ノード情報',
            syncInfo: 'データ同期,',
            syncHelper: 'マスターノードのデータが変更されると、この子ノードにリアルタイムで同期されます,',
            syncBackupAccount: 'バックアップアカウント設定',
            syncWithMaster:
                'プロ版にアップグレード後、すべてのデータがデフォルトで同期されます。ノード管理で同期ポリシーを手動調整できます。',
            syncProxy: 'システムプロキシ設定',
            syncProxyHelper: 'システムプロキシ設定の同期にはDockerの再起動が必要です',
            syncProxyHelper1: 'Dockerの再起動は現在実行中のコンテナサービスに影響する可能性があります。',
            syncProxyHelper2: 'コンテナ - 設定 ページで手動で再起動できます。',
            syncProxyHelper3:
                'システムプロキシ設定の同期にはDockerの再起動が必要で、現在実行中のコンテナサービスに影響する可能性があります',
            syncProxyHelper4:
                'システムプロキシ設定の同期にはDockerの再起動が必要です。後でコンテナ - 設定 ページで手動で再起動できます。',
            syncCustomApp: 'カスタムアプリリポジトリを同期',
            syncAlertSetting: 'システムアラート設定',
            syncNodeInfo: 'ノード基本データ,',
            nodeSyncHelper: 'ノード情報の同期は、以下の情報を同期します：',
            nodeSyncHelper1: '1. 公共のバックアップアカウント情報',
            nodeSyncHelper2: '2. 主ノードとサブノードの接続情報',

            nodeCheck: '可用性チェック',
            checkSSH: 'ノードSSH接続を確認',
            checkUserPermission: 'ノードユーザー権限を確認',
            isNotRoot:
                'このノードではパスワードなしsudoがサポートされておらず、現在のユーザーがrootではないことが検出されました',
            checkLicense: 'ノードライセンス状態を確認',
            checkService: 'ノードの既存サービス情報を確認',
            checkPort: 'ノードポート到達性を確認',
            panelExist:
                'このノードで1Panel V1サービスが実行中です。追加前に移行スクリプトでV2へアップグレードしてください。',
            coreExist:
                '現在のノードはマスターノードとして有効化済みのため、直接スレーブノードとして追加できません。追加する前にまずスレーブノードにダウングレードしてください。詳細はドキュメントを参照してください。',
            agentExist:
                'このノードに1panel-agentが既にインストールされています。続行すると既存データを保持し、1panel-agentサービスのみを置換します。',
            oldDataExist: 'このノードに1Panel V2の過去データが検出されました。以下の情報で現在の設定を上書きします:',
            errLicense: 'このノードに紐づけられたライセンスが利用できません。確認して再試行してください！',
            errNodePort:
                'ノードポート[ {0} ]にアクセスできないことが検出されました。ファイアウォールまたはセキュリティグループでこのポートが開放されているか確認してください。',

            reinstallHelper: 'ノード{0}を再インストールします。続行しますか？',
            unhealthyCheck: '異常チェック',
            fixOperation: '修正操作',
            checkName: 'チェック項目',
            checkSSHConn: 'SSH接続の可用性を確認',
            fixSSHConn: 'ノードを手動で編集し、接続情報を確認',
            checkConnInfo: 'エージェント接続情報を確認',
            checkStatus: 'ノードサービスの可用性を確認',
            fixStatus: '「systemctl status 1panel-agent.service」を実行して、サービスが起動しているか確認します。',
            checkAPI: 'ノードAPIの可用性を確認',
            fixAPI: 'ノードのログを確認し、ファイアウォールのポートが正常に開放されているか確認します。',
            forceDelete: '強制削除',
            operateHelper: '以下のノードに{0}操作を行います。続行しますか？',
            forceDeleteHelper: '強制削除はノード削除エラーを無視し、データベースメタデータを削除します',
            uninstall: 'ノードデータを削除',
            uninstallHelper: 'ノードに関連するすべての1Panelデータが削除されます。慎重に選択してください！',
            baseDir: 'インストールディレクトリ',
            baseDirHelper: 'インストールディレクトリが空の場合、デフォルトで/optディレクトリにインストールされます',
            nodePort: 'ノードポート',
            offline: 'オフラインモード',
            freeCount: '無料枠 [{0}]',
            offlineHelper: 'ノードがオフライン環境にある場合に使用',
        },
        customApp: {
            name: 'カスタムアプリリポジトリ',
            appStoreType: 'アプリストアパッケージソース',
            appStoreUrl: 'リポジトリURL',
            local: 'ローカルパス',
            remote: 'リモートリンク',
            imagePrefix: 'イメージプレフィックス',
            imagePrefixHelper:
                '機能: イメージプレフィックスをカスタマイズし、composeファイル内のイメージフィールドを変更します。例えば、イメージプレフィックスを1panel/customに設定した場合、MaxKBのイメージフィールドは1panel/custom/maxkb:v1.10.0に変更されます',
            closeHelper: 'カスタムアプリリポジトリの使用をキャンセルしますか',
            appStoreUrlHelper: '.tar.gz形式のみサポートされます',
            postNode: 'サブノードへ同期',
            postNodeHelper:
                'カスタムストアパッケージを子ノードのインストールディレクトリの tmp/customApp/apps.tar.gz に同期します',
            nodes: 'ノードを選択',
            selectNode: 'ノードを選択',
            selectNodeError: 'ノードを選択してください',
            licenseHelper: 'プロバージョンはカスタムアプリケーションリポジトリ機能をサポートしています',
        },
        alert: {
            isAlert: 'アラート',
            alertCount: 'アラート数',
            clamHelper: '感染したファイルをスキャンするときにアラートをトリガーします',
            cronJobHelper: 'タスクの実行が失敗したときにアラートをトリガーします',
            licenseHelper: 'プロのバージョンはSMSアラートをサポートします',
            alertCountHelper: '最大毎日のアラーム周波数',
            alert: 'SMSアラート',
            logs: 'アラートログ',
            list: 'アラートリスト',
            addTask: 'アラートを作成',
            editTask: 'アラートを編集',
            alertMethod: '方法',
            alertMsg: 'アラートメッセージ',
            alertRule: 'アラートルール',
            titleSearchHelper: 'アラートタイトルを入力して検索します',
            taskType: 'タイプ',
            ssl: '証明書 (SSL) 期限切れ',
            siteEndTime: 'ウェブサイト期限切れ',
            panelPwdEndTime: 'パネルパスワード期限切れ',
            panelUpdate: '新しいパネルバージョンあり',
            cpu: 'サーバーCPUアラート',
            memory: 'サーバーメモリアラート',
            load: 'サーバーロードアラート',
            disk: 'サーバーディスクアラート',
            website: 'ウェブサイト',
            certificate: 'SSL証明書',
            remainingDays: '残り日数',
            sendCount: '送信回数',
            sms: 'SMS',
            wechat: 'WeChat',
            dingTalk: 'DingTalk',
            feiShu: 'FeiShu',
            mail: 'メール',
            weCom: 'WeCom',
            sendCountRulesHelper: '期限前に送信されるアラートの合計（1日1回）',
            panelUpdateRulesHelper: '新しいパネルバージョンに関するアラートの合計（1日1回）',
            oneDaySendCountRulesHelper: '1日に送信できる最大アラート回数',
            siteEndTimeRulesHelper: '期限が設定されていないウェブサイトはアラートをトリガーしません',
            autoRenewRulesHelper: '自動更新が有効な証明書で残り日数が31日未満の場合、アラートはトリガーされません',
            panelPwdEndTimeRulesHelper: 'パネルパスワード期限切れのアラートは設定されていない場合は使用できません',
            sslRulesHelper: 'すべてのSSL証明書',
            diskInfo: 'ディスク',
            monitoringType: '監視タイプ',
            autoRenew: '自動更新',
            useDisk: 'ディスク使用率',
            usePercentage: '使用割合',
            changeStatus: 'ステータスを変更',
            disableMsg:
                'アラートタスクを停止すると、このタスクはアラートメッセージを送信できなくなります。続行しますか？',
            enableMsg:
                'アラートタスクを有効にすると、このタスクはアラートメッセージを送信できるようになります。続行しますか？',
            useExceed: '使用率超過',
            useExceedRulesHelper: '設定した値を超えた場合にアラートをトリガー',
            cpuUseExceedAvg: '平均CPU使用率が指定した値を超過',
            memoryUseExceedAvg: '平均メモリ使用率が指定した値を超過',
            loadUseExceedAvg: '平均負荷使用率が指定した値を超過',
            cpuUseExceedAvgHelper: '指定時間内の平均CPU使用率が指定した値を超過',
            memoryUseExceedAvgHelper: '指定時間内の平均メモリ使用率が指定した値を超過',
            loadUseExceedAvgHelper: '指定時間内の平均負荷使用率が指定した値を超過',
            resourceAlertRulesHelper: '注意：30分以内に連続してアラートが発生した場合、SMSは1回だけ送信されます',
            specifiedTime: '指定時間',
            deleteTitle: 'アラートを削除',
            deleteMsg: 'アラートタスクを削除してもよろしいですか？',
            allSslTitle: 'すべてのウェブサイトSSL証明書の期限切れアラート',
            sslTitle: 'ウェブサイト{0}のSSL証明書期限切れアラート',
            allSiteEndTimeTitle: 'すべてのウェブサイト期限切れアラート',
            siteEndTimeTitle: 'ウェブサイト{0}の期限切れアラート',
            panelPwdEndTimeTitle: 'パネルパスワード期限切れアラート',
            panelUpdateTitle: '新しいパネルバージョン通知',
            cpuTitle: '高CPU使用率アラート',
            memoryTitle: '高メモリ使用率アラート',
            loadTitle: '高負荷アラート',
            diskTitle: 'マウントディレクトリ{0}の高ディスク使用率アラート',
            allDiskTitle: '高ディスク使用率アラート',
            timeRule: '残り時間が{0}日未満（処理されない場合、翌日再送信）',
            panelUpdateRule:
                '新しいパネルバージョンが検出されたときに1回アラートを送信（処理されない場合、翌日再送信）',
            avgRule: '指定された時間内の平均{1}使用率が{2}%を超過するとアラートがトリガーされ、1日{3}回送信',
            diskRule: 'マウントディレクトリ{0}のディスク使用率が{1}{2}を超過するとアラートがトリガーされ、1日{3}回送信',
            allDiskRule: 'ディスク使用率が{0}{1}を超過するとアラートがトリガーされ、1日{2}回送信',
            cpuName: 'CPU',
            memoryName: 'メモリ',
            loadName: '負荷',
            diskName: 'ディスク',
            syncAlertInfo: '手動プッシュ',
            syncAlertInfoMsg: 'アラートタスクを手動でプッシュしてもよろしいですか？',
            pushError: 'プッシュ失敗',
            pushSuccess: 'プッシュ成功',
            syncError: '同期失敗',
            success: 'アラート成功',
            pushing: 'プッシュ中...',
            error: 'アラート失敗',
            cleanLog: 'ログをクリア',
            cleanAlertLogs: 'アラートログをクリア',
            daily: '1日のアラート数：{0}',
            cumulative: '累積アラート数：{0}',
            clams: 'ウイルススキャン',
            taskName: 'タスク名',
            cronJobType: 'タスクタイプ',
            clamPath: 'スキャンディレクトリ',
            cronjob: 'Cronジョブ',
            app: 'アプリバックアップ',
            web: 'ウェブサイトバックアップ',
            database: 'データベースバックアップ',
            directory: 'ディレクトリバックアップ',
            log: 'ログバックアップ',
            snapshot: 'システムスナップショット',
            clamsRulesHelper: 'アラートが必要なウイルススキャンタスク',
            cronJobRulesHelper: 'このタイプのスケジュールタスクには設定が必要です',
            clamsTitle: 'ウイルススキャンタスク「{0}」が感染ファイルを検出したアラート',
            cronJobAppTitle: 'Cronジョブ - アプリバックアップ「{0}」タスク失敗アラート',
            cronJobWebsiteTitle: 'Cronジョブ - ウェブサイトバックアップ「{0}」タスク失敗アラート',
            cronJobDatabaseTitle: 'Cronジョブ - データベースバックアップ「{0}」タスク失敗アラート',
            cronJobDirectoryTitle: 'Cronジョブ - ディレクトリバックアップ「{0}」タスク失敗アラート',
            cronJobLogTitle: 'Cronジョブ - ログバックアップ「{0}」タスク失敗アラート',
            cronJobSnapshotTitle: 'Cronジョブ - システムスナップショット「{0}」タスク失敗アラート',
            cronJobShellTitle: 'Cronジョブ - シェルスクリプト「{0}」タスク失敗アラート',
            cronJobCurlTitle: 'Cronジョブ - URLアクセス「{0}」タスク失敗アラート',
            cronJobCutWebsiteLogTitle: 'Cronジョブ - ウェブサイトログカット「{0}」タスク失敗アラート',
            cronJobCleanTitle: 'Cronジョブ - キャッシュクリーニング「{0}」タスク失敗アラート',
            cronJobNtpTitle: 'Cronジョブ - サーバー時間同期「{0}」タスク失敗アラート',
            clamsRule: 'ウイルススキャンで感染ファイルが検出されたアラート、1日に{0}回送信',
            cronJobAppRule: 'アプリバックアップタスク失敗アラート、1日に{0}回送信',
            cronJobWebsiteRule: 'ウェブサイトバックアップタスク失敗アラート、1日に{0}回送信',
            cronJobDatabaseRule: 'データベースバックアップタスク失敗アラート、1日に{0}回送信',
            cronJobDirectoryRule: 'ディレクトリバックアップタスク失敗アラート、1日に{0}回送信',
            cronJobLogRule: 'ログバックアップタスク失敗アラート、1日に{0}回送信',
            cronJobSnapshotRule: 'スナップショットバックアップタスク失敗アラート、1日に{0}回送信',
            cronJobShellRule: 'シェルスクリプトタスク失敗アラート、1日に{0}回送信',
            cronJobCurlRule: 'URLアクセスタスク失敗アラート、1日に{0}回送信',
            cronJobCutWebsiteLogRule: 'ウェブサイトログカットタスク失敗アラート、1日に{0}回送信',
            cronJobCleanRule: 'キャッシュクリーニングタスク失敗アラート、1日に{0}回送信',
            cronJobNtpRule: 'サーバー時間同期タスク失敗アラート、1日に{0}回送信',
            alertSmsHelper: 'SMS制限：合計{0}メッセージ、{1}回使用済み',
            goBuy: 'さらに購入',
            phone: '電話',
            phoneHelper: 'アラートメッセージのために実際の電話番号を提供してください',
            dailyAlertNum: '毎日のアラート数',
            dailyAlertNumHelper: '1日のアラート数の合計、最大100件まで',
            timeRange: '時間範囲',
            sendTimeRange: '送信の時間範囲',
            sendTimeRangeHelper: 'アラートを送信できる時間範囲は{0}です',
            to: '-',
            startTime: '開始時間',
            endTime: '終了時間',
            defaultPhone: 'ライセンスに紐付けられたアカウントの電話番号をデフォルトにする',
            noticeAlert: '通知アラート',
            resourceAlert: 'リソースアラート',
            agentOfflineAlertHelper:
                'ノードでオフラインアラートを有効にすると、メインノードが30分ごとにスキャンしてアラートタスクを実行します。',
            offline: 'オフラインアラート',
            offlineHelper:
                'オフラインアラートに設定すると、メインノードが30分ごとにスキャンしてアラートタスクを実行します。',
            offlineOff: 'オフラインアラートを有効にする',
            offlineOffHelper:
                'オフラインアラートを有効にすると、メインノードが30分ごとにスキャンしてアラートタスクを実行します。',
            offlineClose: 'オフラインアラートを無効にする',
            offlineCloseHelper:
                'オフラインアラートを無効にすると、サブノードが独自にアラートを処理する必要があります。アラートの失敗を防ぐため、ネットワーク接続が良好であることを確認してください。',
            alertNotice: 'アラート通知',
            methodConfig: '通知方法の設定',
            commonConfig: 'グローバル設定',
            smsConfig: 'SMS',
            smsConfigHelper: 'SMS 通知の電話番号を設定する',
            emailConfig: 'メール',
            emailConfigHelper: 'SMTP メール送信サービスを設定する',
            deleteConfigTitle: 'アラート設定を削除',
            deleteConfigMsg: 'アラート設定を削除してもよろしいですか？',
            test: 'テスト',
            alertTestOk: 'テスト通知に成功しました',
            alertTestFailed: 'テスト通知に失敗しました',
            displayName: '表示名',
            sender: '送信元アドレス',
            password: 'パスワード',
            host: 'SMTP サーバー',
            port: 'ポート番号',
            encryption: '暗号化方式',
            recipient: '受信者',
            licenseTime: 'ライセンスの有効期限切れ通知',
            licenseTimeTitle: 'ライセンスの有効期限切れ通知',
            displayNameHelper: 'メールの送信者表示名',
            senderHelper: 'メール送信に使用するメールアドレス',
            passwordHelper: 'メールサービスの認証コード',
            hostHelper: 'SMTP サーバーアドレス（例：smtp.qq.com）',
            portHelper: 'SSLは通常465、TLSは通常587',
            sslHelper: 'SMTPポートが465の場合、通常はSSLが必要です',
            tlsHelper: 'SMTPポートが587の場合、通常はTLSが必要です',
        },
        theme: {
            lingXiaGold: '凌霞金',
            classicBlue: 'クラシックブルー',
            freshGreen: 'フレッシュグリーン',
            customColor: 'カスタムカラー',
            setDefault: 'デフォルトに戻す',
            setDefaultHelper: 'テーマカラーを初期状態に戻そうとしています。続行しますか？',
            setHelper: '現在選択されているテーマカラーを保存しようとしています。続行しますか？',
        },
        exchange: {
            exchange: 'ファイル交換',
            exchangeConfirm: '{0} ノードのファイル/フォルダ {1} を {2} ノードの {3} ディレクトリに転送しますか？',
        },
        cluster: {
            cluster: 'アプリケーションの高可用性',
            name: 'クラスタ名',
            addCluster: 'クラスタを追加',
            installNode: 'ノードをインストール',
            master: 'マスターノード',
            slave: 'スレーブノード',
            replicaStatus: 'マスタースレーブステータス',
            unhealthyDeleteError:
                'インストールノードのステータスが異常です。ノードリストを確認してから再試行してください！',
            replicaStatusError: 'ステータスの取得が異常です。マスターノードを確認してください。',
            masterHostError: 'マスターノードのIPは127.0.0.1にできません',
        },
    },
};
export default {
    ...fit2cloudEnLocale,
    ...message,
};
