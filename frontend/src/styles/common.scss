html {
    font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Arial, sans-serif;
}
.flx-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flx-align-center {
    display: flex;
    align-items: center;
}

.sle {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.fade-transform-leave-active,
.fade-transform-enter-active {
    transition: all 0.2s;
}
.fade-transform-enter-from {
    opacity: 0;
    transition: all 0.2s;
    transform: translateX(-30px);
}
.fade-transform-leave-to {
    opacity: 0;
    transition: all 0.2s;
    transform: translateX(30px);
}

/* Breadcrumb */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
    transition: all 0.2s ease;
}
.breadcrumb-enter-from,
.breadcrumb-leave-active {
    opacity: 0;
    transform: translateX(10px);
}
.breadcrumb-leave-active {
    position: absolute;
    z-index: -1;
}

::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background-color: white;
}
::-webkit-scrollbar-thumb {
    background-color: #dddee0;
    border-radius: 20px;
    box-shadow: inset 0 0 0 white;
}

#nprogress {
    .bar {
        background: $primary-color !important;
    }
    .spinner-icon {
        border-top-color: $primary-color !important;
        border-left-color: $primary-color !important;
    }
    .peg {
        box-shadow: 0 0 10px $primary-color, 0 0 5px $primary-color !important;
    }
}

.form-button {
    float: right;
}

.input-help {
    font-size: 12px;
    word-break: keep-all;
    color: #adb0bc;
    width: 100%;
    display: inline-block;
    white-space: pre-line;
}

.input-error {
    font-size: 12px;
    word-break: keep-all;
    color: red;
    transform: scale(0.9);
    transform-origin: left;
    width: 110%;
    display: inline-block;
    white-space: normal;
}

.mask {
    width: 100%;
    height: 100%;
    opacity: 0.4;
    top: 0;
    left: 0;
    pointer-events: none;
}

.mask-prompt {
    position: absolute;
    z-index: 1;
    top: 220px;
    left: 45%;
    transform: translate(-50%, -50%);
    width: 400px;
    text-align: center;
    font-size: 14px;
    .bt {
        margin-top: -2px;
    }
}

.sidebar-container-popper {
    .el-menu--popup-right-start {
        background-color: rgba(0, 94, 235, 0.1);
    }
}

.search-button {
    width: 250px;
}

.drawer-header-button {
    span {
        color: currentColor !important;
        font-size: var(--el-font-size-base) !important;
    }
    .active-button {
        color: var(--el-button-hover-text-color);
        border-color: var(--el-button-hover-border-color);
    }
}

.app-status {
    font-size: 12px;

    .el-card {
        --el-card-padding: 9px;

        .buttons {
            margin-left: 100px;
        }

        .status-content {
            margin-left: 50px;
        }
    }
}

.mini-border-card {
    width: 100%;
    .el-card__body {
        --el-card-padding: 12px 12px 0 22px;
    }
}

.xterm-viewport::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background-color: #000000;
}

.status-count {
    font-size: 24px;
}
.status-label {
    font-size: 14px;
    color: #646a73;
}

.table-link {
    color: $primary-color;
    cursor: pointer;
}

.table-link:hover {
    opacity: 0.6;
}

.app-warn {
    text-align: center;
    margin-top: 100px;
    span:first-child {
        color: #bbbfc4;
    }

    span:nth-child(2) {
        color: $primary-color;
        cursor: pointer;
    }

    span:nth-child(2):hover {
        color: var(--el-color-primary-light-7);
    }

    img {
        width: 300px;
        height: 300px;
    }
}

.common-prompt {
    margin-bottom: 20px !important;
}

.mini-form-item {
    width: 40% !important;
}

.pre-select {
    width: 85px !important;
}

.el-input-group__append {
    border-left: 0;
    background-color: var(--el-fill-color-light) !important;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    box-shadow: 0 1px 0 0 var(--el-input-border-color) inset, 0 -1px 0 0 var(--el-input-border-color) inset,
        -1px 0 0 0 var(--el-input-border-color) inset;

    &:hover {
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9) !important;
    }
}

.tag-button {
    margin-right: 10px;
    &.no-active {
        background: none;
        border: none;
    }
}

.limit-height-popover {
    max-height: 300px;
    overflow: auto;
}

.router-button {
    margin-right: 20px;
}

.text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.star-center {
    height: 16px;
    display: inline-block;
    vertical-align: middle;
}

.p-ml-5 {
    margin-left: 5px !important;
}

.p-w-200 {
    width: 200px !important;
}

.card-interval {
    margin-top: 7px !important;
}

.p-w-300 {
    width: 300px !important;
}

.p-w-100 {
    width: 100px !important;
}

.p-w-150 {
    width: 150px !important;
}

.p-w-400 {
    width: 400px !important;
}

.p-ml-20 {
    margin-left: 20px !important;
}

.p-mt-20 {
    margin-top: 20px !important;
}

.el-tag {
    cursor: pointer;
}

.dialog-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.monaco-editor-tree-light .el-tree-node__content:hover {
    background-color: #e5eefd;
}

.monaco-editor-tree-light .el-tree-node.is-current > .el-tree-node__content {
    background-color: #e5eefd;
}

.monaco-editor-tree-dark .el-tree-node__content:hover {
    background-color: #111417;
}

.monaco-editor-tree-dark .el-tree-node.is-current > .el-tree-node__content {
    background-color: #111417;
}

.check-label {
    background: var(--panel-main-bg-color-10) !important;
    .check-label-a {
        color: var(--panel-color-primary);
    }
}
.check-content {
    background: var(--panel-main-bg-color-10);
    pre {
        margin: 0;
        width: 350px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.el-descriptions {
    overflow: hidden;
    text-overflow: ellipsis;
}

.code-dialog {
    .el-dialog__header {
        --el-dialog-padding-primary: 0px !important;
    }
}
