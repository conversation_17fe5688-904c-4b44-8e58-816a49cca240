.mobile {
    .monitor-tags {
        position: inherit;
        top: 13px;
    }
    .mobile-monitor-chart {
        margin-top: 20px !important;
    }
    .search-button {
        float: none !important;
        .table-button {
            display: inline-flex !important;
        }
    }

    .app-card {
        .app-button {
            margin-right: 0 !important;
        }
    }
    .install-card .a-detail {
        .d-name {
            height: auto !important;
            .h-button {
                float: none !important;
                margin: 5px;
            }
        }
        .d-button {
            min-width: auto !important;
        }
        .d-description {
            overflow: inherit !important;
        }
    }
    .router_card_button {
        padding: 2px 0;
    }
    .el-drawer.rtl {
        width: 80% !important;
    }
    .site-form-wrapper {
        width: 90% !important;
        min-width: auto !important;
        .el-form-item__label {
            width: auto !important;
        }
    }
    .moblie-form {
        overflow: auto;
        .el-input {
            width: 350px;
        }
        .el-textarea__inner {
            width: 350px;
        }
    }

    .app-status {
        font-size: 12px;
        .el-card {
            .status-content {
                margin-left: 20px;
            }
        }
    }

    .mini-form-item {
        width: 100% !important;
    }

    .database-status {
        .title {
            margin-left: 10px !important;
        }
        .content {
            margin-left: 10px !important;
        }
    }

    @media only screen and (max-width: 768px) {
        .el-col-xs-24 {
            margin-bottom: 10px;
        }
    }
    .el-dialog {
        --el-dialog-width: 80% !important;
    }
}
