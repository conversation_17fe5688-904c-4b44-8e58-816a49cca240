:root {
    --panel-gradient-end-color: var(--el-color-primary-light-7);

    --el-color-primary-light-1: #196eed;
    --el-color-primary-light-2: #337eef;
    --el-color-primary-light-4: #669ef3;
    --el-color-primary-light-6: #99bef7;

    --panel-color-primary: #005eeb;
    --panel-color-primary-light-8: #196eed;
    --panel-color-primary-light-1: #196eed;
    --panel-color-primary-light-2: #337eef;
    --panel-color-primary-light-3: #4c8ef1;
    --panel-color-primary-light-4: #669ef3;
    --panel-color-primary-light-5: #7faef5;
    --panel-color-primary-light-6: #99bef7;
    --panel-color-primary-light-7: #b2cef9;
    --panel-color-primary-light-9: #e5eefd;

    --el-color-primary: var(--panel-color-primary);
    --el-color-primary-light-3: var(--panel-color-primary-light-1);
    --el-color-primary-light-5: var(--panel-color-primary-light-5);
    --el-color-primary-light-7: var(--panel-color-primary-light-7);
    --el-color-primary-light-8: var(--panel-color-primary-light-8);
    --el-color-primary-light-9: var(--panel-color-primary-light-9);
    --panel-code-header-footer-color: var(--panel-color-primary-light-9);

    --el-text-color-regular: #646a73;
}

html {
    --el-box-shadow-light: 0px 0px 4px rgba(0, 94, 235, 0.1) !important;

    // * menu
    --el-menu-item-bg-color: rgba(255, 255, 255, 0.3);
    --el-menu-item-bg-color-active: #ffffff;
    --panel-main-bg-color-9: #f4f4f4;
    --panel-menu-bg-color: rgba(0, 94, 235, 0.1);
    --panel-menu-width: 180px;
    --panel-menu-hide-width: 75px;
    --panel-text-color: #1f2329;
    --panel-border: 1px solid #f2f2f2;
    --panel-button-active: #ffffff;
    --panel-button-text-color: var(--panel-color-primary);
    --panel-button-bg-color: #ffffff;
    --panel-footer-border: #e4e7ed;
    --panel-terminal-tag-bg-color: #efefef;
    --panel-terminal-tag-active-bg-color: #575758;
    --panel-terminal-tag-active-text-color: #ebeef5;
    --panel-terminal-tag-hover-text-color: #575758;
    --panel-terminal-bg-color: #1e1e1e;
    --panel-logs-bg-color: #1e1e1e;
    --panel-alert-bg-color: rgba(0, 94, 235, 0.03);

    --panel-alert-bg: #e2e4ec;
    --panel-path-bg: #ffffff;
    --panel-footer-bg: #ffffff;
    --panel-pie-bg-color: #ffffff;

    --el-fill-color-light: #ffffff;
    --el-disabled-bg-color: var(--panel-main-bg-color-9) !important;
}

.el-notification {
    z-index: 99999 !important;
}

.el-message {
    z-index: 99999 !important;
}

.table-box {
    display: flex;
    flex-direction: column;
    height: 100%;

    .table-search {
        display: flex;
        margin-bottom: 10px;
        .el-form {
            max-width: 1260px;
            .el-form-item {
                margin-right: 5px;
                .el-input,
                .el-select,
                .el-date-editor--timerange {
                    width: 210px;
                }
                .el-date-editor--datetimerange,
                .el-date-editor--daterange {
                    width: 400px;
                }

                .el-range-editor.el-input__wrapper {
                    padding: 0 10px;
                }

                .el-select__tags {
                    overflow: hidden;
                    white-space: nowrap;
                }
            }
            .more-item {
                display: inline;
            }
        }
        .search-operation {
            margin-left: 15px;
            white-space: nowrap;
            .search-isOpen {
                margin-left: 20px;
            }
        }
    }

    .table-header {
        .header-button-lf {
            float: left;
            .el-button {
                margin-bottom: 20px;
            }
        }
        .header-button-ri {
            float: right;
            .el-button {
                margin-bottom: 20px;
            }
        }
    }

    .el-table {
        flex: 1;
        .el-table__header th,
        .el-table__body td {
            text-align: center;
        }
        .el-table__header th {
            height: 50px;
            font-size: 15px;
            font-weight: bold;
            color: #252525;
            background: #fafafa;
        }

        .table-image {
            width: 50px;
            height: 50px;
            border-radius: 50%;
        }

        .el-table__header .el-table__cell > .cell {
            white-space: nowrap;
        }
        .el-table__row {
            height: 52px;
        }

        .el-table__empty-block {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .table-empty {
            line-height: 30px;
        }
    }

    .el-pagination {
        display: flex;
        justify-content: flex-end;
        margin: 23px 0 10px;
    }
}

.el-drawer {
    .el-drawer__header {
        padding: 15px 20px 14px;
        margin-bottom: 0;
        border-bottom: var(--panel-border);
        span {
            font-size: 17px;
            color: #303133;
        }
    }

    .el-drawer__footer {
        border-top: var(--panel-border);
    }
    .el-select {
        width: 100%;
    }

    .drawer-multiColumn-form {
        display: flex;
        flex-wrap: wrap;
        .el-form-item {
            width: 47%;
            &:nth-child(2n-1) {
                margin-right: 5%;
            }
        }
    }
}

.el-dialog {
    border-radius: 5px;

    .el-dialog__header {
        .el-dialog__title {
            font-size: 17px;
        }
    }
}

.row-box {
    display: flex;
    flex-flow: wrap;
    .el-card {
        min-width: 100%;
        height: 100%;
        margin-right: 20px;
        border: 0;
    }
}

.el-avatar {
    --el-avatar-bg-color: #ffffff !important;
    border: 0.5px solid #eaeaea;
    box-shadow: 0px 0px 2px 1px rgba(186, 186, 186, 0.22);
    border-radius: 8px !important;
    padding: 5px;
}

.el-card {
    border: none !important;
}

.el-input-group__append {
    button.el-button {
        span {
            vertical-align: text-top !important;
        }
    }
}

.el-input.is-disabled .el-input__wrapper {
    --el-disabled-bg-color: var(--panel-main-bg-color-9);
}

.el-radio-button__inner {
    [class*='el-icon'] + span {
        margin-left: 6px;
    }
    span {
        vertical-align: text-top !important;
    }
}

.logo {
    color: var(--el-color-primary);
}

.custom-input-textarea {
    background-color: #f5f7fa !important;
    color: var(--el-color-info) !important;
}

.custom-input-textarea:hover {
    color: var(--el-color-primary) !important;
    background-color: var(--el-color-primary-light-9) !important;
    border-color: var(--el-button-border-color) !important;
}