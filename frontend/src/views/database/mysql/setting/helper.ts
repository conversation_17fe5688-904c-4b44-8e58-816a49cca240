export const planOptions = [
    {
        id: 1,
        title: '1-2GB',
        data: {
            version: '',
            key_buffer_size: 32,
            query_cache_size: 32,
            tmp_table_size: 32,
            innodb_buffer_pool_size: 64,
            innodb_log_buffer_size: 64,
            sort_buffer_size: 256,
            read_buffer_size: 256,
            read_rnd_buffer_size: 256,
            join_buffer_size: 512,
            thread_stack: 256,
            binlog_cache_size: 64,
            thread_cache_size: 64,
            table_open_cache: 128,
            max_connections: 100,
        },
    },
    {
        id: 2,
        title: '2-4GB',
        data: {
            version: '',
            key_buffer_size: 64,
            query_cache_size: 64,
            tmp_table_size: 64,
            innodb_buffer_pool_size: 128,
            innodb_log_buffer_size: 64,
            sort_buffer_size: 512,
            read_buffer_size: 512,
            read_rnd_buffer_size: 512,
            join_buffer_size: 1024,
            thread_stack: 256,
            binlog_cache_size: 64,
            thread_cache_size: 96,
            table_open_cache: 192,
            max_connections: 200,
        },
    },
    {
        id: 3,
        title: '4-8GB',
        data: {
            version: '',
            key_buffer_size: 128,
            query_cache_size: 128,
            tmp_table_size: 128,
            innodb_buffer_pool_size: 256,
            innodb_log_buffer_size: 64,
            sort_buffer_size: 1024,
            read_buffer_size: 1024,
            read_rnd_buffer_size: 768,
            join_buffer_size: 2048,
            thread_stack: 256,
            binlog_cache_size: 128,
            thread_cache_size: 128,
            table_open_cache: 384,
            max_connections: 300,
        },
    },
    {
        id: 4,
        title: '8-16GB',
        data: {
            version: '',
            key_buffer_size: 256,
            query_cache_size: 256,
            tmp_table_size: 256,
            innodb_buffer_pool_size: 512,
            innodb_log_buffer_size: 64,
            sort_buffer_size: 1024,
            read_buffer_size: 2048,
            read_rnd_buffer_size: 1024,
            join_buffer_size: 2048,
            thread_stack: 384,
            binlog_cache_size: 192,
            thread_cache_size: 192,
            table_open_cache: 1024,
            max_connections: 400,
        },
    },
    {
        id: 5,
        title: '16-32GB',
        data: {
            version: '',
            key_buffer_size: 1024,
            query_cache_size: 384,
            tmp_table_size: 1024,
            innodb_buffer_pool_size: 1024,
            innodb_log_buffer_size: 64,
            sort_buffer_size: 4096,
            read_buffer_size: 4096,
            read_rnd_buffer_size: 2048,
            join_buffer_size: 4096,
            thread_stack: 512,
            binlog_cache_size: 256,
            thread_cache_size: 256,
            table_open_cache: 2048,
            max_connections: 500,
        },
    },
];
