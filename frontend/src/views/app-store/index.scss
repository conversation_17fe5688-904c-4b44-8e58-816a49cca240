.install-card {
    margin-top: 10px;
    cursor: pointer;
    padding: 10px;
    .icon {
        text-align: center;
    }

    .a-detail {
        .d-name {
            .name {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 120px;
                font-weight: 500;
                font-size: 18px;
                color: var(--panel-text-color);
            }
            .status {
                margin-left: 5px;
            }
            .h-button {
                float: right;
            }
            .msg {
                margin-left: 5px;
            }
        }

        .d-description {
            margin-top: 10px;
            overflow: hidden;
            .el-tag {
                margin-right: 5px;
            }
        }
        .description {
            margin-top: 10px;
            font-size: 14px;
            color: var(--el-text-color-regular);
        }
        .d-button {
            margin-top: 10px;
            min-width: 330px;
        }
    }

    .e-card {
        border: var(--panel-border) !important;
        &:hover {
            cursor: pointer;
            border: 1px solid var(--el-color-primary) !important;
        }
    }
}

.table-button {
    display: inline;
    margin-right: 5px;
}

.app-divider {
    margin-top: 5px;
    border: 0;
    border-top: var(--panel-border);
}

.update-prompt {
    text-align: center;
    margin-top: 100px;

    span {
        color: #bbbfc4;
    }

    img {
        width: 300px;
        height: 300px;
    }
}

.tag-button {
    &.no-active {
        background: none;
        border: none;
    }
}

.page-button {
    float: right;
    margin-bottom: 10px;
    margin-top: 10px;
}
