{
    "compilerOptions": {
        "target": "esnext",
        "useDefineForClassFields": true,
        "module": "esnext",
        "moduleResolution": "node",

        /* Strict Type-Checking Options */
        "strict": false /* Enable all strict type-checking options. */,
        // "noImplicitAny": true,                 /* Raise error on expressions and declarations with an implied 'any' type. */
        // "strictNullChecks": true,              /* Enable strict null checks. */
        // "strictFunctionTypes": true,           /* Enable strict checking of function types. */
        // "strictBindCallApply": true,           /* Enable strict 'bind', 'call', and 'apply' methods on functions. */
        // "strictPropertyInitialization": true,  /* Enable strict checking of property initialization in classes. */
        // "noImplicitThis": true,                /* Raise error on 'this' expressions with an implied 'any' type. */
        // "alwaysStrict": true,                  /* Parse in strict mode and emit "use strict" for each source file. */

        "jsx": "preserve",
        "jsxFactory": "h",
        "jsxFragmentFactory": "Fragment",
        "sourceMap": true,
        "resolveJsonModule": true,
        "esModuleInterop": true,
        "lib": ["esnext", "dom"],
        // 解析非相对模块名的基准目录
        "baseUrl": "./",
        // 模块名到基于 baseUrl的路径映射的列表。
        "paths": {
            "@": ["src"],
            "@/*": ["src/*"]
        },
        // 跳过库检查，解决打包失败
        "skipLibCheck": true,
        "ignoreDeprecations": "5.0",
    },
    // "include": ["./src/views"],
    "exclude": ["node_modules", "dist", "**/*.js", "*.json", "*.md"]
}
