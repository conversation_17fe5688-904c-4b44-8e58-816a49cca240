{"name": "1panel-frontend", "private": true, "version": "2.0", "description": "1Panel 前端", "scripts": {"dev": "vite", "serve": "vite", "build:dev": "vite build --mode development", "build:test": "vue-tsc --noEmit && vite build --mode test", "build:pro": "vite build --mode production", "preview": "vite preview", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "release": "standard-version", "commit": "git pull && git add -A && git-cz && git push", "prettier:comment": "自动格式化当前目录下的所有文件", "prettier": "prettier --write ."}, "dependencies": {"@codemirror/lang-html": "^6.4.9", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-php": "^6.0.1", "@codemirror/language": "^6.10.2", "@codemirror/legacy-modes": "^6.4.0", "@codemirror/theme-one-dark": "^6.1.2", "@element-plus/icons-vue": "^1.1.4", "@highlightjs/vue-plugin": "^2.1.0", "@vue-office/docx": "^1.6.2", "@vue-office/excel": "^1.7.8", "@vueuse/core": "^8.9.4", "@xterm/addon-fit": "^0.10.0", "@xterm/xterm": "^5.5.0", "anser": "^2.3.2", "axios": "^1.7.2", "codemirror": "^6.0.1", "crypto-js": "^4.2.0", "echarts": "^5.5.0", "element-plus": "2.9.9", "fit2cloud-ui-plus": "^1.2.2", "highlight.js": "^11.9.0", "js-base64": "^3.7.7", "jsencrypt": "^3.3.2", "md-editor-v3": "^2.11.3", "monaco-editor": "^0.50.0", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^1.6.1", "qs": "^6.12.1", "screenfull": "^6.0.2", "unplugin-vue-define-options": "^0.7.3", "uuid": "^10.0.0", "vue": "^3.4.27", "vue-clipboard3": "^2.0.0", "vue-codemirror": "^6.1.1", "vue-demi": "^0.14.6", "vue-i18n": "^10.0.5", "vue-router": "^4.3.3", "vue-virtual-scroller": "^2.0.0-beta.8"}, "devDependencies": {"@types/node": "^20.14.8", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.22.0", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "autoprefixer": "^10.4.7", "commitizen": "^4.2.4", "eslint": "^8.57.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^8.7.1", "lint-staged": "^12.4.2", "postcss": "^8.4.31", "postcss-html": "^1.4.1", "prettier": "^2.6.2", "rollup-plugin-visualizer": "^5.5.4", "sass": "^1.83.0", "standard-version": "^9.5.0", "stylelint": "^15.10.1", "tailwindcss": "^3.4.1", "typescript": "^4.5.4", "unplugin-auto-import": "^0.16.4", "unplugin-vue-components": "^0.25.0", "vite": "^5.3.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.2", "vite-plugin-vue-setup-extend": "^0.4.0", "vite-svg-loader": "^5.1.0", "vue-tsc": "^0.29.8"}, "overrides": {"esbuild": "npm:esbuild-wasm@latest"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}