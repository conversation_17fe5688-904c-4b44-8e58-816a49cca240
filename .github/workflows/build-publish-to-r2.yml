name: Create Release And Upload Cloudflare R2
on:
  push:
    tags:
      - 'v*'
jobs:
  create-release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '20.2'
      - name: Build Web
        run: |
          cd frontend && npm install && npm run build:pro
        env:
          NODE_OPTIONS: --max-old-space-size=8192
      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.23'
      - name: Build Release
        uses: goreleaser/goreleaser-action@v6
        with:
          distribution: goreleaser
          version: '~> v2'
          args: release --skip=publish --clean
      - name: Upload Assets
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          draft: true
          files: |
            dist/*.tar.gz
            dist/checksums.txt
      - name: Setup Rclone
        uses: AnimMouse/setup-rclone@v1
        with:
          rclone_config: ${{ secrets.RCLONE_CONFIG }}
      - name: Upload to Cloudflare R2
        run: |
          rclone copy dist/ cloudflare_r2:package/v2/stable/${{ github.ref_name }}/release/ --include "*.tar.gz" --include "checksums.txt" --progress
