name: Create Release And Upload assets
on:
  push:
    tags:
      - 'v*'
jobs:
  create-release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '20.2'
      - name: Build Web
        run: |
          cd frontend && npm install && npm run build:pro
        env:
          NODE_OPTIONS: --max-old-space-size=8192
      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.23'
      - name: Build Release
        uses: goreleaser/goreleaser-action@v6
        with:
          distribution: goreleaser
          version: '~> v2'
          args: release --skip=publish --clean
      - name: Upload Assets
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          draft: true
          files: |
            dist/*.tar.gz
            dist/checksums.txt
      - name: Setup OSSUTIL
        uses: yizhoumo/setup-ossutil@v2
        with:
          endpoint: ${{ secrets.OSS_ENDPOINT }}
          access-key-id: ${{ secrets.OSS_ACCESS_KEY_ID }}
          access-key-secret: ${{ secrets.OSS_ACCESS_KEY_SECRET }}
          ossutil-version: '1.7.18'
      - name: Upload Assets to OSS
        run: ossutil cp -r dist/ oss://resource-fit2cloud-com/1panel/package/v2/stable/${{ github.ref_name }}/release/ --include "*.tar.gz" --include "checksums.txt" --only-current-dir --force
